using UnityEngine;

namespace Unity.VisualScripting
{
    /// <summary>
    /// Rounds each component of a 4D vector.
    /// </summary>
    [UnitCategory("Math/Vector 4")]
    [UnitTitle("Round")]
    public sealed class Vector4Round : Round<Vector4, Vector4>
    {
        protected override Vector4 Floor(Vector4 input)
        {
            return new Vector4
            (
                Mathf.Floor(input.x),
                <PERSON><PERSON><PERSON>(input.y),
                <PERSON><PERSON><PERSON>(input.z),
                <PERSON><PERSON>.Floor(input.w)
            );
        }

        protected override Vector4 AwayFromZero(Vector4 input)
        {
            return new Vector4
            (
                Mathf.Round(input.x),
                <PERSON><PERSON>.Round(input.y),
                <PERSON><PERSON>.Round(input.z),
                <PERSON><PERSON>.Round(input.w)
            );
        }

        protected override Vector4 Ceiling(Vector4 input)
        {
            return new Vector4
            (
                Mathf.<PERSON>il(input.x),
                <PERSON><PERSON><PERSON>(input.y),
                <PERSON><PERSON><PERSON>(input.z),
                <PERSON><PERSON><PERSON>il(input.w)
            );
        }
    }
}
