fileFormatVersion: 2
guid: dd9de6d95994d2a44884e6f488c24bb0
ModelImporter:
  serializedVersion: 19301
  internalIDToNameTable:
  - first:
      1: 100000
    second: Chest
  - first:
      1: 100002
    second: Head
  - first:
      1: 100004
    second: Hips
  - first:
      1: 100006
    second: Jaw
  - first:
      1: 100008
    second: JawEnd
  - first:
      1: 100010
    second: LeftEye
  - first:
      1: 100012
    second: LeftFoot
  - first:
      1: 100014
    second: LeftHand
  - first:
      1: 100016
    second: LeftIndexDistal
  - first:
      1: 100018
    second: LeftIndexDistalEnd
  - first:
      1: 100020
    second: LeftIndexIntermediate
  - first:
      1: 100022
    second: LeftIndexProximal
  - first:
      1: 100024
    second: Left<PERSON>owerArm
  - first:
      1: 100026
    second: LeftLowerLeg
  - first:
      1: 100028
    second: LeftMiddleDistal
  - first:
      1: 100030
    second: LeftMiddleDistalEnd
  - first:
      1: 100032
    second: LeftMiddleIntermediate
  - first:
      1: 100034
    second: LeftMiddleProximal
  - first:
      1: 100036
    second: LeftPinkyDistal
  - first:
      1: 100038
    second: LeftPinkyDistalEnd
  - first:
      1: 100040
    second: LeftPinkyIntermediate
  - first:
      1: 100042
    second: LeftPinkyProximal
  - first:
      1: 100044
    second: LeftRingDistal
  - first:
      1: 100046
    second: LeftRingDistalEnd
  - first:
      1: 100048
    second: LeftRingIntermediate
  - first:
      1: 100050
    second: LeftRingProximal
  - first:
      1: 100052
    second: LeftShoulder
  - first:
      1: 100054
    second: LeftThumbDistal
  - first:
      1: 100056
    second: LeftThumbDistalEnd
  - first:
      1: 100058
    second: LeftThumbIntermediate
  - first:
      1: 100060
    second: LeftThumbProximal
  - first:
      1: 100062
    second: LeftToes
  - first:
      1: 100064
    second: LeftToesEnd
  - first:
      1: 100066
    second: LeftUpperArm
  - first:
      1: 100068
    second: LeftUpperLeg
  - first:
      1: 100070
    second: LProp_root
  - first:
      1: 100072
    second: LProp_target
  - first:
      1: 100074
    second: Neck
  - first:
      1: 100076
    second: RightEye
  - first:
      1: 100078
    second: RightFoot
  - first:
      1: 100080
    second: RightHand
  - first:
      1: 100082
    second: RightIndexDistal
  - first:
      1: 100084
    second: RightIndexDistalEnd
  - first:
      1: 100086
    second: RightIndexIntermediate
  - first:
      1: 100088
    second: RightIndexProximal
  - first:
      1: 100090
    second: RightLowerArm
  - first:
      1: 100092
    second: RightLowerLeg
  - first:
      1: 100094
    second: RightMiddleDistal
  - first:
      1: 100096
    second: RightMiddleDistalEnd
  - first:
      1: 100098
    second: RightMiddleIntermediate
  - first:
      1: 100100
    second: RightMiddleProximal
  - first:
      1: 100102
    second: RightPinkyDistal
  - first:
      1: 100104
    second: RightPinkyDistalEnd
  - first:
      1: 100106
    second: RightPinkyIntermediate
  - first:
      1: 100108
    second: RightPinkyProximal
  - first:
      1: 100110
    second: RightRingDistal
  - first:
      1: 100112
    second: RightRingDistalEnd
  - first:
      1: 100114
    second: RightRingIntermediate
  - first:
      1: 100116
    second: RightRingProximal
  - first:
      1: 100118
    second: RightShoulder
  - first:
      1: 100120
    second: RightThumbDistal
  - first:
      1: 100122
    second: RightThumbDistalEnd
  - first:
      1: 100124
    second: RightThumbIntermediate
  - first:
      1: 100126
    second: RightThumbProximal
  - first:
      1: 100128
    second: RightToes
  - first:
      1: 100130
    second: RightToesEnd
  - first:
      1: 100132
    second: RightUpperArm
  - first:
      1: 100134
    second: RightUpperLeg
  - first:
      1: 100136
    second: Root
  - first:
      1: 100138
    second: RProp_root
  - first:
      1: 100140
    second: RProp_target
  - first:
      1: 100142
    second: Spine
  - first:
      1: 100144
    second: UpperChest
  - first:
      1: 100146
    second: //RootNode
  - first:
      1: 100148
    second: Skeleton
  - first:
      4: 400000
    second: Chest
  - first:
      4: 400002
    second: Head
  - first:
      4: 400004
    second: Hips
  - first:
      4: 400006
    second: Jaw
  - first:
      4: 400008
    second: JawEnd
  - first:
      4: 400010
    second: LeftEye
  - first:
      4: 400012
    second: LeftFoot
  - first:
      4: 400014
    second: LeftHand
  - first:
      4: 400016
    second: LeftIndexDistal
  - first:
      4: 400018
    second: LeftIndexDistalEnd
  - first:
      4: 400020
    second: LeftIndexIntermediate
  - first:
      4: 400022
    second: LeftIndexProximal
  - first:
      4: 400024
    second: LeftLowerArm
  - first:
      4: 400026
    second: LeftLowerLeg
  - first:
      4: 400028
    second: LeftMiddleDistal
  - first:
      4: 400030
    second: LeftMiddleDistalEnd
  - first:
      4: 400032
    second: LeftMiddleIntermediate
  - first:
      4: 400034
    second: LeftMiddleProximal
  - first:
      4: 400036
    second: LeftPinkyDistal
  - first:
      4: 400038
    second: LeftPinkyDistalEnd
  - first:
      4: 400040
    second: LeftPinkyIntermediate
  - first:
      4: 400042
    second: LeftPinkyProximal
  - first:
      4: 400044
    second: LeftRingDistal
  - first:
      4: 400046
    second: LeftRingDistalEnd
  - first:
      4: 400048
    second: LeftRingIntermediate
  - first:
      4: 400050
    second: LeftRingProximal
  - first:
      4: 400052
    second: LeftShoulder
  - first:
      4: 400054
    second: LeftThumbDistal
  - first:
      4: 400056
    second: LeftThumbDistalEnd
  - first:
      4: 400058
    second: LeftThumbIntermediate
  - first:
      4: 400060
    second: LeftThumbProximal
  - first:
      4: 400062
    second: LeftToes
  - first:
      4: 400064
    second: LeftToesEnd
  - first:
      4: 400066
    second: LeftUpperArm
  - first:
      4: 400068
    second: LeftUpperLeg
  - first:
      4: 400070
    second: LProp_root
  - first:
      4: 400072
    second: LProp_target
  - first:
      4: 400074
    second: Neck
  - first:
      4: 400076
    second: RightEye
  - first:
      4: 400078
    second: RightFoot
  - first:
      4: 400080
    second: RightHand
  - first:
      4: 400082
    second: RightIndexDistal
  - first:
      4: 400084
    second: RightIndexDistalEnd
  - first:
      4: 400086
    second: RightIndexIntermediate
  - first:
      4: 400088
    second: RightIndexProximal
  - first:
      4: 400090
    second: RightLowerArm
  - first:
      4: 400092
    second: RightLowerLeg
  - first:
      4: 400094
    second: RightMiddleDistal
  - first:
      4: 400096
    second: RightMiddleDistalEnd
  - first:
      4: 400098
    second: RightMiddleIntermediate
  - first:
      4: 400100
    second: RightMiddleProximal
  - first:
      4: 400102
    second: RightPinkyDistal
  - first:
      4: 400104
    second: RightPinkyDistalEnd
  - first:
      4: 400106
    second: RightPinkyIntermediate
  - first:
      4: 400108
    second: RightPinkyProximal
  - first:
      4: 400110
    second: RightRingDistal
  - first:
      4: 400112
    second: RightRingDistalEnd
  - first:
      4: 400114
    second: RightRingIntermediate
  - first:
      4: 400116
    second: RightRingProximal
  - first:
      4: 400118
    second: RightShoulder
  - first:
      4: 400120
    second: RightThumbDistal
  - first:
      4: 400122
    second: RightThumbDistalEnd
  - first:
      4: 400124
    second: RightThumbIntermediate
  - first:
      4: 400126
    second: RightThumbProximal
  - first:
      4: 400128
    second: RightToes
  - first:
      4: 400130
    second: RightToesEnd
  - first:
      4: 400132
    second: RightUpperArm
  - first:
      4: 400134
    second: RightUpperLeg
  - first:
      4: 400136
    second: Root
  - first:
      4: 400138
    second: RProp_root
  - first:
      4: 400140
    second: RProp_target
  - first:
      4: 400142
    second: Spine
  - first:
      4: 400144
    second: UpperChest
  - first:
      4: 400146
    second: //RootNode
  - first:
      4: 400148
    second: Skeleton
  - first:
      74: 7400000
    second: Vct_Dnc_anim1
  - first:
      95: 9500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 0
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Vct_Dnc_anim1
      takeName: Vct_Dnc_anim1
      internalID: 0
      firstFrame: 0
      lastFrame: 673
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 1
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpperLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpperLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLowerLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLowerLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpperArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpperArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLowerArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLowerArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToes
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToes
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftEye
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightEye
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Jaw
      humanName: Jaw
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftThumbProximal
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftThumbIntermediate
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftThumbDistal
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftIndexProximal
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftIndexIntermediate
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftIndexDistal
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftMiddleProximal
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftMiddleIntermediate
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftMiddleDistal
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftRingProximal
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftRingIntermediate
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftRingDistal
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftPinkyProximal
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftPinkyIntermediate
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftPinkyDistal
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightThumbProximal
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightThumbIntermediate
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightThumbDistal
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightIndexProximal
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightIndexIntermediate
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightIndexDistal
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightMiddleProximal
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightMiddleIntermediate
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightMiddleDistal
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightRingProximal
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightRingIntermediate
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightRingDistal
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightPinkyProximal
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightPinkyIntermediate
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightPinkyDistal
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: UpperChest
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Stance(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Skeleton
      parentName: Stance(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Root
      parentName: Skeleton
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Root
      position: {x: 1.4210854e-16, y: 0.99710166, z: 0.011954045}
      rotation: {x: 0.5, y: -0.5, z: -0.5, w: 0.5}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftUpperLeg
      parentName: Hips
      position: {x: 0.050100002, y: -0.0023999999, z: 0.089999996}
      rotation: {x: -6.0931044e-17, y: -3.0055602e-19, z: 0.99998796, w: 0.0049084523}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLowerLeg
      parentName: LeftUpperLeg
      position: {x: -0.4357, y: 0, z: 0}
      rotation: {x: -1.6744574e-27, y: -2.5340972e-18, z: -0.04138495, w: 0.9991433}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftFoot
      parentName: LeftLowerLeg
      position: {x: -0.42479998, y: 0, z: 0}
      rotation: {x: 2.5695366e-17, y: -5.5168214e-16, z: 0.04628874, w: 0.9989281}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftToes
      parentName: LeftFoot
      position: {x: -0.0759, y: -0.1707, z: 0}
      rotation: {x: -3.437584e-16, y: 5.2788353e-16, z: 0.7071068, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftToesEnd
      parentName: LeftToes
      position: {x: -0.054, y: 0, z: 0}
      rotation: {x: -0.000000027953787, y: -0.000000006196583, z: 0.00000004189918,
        w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightUpperLeg
      parentName: Hips
      position: {x: 0.050100002, y: -0.0023999999, z: -0.089999996}
      rotation: {x: 5.449229e-19, y: -1.1072043e-16, z: -0.0049082297, w: 0.99998796}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLowerLeg
      parentName: RightUpperLeg
      position: {x: 0.4357, y: 0, z: 0}
      rotation: {x: 3.9545505e-28, y: 2.5341303e-18, z: -0.041385487, w: 0.9991433}
      scale: {x: 1, y: 1, z: 1}
    - name: RightFoot
      parentName: RightLowerLeg
      position: {x: 0.42479998, y: 0, z: 0}
      rotation: {x: -5.1391127e-18, y: 1.0806892e-16, z: 0.046289016, w: 0.9989281}
      scale: {x: 1, y: 1, z: 1}
    - name: RightToes
      parentName: RightFoot
      position: {x: 0.0759, y: 0.1707, z: 0}
      rotation: {x: -2.571628e-16, y: 2.1386499e-16, z: 0.7071068, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: RightToesEnd
      parentName: RightToes
      position: {x: 0.054, y: 0, z: 0}
      rotation: {x: -0.000000009334332, y: 0.0000000045527395, z: 7.6323896e-17, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: -0.0764, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Chest
      parentName: Spine
      position: {x: -0.1022, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 2.906732e-32, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: UpperChest
      parentName: Chest
      position: {x: -0.09989999, y: 0, z: 0}
      rotation: {x: -2.2404574e-27, y: -1.6913897e-18, z: 0.02762249, w: 0.9996185}
      scale: {x: 1, y: 1, z: 1}
    - name: Neck
      parentName: UpperChest
      position: {x: -0.2295, y: 0, z: 0}
      rotation: {x: -2.932217e-28, y: 9.95209e-18, z: -0.16252996, w: 0.98670363}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: Neck
      position: {x: -0.0971, y: 0, z: 0}
      rotation: {x: 2.8804692e-26, y: -8.279392e-18, z: 0.13521273, w: 0.9908166}
      scale: {x: 0.99999994, y: 0.99999994, z: 1}
    - name: Jaw
      parentName: Head
      position: {x: 0.020399999, y: 0.0234, z: 0}
      rotation: {x: -1.874272e-17, y: -1.874272e-17, z: -0.8433914, w: 0.53729963}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: JawEnd
      parentName: Jaw
      position: {x: -0.0949, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightEye
      parentName: Head
      position: {x: -0.049599998, y: 0.1131, z: -0.0297}
      rotation: {x: 0, y: -0, z: -0.7071068, w: 0.7071068}
      scale: {x: 0.99999994, y: 0.99999994, z: 1}
    - name: LeftEye
      parentName: Head
      position: {x: -0.049599998, y: 0.1131, z: 0.03}
      rotation: {x: 0, y: -0, z: -0.7071068, w: 0.7071068}
      scale: {x: 0.99999994, y: 0.99999994, z: 1}
    - name: LeftShoulder
      parentName: UpperChest
      position: {x: -0.1481, y: 0.0184, z: 0.0413}
      rotation: {x: -0.48599797, y: 0.51362044, z: 0.48599797, w: 0.51362044}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftUpperArm
      parentName: LeftShoulder
      position: {x: -0.14899999, y: 0.0216, z: -0.026199998}
      rotation: {x: 0.00000000108812, y: -0.009647765, z: 0.00000011277942, w: 0.99995345}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLowerArm
      parentName: LeftUpperArm
      position: {x: -0.2598, y: 0, z: 0}
      rotation: {x: 9.57849e-10, y: 0.010789105, z: -0.000000113875615, w: 0.9999418}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHand
      parentName: LeftLowerArm
      position: {x: -0.2583, y: 0, z: 0}
      rotation: {x: 0.00000003896128, y: -0.0011199615, z: 0.000034788034, w: 0.9999994}
      scale: {x: 1.0000001, y: 1, z: 1.0000001}
    - name: LeftThumbProximal
      parentName: LeftHand
      position: {x: -0.032899998, y: -0.0194, z: 0.0326}
      rotation: {x: -0.7071068, y: -1.2159349e-16, z: 9.429606e-17, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftThumbIntermediate
      parentName: LeftThumbProximal
      position: {x: -0.015599999, y: -0.022699999, z: -0.0023}
      rotation: {x: 0.00000017615226, y: 0.00000044237117, z: 0.46174863, w: 0.8870109}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftThumbDistal
      parentName: LeftThumbIntermediate
      position: {x: -0.0314, y: 0, z: 0}
      rotation: {x: 8.530709e-21, y: 7.682767e-21, z: -4.1633363e-16, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftThumbDistalEnd
      parentName: LeftThumbDistal
      position: {x: -0.0323, y: -0.0001, z: 0}
      rotation: {x: -1.6337076e-21, y: 9.065124e-22, z: -3.8857806e-16, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftPinkyProximal
      parentName: LeftHand
      position: {x: -0.0901, y: -0.0109, z: -0.033699997}
      rotation: {x: 1, y: 0.00000048059263, z: 0.000000045818194, w: -2.1958655e-14}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftPinkyIntermediate
      parentName: LeftPinkyProximal
      position: {x: -0.020299999, y: 0, z: 0}
      rotation: {x: 2.1751144e-14, y: 3.3567394e-16, z: -0.00000024029632, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftPinkyDistal
      parentName: LeftPinkyIntermediate
      position: {x: -0.0197, y: 0, z: 0}
      rotation: {x: 1.323489e-23, y: -3.3881318e-21, z: -4.4088408e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftPinkyDistalEnd
      parentName: LeftPinkyDistal
      position: {x: -0.0218, y: 0, z: 0}
      rotation: {x: 1.323489e-23, y: -3.3881318e-21, z: -4.4088408e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftRingProximal
      parentName: LeftHand
      position: {x: -0.101, y: -0.0087, z: -0.015199999}
      rotation: {x: 1, y: 1.2700074e-21, z: 7.7765524e-38, w: 6.123234e-17}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftRingIntermediate
      parentName: LeftRingProximal
      position: {x: -0.027999999, y: 0, z: 0}
      rotation: {x: -9.745124e-17, y: 1.6739269e-15, z: -0.000001198252, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftRingDistal
      parentName: LeftRingIntermediate
      position: {x: -0.0299, y: 0, z: 0}
      rotation: {x: 6.617445e-24, y: -2.7e-44, z: -3.9870106e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftRingDistalEnd
      parentName: LeftRingDistal
      position: {x: -0.026099999, y: 0, z: 0}
      rotation: {x: 6.617445e-24, y: -2.7e-44, z: -3.9870106e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftMiddleProximal
      parentName: LeftHand
      position: {x: -0.1018, y: -0.0092, z: 0.0076999995}
      rotation: {x: 1, y: 0.000001953623, z: 1.6274996e-19, w: 6.123234e-17}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftMiddleIntermediate
      parentName: LeftMiddleProximal
      position: {x: -0.038099997, y: 0, z: 0}
      rotation: {x: -2.1106542e-17, y: 1.3644652e-15, z: -0.0000009768116, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftMiddleDistal
      parentName: LeftMiddleIntermediate
      position: {x: -0.0273, y: 0, z: 0}
      rotation: {x: -6.617445e-24, y: 6e-45, z: -9.385083e-22, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftMiddleDistalEnd
      parentName: LeftMiddleDistal
      position: {x: -0.0296, y: 0, z: 0}
      rotation: {x: -6.617445e-24, y: 6e-45, z: -9.385083e-22, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftIndexProximal
      parentName: LeftHand
      position: {x: -0.1015, y: -0.010399999, z: 0.0323}
      rotation: {x: 1, y: -0.00000080360593, z: -0.000000034856388, w: -2.7949568e-14}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftIndexIntermediate
      parentName: LeftIndexProximal
      position: {x: -0.030399999, y: 0, z: 0}
      rotation: {x: 6.617445e-24, y: -3.3881318e-21, z: 1.9856305e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftIndexDistal
      parentName: LeftIndexIntermediate
      position: {x: -0.0233, y: 0, z: 0}
      rotation: {x: 6.728e-42, y: -3.3881318e-21, z: 1.9856305e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftIndexDistalEnd
      parentName: LeftIndexDistal
      position: {x: -0.0277, y: 0, z: 0}
      rotation: {x: 6.728e-42, y: -3.3881318e-21, z: 1.985631e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LProp_root
      parentName: LeftHand
      position: {x: -0.0946, y: -0.0246, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 0.9999999, y: 1, z: 0.9999999}
    - name: LProp_target
      parentName: LProp_root
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 6.617445e-24, y: 3.3881318e-21, z: 2.0117378e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightShoulder
      parentName: UpperChest
      position: {x: -0.1481, y: 0.0184, z: -0.0413}
      rotation: {x: 0.51362044, y: 0.48599797, z: -0.51362044, w: 0.48599797}
      scale: {x: 1, y: 1, z: 1}
    - name: RightUpperArm
      parentName: RightShoulder
      position: {x: 0.14899999, y: -0.0216, z: 0.026199998}
      rotation: {x: 0.0000000010881188, y: -0.009647758, z: 0.00000011277942, w: 0.99995345}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLowerArm
      parentName: RightUpperArm
      position: {x: 0.2598, y: 0, z: 0}
      rotation: {x: 9.577236e-10, y: 0.010789105, z: -0.00000011387552, w: 0.9999418}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHand
      parentName: RightLowerArm
      position: {x: 0.2583, y: 0, z: 0}
      rotation: {x: 0.000000038961282, y: -0.0011199615, z: 0.00003478804, w: 0.9999994}
      scale: {x: 1.0000001, y: 1, z: 1.0000001}
    - name: RightThumbProximal
      parentName: RightHand
      position: {x: 0.032899998, y: 0.0194, z: -0.0326}
      rotation: {x: -0.7071068, y: -2.4978194e-16, z: -1.1728377e-16, w: 0.7071068}
      scale: {x: 1, y: 1, z: 1}
    - name: RightThumbIntermediate
      parentName: RightThumbProximal
      position: {x: 0.015599999, y: 0.022699999, z: 0.0023}
      rotation: {x: 0.00000017597144, y: 0.00000044227704, z: 0.46174863, w: 0.8870109}
      scale: {x: 1, y: 1, z: 1}
    - name: RightThumbDistal
      parentName: RightThumbIntermediate
      position: {x: 0.0314, y: 0, z: 0}
      rotation: {x: 2.196158e-21, y: 2.2007399e-21, z: -1.110223e-16, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightThumbDistalEnd
      parentName: RightThumbDistal
      position: {x: 0.0323, y: 0.0001, z: 0}
      rotation: {x: 2.1961775e-21, y: 2.2007399e-21, z: -8.326673e-17, w: 1}
      scale: {x: 1.0000001, y: 1.0000001, z: 1}
    - name: RightPinkyProximal
      parentName: RightHand
      position: {x: 0.0901, y: 0.0109, z: 0.033699997}
      rotation: {x: 1, y: 0.0000009611853, z: 0.00000009163642, w: 0.00000053263216}
      scale: {x: 1, y: 1, z: 1}
    - name: RightPinkyIntermediate
      parentName: RightPinkyProximal
      position: {x: 0.020299999, y: 0, z: 0}
      rotation: {x: 0.00000067755235, y: 2.4453367e-20, z: -8.392237e-14, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightPinkyDistal
      parentName: RightPinkyIntermediate
      position: {x: 0.0197, y: 0, z: 0}
      rotation: {x: 0.00000052042185, y: -2.397327e-28, z: -4.6065074e-22, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightPinkyDistalEnd
      parentName: RightPinkyDistal
      position: {x: 0.0218, y: 0, z: 0}
      rotation: {x: 0.00000086590364, y: 1.4576984e-27, z: 1.6834418e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightRingProximal
      parentName: RightHand
      position: {x: 0.101, y: 0.0087, z: 0.015199999}
      rotation: {x: 1, y: 1.7137163e-21, z: -3.3881318e-21, w: -6.123234e-17}
      scale: {x: 1, y: 1, z: 1}
    - name: RightRingIntermediate
      parentName: RightRingProximal
      position: {x: 0.027999999, y: 0, z: 0}
      rotation: {x: -8.827247e-36, y: -3.3881318e-21, z: -2.6053435e-15, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightRingDistal
      parentName: RightRingIntermediate
      position: {x: 0.0299, y: 0, z: 0}
      rotation: {x: -1.9851e-41, y: -3.3881318e-21, z: -5.8590035e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightRingDistalEnd
      parentName: RightRingDistal
      position: {x: 0.026099999, y: 0, z: 0}
      rotation: {x: -1.9851e-41, y: -3.3881318e-21, z: -5.8590023e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightMiddleProximal
      parentName: RightHand
      position: {x: 0.1018, y: 0.0092, z: -0.0076999995}
      rotation: {x: 1, y: 0.000003907246, z: 7.80422e-12, w: 0.0000019973709}
      scale: {x: 1, y: 1, z: 1}
    - name: RightMiddleIntermediate
      parentName: RightMiddleProximal
      position: {x: 0.038099997, y: 0, z: 0}
      rotation: {x: 0.0000027608096, y: -1.8637785e-26, z: -6.7508407e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightMiddleDistal
      parentName: RightMiddleIntermediate
      position: {x: 0.0273, y: 0, z: 0}
      rotation: {x: 0.0000021149021, y: 3.3881314e-21, z: -2.042497e-22, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightMiddleDistalEnd
      parentName: RightMiddleDistal
      position: {x: 0.0296, y: 0, z: 0}
      rotation: {x: 0.0000035173923, y: -1.909517e-26, z: -5.4287864e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightIndexProximal
      parentName: RightHand
      position: {x: 0.1015, y: 0.010399999, z: -0.0323}
      rotation: {x: 1, y: -0.0000016072117, z: -0.00000006971278, w: 0.00000079894824}
      scale: {x: 1, y: 1, z: 1}
    - name: RightIndexIntermediate
      parentName: RightIndexProximal
      position: {x: 0.030399999, y: 0, z: 0}
      rotation: {x: 0.0000011344717, y: -3.388133e-21, z: -1.0963077e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightIndexDistal
      parentName: RightIndexIntermediate
      position: {x: 0.0233, y: 0, z: 0}
      rotation: {x: 0.00000087025205, y: 3.3881314e-21, z: -4.543642e-22, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightIndexDistalEnd
      parentName: RightIndexDistal
      position: {x: 0.0277, y: 0, z: 0}
      rotation: {x: 0.0000014476385, y: -3.3881447e-21, z: -8.972947e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RProp_root
      parentName: RightHand
      position: {x: 0.0946, y: 0.0246, z: 0}
      rotation: {x: 1, y: -0, z: 0, w: 6.123234e-17}
      scale: {x: 0.9999999, y: 1, z: 0.9999999}
    - name: RProp_target
      parentName: RProp_root
      position: {x: -0, y: 0, z: 0}
      rotation: {x: -6.617445e-24, y: -3.3881318e-21, z: -4.337733e-21, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: Hips
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {fileID: 9000000, guid: 0595e99350ddf2949820c55c496b4daa,
    type: 3}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 3
  humanoidOversampling: 1
  avatarSetup: 2
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
