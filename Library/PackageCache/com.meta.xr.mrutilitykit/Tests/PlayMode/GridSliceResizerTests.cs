/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * Licensed under the Oculus SDK License Agreement (the "License");
 * you may not use the Oculus SDK except in compliance with the License,
 * which is provided at the time of installation or download, or which
 * otherwise accompanies this software in either electronic or hard copy form.
 *
 * You may obtain a copy of the License at
 *
 * https://developer.oculus.com/licenses/oculussdk/
 *
 * Unless required by applicable law or agreed to in writing, the Oculus SDK
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

using System.Collections;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json;
using NUnit.Framework;
using UnityEngine;
using UnityEngine.TestTools;
using UnityEngine.TestTools.Utils;

namespace Meta.XR.MRUtilityKit.Tests
{
    public class GridSliceResizerTests : MRUKTestBase
    {
        private readonly JsonSerializerSettings _serializerSettings = new()
        {
            NullValueHandling = NullValueHandling.Ignore,
            Formatting = Formatting.Indented,
            Converters = new List<JsonConverter>
            {
                new IntArrayConverter(),
                new Vector3ArrayConverter(),
                new Vector3Converter(),
                new GridSliceResizerConverter()
            }
        };

        internal struct ResizedMesh
        {
            [JsonProperty("Scale")] public Vector3 Scale;
            [JsonProperty("Vertices")] public Vector3[] Vertices;
            [JsonProperty("Triangles")] public int[] Triangles;
            [JsonProperty("Resizer")] public GridSliceResizer Resizer;
        }

        private GridSliceResizer _resizer;
        private Mesh _testMesh;

        private void Update()
        {
            // To create/update the expected referenced serialized resized mesh
            if (Input.GetKeyDown(KeyCode.Space))
            {
                SerializeResizedMesh("resized_mesh_serialized");
            }
        }

        private void SerializeResizedMesh(string jsonFileName)
        {
            _resizer = Object.FindAnyObjectByType<GridSliceResizer>();
            if (_resizer)
            {
                _testMesh = _resizer.GetComponent<MeshFilter>().mesh;
            }

            var newMesh = new ResizedMesh
            {
                Scale = _resizer.transform.lossyScale,
                Vertices = _testMesh.vertices,
                Triangles = _testMesh.triangles,
                Resizer = _resizer
            };

            var json = JsonConvert.SerializeObject(newMesh, _serializerSettings);
            // Create a StreamWriter
            using var writer = new StreamWriter(Path.Combine(Application.persistentDataPath, jsonFileName + ".json"));
            {
                // Write each vertex to the text file
                writer.WriteLine(json);
            }
        }

        [UnitySetUp]
        public IEnumerator SetUp()
        {
            yield return LoadScene("Packages/com.meta.xr.mrutilitykit/Tests/GridSliceResizerTests.unity", false);
            _resizer = Object.FindAnyObjectByType<GridSliceResizer>();
            if (_resizer)
            {
                _testMesh = _resizer.GetComponent<MeshFilter>().mesh;
            }
        }

        [UnityTearDown]
        public IEnumerator TearDown()
        {
            yield return UnloadScene();
        }

        /// <summary>
        /// Test that a mesh with no custom pivots and set to stretch is upscaled correctly.
        /// </summary>
        [UnityTest]
        [Timeout(DefaultTimeoutMs)]
        public IEnumerator GridSliceResizer_Upscale_PivotCenter_Stretched()
        {
            Assert.IsNotNull(_testMesh);
            Assert.IsNotNull(_resizer);
            var resizedMesh = SetUpGridSliceResizer(_cubeUpscaledCenterStretch);

            Assert.That(_testMesh.vertices, Is.EqualTo(resizedMesh.Vertices).Using(Vector3EqualityComparer.Instance));
            Assert.That(_testMesh.triangles, Is.EqualTo(resizedMesh.Triangles).Using(Vector3EqualityComparer.Instance));

            yield return null;
        }

        /// <summary>
        /// Test that a mesh with no custom pivots is downscaled correctly.
        /// </summary>
        [UnityTest]
        [Timeout(DefaultTimeoutMs)]
        public IEnumerator GridSliceResizer_Downscale_PivotCenter()
        {
            Assert.IsNotNull(_testMesh);
            Assert.IsNotNull(_resizer);
            var resizedMesh = SetUpGridSliceResizer(_cubeDownscaledCenter);

            Assert.That(_testMesh.vertices, Is.EqualTo(resizedMesh.Vertices).Using(Vector3EqualityComparer.Instance));
            Assert.That(_testMesh.triangles, Is.EqualTo(resizedMesh.Triangles).Using(Vector3EqualityComparer.Instance));

            yield return null;
        }

        /// <summary>
        /// Test that a mesh with a custom pivots is upscaled correctly.
        /// </summary>
        [UnityTest]
        [Timeout(DefaultTimeoutMs)]
        public IEnumerator GridSliceResizer_Upscale_PivotOffset()
        {
            Assert.IsNotNull(_testMesh);
            Assert.IsNotNull(_resizer);
            var resizedMesh = SetUpGridSliceResizer(_cubeUpscaledOffset);

            Assert.That(_testMesh.vertices, Is.EqualTo(resizedMesh.Vertices).Using(Vector3EqualityComparer.Instance));
            Assert.That(_testMesh.triangles, Is.EqualTo(resizedMesh.Triangles).Using(Vector3EqualityComparer.Instance));

            yield return null;
        }

        /// <summary>
        /// Test that a mesh with a custom pivots that is outside of the bounding box is upscaled correctly.
        /// </summary>
        [UnityTest]
        [Timeout(DefaultTimeoutMs)]
        public IEnumerator GridSliceResizer_Upscale_PivotOutside()
        {
            Assert.IsNotNull(_testMesh);
            Assert.IsNotNull(_resizer);
            var resizedMesh = SetUpGridSliceResizer(_cubeUpscaledOffsetOutside);

            Assert.That(_testMesh.vertices, Is.EqualTo(resizedMesh.Vertices).Using(Vector3EqualityComparer.Instance));
            Assert.That(_testMesh.triangles, Is.EqualTo(resizedMesh.Triangles).Using(Vector3EqualityComparer.Instance));

            yield return null;
        }

        /// <summary>
        /// Sets up the GridSliceResizer instance so that it generates the expected mesh.
        /// </summary>
        /// <param name="serializedResizedMesh">The serialized resized mesh. </param>
        /// <returns>The resized mesh to verify.</returns>
        private ResizedMesh SetUpGridSliceResizer(string serializedResizedMesh)
        {
            var resizedMesh =
                JsonConvert.DeserializeObject<ResizedMesh>(serializedResizedMesh, _serializerSettings);
            _resizer.PivotOffset = resizedMesh.Resizer.PivotOffset;
            _resizer.StretchCenter = resizedMesh.Resizer.StretchCenter;
            _resizer.ScalingX = resizedMesh.Resizer.ScalingX;
            _resizer.ScalingY = resizedMesh.Resizer.ScalingY;
            _resizer.ScalingZ = resizedMesh.Resizer.ScalingZ;
            _resizer.BorderXPositive = resizedMesh.Resizer.BorderXPositive;
            _resizer.BorderXNegative = resizedMesh.Resizer.BorderXNegative;
            _resizer.BorderYPositive = resizedMesh.Resizer.BorderYPositive;
            _resizer.BorderYNegative = resizedMesh.Resizer.BorderYNegative;
            _resizer.BorderZPositive = resizedMesh.Resizer.BorderZPositive;
            _resizer.BorderZNegative = resizedMesh.Resizer.BorderZNegative;
            _resizer.transform.localScale = resizedMesh.Scale;
            _testMesh = _resizer.ProcessVertices();
            return resizedMesh;
        }

        private const string _cubeUpscaledCenterStretch = @"{
  ""Scale"": [2.0,2.0,2.0],
  ""Vertices"": [
    [-0.5,-0.4,0.0500000119],
    [-0.5,-0.475,0.0500000119],
    [-0.5,-0.475,0.199999988],
    [-0.5,-0.4,0.199999988],
    [-0.5,-0.150000066,0.0500000119],
    [-0.5,-0.475,0.399999976],
    [-0.5,-0.150000066,0.199999988],
    [-0.5,0.150000036,0.0500000119],
    [-0.5,-0.4,0.399999976],
    [-0.5,-0.475,0.6],
    [-0.5,0.150000036,0.199999988],
    [-0.5,0.4,0.0500000119],
    [-0.5,-0.4,0.6],
    [-0.5,-0.475,0.8],
    [-0.5,-0.150000066,0.399999976],
    [-0.5,0.4,0.199999988],
    [-0.5,0.475,0.0500000119],
    [-0.5,0.475,0.199999988],
    [-0.5,-0.4,0.8],
    [-0.5,-0.475,0.95],
    [-0.5,-0.4,0.95],
    [-0.5,-0.150000066,0.6],
    [-0.5,0.150000036,0.399999976],
    [-0.5,0.4,0.399999976],
    [-0.5,0.475,0.399999976],
    [-0.5,-0.150000066,0.8],
    [-0.5,-0.150000066,0.95],
    [-0.5,0.150000036,0.6],
    [-0.5,0.4,0.6],
    [-0.5,0.475,0.6],
    [-0.5,0.150000036,0.8],
    [-0.5,0.150000036,0.95],
    [-0.5,0.4,0.8],
    [-0.5,0.475,0.8],
    [-0.5,0.4,0.95],
    [-0.5,0.475,0.95],
    [-0.4,0.5,0.0500000119],
    [-0.475,0.5,0.0500000119],
    [-0.475,0.5,0.199999988],
    [-0.4,0.5,0.199999988],
    [-0.25000006,0.5,0.0500000119],
    [-0.475,0.5,0.399999976],
    [-0.25000006,0.5,0.199999988],
    [0.116666704,0.5,0.0500000119],
    [-0.4,0.5,0.399999976],
    [-0.475,0.5,0.6],
    [0.116666704,0.5,0.199999988],
    [0.35,0.5,0.0500000119],
    [-0.25000006,0.5,0.399999976],
    [-0.4,0.5,0.6],
    [-0.475,0.5,0.8],
    [0.35,0.5,0.199999988],
    [0.474999964,0.5,0.0500000119],
    [0.474999964,0.5,0.199999988],
    [0.116666704,0.5,0.399999976],
    [0.35,0.5,0.399999976],
    [0.474999964,0.5,0.399999976],
    [-0.25000006,0.5,0.6],
    [-0.4,0.5,0.8],
    [-0.475,0.5,0.95],
    [-0.4,0.5,0.95],
    [-0.25000006,0.5,0.8],
    [-0.25000006,0.5,0.95],
    [0.116666704,0.5,0.6],
    [0.35,0.5,0.6],
    [0.474999964,0.5,0.6],
    [0.116666704,0.5,0.8],
    [0.116666704,0.5,0.95],
    [0.35,0.5,0.8],
    [0.474999964,0.5,0.8],
    [0.35,0.5,0.95],
    [0.474999964,0.5,0.95],
    [-0.475,-0.4,1.0],
    [-0.475,-0.475,1.0],
    [-0.4,-0.475,1.0],
    [-0.4,-0.4,1.0],
    [-0.475,-0.150000066,1.0],
    [-0.25000006,-0.475,1.0],
    [-0.4,-0.150000066,1.0],
    [-0.475,0.150000036,1.0],
    [-0.25000006,-0.4,1.0],
    [0.116666704,-0.475,1.0],
    [-0.4,0.150000036,1.0],
    [-0.475,0.4,1.0],
    [-0.25000006,-0.150000066,1.0],
    [0.116666704,-0.4,1.0],
    [0.35,-0.475,1.0],
    [-0.4,0.4,1.0],
    [-0.475,0.475,1.0],
    [-0.4,0.475,1.0],
    [-0.25000006,0.150000036,1.0],
    [-0.25000006,0.4,1.0],
    [-0.25000006,0.475,1.0],
    [0.116666704,-0.150000066,1.0],
    [0.35,-0.4,1.0],
    [0.474999964,-0.475,1.0],
    [0.474999964,-0.4,1.0],
    [0.35,-0.150000066,1.0],
    [0.474999964,-0.150000066,1.0],
    [0.116666704,0.150000036,1.0],
    [0.116666704,0.4,1.0],
    [0.116666704,0.475,1.0],
    [0.35,0.150000036,1.0],
    [0.474999964,0.150000036,1.0],
    [0.35,0.4,1.0],
    [0.35,0.475,1.0],
    [0.474999964,0.4,1.0],
    [0.474999964,0.475,1.0],
    [0.5,0.4,0.0500000119],
    [0.5,0.475,0.0500000119],
    [0.5,0.475,0.199999988],
    [0.5,0.4,0.199999988],
    [0.5,0.150000036,0.0500000119],
    [0.5,0.475,0.399999976],
    [0.5,0.150000036,0.199999988],
    [0.5,-0.150000066,0.0500000119],
    [0.5,0.4,0.399999976],
    [0.5,0.475,0.6],
    [0.5,-0.150000066,0.199999988],
    [0.5,-0.4,0.0500000119],
    [0.5,0.4,0.6],
    [0.5,0.475,0.8],
    [0.5,0.150000036,0.399999976],
    [0.5,-0.4,0.199999988],
    [0.5,-0.475,0.0500000119],
    [0.5,-0.475,0.199999988],
    [0.5,0.4,0.8],
    [0.5,0.475,0.95],
    [0.5,0.4,0.95],
    [0.5,0.150000036,0.6],
    [0.5,-0.150000066,0.399999976],
    [0.5,-0.4,0.399999976],
    [0.5,-0.475,0.399999976],
    [0.5,0.150000036,0.8],
    [0.5,0.150000036,0.95],
    [0.5,-0.150000066,0.6],
    [0.5,-0.4,0.6],
    [0.5,-0.475,0.6],
    [0.5,-0.150000066,0.8],
    [0.5,-0.150000066,0.95],
    [0.5,-0.4,0.8],
    [0.5,-0.475,0.8],
    [0.5,-0.4,0.95],
    [0.5,-0.475,0.95],
    [-0.475,-0.5,0.8],
    [-0.4,-0.5,0.95],
    [-0.475,-0.5,0.95],
    [-0.4,-0.5,0.8],
    [-0.475,-0.5,0.6],
    [-0.25000006,-0.5,0.95],
    [-0.4,-0.5,0.6],
    [-0.475,-0.5,0.399999976],
    [-0.25000006,-0.5,0.8],
    [0.116666704,-0.5,0.95],
    [-0.4,-0.5,0.399999976],
    [-0.475,-0.5,0.199999988],
    [0.116666704,-0.5,0.8],
    [0.35,-0.5,0.95],
    [-0.25000006,-0.5,0.6],
    [-0.4,-0.5,0.199999988],
    [-0.475,-0.5,0.0500000119],
    [-0.4,-0.5,0.0500000119],
    [0.35,-0.5,0.8],
    [0.474999964,-0.5,0.95],
    [0.474999964,-0.5,0.8],
    [0.116666704,-0.5,0.6],
    [-0.25000006,-0.5,0.399999976],
    [-0.25000006,-0.5,0.199999988],
    [-0.25000006,-0.5,0.0500000119],
    [0.35,-0.5,0.6],
    [0.474999964,-0.5,0.6],
    [0.116666704,-0.5,0.399999976],
    [0.116666704,-0.5,0.199999988],
    [0.116666704,-0.5,0.0500000119],
    [0.35,-0.5,0.399999976],
    [0.474999964,-0.5,0.399999976],
    [0.35,-0.5,0.199999988],
    [0.35,-0.5,0.0500000119],
    [0.474999964,-0.5,0.199999988],
    [0.474999964,-0.5,0.0500000119],
    [0.474999964,-0.4,0.0],
    [0.474999964,-0.475,0.0],
    [0.35,-0.475,0.0],
    [0.35,-0.4,0.0],
    [0.474999964,-0.150000066,0.0],
    [0.116666704,-0.475,0.0],
    [0.35,-0.150000066,0.0],
    [0.474999964,0.150000036,0.0],
    [0.116666704,-0.4,0.0],
    [-0.25000006,-0.475,0.0],
    [0.35,0.150000036,0.0],
    [0.474999964,0.4,0.0],
    [0.116666704,-0.150000066,0.0],
    [-0.25000006,-0.4,0.0],
    [-0.4,-0.475,0.0],
    [0.35,0.4,0.0],
    [0.474999964,0.475,0.0],
    [0.35,0.475,0.0],
    [0.116666704,0.150000036,0.0],
    [0.116666704,0.4,0.0],
    [0.116666704,0.475,0.0],
    [-0.25000006,-0.150000066,0.0],
    [-0.4,-0.4,0.0],
    [-0.475,-0.475,0.0],
    [-0.475,-0.4,0.0],
    [-0.4,-0.150000066,0.0],
    [-0.475,-0.150000066,0.0],
    [-0.25000006,0.150000036,0.0],
    [-0.25000006,0.4,0.0],
    [-0.25000006,0.475,0.0],
    [-0.4,0.150000036,0.0],
    [-0.475,0.150000036,0.0],
    [-0.4,0.4,0.0],
    [-0.4,0.475,0.0],
    [-0.475,0.4,0.0],
    [-0.475,0.475,0.0],
    [0.5,-0.475,0.0500000119],
    [0.4926777,-0.4926777,0.0500000119],
    [0.489433765,-0.489433765,0.021132499],
    [0.4926777,-0.475,0.0146446526],
    [0.474999964,-0.475,0.0],
    [0.4926777,-0.475,0.0146446526],
    [0.489433765,-0.489433765,0.021132499],
    [0.474999964,-0.4926777,0.0146446526],
    [0.474999964,-0.5,0.0500000119],
    [0.474999964,-0.4926777,0.0146446526],
    [0.489433765,-0.489433765,0.021132499],
    [0.4926777,-0.4926777,0.0500000119],
    [0.5,-0.475,0.95],
    [0.4926777,-0.475,0.9853554],
    [0.489433765,-0.489433765,0.978867531],
    [0.4926777,-0.4926777,0.95],
    [0.474999964,-0.5,0.95],
    [0.4926777,-0.4926777,0.95],
    [0.489433765,-0.489433765,0.978867531],
    [0.474999964,-0.4926777,0.9853554],
    [0.474999964,-0.475,1.0],
    [0.474999964,-0.4926777,0.9853554],
    [0.489433765,-0.489433765,0.978867531],
    [0.4926777,-0.475,0.9853554],
    [0.5,0.475,0.0500000119],
    [0.4926777,0.475,0.0146446526],
    [0.489433765,0.489433765,0.021132499],
    [0.4926777,0.4926777,0.0500000119],
    [0.474999964,0.5,0.0500000119],
    [0.4926777,0.4926777,0.0500000119],
    [0.489433765,0.489433765,0.021132499],
    [0.474999964,0.4926777,0.0146446526],
    [0.474999964,0.475,0.0],
    [0.474999964,0.4926777,0.0146446526],
    [0.489433765,0.489433765,0.021132499],
    [0.4926777,0.475,0.0146446526],
    [0.5,0.475,0.95],
    [0.4926777,0.4926777,0.95],
    [0.489433765,0.489433765,0.978867531],
    [0.4926777,0.475,0.9853554],
    [0.474999964,0.475,1.0],
    [0.4926777,0.475,0.9853554],
    [0.489433765,0.489433765,0.978867531],
    [0.474999964,0.4926777,0.9853554],
    [0.474999964,0.5,0.95],
    [0.474999964,0.4926777,0.9853554],
    [0.489433765,0.489433765,0.978867531],
    [0.4926777,0.4926777,0.95],
    [-0.5,-0.475,0.0500000119],
    [-0.4926777,-0.475,0.0146446526],
    [-0.489433765,-0.489433765,0.021132499],
    [-0.4926777,-0.4926777,0.0500000119],
    [-0.475,-0.5,0.0500000119],
    [-0.4926777,-0.4926777,0.0500000119],
    [-0.489433765,-0.489433765,0.021132499],
    [-0.475,-0.4926777,0.0146446526],
    [-0.475,-0.475,0.0],
    [-0.475,-0.4926777,0.0146446526],
    [-0.489433765,-0.489433765,0.021132499],
    [-0.4926777,-0.475,0.0146446526],
    [-0.5,-0.475,0.95],
    [-0.4926777,-0.4926777,0.95],
    [-0.489433765,-0.489433765,0.978867531],
    [-0.4926777,-0.475,0.9853554],
    [-0.475,-0.475,1.0],
    [-0.4926777,-0.475,0.9853554],
    [-0.489433765,-0.489433765,0.978867531],
    [-0.475,-0.4926777,0.9853554],
    [-0.475,-0.5,0.95],
    [-0.475,-0.4926777,0.9853554],
    [-0.489433765,-0.489433765,0.978867531],
    [-0.4926777,-0.4926777,0.95],
    [-0.475,0.5,0.0500000119],
    [-0.475,0.4926777,0.0146446526],
    [-0.489433765,0.489433765,0.021132499],
    [-0.4926777,0.4926777,0.0500000119],
    [-0.5,0.475,0.0500000119],
    [-0.4926777,0.4926777,0.0500000119],
    [-0.489433765,0.489433765,0.021132499],
    [-0.4926777,0.475,0.0146446526],
    [-0.475,0.475,0.0],
    [-0.4926777,0.475,0.0146446526],
    [-0.489433765,0.489433765,0.021132499],
    [-0.475,0.4926777,0.0146446526],
    [-0.475,0.5,0.95],
    [-0.4926777,0.4926777,0.95],
    [-0.489433765,0.489433765,0.978867531],
    [-0.475,0.4926777,0.9853554],
    [-0.475,0.475,1.0],
    [-0.475,0.4926777,0.9853554],
    [-0.489433765,0.489433765,0.978867531],
    [-0.4926777,0.475,0.9853554],
    [-0.5,0.475,0.95],
    [-0.4926777,0.475,0.9853554],
    [-0.489433765,0.489433765,0.978867531],
    [-0.4926777,0.4926777,0.95],
    [0.474999964,-0.4,0.0],
    [0.4926777,-0.475,0.0146446526],
    [0.474999964,-0.475,0.0],
    [0.4926777,-0.4,0.0146446526],
    [0.474999964,-0.150000066,0.0],
    [0.4926777,-0.150000066,0.0146446526],
    [0.474999964,0.150000036,0.0],
    [0.4926777,0.150000036,0.0146446526],
    [0.474999964,0.4,0.0],
    [0.4926777,0.4,0.0146446526],
    [0.474999964,0.475,0.0],
    [0.4926777,0.475,0.0146446526],
    [0.4926777,-0.4,0.0146446526],
    [0.5,-0.475,0.0500000119],
    [0.4926777,-0.475,0.0146446526],
    [0.5,-0.4,0.0500000119],
    [0.4926777,-0.150000066,0.0146446526],
    [0.5,-0.150000066,0.0500000119],
    [0.4926777,0.150000036,0.0146446526],
    [0.5,0.150000036,0.0500000119],
    [0.4926777,0.4,0.0146446526],
    [0.5,0.4,0.0500000119],
    [0.4926777,0.475,0.0146446526],
    [0.5,0.475,0.0500000119],
    [0.474999964,-0.5,0.8],
    [0.4926777,-0.4926777,0.95],
    [0.474999964,-0.5,0.95],
    [0.4926777,-0.4926777,0.8],
    [0.474999964,-0.5,0.6],
    [0.4926777,-0.4926777,0.6],
    [0.474999964,-0.5,0.399999976],
    [0.4926777,-0.4926777,0.399999976],
    [0.474999964,-0.5,0.199999988],
    [0.4926777,-0.4926777,0.199999988],
    [0.474999964,-0.5,0.0500000119],
    [0.4926777,-0.4926777,0.0500000119],
    [0.4926777,-0.4926777,0.8],
    [0.5,-0.475,0.95],
    [0.4926777,-0.4926777,0.95],
    [0.5,-0.475,0.8],
    [0.4926777,-0.4926777,0.6],
    [0.5,-0.475,0.6],
    [0.4926777,-0.4926777,0.399999976],
    [0.5,-0.475,0.399999976],
    [0.4926777,-0.4926777,0.199999988],
    [0.5,-0.475,0.199999988],
    [0.4926777,-0.4926777,0.0500000119],
    [0.5,-0.475,0.0500000119],
    [0.474999964,0.4,1.0],
    [0.4926777,0.475,0.9853554],
    [0.474999964,0.475,1.0],
    [0.4926777,0.4,0.9853554],
    [0.474999964,0.150000036,1.0],
    [0.4926777,0.150000036,0.9853554],
    [0.474999964,-0.150000066,1.0],
    [0.4926777,-0.150000066,0.9853554],
    [0.474999964,-0.4,1.0],
    [0.4926777,-0.4,0.9853554],
    [0.474999964,-0.475,1.0],
    [0.4926777,-0.475,0.9853554],
    [0.4926777,0.4,0.9853554],
    [0.5,0.475,0.95],
    [0.4926777,0.475,0.9853554],
    [0.5,0.4,0.95],
    [0.4926777,0.150000036,0.9853554],
    [0.5,0.150000036,0.95],
    [0.4926777,-0.150000066,0.9853554],
    [0.5,-0.150000066,0.95],
    [0.4926777,-0.4,0.9853554],
    [0.5,-0.4,0.95],
    [0.4926777,-0.475,0.9853554],
    [0.5,-0.475,0.95],
    [0.474999964,0.5,0.199999988],
    [0.4926777,0.4926777,0.0500000119],
    [0.474999964,0.5,0.0500000119],
    [0.4926777,0.4926777,0.199999988],
    [0.474999964,0.5,0.399999976],
    [0.4926777,0.4926777,0.399999976],
    [0.474999964,0.5,0.6],
    [0.4926777,0.4926777,0.6],
    [0.474999964,0.5,0.8],
    [0.4926777,0.4926777,0.8],
    [0.474999964,0.5,0.95],
    [0.4926777,0.4926777,0.95],
    [0.4926777,0.4926777,0.199999988],
    [0.5,0.475,0.0500000119],
    [0.4926777,0.4926777,0.0500000119],
    [0.5,0.475,0.199999988],
    [0.4926777,0.4926777,0.399999976],
    [0.5,0.475,0.399999976],
    [0.4926777,0.4926777,0.6],
    [0.5,0.475,0.6],
    [0.4926777,0.4926777,0.8],
    [0.5,0.475,0.8],
    [0.4926777,0.4926777,0.95],
    [0.5,0.475,0.95],
    [0.35,0.475,0.0],
    [0.474999964,0.4926777,0.0146446526],
    [0.474999964,0.475,0.0],
    [0.35,0.4926777,0.0146446526],
    [0.116666704,0.475,0.0],
    [0.116666704,0.4926777,0.0146446526],
    [-0.25000006,0.475,0.0],
    [-0.25000006,0.4926777,0.0146446526],
    [-0.4,0.475,0.0],
    [-0.4,0.4926777,0.0146446526],
    [-0.475,0.475,0.0],
    [-0.475,0.4926777,0.0146446526],
    [0.35,0.4926777,0.0146446526],
    [0.474999964,0.5,0.0500000119],
    [0.474999964,0.4926777,0.0146446526],
    [0.35,0.5,0.0500000119],
    [0.116666704,0.4926777,0.0146446526],
    [0.116666704,0.5,0.0500000119],
    [-0.25000006,0.4926777,0.0146446526],
    [-0.25000006,0.5,0.0500000119],
    [-0.4,0.4926777,0.0146446526],
    [-0.4,0.5,0.0500000119],
    [-0.475,0.4926777,0.0146446526],
    [-0.475,0.5,0.0500000119],
    [-0.4,0.475,1.0],
    [-0.475,0.4926777,0.9853554],
    [-0.475,0.475,1.0],
    [-0.4,0.4926777,0.9853554],
    [-0.25000006,0.475,1.0],
    [-0.25000006,0.4926777,0.9853554],
    [0.116666704,0.475,1.0],
    [0.116666704,0.4926777,0.9853554],
    [0.35,0.475,1.0],
    [0.35,0.4926777,0.9853554],
    [0.474999964,0.475,1.0],
    [0.474999964,0.4926777,0.9853554],
    [-0.4,0.4926777,0.9853554],
    [-0.475,0.5,0.95],
    [-0.475,0.4926777,0.9853554],
    [-0.4,0.5,0.95],
    [-0.25000006,0.4926777,0.9853554],
    [-0.25000006,0.5,0.95],
    [0.116666704,0.4926777,0.9853554],
    [0.116666704,0.5,0.95],
    [0.35,0.4926777,0.9853554],
    [0.35,0.5,0.95],
    [0.474999964,0.4926777,0.9853554],
    [0.474999964,0.5,0.95],
    [-0.5,0.475,0.199999988],
    [-0.4926777,0.4926777,0.0500000119],
    [-0.5,0.475,0.0500000119],
    [-0.4926777,0.4926777,0.199999988],
    [-0.5,0.475,0.399999976],
    [-0.4926777,0.4926777,0.399999976],
    [-0.5,0.475,0.6],
    [-0.4926777,0.4926777,0.6],
    [-0.5,0.475,0.8],
    [-0.4926777,0.4926777,0.8],
    [-0.5,0.475,0.95],
    [-0.4926777,0.4926777,0.95],
    [-0.4926777,0.4926777,0.199999988],
    [-0.475,0.5,0.0500000119],
    [-0.4926777,0.4926777,0.0500000119],
    [-0.475,0.5,0.199999988],
    [-0.4926777,0.4926777,0.399999976],
    [-0.475,0.5,0.399999976],
    [-0.4926777,0.4926777,0.6],
    [-0.475,0.5,0.6],
    [-0.4926777,0.4926777,0.8],
    [-0.475,0.5,0.8],
    [-0.4926777,0.4926777,0.95],
    [-0.475,0.5,0.95],
    [-0.475,0.4,0.0],
    [-0.4926777,0.475,0.0146446526],
    [-0.475,0.475,0.0],
    [-0.4926777,0.4,0.0146446526],
    [-0.475,0.150000036,0.0],
    [-0.4926777,0.150000036,0.0146446526],
    [-0.475,-0.150000066,0.0],
    [-0.4926777,-0.150000066,0.0146446526],
    [-0.475,-0.4,0.0],
    [-0.4926777,-0.4,0.0146446526],
    [-0.475,-0.475,0.0],
    [-0.4926777,-0.475,0.0146446526],
    [-0.4926777,0.4,0.0146446526],
    [-0.5,0.475,0.0500000119],
    [-0.4926777,0.475,0.0146446526],
    [-0.5,0.4,0.0500000119],
    [-0.4926777,0.150000036,0.0146446526],
    [-0.5,0.150000036,0.0500000119],
    [-0.4926777,-0.150000066,0.0146446526],
    [-0.5,-0.150000066,0.0500000119],
    [-0.4926777,-0.4,0.0146446526],
    [-0.5,-0.4,0.0500000119],
    [-0.4926777,-0.475,0.0146446526],
    [-0.5,-0.475,0.0500000119],
    [-0.475,-0.4,1.0],
    [-0.4926777,-0.475,0.9853554],
    [-0.475,-0.475,1.0],
    [-0.4926777,-0.4,0.9853554],
    [-0.475,-0.150000066,1.0],
    [-0.4926777,-0.150000066,0.9853554],
    [-0.475,0.150000036,1.0],
    [-0.4926777,0.150000036,0.9853554],
    [-0.475,0.4,1.0],
    [-0.4926777,0.4,0.9853554],
    [-0.475,0.475,1.0],
    [-0.4926777,0.475,0.9853554],
    [-0.4926777,-0.4,0.9853554],
    [-0.5,-0.475,0.95],
    [-0.4926777,-0.475,0.9853554],
    [-0.5,-0.4,0.95],
    [-0.4926777,-0.150000066,0.9853554],
    [-0.5,-0.150000066,0.95],
    [-0.4926777,0.150000036,0.9853554],
    [-0.5,0.150000036,0.95],
    [-0.4926777,0.4,0.9853554],
    [-0.5,0.4,0.95],
    [-0.4926777,0.475,0.9853554],
    [-0.5,0.475,0.95],
    [-0.475,-0.5,0.199999988],
    [-0.4926777,-0.4926777,0.0500000119],
    [-0.475,-0.5,0.0500000119],
    [-0.4926777,-0.4926777,0.199999988],
    [-0.475,-0.5,0.399999976],
    [-0.4926777,-0.4926777,0.399999976],
    [-0.475,-0.5,0.6],
    [-0.4926777,-0.4926777,0.6],
    [-0.475,-0.5,0.8],
    [-0.4926777,-0.4926777,0.8],
    [-0.475,-0.5,0.95],
    [-0.4926777,-0.4926777,0.95],
    [-0.4926777,-0.4926777,0.199999988],
    [-0.5,-0.475,0.0500000119],
    [-0.4926777,-0.4926777,0.0500000119],
    [-0.5,-0.475,0.199999988],
    [-0.4926777,-0.4926777,0.399999976],
    [-0.5,-0.475,0.399999976],
    [-0.4926777,-0.4926777,0.6],
    [-0.5,-0.475,0.6],
    [-0.4926777,-0.4926777,0.8],
    [-0.5,-0.475,0.8],
    [-0.4926777,-0.4926777,0.95],
    [-0.5,-0.475,0.95],
    [-0.4,-0.475,0.0],
    [-0.475,-0.4926777,0.0146446526],
    [-0.475,-0.475,0.0],
    [-0.4,-0.4926777,0.0146446526],
    [-0.25000006,-0.475,0.0],
    [-0.25000006,-0.4926777,0.0146446526],
    [0.116666704,-0.475,0.0],
    [0.116666704,-0.4926777,0.0146446526],
    [0.35,-0.475,0.0],
    [0.35,-0.4926777,0.0146446526],
    [0.474999964,-0.475,0.0],
    [0.474999964,-0.4926777,0.0146446526],
    [-0.4,-0.4926777,0.0146446526],
    [-0.475,-0.5,0.0500000119],
    [-0.475,-0.4926777,0.0146446526],
    [-0.4,-0.5,0.0500000119],
    [-0.25000006,-0.4926777,0.0146446526],
    [-0.25000006,-0.5,0.0500000119],
    [0.116666704,-0.4926777,0.0146446526],
    [0.116666704,-0.5,0.0500000119],
    [0.35,-0.4926777,0.0146446526],
    [0.35,-0.5,0.0500000119],
    [0.474999964,-0.4926777,0.0146446526],
    [0.474999964,-0.5,0.0500000119],
    [0.35,-0.475,1.0],
    [0.474999964,-0.4926777,0.9853554],
    [0.474999964,-0.475,1.0],
    [0.35,-0.4926777,0.9853554],
    [0.116666704,-0.475,1.0],
    [0.116666704,-0.4926777,0.9853554],
    [-0.25000006,-0.475,1.0],
    [-0.25000006,-0.4926777,0.9853554],
    [-0.4,-0.475,1.0],
    [-0.4,-0.4926777,0.9853554],
    [-0.475,-0.475,1.0],
    [-0.475,-0.4926777,0.9853554],
    [0.35,-0.4926777,0.9853554],
    [0.474999964,-0.5,0.95],
    [0.474999964,-0.4926777,0.9853554],
    [0.35,-0.5,0.95],
    [0.116666704,-0.4926777,0.9853554],
    [0.116666704,-0.5,0.95],
    [-0.25000006,-0.4926777,0.9853554],
    [-0.25000006,-0.5,0.95],
    [-0.4,-0.4926777,0.9853554],
    [-0.4,-0.5,0.95],
    [-0.475,-0.4926777,0.9853554],
    [-0.475,-0.5,0.95]
  ],
  ""Triangles"": [
    0,
    1,
    2,
    0,
    2,
    3,
    4,
    0,
    3,
    3,
    2,
    5,
    4,
    3,
    6,
    7,
    4,
    6,
    3,
    5,
    8,
    6,
    3,
    8,
    8,
    5,
    9,
    7,
    6,
    10,
    11,
    7,
    10,
    8,
    9,
    12,
    12,
    9,
    13,
    6,
    8,
    14,
    10,
    6,
    14,
    14,
    8,
    12,
    11,
    10,
    15,
    16,
    11,
    15,
    16,
    15,
    17,
    12,
    13,
    18,
    18,
    13,
    19,
    18,
    19,
    20,
    14,
    12,
    21,
    21,
    12,
    18,
    10,
    14,
    22,
    15,
    10,
    22,
    22,
    14,
    21,
    17,
    15,
    23,
    15,
    22,
    23,
    17,
    23,
    24,
    25,
    18,
    20,
    21,
    18,
    25,
    25,
    20,
    26,
    22,
    21,
    27,
    23,
    22,
    27,
    27,
    21,
    25,
    24,
    23,
    28,
    23,
    27,
    28,
    24,
    28,
    29,
    30,
    25,
    26,
    27,
    25,
    30,
    28,
    27,
    30,
    30,
    26,
    31,
    29,
    28,
    32,
    28,
    30,
    32,
    32,
    30,
    31,
    29,
    32,
    33,
    32,
    31,
    34,
    33,
    32,
    34,
    33,
    34,
    35,
    36,
    37,
    38,
    36,
    38,
    39,
    40,
    36,
    39,
    39,
    38,
    41,
    40,
    39,
    42,
    43,
    40,
    42,
    39,
    41,
    44,
    42,
    39,
    44,
    44,
    41,
    45,
    43,
    42,
    46,
    47,
    43,
    46,
    42,
    44,
    48,
    46,
    42,
    48,
    44,
    45,
    49,
    48,
    44,
    49,
    49,
    45,
    50,
    47,
    46,
    51,
    52,
    47,
    51,
    52,
    51,
    53,
    46,
    48,
    54,
    51,
    46,
    54,
    53,
    51,
    55,
    51,
    54,
    55,
    53,
    55,
    56,
    48,
    49,
    57,
    54,
    48,
    57,
    49,
    50,
    58,
    57,
    49,
    58,
    58,
    50,
    59,
    58,
    59,
    60,
    61,
    58,
    60,
    57,
    58,
    61,
    61,
    60,
    62,
    54,
    57,
    63,
    55,
    54,
    63,
    63,
    57,
    61,
    56,
    55,
    64,
    55,
    63,
    64,
    56,
    64,
    65,
    66,
    61,
    62,
    63,
    61,
    66,
    64,
    63,
    66,
    66,
    62,
    67,
    65,
    64,
    68,
    64,
    66,
    68,
    68,
    66,
    67,
    65,
    68,
    69,
    68,
    67,
    70,
    69,
    68,
    70,
    69,
    70,
    71,
    72,
    73,
    74,
    72,
    74,
    75,
    76,
    72,
    75,
    75,
    74,
    77,
    76,
    75,
    78,
    79,
    76,
    78,
    75,
    77,
    80,
    78,
    75,
    80,
    80,
    77,
    81,
    79,
    78,
    82,
    83,
    79,
    82,
    78,
    80,
    84,
    82,
    78,
    84,
    80,
    81,
    85,
    84,
    80,
    85,
    85,
    81,
    86,
    83,
    82,
    87,
    88,
    83,
    87,
    88,
    87,
    89,
    82,
    84,
    90,
    87,
    82,
    90,
    89,
    87,
    91,
    87,
    90,
    91,
    89,
    91,
    92,
    84,
    85,
    93,
    90,
    84,
    93,
    85,
    86,
    94,
    93,
    85,
    94,
    94,
    86,
    95,
    94,
    95,
    96,
    97,
    94,
    96,
    93,
    94,
    97,
    97,
    96,
    98,
    90,
    93,
    99,
    91,
    90,
    99,
    99,
    93,
    97,
    92,
    91,
    100,
    91,
    99,
    100,
    92,
    100,
    101,
    102,
    97,
    98,
    99,
    97,
    102,
    100,
    99,
    102,
    102,
    98,
    103,
    101,
    100,
    104,
    100,
    102,
    104,
    104,
    102,
    103,
    101,
    104,
    105,
    104,
    103,
    106,
    105,
    104,
    106,
    105,
    106,
    107,
    108,
    109,
    110,
    108,
    110,
    111,
    112,
    108,
    111,
    111,
    110,
    113,
    112,
    111,
    114,
    115,
    112,
    114,
    111,
    113,
    116,
    114,
    111,
    116,
    116,
    113,
    117,
    115,
    114,
    118,
    119,
    115,
    118,
    116,
    117,
    120,
    120,
    117,
    121,
    114,
    116,
    122,
    118,
    114,
    122,
    122,
    116,
    120,
    119,
    118,
    123,
    124,
    119,
    123,
    124,
    123,
    125,
    120,
    121,
    126,
    126,
    121,
    127,
    126,
    127,
    128,
    122,
    120,
    129,
    129,
    120,
    126,
    118,
    122,
    130,
    123,
    118,
    130,
    130,
    122,
    129,
    125,
    123,
    131,
    123,
    130,
    131,
    125,
    131,
    132,
    133,
    126,
    128,
    129,
    126,
    133,
    133,
    128,
    134,
    130,
    129,
    135,
    131,
    130,
    135,
    135,
    129,
    133,
    132,
    131,
    136,
    131,
    135,
    136,
    132,
    136,
    137,
    138,
    133,
    134,
    135,
    133,
    138,
    136,
    135,
    138,
    138,
    134,
    139,
    137,
    136,
    140,
    136,
    138,
    140,
    140,
    138,
    139,
    137,
    140,
    141,
    140,
    139,
    142,
    141,
    140,
    142,
    141,
    142,
    143,
    144,
    145,
    146,
    144,
    147,
    145,
    148,
    147,
    144,
    147,
    149,
    145,
    148,
    150,
    147,
    151,
    150,
    148,
    147,
    152,
    149,
    150,
    152,
    147,
    152,
    153,
    149,
    151,
    154,
    150,
    155,
    154,
    151,
    152,
    156,
    153,
    156,
    157,
    153,
    150,
    158,
    152,
    154,
    158,
    150,
    158,
    156,
    152,
    155,
    159,
    154,
    160,
    159,
    155,
    160,
    161,
    159,
    156,
    162,
    157,
    162,
    163,
    157,
    162,
    164,
    163,
    158,
    165,
    156,
    165,
    162,
    156,
    154,
    166,
    158,
    159,
    166,
    154,
    166,
    165,
    158,
    161,
    167,
    159,
    159,
    167,
    166,
    161,
    168,
    167,
    169,
    164,
    162,
    165,
    169,
    162,
    169,
    170,
    164,
    166,
    171,
    165,
    167,
    171,
    166,
    171,
    169,
    165,
    168,
    172,
    167,
    167,
    172,
    171,
    168,
    173,
    172,
    174,
    170,
    169,
    171,
    174,
    169,
    172,
    174,
    171,
    174,
    175,
    170,
    173,
    176,
    172,
    172,
    176,
    174,
    176,
    175,
    174,
    173,
    177,
    176,
    176,
    178,
    175,
    177,
    178,
    176,
    177,
    179,
    178,
    180,
    181,
    182,
    180,
    182,
    183,
    184,
    180,
    183,
    183,
    182,
    185,
    184,
    183,
    186,
    187,
    184,
    186,
    183,
    185,
    188,
    186,
    183,
    188,
    188,
    185,
    189,
    187,
    186,
    190,
    191,
    187,
    190,
    186,
    188,
    192,
    190,
    186,
    192,
    188,
    189,
    193,
    192,
    188,
    193,
    193,
    189,
    194,
    191,
    190,
    195,
    196,
    191,
    195,
    196,
    195,
    197,
    190,
    192,
    198,
    195,
    190,
    198,
    197,
    195,
    199,
    195,
    198,
    199,
    197,
    199,
    200,
    192,
    193,
    201,
    198,
    192,
    201,
    193,
    194,
    202,
    201,
    193,
    202,
    202,
    194,
    203,
    202,
    203,
    204,
    205,
    202,
    204,
    201,
    202,
    205,
    205,
    204,
    206,
    198,
    201,
    207,
    199,
    198,
    207,
    207,
    201,
    205,
    200,
    199,
    208,
    199,
    207,
    208,
    200,
    208,
    209,
    210,
    205,
    206,
    207,
    205,
    210,
    208,
    207,
    210,
    210,
    206,
    211,
    209,
    208,
    212,
    208,
    210,
    212,
    212,
    210,
    211,
    209,
    212,
    213,
    212,
    211,
    214,
    213,
    212,
    214,
    213,
    214,
    215,
    216,
    217,
    218,
    216,
    218,
    219,
    220,
    221,
    222,
    220,
    222,
    223,
    224,
    225,
    226,
    224,
    226,
    227,
    228,
    229,
    230,
    228,
    230,
    231,
    232,
    233,
    234,
    232,
    234,
    235,
    236,
    237,
    238,
    236,
    238,
    239,
    240,
    241,
    242,
    240,
    242,
    243,
    244,
    245,
    246,
    244,
    246,
    247,
    248,
    249,
    250,
    248,
    250,
    251,
    252,
    253,
    254,
    252,
    254,
    255,
    256,
    257,
    258,
    256,
    258,
    259,
    260,
    261,
    262,
    260,
    262,
    263,
    264,
    265,
    266,
    264,
    266,
    267,
    268,
    269,
    270,
    268,
    270,
    271,
    272,
    273,
    274,
    272,
    274,
    275,
    276,
    277,
    278,
    276,
    278,
    279,
    280,
    281,
    282,
    280,
    282,
    283,
    284,
    285,
    286,
    284,
    286,
    287,
    288,
    289,
    290,
    288,
    290,
    291,
    292,
    293,
    294,
    292,
    294,
    295,
    296,
    297,
    298,
    296,
    298,
    299,
    300,
    301,
    302,
    300,
    302,
    303,
    304,
    305,
    306,
    304,
    306,
    307,
    308,
    309,
    310,
    308,
    310,
    311,
    312,
    313,
    314,
    312,
    315,
    313,
    316,
    315,
    312,
    316,
    317,
    315,
    318,
    317,
    316,
    318,
    319,
    317,
    320,
    319,
    318,
    320,
    321,
    319,
    322,
    321,
    320,
    322,
    323,
    321,
    324,
    325,
    326,
    324,
    327,
    325,
    328,
    327,
    324,
    328,
    329,
    327,
    330,
    329,
    328,
    330,
    331,
    329,
    332,
    331,
    330,
    332,
    333,
    331,
    334,
    333,
    332,
    334,
    335,
    333,
    336,
    337,
    338,
    336,
    339,
    337,
    340,
    339,
    336,
    340,
    341,
    339,
    342,
    341,
    340,
    342,
    343,
    341,
    344,
    343,
    342,
    344,
    345,
    343,
    346,
    345,
    344,
    346,
    347,
    345,
    348,
    349,
    350,
    348,
    351,
    349,
    352,
    351,
    348,
    352,
    353,
    351,
    354,
    353,
    352,
    354,
    355,
    353,
    356,
    355,
    354,
    356,
    357,
    355,
    358,
    357,
    356,
    358,
    359,
    357,
    360,
    361,
    362,
    360,
    363,
    361,
    364,
    363,
    360,
    364,
    365,
    363,
    366,
    365,
    364,
    366,
    367,
    365,
    368,
    367,
    366,
    368,
    369,
    367,
    370,
    369,
    368,
    370,
    371,
    369,
    372,
    373,
    374,
    372,
    375,
    373,
    376,
    375,
    372,
    376,
    377,
    375,
    378,
    377,
    376,
    378,
    379,
    377,
    380,
    379,
    378,
    380,
    381,
    379,
    382,
    381,
    380,
    382,
    383,
    381,
    384,
    385,
    386,
    384,
    387,
    385,
    388,
    387,
    384,
    388,
    389,
    387,
    390,
    389,
    388,
    390,
    391,
    389,
    392,
    391,
    390,
    392,
    393,
    391,
    394,
    393,
    392,
    394,
    395,
    393,
    396,
    397,
    398,
    396,
    399,
    397,
    400,
    399,
    396,
    400,
    401,
    399,
    402,
    401,
    400,
    402,
    403,
    401,
    404,
    403,
    402,
    404,
    405,
    403,
    406,
    405,
    404,
    406,
    407,
    405,
    408,
    409,
    410,
    408,
    411,
    409,
    412,
    411,
    408,
    412,
    413,
    411,
    414,
    413,
    412,
    414,
    415,
    413,
    416,
    415,
    414,
    416,
    417,
    415,
    418,
    417,
    416,
    418,
    419,
    417,
    420,
    421,
    422,
    420,
    423,
    421,
    424,
    423,
    420,
    424,
    425,
    423,
    426,
    425,
    424,
    426,
    427,
    425,
    428,
    427,
    426,
    428,
    429,
    427,
    430,
    429,
    428,
    430,
    431,
    429,
    432,
    433,
    434,
    432,
    435,
    433,
    436,
    435,
    432,
    436,
    437,
    435,
    438,
    437,
    436,
    438,
    439,
    437,
    440,
    439,
    438,
    440,
    441,
    439,
    442,
    441,
    440,
    442,
    443,
    441,
    444,
    445,
    446,
    444,
    447,
    445,
    448,
    447,
    444,
    448,
    449,
    447,
    450,
    449,
    448,
    450,
    451,
    449,
    452,
    451,
    450,
    452,
    453,
    451,
    454,
    453,
    452,
    454,
    455,
    453,
    456,
    457,
    458,
    456,
    459,
    457,
    460,
    459,
    456,
    460,
    461,
    459,
    462,
    461,
    460,
    462,
    463,
    461,
    464,
    463,
    462,
    464,
    465,
    463,
    466,
    465,
    464,
    466,
    467,
    465,
    468,
    469,
    470,
    468,
    471,
    469,
    472,
    471,
    468,
    472,
    473,
    471,
    474,
    473,
    472,
    474,
    475,
    473,
    476,
    475,
    474,
    476,
    477,
    475,
    478,
    477,
    476,
    478,
    479,
    477,
    480,
    481,
    482,
    480,
    483,
    481,
    484,
    483,
    480,
    484,
    485,
    483,
    486,
    485,
    484,
    486,
    487,
    485,
    488,
    487,
    486,
    488,
    489,
    487,
    490,
    489,
    488,
    490,
    491,
    489,
    492,
    493,
    494,
    492,
    495,
    493,
    496,
    495,
    492,
    496,
    497,
    495,
    498,
    497,
    496,
    498,
    499,
    497,
    500,
    499,
    498,
    500,
    501,
    499,
    502,
    501,
    500,
    502,
    503,
    501,
    504,
    505,
    506,
    504,
    507,
    505,
    508,
    507,
    504,
    508,
    509,
    507,
    510,
    509,
    508,
    510,
    511,
    509,
    512,
    511,
    510,
    512,
    513,
    511,
    514,
    513,
    512,
    514,
    515,
    513,
    516,
    517,
    518,
    516,
    519,
    517,
    520,
    519,
    516,
    520,
    521,
    519,
    522,
    521,
    520,
    522,
    523,
    521,
    524,
    523,
    522,
    524,
    525,
    523,
    526,
    525,
    524,
    526,
    527,
    525,
    528,
    529,
    530,
    528,
    531,
    529,
    532,
    531,
    528,
    532,
    533,
    531,
    534,
    533,
    532,
    534,
    535,
    533,
    536,
    535,
    534,
    536,
    537,
    535,
    538,
    537,
    536,
    538,
    539,
    537,
    540,
    541,
    542,
    540,
    543,
    541,
    544,
    543,
    540,
    544,
    545,
    543,
    546,
    545,
    544,
    546,
    547,
    545,
    548,
    547,
    546,
    548,
    549,
    547,
    550,
    549,
    548,
    550,
    551,
    549,
    552,
    553,
    554,
    552,
    555,
    553,
    556,
    555,
    552,
    556,
    557,
    555,
    558,
    557,
    556,
    558,
    559,
    557,
    560,
    559,
    558,
    560,
    561,
    559,
    562,
    561,
    560,
    562,
    563,
    561,
    564,
    565,
    566,
    564,
    567,
    565,
    568,
    567,
    564,
    568,
    569,
    567,
    570,
    569,
    568,
    570,
    571,
    569,
    572,
    571,
    570,
    572,
    573,
    571,
    574,
    573,
    572,
    574,
    575,
    573,
    576,
    577,
    578,
    576,
    579,
    577,
    580,
    579,
    576,
    580,
    581,
    579,
    582,
    581,
    580,
    582,
    583,
    581,
    584,
    583,
    582,
    584,
    585,
    583,
    586,
    585,
    584,
    586,
    587,
    585,
    588,
    589,
    590,
    588,
    591,
    589,
    592,
    591,
    588,
    592,
    593,
    591,
    594,
    593,
    592,
    594,
    595,
    593,
    596,
    595,
    594,
    596,
    597,
    595,
    598,
    597,
    596,
    598,
    599,
    597
  ],
  ""Resizer"": {
    ""PivotOffset"": [0.0,0.0,0.0],
    ""ScalingX"": ""SLICE_WITH_ASYMMETRICAL_BORDER"",
    ""BorderXNegative"": 0.25,
    ""BorderXPositive"": 0.75,
    ""ScalingY"": ""SLICE"",
    ""BorderYNegative"": 0.5,
    ""BorderYPositive"": 0.5,
    ""ScalingZ"": ""SCALE"",
    ""BorderZNegative"": 0.5,
    ""BorderZPositive"": 0.5,
    ""StretchCenter"": -1
  }
}

";

        private const string _cubeDownscaledCenter = @"{
  ""Scale"": [0.5,1.0,0.75],
  ""Vertices"": [
    [-0.5,-0.3,0.0500000119],
    [-0.5,-0.45,0.0500000119],
    [-0.5,-0.45,0.199999988],
    [-0.5,-0.3,0.199999988],
    [-0.5,-0.100000039,0.0500000119],
    [-0.5,-0.45,0.399999976],
    [-0.5,-0.100000039,0.199999988],
    [-0.5,0.100000024,0.0500000119],
    [-0.5,-0.3,0.399999976],
    [-0.5,-0.45,0.6],
    [-0.5,0.100000024,0.199999988],
    [-0.5,0.3,0.0500000119],
    [-0.5,-0.3,0.6],
    [-0.5,-0.45,0.8000001],
    [-0.5,-0.100000039,0.399999976],
    [-0.5,0.3,0.199999988],
    [-0.5,0.45,0.0500000119],
    [-0.5,0.45,0.199999988],
    [-0.5,-0.3,0.8000001],
    [-0.5,-0.45,0.95],
    [-0.5,-0.3,0.95],
    [-0.5,-0.100000039,0.6],
    [-0.5,0.100000024,0.399999976],
    [-0.5,0.3,0.399999976],
    [-0.5,0.45,0.399999976],
    [-0.5,-0.100000039,0.8000001],
    [-0.5,-0.100000039,0.95],
    [-0.5,0.100000024,0.6],
    [-0.5,0.3,0.6],
    [-0.5,0.45,0.6],
    [-0.5,0.100000024,0.8000001],
    [-0.5,0.100000024,0.95],
    [-0.5,0.3,0.8000001],
    [-0.5,0.45,0.8000001],
    [-0.5,0.3,0.95],
    [-0.5,0.45,0.95],
    [-0.233333349,0.5,0.0500000119],
    [-0.433333337,0.5,0.0500000119],
    [-0.433333337,0.5,0.199999988],
    [-0.233333349,0.5,0.199999988],
    [0.0,0.5,0.0500000119],
    [-0.433333337,0.5,0.399999976],
    [0.0,0.5,0.199999988],
    [0.06666669,0.5,0.0500000119],
    [-0.233333349,0.5,0.399999976],
    [-0.433333337,0.5,0.6],
    [0.06666669,0.5,0.199999988],
    [0.200000018,0.5,0.0500000119],
    [0.0,0.5,0.399999976],
    [-0.233333349,0.5,0.6],
    [-0.433333337,0.5,0.8000001],
    [0.200000018,0.5,0.199999988],
    [0.399999976,0.5,0.0500000119],
    [0.399999976,0.5,0.199999988],
    [0.06666669,0.5,0.399999976],
    [0.200000018,0.5,0.399999976],
    [0.399999976,0.5,0.399999976],
    [0.0,0.5,0.6],
    [-0.233333349,0.5,0.8000001],
    [-0.433333337,0.5,0.95],
    [-0.233333349,0.5,0.95],
    [0.0,0.5,0.8000001],
    [0.0,0.5,0.95],
    [0.06666669,0.5,0.6],
    [0.200000018,0.5,0.6],
    [0.399999976,0.5,0.6],
    [0.06666669,0.5,0.8000001],
    [0.06666669,0.5,0.95],
    [0.200000018,0.5,0.8000001],
    [0.399999976,0.5,0.8000001],
    [0.200000018,0.5,0.95],
    [0.399999976,0.5,0.95],
    [-0.433333337,-0.3,1.0],
    [-0.433333337,-0.45,1.0],
    [-0.233333349,-0.45,1.0],
    [-0.233333349,-0.3,1.0],
    [-0.433333337,-0.100000039,1.0],
    [0.0,-0.45,1.0],
    [-0.233333349,-0.100000039,1.0],
    [-0.433333337,0.100000024,1.0],
    [0.0,-0.3,1.0],
    [0.06666669,-0.45,1.0],
    [-0.233333349,0.100000024,1.0],
    [-0.433333337,0.3,1.0],
    [0.0,-0.100000039,1.0],
    [0.06666669,-0.3,1.0],
    [0.200000018,-0.45,1.0],
    [-0.233333349,0.3,1.0],
    [-0.433333337,0.45,1.0],
    [-0.233333349,0.45,1.0],
    [0.0,0.100000024,1.0],
    [0.0,0.3,1.0],
    [0.0,0.45,1.0],
    [0.06666669,-0.100000039,1.0],
    [0.200000018,-0.3,1.0],
    [0.399999976,-0.45,1.0],
    [0.399999976,-0.3,1.0],
    [0.200000018,-0.100000039,1.0],
    [0.399999976,-0.100000039,1.0],
    [0.06666669,0.100000024,1.0],
    [0.06666669,0.3,1.0],
    [0.06666669,0.45,1.0],
    [0.200000018,0.100000024,1.0],
    [0.399999976,0.100000024,1.0],
    [0.200000018,0.3,1.0],
    [0.200000018,0.45,1.0],
    [0.399999976,0.3,1.0],
    [0.399999976,0.45,1.0],
    [0.5,0.3,0.0500000119],
    [0.5,0.45,0.0500000119],
    [0.5,0.45,0.199999988],
    [0.5,0.3,0.199999988],
    [0.5,0.100000024,0.0500000119],
    [0.5,0.45,0.399999976],
    [0.5,0.100000024,0.199999988],
    [0.5,-0.100000039,0.0500000119],
    [0.5,0.3,0.399999976],
    [0.5,0.45,0.6],
    [0.5,-0.100000039,0.199999988],
    [0.5,-0.3,0.0500000119],
    [0.5,0.3,0.6],
    [0.5,0.45,0.8000001],
    [0.5,0.100000024,0.399999976],
    [0.5,-0.3,0.199999988],
    [0.5,-0.45,0.0500000119],
    [0.5,-0.45,0.199999988],
    [0.5,0.3,0.8000001],
    [0.5,0.45,0.95],
    [0.5,0.3,0.95],
    [0.5,0.100000024,0.6],
    [0.5,-0.100000039,0.399999976],
    [0.5,-0.3,0.399999976],
    [0.5,-0.45,0.399999976],
    [0.5,0.100000024,0.8000001],
    [0.5,0.100000024,0.95],
    [0.5,-0.100000039,0.6],
    [0.5,-0.3,0.6],
    [0.5,-0.45,0.6],
    [0.5,-0.100000039,0.8000001],
    [0.5,-0.100000039,0.95],
    [0.5,-0.3,0.8000001],
    [0.5,-0.45,0.8000001],
    [0.5,-0.3,0.95],
    [0.5,-0.45,0.95],
    [-0.433333337,-0.5,0.8000001],
    [-0.233333349,-0.5,0.95],
    [-0.433333337,-0.5,0.95],
    [-0.233333349,-0.5,0.8000001],
    [-0.433333337,-0.5,0.6],
    [0.0,-0.5,0.95],
    [-0.233333349,-0.5,0.6],
    [-0.433333337,-0.5,0.399999976],
    [0.0,-0.5,0.8000001],
    [0.06666669,-0.5,0.95],
    [-0.233333349,-0.5,0.399999976],
    [-0.433333337,-0.5,0.199999988],
    [0.06666669,-0.5,0.8000001],
    [0.200000018,-0.5,0.95],
    [0.0,-0.5,0.6],
    [-0.233333349,-0.5,0.199999988],
    [-0.433333337,-0.5,0.0500000119],
    [-0.233333349,-0.5,0.0500000119],
    [0.200000018,-0.5,0.8000001],
    [0.399999976,-0.5,0.95],
    [0.399999976,-0.5,0.8000001],
    [0.06666669,-0.5,0.6],
    [0.0,-0.5,0.399999976],
    [0.0,-0.5,0.199999988],
    [0.0,-0.5,0.0500000119],
    [0.200000018,-0.5,0.6],
    [0.399999976,-0.5,0.6],
    [0.06666669,-0.5,0.399999976],
    [0.06666669,-0.5,0.199999988],
    [0.06666669,-0.5,0.0500000119],
    [0.200000018,-0.5,0.399999976],
    [0.399999976,-0.5,0.399999976],
    [0.200000018,-0.5,0.199999988],
    [0.200000018,-0.5,0.0500000119],
    [0.399999976,-0.5,0.199999988],
    [0.399999976,-0.5,0.0500000119],
    [0.399999976,-0.3,0.0],
    [0.399999976,-0.45,0.0],
    [0.200000018,-0.45,0.0],
    [0.200000018,-0.3,0.0],
    [0.399999976,-0.100000039,0.0],
    [0.06666669,-0.45,0.0],
    [0.200000018,-0.100000039,0.0],
    [0.399999976,0.100000024,0.0],
    [0.06666669,-0.3,0.0],
    [0.0,-0.45,0.0],
    [0.200000018,0.100000024,0.0],
    [0.399999976,0.3,0.0],
    [0.06666669,-0.100000039,0.0],
    [0.0,-0.3,0.0],
    [-0.233333349,-0.45,0.0],
    [0.200000018,0.3,0.0],
    [0.399999976,0.45,0.0],
    [0.200000018,0.45,0.0],
    [0.06666669,0.100000024,0.0],
    [0.06666669,0.3,0.0],
    [0.06666669,0.45,0.0],
    [0.0,-0.100000039,0.0],
    [-0.233333349,-0.3,0.0],
    [-0.433333337,-0.45,0.0],
    [-0.433333337,-0.3,0.0],
    [-0.233333349,-0.100000039,0.0],
    [-0.433333337,-0.100000039,0.0],
    [0.0,0.100000024,0.0],
    [0.0,0.3,0.0],
    [0.0,0.45,0.0],
    [-0.233333349,0.100000024,0.0],
    [-0.433333337,0.100000024,0.0],
    [-0.233333349,0.3,0.0],
    [-0.233333349,0.45,0.0],
    [-0.433333337,0.3,0.0],
    [-0.433333337,0.45,0.0],
    [0.5,-0.45,0.0500000119],
    [0.470710754,-0.485355377,0.0500000119],
    [0.457735062,-0.478867531,0.021132499],
    [0.470710754,-0.45,0.0146446526],
    [0.399999976,-0.45,0.0],
    [0.470710754,-0.45,0.0146446526],
    [0.457735062,-0.478867531,0.021132499],
    [0.399999976,-0.485355377,0.0146446526],
    [0.399999976,-0.5,0.0500000119],
    [0.399999976,-0.485355377,0.0146446526],
    [0.457735062,-0.478867531,0.021132499],
    [0.470710754,-0.485355377,0.0500000119],
    [0.5,-0.45,0.95],
    [0.470710754,-0.45,0.9853554],
    [0.457735062,-0.478867531,0.978867531],
    [0.470710754,-0.485355377,0.95],
    [0.399999976,-0.5,0.95],
    [0.470710754,-0.485355377,0.95],
    [0.457735062,-0.478867531,0.978867531],
    [0.399999976,-0.485355377,0.9853554],
    [0.399999976,-0.45,1.0],
    [0.399999976,-0.485355377,0.9853554],
    [0.457735062,-0.478867531,0.978867531],
    [0.470710754,-0.45,0.9853554],
    [0.5,0.45,0.0500000119],
    [0.470710754,0.45,0.0146446526],
    [0.457735062,0.478867531,0.021132499],
    [0.470710754,0.485355377,0.0500000119],
    [0.399999976,0.5,0.0500000119],
    [0.470710754,0.485355377,0.0500000119],
    [0.457735062,0.478867531,0.021132499],
    [0.399999976,0.485355377,0.0146446526],
    [0.399999976,0.45,0.0],
    [0.399999976,0.485355377,0.0146446526],
    [0.457735062,0.478867531,0.021132499],
    [0.470710754,0.45,0.0146446526],
    [0.5,0.45,0.95],
    [0.470710754,0.485355377,0.95],
    [0.457735062,0.478867531,0.978867531],
    [0.470710754,0.45,0.9853554],
    [0.399999976,0.45,1.0],
    [0.470710754,0.45,0.9853554],
    [0.457735062,0.478867531,0.978867531],
    [0.399999976,0.485355377,0.9853554],
    [0.399999976,0.5,0.95],
    [0.399999976,0.485355377,0.9853554],
    [0.457735062,0.478867531,0.978867531],
    [0.470710754,0.485355377,0.95],
    [-0.5,-0.45,0.0500000119],
    [-0.480473846,-0.45,0.0146446526],
    [-0.4718234,-0.478867531,0.021132499],
    [-0.480473846,-0.485355377,0.0500000119],
    [-0.433333337,-0.5,0.0500000119],
    [-0.480473846,-0.485355377,0.0500000119],
    [-0.4718234,-0.478867531,0.021132499],
    [-0.433333337,-0.485355377,0.0146446526],
    [-0.433333337,-0.45,0.0],
    [-0.433333337,-0.485355377,0.0146446526],
    [-0.4718234,-0.478867531,0.021132499],
    [-0.480473846,-0.45,0.0146446526],
    [-0.5,-0.45,0.95],
    [-0.480473846,-0.485355377,0.95],
    [-0.4718234,-0.478867531,0.978867531],
    [-0.480473846,-0.45,0.9853554],
    [-0.433333337,-0.45,1.0],
    [-0.480473846,-0.45,0.9853554],
    [-0.4718234,-0.478867531,0.978867531],
    [-0.433333337,-0.485355377,0.9853554],
    [-0.433333337,-0.5,0.95],
    [-0.433333337,-0.485355377,0.9853554],
    [-0.4718234,-0.478867531,0.978867531],
    [-0.480473846,-0.485355377,0.95],
    [-0.433333337,0.5,0.0500000119],
    [-0.433333337,0.485355377,0.0146446526],
    [-0.4718234,0.478867531,0.021132499],
    [-0.480473846,0.485355377,0.0500000119],
    [-0.5,0.45,0.0500000119],
    [-0.480473846,0.485355377,0.0500000119],
    [-0.4718234,0.478867531,0.021132499],
    [-0.480473846,0.45,0.0146446526],
    [-0.433333337,0.45,0.0],
    [-0.480473846,0.45,0.0146446526],
    [-0.4718234,0.478867531,0.021132499],
    [-0.433333337,0.485355377,0.0146446526],
    [-0.433333337,0.5,0.95],
    [-0.480473846,0.485355377,0.95],
    [-0.4718234,0.478867531,0.978867531],
    [-0.433333337,0.485355377,0.9853554],
    [-0.433333337,0.45,1.0],
    [-0.433333337,0.485355377,0.9853554],
    [-0.4718234,0.478867531,0.978867531],
    [-0.480473846,0.45,0.9853554],
    [-0.5,0.45,0.95],
    [-0.480473846,0.45,0.9853554],
    [-0.4718234,0.478867531,0.978867531],
    [-0.480473846,0.485355377,0.95],
    [0.399999976,-0.3,0.0],
    [0.470710754,-0.45,0.0146446526],
    [0.399999976,-0.45,0.0],
    [0.470710754,-0.3,0.0146446526],
    [0.399999976,-0.100000039,0.0],
    [0.470710754,-0.100000039,0.0146446526],
    [0.399999976,0.100000024,0.0],
    [0.470710754,0.100000024,0.0146446526],
    [0.399999976,0.3,0.0],
    [0.470710754,0.3,0.0146446526],
    [0.399999976,0.45,0.0],
    [0.470710754,0.45,0.0146446526],
    [0.470710754,-0.3,0.0146446526],
    [0.5,-0.45,0.0500000119],
    [0.470710754,-0.45,0.0146446526],
    [0.5,-0.3,0.0500000119],
    [0.470710754,-0.100000039,0.0146446526],
    [0.5,-0.100000039,0.0500000119],
    [0.470710754,0.100000024,0.0146446526],
    [0.5,0.100000024,0.0500000119],
    [0.470710754,0.3,0.0146446526],
    [0.5,0.3,0.0500000119],
    [0.470710754,0.45,0.0146446526],
    [0.5,0.45,0.0500000119],
    [0.399999976,-0.5,0.8000001],
    [0.470710754,-0.485355377,0.95],
    [0.399999976,-0.5,0.95],
    [0.470710754,-0.485355377,0.8000001],
    [0.399999976,-0.5,0.6],
    [0.470710754,-0.485355377,0.6],
    [0.399999976,-0.5,0.399999976],
    [0.470710754,-0.485355377,0.399999976],
    [0.399999976,-0.5,0.199999988],
    [0.470710754,-0.485355377,0.199999988],
    [0.399999976,-0.5,0.0500000119],
    [0.470710754,-0.485355377,0.0500000119],
    [0.470710754,-0.485355377,0.8000001],
    [0.5,-0.45,0.95],
    [0.470710754,-0.485355377,0.95],
    [0.5,-0.45,0.8000001],
    [0.470710754,-0.485355377,0.6],
    [0.5,-0.45,0.6],
    [0.470710754,-0.485355377,0.399999976],
    [0.5,-0.45,0.399999976],
    [0.470710754,-0.485355377,0.199999988],
    [0.5,-0.45,0.199999988],
    [0.470710754,-0.485355377,0.0500000119],
    [0.5,-0.45,0.0500000119],
    [0.399999976,0.3,1.0],
    [0.470710754,0.45,0.9853554],
    [0.399999976,0.45,1.0],
    [0.470710754,0.3,0.9853554],
    [0.399999976,0.100000024,1.0],
    [0.470710754,0.100000024,0.9853554],
    [0.399999976,-0.100000039,1.0],
    [0.470710754,-0.100000039,0.9853554],
    [0.399999976,-0.3,1.0],
    [0.470710754,-0.3,0.9853554],
    [0.399999976,-0.45,1.0],
    [0.470710754,-0.45,0.9853554],
    [0.470710754,0.3,0.9853554],
    [0.5,0.45,0.95],
    [0.470710754,0.45,0.9853554],
    [0.5,0.3,0.95],
    [0.470710754,0.100000024,0.9853554],
    [0.5,0.100000024,0.95],
    [0.470710754,-0.100000039,0.9853554],
    [0.5,-0.100000039,0.95],
    [0.470710754,-0.3,0.9853554],
    [0.5,-0.3,0.95],
    [0.470710754,-0.45,0.9853554],
    [0.5,-0.45,0.95],
    [0.399999976,0.5,0.199999988],
    [0.470710754,0.485355377,0.0500000119],
    [0.399999976,0.5,0.0500000119],
    [0.470710754,0.485355377,0.199999988],
    [0.399999976,0.5,0.399999976],
    [0.470710754,0.485355377,0.399999976],
    [0.399999976,0.5,0.6],
    [0.470710754,0.485355377,0.6],
    [0.399999976,0.5,0.8000001],
    [0.470710754,0.485355377,0.8000001],
    [0.399999976,0.5,0.95],
    [0.470710754,0.485355377,0.95],
    [0.470710754,0.485355377,0.199999988],
    [0.5,0.45,0.0500000119],
    [0.470710754,0.485355377,0.0500000119],
    [0.5,0.45,0.199999988],
    [0.470710754,0.485355377,0.399999976],
    [0.5,0.45,0.399999976],
    [0.470710754,0.485355377,0.6],
    [0.5,0.45,0.6],
    [0.470710754,0.485355377,0.8000001],
    [0.5,0.45,0.8000001],
    [0.470710754,0.485355377,0.95],
    [0.5,0.45,0.95],
    [0.200000018,0.45,0.0],
    [0.399999976,0.485355377,0.0146446526],
    [0.399999976,0.45,0.0],
    [0.200000018,0.485355377,0.0146446526],
    [0.06666669,0.45,0.0],
    [0.06666669,0.485355377,0.0146446526],
    [0.0,0.45,0.0],
    [0.0,0.485355377,0.0146446526],
    [-0.233333349,0.45,0.0],
    [-0.233333349,0.485355377,0.0146446526],
    [-0.433333337,0.45,0.0],
    [-0.433333337,0.485355377,0.0146446526],
    [0.200000018,0.485355377,0.0146446526],
    [0.399999976,0.5,0.0500000119],
    [0.399999976,0.485355377,0.0146446526],
    [0.200000018,0.5,0.0500000119],
    [0.06666669,0.485355377,0.0146446526],
    [0.06666669,0.5,0.0500000119],
    [0.0,0.485355377,0.0146446526],
    [0.0,0.5,0.0500000119],
    [-0.233333349,0.485355377,0.0146446526],
    [-0.233333349,0.5,0.0500000119],
    [-0.433333337,0.485355377,0.0146446526],
    [-0.433333337,0.5,0.0500000119],
    [-0.233333349,0.45,1.0],
    [-0.433333337,0.485355377,0.9853554],
    [-0.433333337,0.45,1.0],
    [-0.233333349,0.485355377,0.9853554],
    [0.0,0.45,1.0],
    [0.0,0.485355377,0.9853554],
    [0.06666669,0.45,1.0],
    [0.06666669,0.485355377,0.9853554],
    [0.200000018,0.45,1.0],
    [0.200000018,0.485355377,0.9853554],
    [0.399999976,0.45,1.0],
    [0.399999976,0.485355377,0.9853554],
    [-0.233333349,0.485355377,0.9853554],
    [-0.433333337,0.5,0.95],
    [-0.433333337,0.485355377,0.9853554],
    [-0.233333349,0.5,0.95],
    [0.0,0.485355377,0.9853554],
    [0.0,0.5,0.95],
    [0.06666669,0.485355377,0.9853554],
    [0.06666669,0.5,0.95],
    [0.200000018,0.485355377,0.9853554],
    [0.200000018,0.5,0.95],
    [0.399999976,0.485355377,0.9853554],
    [0.399999976,0.5,0.95],
    [-0.5,0.45,0.199999988],
    [-0.480473846,0.485355377,0.0500000119],
    [-0.5,0.45,0.0500000119],
    [-0.480473846,0.485355377,0.199999988],
    [-0.5,0.45,0.399999976],
    [-0.480473846,0.485355377,0.399999976],
    [-0.5,0.45,0.6],
    [-0.480473846,0.485355377,0.6],
    [-0.5,0.45,0.8000001],
    [-0.480473846,0.485355377,0.8000001],
    [-0.5,0.45,0.95],
    [-0.480473846,0.485355377,0.95],
    [-0.480473846,0.485355377,0.199999988],
    [-0.433333337,0.5,0.0500000119],
    [-0.480473846,0.485355377,0.0500000119],
    [-0.433333337,0.5,0.199999988],
    [-0.480473846,0.485355377,0.399999976],
    [-0.433333337,0.5,0.399999976],
    [-0.480473846,0.485355377,0.6],
    [-0.433333337,0.5,0.6],
    [-0.480473846,0.485355377,0.8000001],
    [-0.433333337,0.5,0.8000001],
    [-0.480473846,0.485355377,0.95],
    [-0.433333337,0.5,0.95],
    [-0.433333337,0.3,0.0],
    [-0.480473846,0.45,0.0146446526],
    [-0.433333337,0.45,0.0],
    [-0.480473846,0.3,0.0146446526],
    [-0.433333337,0.100000024,0.0],
    [-0.480473846,0.100000024,0.0146446526],
    [-0.433333337,-0.100000039,0.0],
    [-0.480473846,-0.100000039,0.0146446526],
    [-0.433333337,-0.3,0.0],
    [-0.480473846,-0.3,0.0146446526],
    [-0.433333337,-0.45,0.0],
    [-0.480473846,-0.45,0.0146446526],
    [-0.480473846,0.3,0.0146446526],
    [-0.5,0.45,0.0500000119],
    [-0.480473846,0.45,0.0146446526],
    [-0.5,0.3,0.0500000119],
    [-0.480473846,0.100000024,0.0146446526],
    [-0.5,0.100000024,0.0500000119],
    [-0.480473846,-0.100000039,0.0146446526],
    [-0.5,-0.100000039,0.0500000119],
    [-0.480473846,-0.3,0.0146446526],
    [-0.5,-0.3,0.0500000119],
    [-0.480473846,-0.45,0.0146446526],
    [-0.5,-0.45,0.0500000119],
    [-0.433333337,-0.3,1.0],
    [-0.480473846,-0.45,0.9853554],
    [-0.433333337,-0.45,1.0],
    [-0.480473846,-0.3,0.9853554],
    [-0.433333337,-0.100000039,1.0],
    [-0.480473846,-0.100000039,0.9853554],
    [-0.433333337,0.100000024,1.0],
    [-0.480473846,0.100000024,0.9853554],
    [-0.433333337,0.3,1.0],
    [-0.480473846,0.3,0.9853554],
    [-0.433333337,0.45,1.0],
    [-0.480473846,0.45,0.9853554],
    [-0.480473846,-0.3,0.9853554],
    [-0.5,-0.45,0.95],
    [-0.480473846,-0.45,0.9853554],
    [-0.5,-0.3,0.95],
    [-0.480473846,-0.100000039,0.9853554],
    [-0.5,-0.100000039,0.95],
    [-0.480473846,0.100000024,0.9853554],
    [-0.5,0.100000024,0.95],
    [-0.480473846,0.3,0.9853554],
    [-0.5,0.3,0.95],
    [-0.480473846,0.45,0.9853554],
    [-0.5,0.45,0.95],
    [-0.433333337,-0.5,0.199999988],
    [-0.480473846,-0.485355377,0.0500000119],
    [-0.433333337,-0.5,0.0500000119],
    [-0.480473846,-0.485355377,0.199999988],
    [-0.433333337,-0.5,0.399999976],
    [-0.480473846,-0.485355377,0.399999976],
    [-0.433333337,-0.5,0.6],
    [-0.480473846,-0.485355377,0.6],
    [-0.433333337,-0.5,0.8000001],
    [-0.480473846,-0.485355377,0.8000001],
    [-0.433333337,-0.5,0.95],
    [-0.480473846,-0.485355377,0.95],
    [-0.480473846,-0.485355377,0.199999988],
    [-0.5,-0.45,0.0500000119],
    [-0.480473846,-0.485355377,0.0500000119],
    [-0.5,-0.45,0.199999988],
    [-0.480473846,-0.485355377,0.399999976],
    [-0.5,-0.45,0.399999976],
    [-0.480473846,-0.485355377,0.6],
    [-0.5,-0.45,0.6],
    [-0.480473846,-0.485355377,0.8000001],
    [-0.5,-0.45,0.8000001],
    [-0.480473846,-0.485355377,0.95],
    [-0.5,-0.45,0.95],
    [-0.233333349,-0.45,0.0],
    [-0.433333337,-0.485355377,0.0146446526],
    [-0.433333337,-0.45,0.0],
    [-0.233333349,-0.485355377,0.0146446526],
    [0.0,-0.45,0.0],
    [0.0,-0.485355377,0.0146446526],
    [0.06666669,-0.45,0.0],
    [0.06666669,-0.485355377,0.0146446526],
    [0.200000018,-0.45,0.0],
    [0.200000018,-0.485355377,0.0146446526],
    [0.399999976,-0.45,0.0],
    [0.399999976,-0.485355377,0.0146446526],
    [-0.233333349,-0.485355377,0.0146446526],
    [-0.433333337,-0.5,0.0500000119],
    [-0.433333337,-0.485355377,0.0146446526],
    [-0.233333349,-0.5,0.0500000119],
    [0.0,-0.485355377,0.0146446526],
    [0.0,-0.5,0.0500000119],
    [0.06666669,-0.485355377,0.0146446526],
    [0.06666669,-0.5,0.0500000119],
    [0.200000018,-0.485355377,0.0146446526],
    [0.200000018,-0.5,0.0500000119],
    [0.399999976,-0.485355377,0.0146446526],
    [0.399999976,-0.5,0.0500000119],
    [0.200000018,-0.45,1.0],
    [0.399999976,-0.485355377,0.9853554],
    [0.399999976,-0.45,1.0],
    [0.200000018,-0.485355377,0.9853554],
    [0.06666669,-0.45,1.0],
    [0.06666669,-0.485355377,0.9853554],
    [0.0,-0.45,1.0],
    [0.0,-0.485355377,0.9853554],
    [-0.233333349,-0.45,1.0],
    [-0.233333349,-0.485355377,0.9853554],
    [-0.433333337,-0.45,1.0],
    [-0.433333337,-0.485355377,0.9853554],
    [0.200000018,-0.485355377,0.9853554],
    [0.399999976,-0.5,0.95],
    [0.399999976,-0.485355377,0.9853554],
    [0.200000018,-0.5,0.95],
    [0.06666669,-0.485355377,0.9853554],
    [0.06666669,-0.5,0.95],
    [0.0,-0.485355377,0.9853554],
    [0.0,-0.5,0.95],
    [-0.233333349,-0.485355377,0.9853554],
    [-0.233333349,-0.5,0.95],
    [-0.433333337,-0.485355377,0.9853554],
    [-0.433333337,-0.5,0.95]
  ],
  ""Triangles"": [
    0,
    1,
    2,
    0,
    2,
    3,
    4,
    0,
    3,
    3,
    2,
    5,
    4,
    3,
    6,
    7,
    4,
    6,
    3,
    5,
    8,
    6,
    3,
    8,
    8,
    5,
    9,
    7,
    6,
    10,
    11,
    7,
    10,
    8,
    9,
    12,
    12,
    9,
    13,
    6,
    8,
    14,
    10,
    6,
    14,
    14,
    8,
    12,
    11,
    10,
    15,
    16,
    11,
    15,
    16,
    15,
    17,
    12,
    13,
    18,
    18,
    13,
    19,
    18,
    19,
    20,
    14,
    12,
    21,
    21,
    12,
    18,
    10,
    14,
    22,
    15,
    10,
    22,
    22,
    14,
    21,
    17,
    15,
    23,
    15,
    22,
    23,
    17,
    23,
    24,
    25,
    18,
    20,
    21,
    18,
    25,
    25,
    20,
    26,
    22,
    21,
    27,
    23,
    22,
    27,
    27,
    21,
    25,
    24,
    23,
    28,
    23,
    27,
    28,
    24,
    28,
    29,
    30,
    25,
    26,
    27,
    25,
    30,
    28,
    27,
    30,
    30,
    26,
    31,
    29,
    28,
    32,
    28,
    30,
    32,
    32,
    30,
    31,
    29,
    32,
    33,
    32,
    31,
    34,
    33,
    32,
    34,
    33,
    34,
    35,
    36,
    37,
    38,
    36,
    38,
    39,
    40,
    36,
    39,
    39,
    38,
    41,
    40,
    39,
    42,
    43,
    40,
    42,
    39,
    41,
    44,
    42,
    39,
    44,
    44,
    41,
    45,
    43,
    42,
    46,
    47,
    43,
    46,
    42,
    44,
    48,
    46,
    42,
    48,
    44,
    45,
    49,
    48,
    44,
    49,
    49,
    45,
    50,
    47,
    46,
    51,
    52,
    47,
    51,
    52,
    51,
    53,
    46,
    48,
    54,
    51,
    46,
    54,
    53,
    51,
    55,
    51,
    54,
    55,
    53,
    55,
    56,
    48,
    49,
    57,
    54,
    48,
    57,
    49,
    50,
    58,
    57,
    49,
    58,
    58,
    50,
    59,
    58,
    59,
    60,
    61,
    58,
    60,
    57,
    58,
    61,
    61,
    60,
    62,
    54,
    57,
    63,
    55,
    54,
    63,
    63,
    57,
    61,
    56,
    55,
    64,
    55,
    63,
    64,
    56,
    64,
    65,
    66,
    61,
    62,
    63,
    61,
    66,
    64,
    63,
    66,
    66,
    62,
    67,
    65,
    64,
    68,
    64,
    66,
    68,
    68,
    66,
    67,
    65,
    68,
    69,
    68,
    67,
    70,
    69,
    68,
    70,
    69,
    70,
    71,
    72,
    73,
    74,
    72,
    74,
    75,
    76,
    72,
    75,
    75,
    74,
    77,
    76,
    75,
    78,
    79,
    76,
    78,
    75,
    77,
    80,
    78,
    75,
    80,
    80,
    77,
    81,
    79,
    78,
    82,
    83,
    79,
    82,
    78,
    80,
    84,
    82,
    78,
    84,
    80,
    81,
    85,
    84,
    80,
    85,
    85,
    81,
    86,
    83,
    82,
    87,
    88,
    83,
    87,
    88,
    87,
    89,
    82,
    84,
    90,
    87,
    82,
    90,
    89,
    87,
    91,
    87,
    90,
    91,
    89,
    91,
    92,
    84,
    85,
    93,
    90,
    84,
    93,
    85,
    86,
    94,
    93,
    85,
    94,
    94,
    86,
    95,
    94,
    95,
    96,
    97,
    94,
    96,
    93,
    94,
    97,
    97,
    96,
    98,
    90,
    93,
    99,
    91,
    90,
    99,
    99,
    93,
    97,
    92,
    91,
    100,
    91,
    99,
    100,
    92,
    100,
    101,
    102,
    97,
    98,
    99,
    97,
    102,
    100,
    99,
    102,
    102,
    98,
    103,
    101,
    100,
    104,
    100,
    102,
    104,
    104,
    102,
    103,
    101,
    104,
    105,
    104,
    103,
    106,
    105,
    104,
    106,
    105,
    106,
    107,
    108,
    109,
    110,
    108,
    110,
    111,
    112,
    108,
    111,
    111,
    110,
    113,
    112,
    111,
    114,
    115,
    112,
    114,
    111,
    113,
    116,
    114,
    111,
    116,
    116,
    113,
    117,
    115,
    114,
    118,
    119,
    115,
    118,
    116,
    117,
    120,
    120,
    117,
    121,
    114,
    116,
    122,
    118,
    114,
    122,
    122,
    116,
    120,
    119,
    118,
    123,
    124,
    119,
    123,
    124,
    123,
    125,
    120,
    121,
    126,
    126,
    121,
    127,
    126,
    127,
    128,
    122,
    120,
    129,
    129,
    120,
    126,
    118,
    122,
    130,
    123,
    118,
    130,
    130,
    122,
    129,
    125,
    123,
    131,
    123,
    130,
    131,
    125,
    131,
    132,
    133,
    126,
    128,
    129,
    126,
    133,
    133,
    128,
    134,
    130,
    129,
    135,
    131,
    130,
    135,
    135,
    129,
    133,
    132,
    131,
    136,
    131,
    135,
    136,
    132,
    136,
    137,
    138,
    133,
    134,
    135,
    133,
    138,
    136,
    135,
    138,
    138,
    134,
    139,
    137,
    136,
    140,
    136,
    138,
    140,
    140,
    138,
    139,
    137,
    140,
    141,
    140,
    139,
    142,
    141,
    140,
    142,
    141,
    142,
    143,
    144,
    145,
    146,
    144,
    147,
    145,
    148,
    147,
    144,
    147,
    149,
    145,
    148,
    150,
    147,
    151,
    150,
    148,
    147,
    152,
    149,
    150,
    152,
    147,
    152,
    153,
    149,
    151,
    154,
    150,
    155,
    154,
    151,
    152,
    156,
    153,
    156,
    157,
    153,
    150,
    158,
    152,
    154,
    158,
    150,
    158,
    156,
    152,
    155,
    159,
    154,
    160,
    159,
    155,
    160,
    161,
    159,
    156,
    162,
    157,
    162,
    163,
    157,
    162,
    164,
    163,
    158,
    165,
    156,
    165,
    162,
    156,
    154,
    166,
    158,
    159,
    166,
    154,
    166,
    165,
    158,
    161,
    167,
    159,
    159,
    167,
    166,
    161,
    168,
    167,
    169,
    164,
    162,
    165,
    169,
    162,
    169,
    170,
    164,
    166,
    171,
    165,
    167,
    171,
    166,
    171,
    169,
    165,
    168,
    172,
    167,
    167,
    172,
    171,
    168,
    173,
    172,
    174,
    170,
    169,
    171,
    174,
    169,
    172,
    174,
    171,
    174,
    175,
    170,
    173,
    176,
    172,
    172,
    176,
    174,
    176,
    175,
    174,
    173,
    177,
    176,
    176,
    178,
    175,
    177,
    178,
    176,
    177,
    179,
    178,
    180,
    181,
    182,
    180,
    182,
    183,
    184,
    180,
    183,
    183,
    182,
    185,
    184,
    183,
    186,
    187,
    184,
    186,
    183,
    185,
    188,
    186,
    183,
    188,
    188,
    185,
    189,
    187,
    186,
    190,
    191,
    187,
    190,
    186,
    188,
    192,
    190,
    186,
    192,
    188,
    189,
    193,
    192,
    188,
    193,
    193,
    189,
    194,
    191,
    190,
    195,
    196,
    191,
    195,
    196,
    195,
    197,
    190,
    192,
    198,
    195,
    190,
    198,
    197,
    195,
    199,
    195,
    198,
    199,
    197,
    199,
    200,
    192,
    193,
    201,
    198,
    192,
    201,
    193,
    194,
    202,
    201,
    193,
    202,
    202,
    194,
    203,
    202,
    203,
    204,
    205,
    202,
    204,
    201,
    202,
    205,
    205,
    204,
    206,
    198,
    201,
    207,
    199,
    198,
    207,
    207,
    201,
    205,
    200,
    199,
    208,
    199,
    207,
    208,
    200,
    208,
    209,
    210,
    205,
    206,
    207,
    205,
    210,
    208,
    207,
    210,
    210,
    206,
    211,
    209,
    208,
    212,
    208,
    210,
    212,
    212,
    210,
    211,
    209,
    212,
    213,
    212,
    211,
    214,
    213,
    212,
    214,
    213,
    214,
    215,
    216,
    217,
    218,
    216,
    218,
    219,
    220,
    221,
    222,
    220,
    222,
    223,
    224,
    225,
    226,
    224,
    226,
    227,
    228,
    229,
    230,
    228,
    230,
    231,
    232,
    233,
    234,
    232,
    234,
    235,
    236,
    237,
    238,
    236,
    238,
    239,
    240,
    241,
    242,
    240,
    242,
    243,
    244,
    245,
    246,
    244,
    246,
    247,
    248,
    249,
    250,
    248,
    250,
    251,
    252,
    253,
    254,
    252,
    254,
    255,
    256,
    257,
    258,
    256,
    258,
    259,
    260,
    261,
    262,
    260,
    262,
    263,
    264,
    265,
    266,
    264,
    266,
    267,
    268,
    269,
    270,
    268,
    270,
    271,
    272,
    273,
    274,
    272,
    274,
    275,
    276,
    277,
    278,
    276,
    278,
    279,
    280,
    281,
    282,
    280,
    282,
    283,
    284,
    285,
    286,
    284,
    286,
    287,
    288,
    289,
    290,
    288,
    290,
    291,
    292,
    293,
    294,
    292,
    294,
    295,
    296,
    297,
    298,
    296,
    298,
    299,
    300,
    301,
    302,
    300,
    302,
    303,
    304,
    305,
    306,
    304,
    306,
    307,
    308,
    309,
    310,
    308,
    310,
    311,
    312,
    313,
    314,
    312,
    315,
    313,
    316,
    315,
    312,
    316,
    317,
    315,
    318,
    317,
    316,
    318,
    319,
    317,
    320,
    319,
    318,
    320,
    321,
    319,
    322,
    321,
    320,
    322,
    323,
    321,
    324,
    325,
    326,
    324,
    327,
    325,
    328,
    327,
    324,
    328,
    329,
    327,
    330,
    329,
    328,
    330,
    331,
    329,
    332,
    331,
    330,
    332,
    333,
    331,
    334,
    333,
    332,
    334,
    335,
    333,
    336,
    337,
    338,
    336,
    339,
    337,
    340,
    339,
    336,
    340,
    341,
    339,
    342,
    341,
    340,
    342,
    343,
    341,
    344,
    343,
    342,
    344,
    345,
    343,
    346,
    345,
    344,
    346,
    347,
    345,
    348,
    349,
    350,
    348,
    351,
    349,
    352,
    351,
    348,
    352,
    353,
    351,
    354,
    353,
    352,
    354,
    355,
    353,
    356,
    355,
    354,
    356,
    357,
    355,
    358,
    357,
    356,
    358,
    359,
    357,
    360,
    361,
    362,
    360,
    363,
    361,
    364,
    363,
    360,
    364,
    365,
    363,
    366,
    365,
    364,
    366,
    367,
    365,
    368,
    367,
    366,
    368,
    369,
    367,
    370,
    369,
    368,
    370,
    371,
    369,
    372,
    373,
    374,
    372,
    375,
    373,
    376,
    375,
    372,
    376,
    377,
    375,
    378,
    377,
    376,
    378,
    379,
    377,
    380,
    379,
    378,
    380,
    381,
    379,
    382,
    381,
    380,
    382,
    383,
    381,
    384,
    385,
    386,
    384,
    387,
    385,
    388,
    387,
    384,
    388,
    389,
    387,
    390,
    389,
    388,
    390,
    391,
    389,
    392,
    391,
    390,
    392,
    393,
    391,
    394,
    393,
    392,
    394,
    395,
    393,
    396,
    397,
    398,
    396,
    399,
    397,
    400,
    399,
    396,
    400,
    401,
    399,
    402,
    401,
    400,
    402,
    403,
    401,
    404,
    403,
    402,
    404,
    405,
    403,
    406,
    405,
    404,
    406,
    407,
    405,
    408,
    409,
    410,
    408,
    411,
    409,
    412,
    411,
    408,
    412,
    413,
    411,
    414,
    413,
    412,
    414,
    415,
    413,
    416,
    415,
    414,
    416,
    417,
    415,
    418,
    417,
    416,
    418,
    419,
    417,
    420,
    421,
    422,
    420,
    423,
    421,
    424,
    423,
    420,
    424,
    425,
    423,
    426,
    425,
    424,
    426,
    427,
    425,
    428,
    427,
    426,
    428,
    429,
    427,
    430,
    429,
    428,
    430,
    431,
    429,
    432,
    433,
    434,
    432,
    435,
    433,
    436,
    435,
    432,
    436,
    437,
    435,
    438,
    437,
    436,
    438,
    439,
    437,
    440,
    439,
    438,
    440,
    441,
    439,
    442,
    441,
    440,
    442,
    443,
    441,
    444,
    445,
    446,
    444,
    447,
    445,
    448,
    447,
    444,
    448,
    449,
    447,
    450,
    449,
    448,
    450,
    451,
    449,
    452,
    451,
    450,
    452,
    453,
    451,
    454,
    453,
    452,
    454,
    455,
    453,
    456,
    457,
    458,
    456,
    459,
    457,
    460,
    459,
    456,
    460,
    461,
    459,
    462,
    461,
    460,
    462,
    463,
    461,
    464,
    463,
    462,
    464,
    465,
    463,
    466,
    465,
    464,
    466,
    467,
    465,
    468,
    469,
    470,
    468,
    471,
    469,
    472,
    471,
    468,
    472,
    473,
    471,
    474,
    473,
    472,
    474,
    475,
    473,
    476,
    475,
    474,
    476,
    477,
    475,
    478,
    477,
    476,
    478,
    479,
    477,
    480,
    481,
    482,
    480,
    483,
    481,
    484,
    483,
    480,
    484,
    485,
    483,
    486,
    485,
    484,
    486,
    487,
    485,
    488,
    487,
    486,
    488,
    489,
    487,
    490,
    489,
    488,
    490,
    491,
    489,
    492,
    493,
    494,
    492,
    495,
    493,
    496,
    495,
    492,
    496,
    497,
    495,
    498,
    497,
    496,
    498,
    499,
    497,
    500,
    499,
    498,
    500,
    501,
    499,
    502,
    501,
    500,
    502,
    503,
    501,
    504,
    505,
    506,
    504,
    507,
    505,
    508,
    507,
    504,
    508,
    509,
    507,
    510,
    509,
    508,
    510,
    511,
    509,
    512,
    511,
    510,
    512,
    513,
    511,
    514,
    513,
    512,
    514,
    515,
    513,
    516,
    517,
    518,
    516,
    519,
    517,
    520,
    519,
    516,
    520,
    521,
    519,
    522,
    521,
    520,
    522,
    523,
    521,
    524,
    523,
    522,
    524,
    525,
    523,
    526,
    525,
    524,
    526,
    527,
    525,
    528,
    529,
    530,
    528,
    531,
    529,
    532,
    531,
    528,
    532,
    533,
    531,
    534,
    533,
    532,
    534,
    535,
    533,
    536,
    535,
    534,
    536,
    537,
    535,
    538,
    537,
    536,
    538,
    539,
    537,
    540,
    541,
    542,
    540,
    543,
    541,
    544,
    543,
    540,
    544,
    545,
    543,
    546,
    545,
    544,
    546,
    547,
    545,
    548,
    547,
    546,
    548,
    549,
    547,
    550,
    549,
    548,
    550,
    551,
    549,
    552,
    553,
    554,
    552,
    555,
    553,
    556,
    555,
    552,
    556,
    557,
    555,
    558,
    557,
    556,
    558,
    559,
    557,
    560,
    559,
    558,
    560,
    561,
    559,
    562,
    561,
    560,
    562,
    563,
    561,
    564,
    565,
    566,
    564,
    567,
    565,
    568,
    567,
    564,
    568,
    569,
    567,
    570,
    569,
    568,
    570,
    571,
    569,
    572,
    571,
    570,
    572,
    573,
    571,
    574,
    573,
    572,
    574,
    575,
    573,
    576,
    577,
    578,
    576,
    579,
    577,
    580,
    579,
    576,
    580,
    581,
    579,
    582,
    581,
    580,
    582,
    583,
    581,
    584,
    583,
    582,
    584,
    585,
    583,
    586,
    585,
    584,
    586,
    587,
    585,
    588,
    589,
    590,
    588,
    591,
    589,
    592,
    591,
    588,
    592,
    593,
    591,
    594,
    593,
    592,
    594,
    595,
    593,
    596,
    595,
    594,
    596,
    597,
    595,
    598,
    597,
    596,
    598,
    599,
    597
  ],
  ""Resizer"": {
    ""PivotOffset"": [0.0,0.0,0.0],
    ""ScalingX"": ""SLICE_WITH_ASYMMETRICAL_BORDER"",
    ""BorderXNegative"": 0.25,
    ""BorderXPositive"": 0.75,
    ""ScalingY"": ""SLICE"",
    ""BorderYNegative"": 0.5,
    ""BorderYPositive"": 0.5,
    ""ScalingZ"": ""SCALE"",
    ""BorderZNegative"": 0.5,
    ""BorderZPositive"": 0.5,
    ""StretchCenter"": 0
  }
}

";

        private const string _cubeUpscaledOffset = @"{
  ""Scale"": [2.0,2.0,2.0],
  ""Vertices"": [
    [-0.6999999,-0.6,-0.149999991],
    [-0.6999999,-0.674999952,-0.149999991],
    [-0.6999999,-0.674999952,-1.49011612E-08],
    [-0.6999999,-0.6,-1.49011612E-08],
    [-0.6999999,-0.150000021,-0.149999991],
    [-0.6999999,-0.674999952,0.199999973],
    [-0.6999999,-0.150000021,-1.49011612E-08],
    [-0.6999999,-0.04999999,-0.149999991],
    [-0.6999999,-0.6,0.199999973],
    [-0.6999999,-0.674999952,0.400000036],
    [-0.6999999,-0.04999999,-1.49011612E-08],
    [-0.6999999,0.0500000045,-0.149999991],
    [-0.6999999,-0.6,0.400000036],
    [-0.6999999,-0.674999952,0.6],
    [-0.6999999,-0.150000021,0.199999973],
    [-0.6999999,0.0500000045,-1.49011612E-08],
    [-0.6999999,0.275,-0.149999991],
    [-0.6999999,0.275,-1.49011612E-08],
    [-0.6999999,-0.6,0.6],
    [-0.6999999,-0.674999952,0.75],
    [-0.6999999,-0.6,0.75],
    [-0.6999999,-0.150000021,0.400000036],
    [-0.6999999,-0.04999999,0.199999973],
    [-0.6999999,0.0500000045,0.199999973],
    [-0.6999999,0.275,0.199999973],
    [-0.6999999,-0.150000021,0.6],
    [-0.6999999,-0.150000021,0.75],
    [-0.6999999,-0.04999999,0.400000036],
    [-0.6999999,0.0500000045,0.400000036],
    [-0.6999999,0.275,0.400000036],
    [-0.6999999,-0.04999999,0.6],
    [-0.6999999,-0.04999999,0.75],
    [-0.6999999,0.0500000045,0.6],
    [-0.6999999,0.275,0.6],
    [-0.6999999,0.0500000045,0.75],
    [-0.6999999,0.275,0.75],
    [-0.599999964,0.3,-0.149999991],
    [-0.674999952,0.3,-0.149999991],
    [-0.674999952,0.3,-1.49011612E-08],
    [-0.599999964,0.3,-1.49011612E-08],
    [-0.49999997,0.3,-0.149999991],
    [-0.674999952,0.3,0.199999973],
    [-0.49999997,0.3,-1.49011612E-08],
    [-0.0499999821,0.3,-0.149999991],
    [-0.599999964,0.3,0.199999973],
    [-0.674999952,0.3,0.400000036],
    [-0.0499999821,0.3,-1.49011612E-08],
    [0.0500000045,0.3,-0.149999991],
    [-0.49999997,0.3,0.199999973],
    [-0.599999964,0.3,0.400000036],
    [-0.674999952,0.3,0.6],
    [0.0500000045,0.3,-1.49011612E-08],
    [0.275,0.3,-0.149999991],
    [0.275,0.3,-1.49011612E-08],
    [-0.0499999821,0.3,0.199999973],
    [0.0500000045,0.3,0.199999973],
    [0.275,0.3,0.199999973],
    [-0.49999997,0.3,0.400000036],
    [-0.599999964,0.3,0.6],
    [-0.674999952,0.3,0.75],
    [-0.599999964,0.3,0.75],
    [-0.49999997,0.3,0.6],
    [-0.49999997,0.3,0.75],
    [-0.0499999821,0.3,0.400000036],
    [0.0500000045,0.3,0.400000036],
    [0.275,0.3,0.400000036],
    [-0.0499999821,0.3,0.6],
    [-0.0499999821,0.3,0.75],
    [0.0500000045,0.3,0.6],
    [0.275,0.3,0.6],
    [0.0500000045,0.3,0.75],
    [0.275,0.3,0.75],
    [-0.674999952,-0.6,0.8],
    [-0.674999952,-0.674999952,0.8],
    [-0.599999964,-0.674999952,0.8],
    [-0.599999964,-0.6,0.8],
    [-0.674999952,-0.150000021,0.8],
    [-0.49999997,-0.674999952,0.8],
    [-0.599999964,-0.150000021,0.8],
    [-0.674999952,-0.04999999,0.8],
    [-0.49999997,-0.6,0.8],
    [-0.0499999821,-0.674999952,0.8],
    [-0.599999964,-0.04999999,0.8],
    [-0.674999952,0.0500000045,0.8],
    [-0.49999997,-0.150000021,0.8],
    [-0.0499999821,-0.6,0.8],
    [0.0500000045,-0.674999952,0.8],
    [-0.599999964,0.0500000045,0.8],
    [-0.674999952,0.275,0.8],
    [-0.599999964,0.275,0.8],
    [-0.49999997,-0.04999999,0.8],
    [-0.49999997,0.0500000045,0.8],
    [-0.49999997,0.275,0.8],
    [-0.0499999821,-0.150000021,0.8],
    [0.0500000045,-0.6,0.8],
    [0.275,-0.674999952,0.8],
    [0.275,-0.6,0.8],
    [0.0500000045,-0.150000021,0.8],
    [0.275,-0.150000021,0.8],
    [-0.0499999821,-0.04999999,0.8],
    [-0.0499999821,0.0500000045,0.8],
    [-0.0499999821,0.275,0.8],
    [0.0500000045,-0.04999999,0.8],
    [0.275,-0.04999999,0.8],
    [0.0500000045,0.0500000045,0.8],
    [0.0500000045,0.275,0.8],
    [0.275,0.0500000045,0.8],
    [0.275,0.275,0.8],
    [0.300000042,0.0500000045,-0.149999991],
    [0.300000042,0.275,-0.149999991],
    [0.300000042,0.275,-1.49011612E-08],
    [0.300000042,0.0500000045,-1.49011612E-08],
    [0.300000042,-0.04999999,-0.149999991],
    [0.300000042,0.275,0.199999973],
    [0.300000042,-0.04999999,-1.49011612E-08],
    [0.300000042,-0.150000021,-0.149999991],
    [0.300000042,0.0500000045,0.199999973],
    [0.300000042,0.275,0.400000036],
    [0.300000042,-0.150000021,-1.49011612E-08],
    [0.300000042,-0.6,-0.149999991],
    [0.300000042,0.0500000045,0.400000036],
    [0.300000042,0.275,0.6],
    [0.300000042,-0.04999999,0.199999973],
    [0.300000042,-0.6,-1.49011612E-08],
    [0.300000042,-0.674999952,-0.149999991],
    [0.300000042,-0.674999952,-1.49011612E-08],
    [0.300000042,0.0500000045,0.6],
    [0.300000042,0.275,0.75],
    [0.300000042,0.0500000045,0.75],
    [0.300000042,-0.04999999,0.400000036],
    [0.300000042,-0.150000021,0.199999973],
    [0.300000042,-0.6,0.199999973],
    [0.300000042,-0.674999952,0.199999973],
    [0.300000042,-0.04999999,0.6],
    [0.300000042,-0.04999999,0.75],
    [0.300000042,-0.150000021,0.400000036],
    [0.300000042,-0.6,0.400000036],
    [0.300000042,-0.674999952,0.400000036],
    [0.300000042,-0.150000021,0.6],
    [0.300000042,-0.150000021,0.75],
    [0.300000042,-0.6,0.6],
    [0.300000042,-0.674999952,0.6],
    [0.300000042,-0.6,0.75],
    [0.300000042,-0.674999952,0.75],
    [-0.674999952,-0.7,0.6],
    [-0.599999964,-0.7,0.75],
    [-0.674999952,-0.7,0.75],
    [-0.599999964,-0.7,0.6],
    [-0.674999952,-0.7,0.400000036],
    [-0.49999997,-0.7,0.75],
    [-0.599999964,-0.7,0.400000036],
    [-0.674999952,-0.7,0.199999973],
    [-0.49999997,-0.7,0.6],
    [-0.0499999821,-0.7,0.75],
    [-0.599999964,-0.7,0.199999973],
    [-0.674999952,-0.7,-1.49011612E-08],
    [-0.0499999821,-0.7,0.6],
    [0.0500000045,-0.7,0.75],
    [-0.49999997,-0.7,0.400000036],
    [-0.599999964,-0.7,-1.49011612E-08],
    [-0.674999952,-0.7,-0.149999991],
    [-0.599999964,-0.7,-0.149999991],
    [0.0500000045,-0.7,0.6],
    [0.275,-0.7,0.75],
    [0.275,-0.7,0.6],
    [-0.0499999821,-0.7,0.400000036],
    [-0.49999997,-0.7,0.199999973],
    [-0.49999997,-0.7,-1.49011612E-08],
    [-0.49999997,-0.7,-0.149999991],
    [0.0500000045,-0.7,0.400000036],
    [0.275,-0.7,0.400000036],
    [-0.0499999821,-0.7,0.199999973],
    [-0.0499999821,-0.7,-1.49011612E-08],
    [-0.0499999821,-0.7,-0.149999991],
    [0.0500000045,-0.7,0.199999973],
    [0.275,-0.7,0.199999973],
    [0.0500000045,-0.7,-1.49011612E-08],
    [0.0500000045,-0.7,-0.149999991],
    [0.275,-0.7,-1.49011612E-08],
    [0.275,-0.7,-0.149999991],
    [0.275,-0.6,-0.199999988],
    [0.275,-0.674999952,-0.199999988],
    [0.0500000045,-0.674999952,-0.199999988],
    [0.0500000045,-0.6,-0.199999988],
    [0.275,-0.150000021,-0.199999988],
    [-0.0499999821,-0.674999952,-0.199999988],
    [0.0500000045,-0.150000021,-0.199999988],
    [0.275,-0.04999999,-0.199999988],
    [-0.0499999821,-0.6,-0.199999988],
    [-0.49999997,-0.674999952,-0.199999988],
    [0.0500000045,-0.04999999,-0.199999988],
    [0.275,0.0500000045,-0.199999988],
    [-0.0499999821,-0.150000021,-0.199999988],
    [-0.49999997,-0.6,-0.199999988],
    [-0.599999964,-0.674999952,-0.199999988],
    [0.0500000045,0.0500000045,-0.199999988],
    [0.275,0.275,-0.199999988],
    [0.0500000045,0.275,-0.199999988],
    [-0.0499999821,-0.04999999,-0.199999988],
    [-0.0499999821,0.0500000045,-0.199999988],
    [-0.0499999821,0.275,-0.199999988],
    [-0.49999997,-0.150000021,-0.199999988],
    [-0.599999964,-0.6,-0.199999988],
    [-0.674999952,-0.674999952,-0.199999988],
    [-0.674999952,-0.6,-0.199999988],
    [-0.599999964,-0.150000021,-0.199999988],
    [-0.674999952,-0.150000021,-0.199999988],
    [-0.49999997,-0.04999999,-0.199999988],
    [-0.49999997,0.0500000045,-0.199999988],
    [-0.49999997,0.275,-0.199999988],
    [-0.599999964,-0.04999999,-0.199999988],
    [-0.674999952,-0.04999999,-0.199999988],
    [-0.599999964,0.0500000045,-0.199999988],
    [-0.599999964,0.275,-0.199999988],
    [-0.674999952,0.0500000045,-0.199999988],
    [-0.674999952,0.275,-0.199999988],
    [0.300000042,-0.674999952,-0.149999991],
    [0.29267773,-0.6926777,-0.149999991],
    [0.2894338,-0.689433753,-0.1788675],
    [0.29267773,-0.674999952,-0.18535535],
    [0.275,-0.674999952,-0.199999988],
    [0.29267773,-0.674999952,-0.18535535],
    [0.2894338,-0.689433753,-0.1788675],
    [0.275,-0.6926777,-0.18535535],
    [0.275,-0.7,-0.149999991],
    [0.275,-0.6926777,-0.18535535],
    [0.2894338,-0.689433753,-0.1788675],
    [0.29267773,-0.6926777,-0.149999991],
    [0.300000042,-0.674999952,0.75],
    [0.29267773,-0.674999952,0.7853554],
    [0.2894338,-0.689433753,0.778867543],
    [0.29267773,-0.6926777,0.75],
    [0.275,-0.7,0.75],
    [0.29267773,-0.6926777,0.75],
    [0.2894338,-0.689433753,0.778867543],
    [0.275,-0.6926777,0.7853554],
    [0.275,-0.674999952,0.8],
    [0.275,-0.6926777,0.7853554],
    [0.2894338,-0.689433753,0.778867543],
    [0.29267773,-0.674999952,0.7853554],
    [0.300000042,0.275,-0.149999991],
    [0.29267773,0.275,-0.18535535],
    [0.2894338,0.289433777,-0.1788675],
    [0.29267773,0.2926777,-0.149999991],
    [0.275,0.3,-0.149999991],
    [0.29267773,0.2926777,-0.149999991],
    [0.2894338,0.289433777,-0.1788675],
    [0.275,0.2926777,-0.18535535],
    [0.275,0.275,-0.199999988],
    [0.275,0.2926777,-0.18535535],
    [0.2894338,0.289433777,-0.1788675],
    [0.29267773,0.275,-0.18535535],
    [0.300000042,0.275,0.75],
    [0.29267773,0.2926777,0.75],
    [0.2894338,0.289433777,0.778867543],
    [0.29267773,0.275,0.7853554],
    [0.275,0.275,0.8],
    [0.29267773,0.275,0.7853554],
    [0.2894338,0.289433777,0.778867543],
    [0.275,0.2926777,0.7853554],
    [0.275,0.3,0.75],
    [0.275,0.2926777,0.7853554],
    [0.2894338,0.289433777,0.778867543],
    [0.29267773,0.2926777,0.75],
    [-0.6999999,-0.674999952,-0.149999991],
    [-0.6926776,-0.674999952,-0.18535535],
    [-0.6894337,-0.689433753,-0.1788675],
    [-0.6926776,-0.6926777,-0.149999991],
    [-0.674999952,-0.7,-0.149999991],
    [-0.6926776,-0.6926777,-0.149999991],
    [-0.6894337,-0.689433753,-0.1788675],
    [-0.674999952,-0.6926777,-0.18535535],
    [-0.674999952,-0.674999952,-0.199999988],
    [-0.674999952,-0.6926777,-0.18535535],
    [-0.6894337,-0.689433753,-0.1788675],
    [-0.6926776,-0.674999952,-0.18535535],
    [-0.6999999,-0.674999952,0.75],
    [-0.6926776,-0.6926777,0.75],
    [-0.6894337,-0.689433753,0.778867543],
    [-0.6926776,-0.674999952,0.7853554],
    [-0.674999952,-0.674999952,0.8],
    [-0.6926776,-0.674999952,0.7853554],
    [-0.6894337,-0.689433753,0.778867543],
    [-0.674999952,-0.6926777,0.7853554],
    [-0.674999952,-0.7,0.75],
    [-0.674999952,-0.6926777,0.7853554],
    [-0.6894337,-0.689433753,0.778867543],
    [-0.6926776,-0.6926777,0.75],
    [-0.674999952,0.3,-0.149999991],
    [-0.674999952,0.2926777,-0.18535535],
    [-0.6894337,0.289433777,-0.1788675],
    [-0.6926776,0.2926777,-0.149999991],
    [-0.6999999,0.275,-0.149999991],
    [-0.6926776,0.2926777,-0.149999991],
    [-0.6894337,0.289433777,-0.1788675],
    [-0.6926776,0.275,-0.18535535],
    [-0.674999952,0.275,-0.199999988],
    [-0.6926776,0.275,-0.18535535],
    [-0.6894337,0.289433777,-0.1788675],
    [-0.674999952,0.2926777,-0.18535535],
    [-0.674999952,0.3,0.75],
    [-0.6926776,0.2926777,0.75],
    [-0.6894337,0.289433777,0.778867543],
    [-0.674999952,0.2926777,0.7853554],
    [-0.674999952,0.275,0.8],
    [-0.674999952,0.2926777,0.7853554],
    [-0.6894337,0.289433777,0.778867543],
    [-0.6926776,0.275,0.7853554],
    [-0.6999999,0.275,0.75],
    [-0.6926776,0.275,0.7853554],
    [-0.6894337,0.289433777,0.778867543],
    [-0.6926776,0.2926777,0.75],
    [0.275,-0.6,-0.199999988],
    [0.29267773,-0.674999952,-0.18535535],
    [0.275,-0.674999952,-0.199999988],
    [0.29267773,-0.6,-0.18535535],
    [0.275,-0.150000021,-0.199999988],
    [0.29267773,-0.150000021,-0.18535535],
    [0.275,-0.04999999,-0.199999988],
    [0.29267773,-0.04999999,-0.18535535],
    [0.275,0.0500000045,-0.199999988],
    [0.29267773,0.0500000045,-0.18535535],
    [0.275,0.275,-0.199999988],
    [0.29267773,0.275,-0.18535535],
    [0.29267773,-0.6,-0.18535535],
    [0.300000042,-0.674999952,-0.149999991],
    [0.29267773,-0.674999952,-0.18535535],
    [0.300000042,-0.6,-0.149999991],
    [0.29267773,-0.150000021,-0.18535535],
    [0.300000042,-0.150000021,-0.149999991],
    [0.29267773,-0.04999999,-0.18535535],
    [0.300000042,-0.04999999,-0.149999991],
    [0.29267773,0.0500000045,-0.18535535],
    [0.300000042,0.0500000045,-0.149999991],
    [0.29267773,0.275,-0.18535535],
    [0.300000042,0.275,-0.149999991],
    [0.275,-0.7,0.6],
    [0.29267773,-0.6926777,0.75],
    [0.275,-0.7,0.75],
    [0.29267773,-0.6926777,0.6],
    [0.275,-0.7,0.400000036],
    [0.29267773,-0.6926777,0.400000036],
    [0.275,-0.7,0.199999973],
    [0.29267773,-0.6926777,0.199999973],
    [0.275,-0.7,-1.49011612E-08],
    [0.29267773,-0.6926777,-1.49011612E-08],
    [0.275,-0.7,-0.149999991],
    [0.29267773,-0.6926777,-0.149999991],
    [0.29267773,-0.6926777,0.6],
    [0.300000042,-0.674999952,0.75],
    [0.29267773,-0.6926777,0.75],
    [0.300000042,-0.674999952,0.6],
    [0.29267773,-0.6926777,0.400000036],
    [0.300000042,-0.674999952,0.400000036],
    [0.29267773,-0.6926777,0.199999973],
    [0.300000042,-0.674999952,0.199999973],
    [0.29267773,-0.6926777,-1.49011612E-08],
    [0.300000042,-0.674999952,-1.49011612E-08],
    [0.29267773,-0.6926777,-0.149999991],
    [0.300000042,-0.674999952,-0.149999991],
    [0.275,0.0500000045,0.8],
    [0.29267773,0.275,0.7853554],
    [0.275,0.275,0.8],
    [0.29267773,0.0500000045,0.7853554],
    [0.275,-0.04999999,0.8],
    [0.29267773,-0.04999999,0.7853554],
    [0.275,-0.150000021,0.8],
    [0.29267773,-0.150000021,0.7853554],
    [0.275,-0.6,0.8],
    [0.29267773,-0.6,0.7853554],
    [0.275,-0.674999952,0.8],
    [0.29267773,-0.674999952,0.7853554],
    [0.29267773,0.0500000045,0.7853554],
    [0.300000042,0.275,0.75],
    [0.29267773,0.275,0.7853554],
    [0.300000042,0.0500000045,0.75],
    [0.29267773,-0.04999999,0.7853554],
    [0.300000042,-0.04999999,0.75],
    [0.29267773,-0.150000021,0.7853554],
    [0.300000042,-0.150000021,0.75],
    [0.29267773,-0.6,0.7853554],
    [0.300000042,-0.6,0.75],
    [0.29267773,-0.674999952,0.7853554],
    [0.300000042,-0.674999952,0.75],
    [0.275,0.3,-1.49011612E-08],
    [0.29267773,0.2926777,-0.149999991],
    [0.275,0.3,-0.149999991],
    [0.29267773,0.2926777,-1.49011612E-08],
    [0.275,0.3,0.199999973],
    [0.29267773,0.2926777,0.199999973],
    [0.275,0.3,0.400000036],
    [0.29267773,0.2926777,0.400000036],
    [0.275,0.3,0.6],
    [0.29267773,0.2926777,0.6],
    [0.275,0.3,0.75],
    [0.29267773,0.2926777,0.75],
    [0.29267773,0.2926777,-1.49011612E-08],
    [0.300000042,0.275,-0.149999991],
    [0.29267773,0.2926777,-0.149999991],
    [0.300000042,0.275,-1.49011612E-08],
    [0.29267773,0.2926777,0.199999973],
    [0.300000042,0.275,0.199999973],
    [0.29267773,0.2926777,0.400000036],
    [0.300000042,0.275,0.400000036],
    [0.29267773,0.2926777,0.6],
    [0.300000042,0.275,0.6],
    [0.29267773,0.2926777,0.75],
    [0.300000042,0.275,0.75],
    [0.0500000045,0.275,-0.199999988],
    [0.275,0.2926777,-0.18535535],
    [0.275,0.275,-0.199999988],
    [0.0500000045,0.2926777,-0.18535535],
    [-0.0499999821,0.275,-0.199999988],
    [-0.0499999821,0.2926777,-0.18535535],
    [-0.49999997,0.275,-0.199999988],
    [-0.49999997,0.2926777,-0.18535535],
    [-0.599999964,0.275,-0.199999988],
    [-0.599999964,0.2926777,-0.18535535],
    [-0.674999952,0.275,-0.199999988],
    [-0.674999952,0.2926777,-0.18535535],
    [0.0500000045,0.2926777,-0.18535535],
    [0.275,0.3,-0.149999991],
    [0.275,0.2926777,-0.18535535],
    [0.0500000045,0.3,-0.149999991],
    [-0.0499999821,0.2926777,-0.18535535],
    [-0.0499999821,0.3,-0.149999991],
    [-0.49999997,0.2926777,-0.18535535],
    [-0.49999997,0.3,-0.149999991],
    [-0.599999964,0.2926777,-0.18535535],
    [-0.599999964,0.3,-0.149999991],
    [-0.674999952,0.2926777,-0.18535535],
    [-0.674999952,0.3,-0.149999991],
    [-0.599999964,0.275,0.8],
    [-0.674999952,0.2926777,0.7853554],
    [-0.674999952,0.275,0.8],
    [-0.599999964,0.2926777,0.7853554],
    [-0.49999997,0.275,0.8],
    [-0.49999997,0.2926777,0.7853554],
    [-0.0499999821,0.275,0.8],
    [-0.0499999821,0.2926777,0.7853554],
    [0.0500000045,0.275,0.8],
    [0.0500000045,0.2926777,0.7853554],
    [0.275,0.275,0.8],
    [0.275,0.2926777,0.7853554],
    [-0.599999964,0.2926777,0.7853554],
    [-0.674999952,0.3,0.75],
    [-0.674999952,0.2926777,0.7853554],
    [-0.599999964,0.3,0.75],
    [-0.49999997,0.2926777,0.7853554],
    [-0.49999997,0.3,0.75],
    [-0.0499999821,0.2926777,0.7853554],
    [-0.0499999821,0.3,0.75],
    [0.0500000045,0.2926777,0.7853554],
    [0.0500000045,0.3,0.75],
    [0.275,0.2926777,0.7853554],
    [0.275,0.3,0.75],
    [-0.6999999,0.275,-1.49011612E-08],
    [-0.6926776,0.2926777,-0.149999991],
    [-0.6999999,0.275,-0.149999991],
    [-0.6926776,0.2926777,-1.49011612E-08],
    [-0.6999999,0.275,0.199999973],
    [-0.6926776,0.2926777,0.199999973],
    [-0.6999999,0.275,0.400000036],
    [-0.6926776,0.2926777,0.400000036],
    [-0.6999999,0.275,0.6],
    [-0.6926776,0.2926777,0.6],
    [-0.6999999,0.275,0.75],
    [-0.6926776,0.2926777,0.75],
    [-0.6926776,0.2926777,-1.49011612E-08],
    [-0.674999952,0.3,-0.149999991],
    [-0.6926776,0.2926777,-0.149999991],
    [-0.674999952,0.3,-1.49011612E-08],
    [-0.6926776,0.2926777,0.199999973],
    [-0.674999952,0.3,0.199999973],
    [-0.6926776,0.2926777,0.400000036],
    [-0.674999952,0.3,0.400000036],
    [-0.6926776,0.2926777,0.6],
    [-0.674999952,0.3,0.6],
    [-0.6926776,0.2926777,0.75],
    [-0.674999952,0.3,0.75],
    [-0.674999952,0.0500000045,-0.199999988],
    [-0.6926776,0.275,-0.18535535],
    [-0.674999952,0.275,-0.199999988],
    [-0.6926776,0.0500000045,-0.18535535],
    [-0.674999952,-0.04999999,-0.199999988],
    [-0.6926776,-0.04999999,-0.18535535],
    [-0.674999952,-0.150000021,-0.199999988],
    [-0.6926776,-0.150000021,-0.18535535],
    [-0.674999952,-0.6,-0.199999988],
    [-0.6926776,-0.6,-0.18535535],
    [-0.674999952,-0.674999952,-0.199999988],
    [-0.6926776,-0.674999952,-0.18535535],
    [-0.6926776,0.0500000045,-0.18535535],
    [-0.6999999,0.275,-0.149999991],
    [-0.6926776,0.275,-0.18535535],
    [-0.6999999,0.0500000045,-0.149999991],
    [-0.6926776,-0.04999999,-0.18535535],
    [-0.6999999,-0.04999999,-0.149999991],
    [-0.6926776,-0.150000021,-0.18535535],
    [-0.6999999,-0.150000021,-0.149999991],
    [-0.6926776,-0.6,-0.18535535],
    [-0.6999999,-0.6,-0.149999991],
    [-0.6926776,-0.674999952,-0.18535535],
    [-0.6999999,-0.674999952,-0.149999991],
    [-0.674999952,-0.6,0.8],
    [-0.6926776,-0.674999952,0.7853554],
    [-0.674999952,-0.674999952,0.8],
    [-0.6926776,-0.6,0.7853554],
    [-0.674999952,-0.150000021,0.8],
    [-0.6926776,-0.150000021,0.7853554],
    [-0.674999952,-0.04999999,0.8],
    [-0.6926776,-0.04999999,0.7853554],
    [-0.674999952,0.0500000045,0.8],
    [-0.6926776,0.0500000045,0.7853554],
    [-0.674999952,0.275,0.8],
    [-0.6926776,0.275,0.7853554],
    [-0.6926776,-0.6,0.7853554],
    [-0.6999999,-0.674999952,0.75],
    [-0.6926776,-0.674999952,0.7853554],
    [-0.6999999,-0.6,0.75],
    [-0.6926776,-0.150000021,0.7853554],
    [-0.6999999,-0.150000021,0.75],
    [-0.6926776,-0.04999999,0.7853554],
    [-0.6999999,-0.04999999,0.75],
    [-0.6926776,0.0500000045,0.7853554],
    [-0.6999999,0.0500000045,0.75],
    [-0.6926776,0.275,0.7853554],
    [-0.6999999,0.275,0.75],
    [-0.674999952,-0.7,-1.49011612E-08],
    [-0.6926776,-0.6926777,-0.149999991],
    [-0.674999952,-0.7,-0.149999991],
    [-0.6926776,-0.6926777,-1.49011612E-08],
    [-0.674999952,-0.7,0.199999973],
    [-0.6926776,-0.6926777,0.199999973],
    [-0.674999952,-0.7,0.400000036],
    [-0.6926776,-0.6926777,0.400000036],
    [-0.674999952,-0.7,0.6],
    [-0.6926776,-0.6926777,0.6],
    [-0.674999952,-0.7,0.75],
    [-0.6926776,-0.6926777,0.75],
    [-0.6926776,-0.6926777,-1.49011612E-08],
    [-0.6999999,-0.674999952,-0.149999991],
    [-0.6926776,-0.6926777,-0.149999991],
    [-0.6999999,-0.674999952,-1.49011612E-08],
    [-0.6926776,-0.6926777,0.199999973],
    [-0.6999999,-0.674999952,0.199999973],
    [-0.6926776,-0.6926777,0.400000036],
    [-0.6999999,-0.674999952,0.400000036],
    [-0.6926776,-0.6926777,0.6],
    [-0.6999999,-0.674999952,0.6],
    [-0.6926776,-0.6926777,0.75],
    [-0.6999999,-0.674999952,0.75],
    [-0.599999964,-0.674999952,-0.199999988],
    [-0.674999952,-0.6926777,-0.18535535],
    [-0.674999952,-0.674999952,-0.199999988],
    [-0.599999964,-0.6926777,-0.18535535],
    [-0.49999997,-0.674999952,-0.199999988],
    [-0.49999997,-0.6926777,-0.18535535],
    [-0.0499999821,-0.674999952,-0.199999988],
    [-0.0499999821,-0.6926777,-0.18535535],
    [0.0500000045,-0.674999952,-0.199999988],
    [0.0500000045,-0.6926777,-0.18535535],
    [0.275,-0.674999952,-0.199999988],
    [0.275,-0.6926777,-0.18535535],
    [-0.599999964,-0.6926777,-0.18535535],
    [-0.674999952,-0.7,-0.149999991],
    [-0.674999952,-0.6926777,-0.18535535],
    [-0.599999964,-0.7,-0.149999991],
    [-0.49999997,-0.6926777,-0.18535535],
    [-0.49999997,-0.7,-0.149999991],
    [-0.0499999821,-0.6926777,-0.18535535],
    [-0.0499999821,-0.7,-0.149999991],
    [0.0500000045,-0.6926777,-0.18535535],
    [0.0500000045,-0.7,-0.149999991],
    [0.275,-0.6926777,-0.18535535],
    [0.275,-0.7,-0.149999991],
    [0.0500000045,-0.674999952,0.8],
    [0.275,-0.6926777,0.7853554],
    [0.275,-0.674999952,0.8],
    [0.0500000045,-0.6926777,0.7853554],
    [-0.0499999821,-0.674999952,0.8],
    [-0.0499999821,-0.6926777,0.7853554],
    [-0.49999997,-0.674999952,0.8],
    [-0.49999997,-0.6926777,0.7853554],
    [-0.599999964,-0.674999952,0.8],
    [-0.599999964,-0.6926777,0.7853554],
    [-0.674999952,-0.674999952,0.8],
    [-0.674999952,-0.6926777,0.7853554],
    [0.0500000045,-0.6926777,0.7853554],
    [0.275,-0.7,0.75],
    [0.275,-0.6926777,0.7853554],
    [0.0500000045,-0.7,0.75],
    [-0.0499999821,-0.6926777,0.7853554],
    [-0.0499999821,-0.7,0.75],
    [-0.49999997,-0.6926777,0.7853554],
    [-0.49999997,-0.7,0.75],
    [-0.599999964,-0.6926777,0.7853554],
    [-0.599999964,-0.7,0.75],
    [-0.674999952,-0.6926777,0.7853554],
    [-0.674999952,-0.7,0.75]
  ],
  ""Triangles"": [
    0,
    1,
    2,
    0,
    2,
    3,
    4,
    0,
    3,
    3,
    2,
    5,
    4,
    3,
    6,
    7,
    4,
    6,
    3,
    5,
    8,
    6,
    3,
    8,
    8,
    5,
    9,
    7,
    6,
    10,
    11,
    7,
    10,
    8,
    9,
    12,
    12,
    9,
    13,
    6,
    8,
    14,
    10,
    6,
    14,
    14,
    8,
    12,
    11,
    10,
    15,
    16,
    11,
    15,
    16,
    15,
    17,
    12,
    13,
    18,
    18,
    13,
    19,
    18,
    19,
    20,
    14,
    12,
    21,
    21,
    12,
    18,
    10,
    14,
    22,
    15,
    10,
    22,
    22,
    14,
    21,
    17,
    15,
    23,
    15,
    22,
    23,
    17,
    23,
    24,
    25,
    18,
    20,
    21,
    18,
    25,
    25,
    20,
    26,
    22,
    21,
    27,
    23,
    22,
    27,
    27,
    21,
    25,
    24,
    23,
    28,
    23,
    27,
    28,
    24,
    28,
    29,
    30,
    25,
    26,
    27,
    25,
    30,
    28,
    27,
    30,
    30,
    26,
    31,
    29,
    28,
    32,
    28,
    30,
    32,
    32,
    30,
    31,
    29,
    32,
    33,
    32,
    31,
    34,
    33,
    32,
    34,
    33,
    34,
    35,
    36,
    37,
    38,
    36,
    38,
    39,
    40,
    36,
    39,
    39,
    38,
    41,
    40,
    39,
    42,
    43,
    40,
    42,
    39,
    41,
    44,
    42,
    39,
    44,
    44,
    41,
    45,
    43,
    42,
    46,
    47,
    43,
    46,
    42,
    44,
    48,
    46,
    42,
    48,
    44,
    45,
    49,
    48,
    44,
    49,
    49,
    45,
    50,
    47,
    46,
    51,
    52,
    47,
    51,
    52,
    51,
    53,
    46,
    48,
    54,
    51,
    46,
    54,
    53,
    51,
    55,
    51,
    54,
    55,
    53,
    55,
    56,
    48,
    49,
    57,
    54,
    48,
    57,
    49,
    50,
    58,
    57,
    49,
    58,
    58,
    50,
    59,
    58,
    59,
    60,
    61,
    58,
    60,
    57,
    58,
    61,
    61,
    60,
    62,
    54,
    57,
    63,
    55,
    54,
    63,
    63,
    57,
    61,
    56,
    55,
    64,
    55,
    63,
    64,
    56,
    64,
    65,
    66,
    61,
    62,
    63,
    61,
    66,
    64,
    63,
    66,
    66,
    62,
    67,
    65,
    64,
    68,
    64,
    66,
    68,
    68,
    66,
    67,
    65,
    68,
    69,
    68,
    67,
    70,
    69,
    68,
    70,
    69,
    70,
    71,
    72,
    73,
    74,
    72,
    74,
    75,
    76,
    72,
    75,
    75,
    74,
    77,
    76,
    75,
    78,
    79,
    76,
    78,
    75,
    77,
    80,
    78,
    75,
    80,
    80,
    77,
    81,
    79,
    78,
    82,
    83,
    79,
    82,
    78,
    80,
    84,
    82,
    78,
    84,
    80,
    81,
    85,
    84,
    80,
    85,
    85,
    81,
    86,
    83,
    82,
    87,
    88,
    83,
    87,
    88,
    87,
    89,
    82,
    84,
    90,
    87,
    82,
    90,
    89,
    87,
    91,
    87,
    90,
    91,
    89,
    91,
    92,
    84,
    85,
    93,
    90,
    84,
    93,
    85,
    86,
    94,
    93,
    85,
    94,
    94,
    86,
    95,
    94,
    95,
    96,
    97,
    94,
    96,
    93,
    94,
    97,
    97,
    96,
    98,
    90,
    93,
    99,
    91,
    90,
    99,
    99,
    93,
    97,
    92,
    91,
    100,
    91,
    99,
    100,
    92,
    100,
    101,
    102,
    97,
    98,
    99,
    97,
    102,
    100,
    99,
    102,
    102,
    98,
    103,
    101,
    100,
    104,
    100,
    102,
    104,
    104,
    102,
    103,
    101,
    104,
    105,
    104,
    103,
    106,
    105,
    104,
    106,
    105,
    106,
    107,
    108,
    109,
    110,
    108,
    110,
    111,
    112,
    108,
    111,
    111,
    110,
    113,
    112,
    111,
    114,
    115,
    112,
    114,
    111,
    113,
    116,
    114,
    111,
    116,
    116,
    113,
    117,
    115,
    114,
    118,
    119,
    115,
    118,
    116,
    117,
    120,
    120,
    117,
    121,
    114,
    116,
    122,
    118,
    114,
    122,
    122,
    116,
    120,
    119,
    118,
    123,
    124,
    119,
    123,
    124,
    123,
    125,
    120,
    121,
    126,
    126,
    121,
    127,
    126,
    127,
    128,
    122,
    120,
    129,
    129,
    120,
    126,
    118,
    122,
    130,
    123,
    118,
    130,
    130,
    122,
    129,
    125,
    123,
    131,
    123,
    130,
    131,
    125,
    131,
    132,
    133,
    126,
    128,
    129,
    126,
    133,
    133,
    128,
    134,
    130,
    129,
    135,
    131,
    130,
    135,
    135,
    129,
    133,
    132,
    131,
    136,
    131,
    135,
    136,
    132,
    136,
    137,
    138,
    133,
    134,
    135,
    133,
    138,
    136,
    135,
    138,
    138,
    134,
    139,
    137,
    136,
    140,
    136,
    138,
    140,
    140,
    138,
    139,
    137,
    140,
    141,
    140,
    139,
    142,
    141,
    140,
    142,
    141,
    142,
    143,
    144,
    145,
    146,
    144,
    147,
    145,
    148,
    147,
    144,
    147,
    149,
    145,
    148,
    150,
    147,
    151,
    150,
    148,
    147,
    152,
    149,
    150,
    152,
    147,
    152,
    153,
    149,
    151,
    154,
    150,
    155,
    154,
    151,
    152,
    156,
    153,
    156,
    157,
    153,
    150,
    158,
    152,
    154,
    158,
    150,
    158,
    156,
    152,
    155,
    159,
    154,
    160,
    159,
    155,
    160,
    161,
    159,
    156,
    162,
    157,
    162,
    163,
    157,
    162,
    164,
    163,
    158,
    165,
    156,
    165,
    162,
    156,
    154,
    166,
    158,
    159,
    166,
    154,
    166,
    165,
    158,
    161,
    167,
    159,
    159,
    167,
    166,
    161,
    168,
    167,
    169,
    164,
    162,
    165,
    169,
    162,
    169,
    170,
    164,
    166,
    171,
    165,
    167,
    171,
    166,
    171,
    169,
    165,
    168,
    172,
    167,
    167,
    172,
    171,
    168,
    173,
    172,
    174,
    170,
    169,
    171,
    174,
    169,
    172,
    174,
    171,
    174,
    175,
    170,
    173,
    176,
    172,
    172,
    176,
    174,
    176,
    175,
    174,
    173,
    177,
    176,
    176,
    178,
    175,
    177,
    178,
    176,
    177,
    179,
    178,
    180,
    181,
    182,
    180,
    182,
    183,
    184,
    180,
    183,
    183,
    182,
    185,
    184,
    183,
    186,
    187,
    184,
    186,
    183,
    185,
    188,
    186,
    183,
    188,
    188,
    185,
    189,
    187,
    186,
    190,
    191,
    187,
    190,
    186,
    188,
    192,
    190,
    186,
    192,
    188,
    189,
    193,
    192,
    188,
    193,
    193,
    189,
    194,
    191,
    190,
    195,
    196,
    191,
    195,
    196,
    195,
    197,
    190,
    192,
    198,
    195,
    190,
    198,
    197,
    195,
    199,
    195,
    198,
    199,
    197,
    199,
    200,
    192,
    193,
    201,
    198,
    192,
    201,
    193,
    194,
    202,
    201,
    193,
    202,
    202,
    194,
    203,
    202,
    203,
    204,
    205,
    202,
    204,
    201,
    202,
    205,
    205,
    204,
    206,
    198,
    201,
    207,
    199,
    198,
    207,
    207,
    201,
    205,
    200,
    199,
    208,
    199,
    207,
    208,
    200,
    208,
    209,
    210,
    205,
    206,
    207,
    205,
    210,
    208,
    207,
    210,
    210,
    206,
    211,
    209,
    208,
    212,
    208,
    210,
    212,
    212,
    210,
    211,
    209,
    212,
    213,
    212,
    211,
    214,
    213,
    212,
    214,
    213,
    214,
    215,
    216,
    217,
    218,
    216,
    218,
    219,
    220,
    221,
    222,
    220,
    222,
    223,
    224,
    225,
    226,
    224,
    226,
    227,
    228,
    229,
    230,
    228,
    230,
    231,
    232,
    233,
    234,
    232,
    234,
    235,
    236,
    237,
    238,
    236,
    238,
    239,
    240,
    241,
    242,
    240,
    242,
    243,
    244,
    245,
    246,
    244,
    246,
    247,
    248,
    249,
    250,
    248,
    250,
    251,
    252,
    253,
    254,
    252,
    254,
    255,
    256,
    257,
    258,
    256,
    258,
    259,
    260,
    261,
    262,
    260,
    262,
    263,
    264,
    265,
    266,
    264,
    266,
    267,
    268,
    269,
    270,
    268,
    270,
    271,
    272,
    273,
    274,
    272,
    274,
    275,
    276,
    277,
    278,
    276,
    278,
    279,
    280,
    281,
    282,
    280,
    282,
    283,
    284,
    285,
    286,
    284,
    286,
    287,
    288,
    289,
    290,
    288,
    290,
    291,
    292,
    293,
    294,
    292,
    294,
    295,
    296,
    297,
    298,
    296,
    298,
    299,
    300,
    301,
    302,
    300,
    302,
    303,
    304,
    305,
    306,
    304,
    306,
    307,
    308,
    309,
    310,
    308,
    310,
    311,
    312,
    313,
    314,
    312,
    315,
    313,
    316,
    315,
    312,
    316,
    317,
    315,
    318,
    317,
    316,
    318,
    319,
    317,
    320,
    319,
    318,
    320,
    321,
    319,
    322,
    321,
    320,
    322,
    323,
    321,
    324,
    325,
    326,
    324,
    327,
    325,
    328,
    327,
    324,
    328,
    329,
    327,
    330,
    329,
    328,
    330,
    331,
    329,
    332,
    331,
    330,
    332,
    333,
    331,
    334,
    333,
    332,
    334,
    335,
    333,
    336,
    337,
    338,
    336,
    339,
    337,
    340,
    339,
    336,
    340,
    341,
    339,
    342,
    341,
    340,
    342,
    343,
    341,
    344,
    343,
    342,
    344,
    345,
    343,
    346,
    345,
    344,
    346,
    347,
    345,
    348,
    349,
    350,
    348,
    351,
    349,
    352,
    351,
    348,
    352,
    353,
    351,
    354,
    353,
    352,
    354,
    355,
    353,
    356,
    355,
    354,
    356,
    357,
    355,
    358,
    357,
    356,
    358,
    359,
    357,
    360,
    361,
    362,
    360,
    363,
    361,
    364,
    363,
    360,
    364,
    365,
    363,
    366,
    365,
    364,
    366,
    367,
    365,
    368,
    367,
    366,
    368,
    369,
    367,
    370,
    369,
    368,
    370,
    371,
    369,
    372,
    373,
    374,
    372,
    375,
    373,
    376,
    375,
    372,
    376,
    377,
    375,
    378,
    377,
    376,
    378,
    379,
    377,
    380,
    379,
    378,
    380,
    381,
    379,
    382,
    381,
    380,
    382,
    383,
    381,
    384,
    385,
    386,
    384,
    387,
    385,
    388,
    387,
    384,
    388,
    389,
    387,
    390,
    389,
    388,
    390,
    391,
    389,
    392,
    391,
    390,
    392,
    393,
    391,
    394,
    393,
    392,
    394,
    395,
    393,
    396,
    397,
    398,
    396,
    399,
    397,
    400,
    399,
    396,
    400,
    401,
    399,
    402,
    401,
    400,
    402,
    403,
    401,
    404,
    403,
    402,
    404,
    405,
    403,
    406,
    405,
    404,
    406,
    407,
    405,
    408,
    409,
    410,
    408,
    411,
    409,
    412,
    411,
    408,
    412,
    413,
    411,
    414,
    413,
    412,
    414,
    415,
    413,
    416,
    415,
    414,
    416,
    417,
    415,
    418,
    417,
    416,
    418,
    419,
    417,
    420,
    421,
    422,
    420,
    423,
    421,
    424,
    423,
    420,
    424,
    425,
    423,
    426,
    425,
    424,
    426,
    427,
    425,
    428,
    427,
    426,
    428,
    429,
    427,
    430,
    429,
    428,
    430,
    431,
    429,
    432,
    433,
    434,
    432,
    435,
    433,
    436,
    435,
    432,
    436,
    437,
    435,
    438,
    437,
    436,
    438,
    439,
    437,
    440,
    439,
    438,
    440,
    441,
    439,
    442,
    441,
    440,
    442,
    443,
    441,
    444,
    445,
    446,
    444,
    447,
    445,
    448,
    447,
    444,
    448,
    449,
    447,
    450,
    449,
    448,
    450,
    451,
    449,
    452,
    451,
    450,
    452,
    453,
    451,
    454,
    453,
    452,
    454,
    455,
    453,
    456,
    457,
    458,
    456,
    459,
    457,
    460,
    459,
    456,
    460,
    461,
    459,
    462,
    461,
    460,
    462,
    463,
    461,
    464,
    463,
    462,
    464,
    465,
    463,
    466,
    465,
    464,
    466,
    467,
    465,
    468,
    469,
    470,
    468,
    471,
    469,
    472,
    471,
    468,
    472,
    473,
    471,
    474,
    473,
    472,
    474,
    475,
    473,
    476,
    475,
    474,
    476,
    477,
    475,
    478,
    477,
    476,
    478,
    479,
    477,
    480,
    481,
    482,
    480,
    483,
    481,
    484,
    483,
    480,
    484,
    485,
    483,
    486,
    485,
    484,
    486,
    487,
    485,
    488,
    487,
    486,
    488,
    489,
    487,
    490,
    489,
    488,
    490,
    491,
    489,
    492,
    493,
    494,
    492,
    495,
    493,
    496,
    495,
    492,
    496,
    497,
    495,
    498,
    497,
    496,
    498,
    499,
    497,
    500,
    499,
    498,
    500,
    501,
    499,
    502,
    501,
    500,
    502,
    503,
    501,
    504,
    505,
    506,
    504,
    507,
    505,
    508,
    507,
    504,
    508,
    509,
    507,
    510,
    509,
    508,
    510,
    511,
    509,
    512,
    511,
    510,
    512,
    513,
    511,
    514,
    513,
    512,
    514,
    515,
    513,
    516,
    517,
    518,
    516,
    519,
    517,
    520,
    519,
    516,
    520,
    521,
    519,
    522,
    521,
    520,
    522,
    523,
    521,
    524,
    523,
    522,
    524,
    525,
    523,
    526,
    525,
    524,
    526,
    527,
    525,
    528,
    529,
    530,
    528,
    531,
    529,
    532,
    531,
    528,
    532,
    533,
    531,
    534,
    533,
    532,
    534,
    535,
    533,
    536,
    535,
    534,
    536,
    537,
    535,
    538,
    537,
    536,
    538,
    539,
    537,
    540,
    541,
    542,
    540,
    543,
    541,
    544,
    543,
    540,
    544,
    545,
    543,
    546,
    545,
    544,
    546,
    547,
    545,
    548,
    547,
    546,
    548,
    549,
    547,
    550,
    549,
    548,
    550,
    551,
    549,
    552,
    553,
    554,
    552,
    555,
    553,
    556,
    555,
    552,
    556,
    557,
    555,
    558,
    557,
    556,
    558,
    559,
    557,
    560,
    559,
    558,
    560,
    561,
    559,
    562,
    561,
    560,
    562,
    563,
    561,
    564,
    565,
    566,
    564,
    567,
    565,
    568,
    567,
    564,
    568,
    569,
    567,
    570,
    569,
    568,
    570,
    571,
    569,
    572,
    571,
    570,
    572,
    573,
    571,
    574,
    573,
    572,
    574,
    575,
    573,
    576,
    577,
    578,
    576,
    579,
    577,
    580,
    579,
    576,
    580,
    581,
    579,
    582,
    581,
    580,
    582,
    583,
    581,
    584,
    583,
    582,
    584,
    585,
    583,
    586,
    585,
    584,
    586,
    587,
    585,
    588,
    589,
    590,
    588,
    591,
    589,
    592,
    591,
    588,
    592,
    593,
    591,
    594,
    593,
    592,
    594,
    595,
    593,
    596,
    595,
    594,
    596,
    597,
    595,
    598,
    597,
    596,
    598,
    599,
    597
  ],
  ""Resizer"": {
    ""PivotOffset"": [0.2,0.2,0.2],
    ""ScalingX"": ""SLICE_WITH_ASYMMETRICAL_BORDER"",
    ""BorderXNegative"": 0.25,
    ""BorderXPositive"": 0.75,
    ""ScalingY"": ""SLICE"",
    ""BorderYNegative"": 0.5,
    ""BorderYPositive"": 0.5,
    ""ScalingZ"": ""SCALE"",
    ""BorderZNegative"": 0.5,
    ""BorderZPositive"": 0.5,
    ""StretchCenter"": 0
  }
}

";

        private const string _cubeUpscaledOffsetOutside = @"{
  ""Scale"": [2.0,2.0,2.0],
  ""Vertices"": [
    [-1.0,-0.9,-0.45],
    [-1.0,-0.975,-0.45],
    [-1.0,-0.975,-0.3],
    [-1.0,-0.9,-0.3],
    [-1.0,-0.8,-0.45],
    [-1.0,-0.975,-0.100000024],
    [-1.0,-0.8,-0.3],
    [-1.0,-0.199999988,-0.45],
    [-1.0,-0.9,-0.100000024],
    [-1.0,-0.975,0.100000024],
    [-1.0,-0.199999988,-0.3],
    [-1.0,-0.099999994,-0.45],
    [-1.0,-0.9,0.100000024],
    [-1.0,-0.975,0.3],
    [-1.0,-0.8,-0.100000024],
    [-1.0,-0.099999994,-0.3],
    [-1.0,-0.025000006,-0.45],
    [-1.0,-0.025000006,-0.3],
    [-1.0,-0.9,0.3],
    [-1.0,-0.975,0.45],
    [-1.0,-0.9,0.45],
    [-1.0,-0.8,0.100000024],
    [-1.0,-0.199999988,-0.100000024],
    [-1.0,-0.099999994,-0.100000024],
    [-1.0,-0.025000006,-0.100000024],
    [-1.0,-0.8,0.3],
    [-1.0,-0.8,0.45],
    [-1.0,-0.199999988,0.100000024],
    [-1.0,-0.099999994,0.100000024],
    [-1.0,-0.025000006,0.100000024],
    [-1.0,-0.199999988,0.3],
    [-1.0,-0.199999988,0.45],
    [-1.0,-0.099999994,0.3],
    [-1.0,-0.025000006,0.3],
    [-1.0,-0.099999994,0.45],
    [-1.0,-0.025000006,0.45],
    [-0.9,0.0,-0.45],
    [-0.975,0.0,-0.45],
    [-0.975,0.0,-0.3],
    [-0.9,0.0,-0.3],
    [-0.8,0.0,-0.45],
    [-0.975,0.0,-0.100000024],
    [-0.8,0.0,-0.3],
    [-0.7,0.0,-0.45],
    [-0.9,0.0,-0.100000024],
    [-0.975,0.0,0.100000024],
    [-0.7,0.0,-0.3],
    [-0.099999994,0.0,-0.45],
    [-0.8,0.0,-0.100000024],
    [-0.9,0.0,0.100000024],
    [-0.975,0.0,0.3],
    [-0.099999994,0.0,-0.3],
    [-0.025000006,0.0,-0.45],
    [-0.025000006,0.0,-0.3],
    [-0.7,0.0,-0.100000024],
    [-0.099999994,0.0,-0.100000024],
    [-0.025000006,0.0,-0.100000024],
    [-0.8,0.0,0.100000024],
    [-0.9,0.0,0.3],
    [-0.975,0.0,0.45],
    [-0.9,0.0,0.45],
    [-0.8,0.0,0.3],
    [-0.8,0.0,0.45],
    [-0.7,0.0,0.100000024],
    [-0.099999994,0.0,0.100000024],
    [-0.025000006,0.0,0.100000024],
    [-0.7,0.0,0.3],
    [-0.7,0.0,0.45],
    [-0.099999994,0.0,0.3],
    [-0.025000006,0.0,0.3],
    [-0.099999994,0.0,0.45],
    [-0.025000006,0.0,0.45],
    [-0.975,-0.9,0.5],
    [-0.975,-0.975,0.5],
    [-0.9,-0.975,0.5],
    [-0.9,-0.9,0.5],
    [-0.975,-0.8,0.5],
    [-0.8,-0.975,0.5],
    [-0.9,-0.8,0.5],
    [-0.975,-0.199999988,0.5],
    [-0.8,-0.9,0.5],
    [-0.7,-0.975,0.5],
    [-0.9,-0.199999988,0.5],
    [-0.975,-0.099999994,0.5],
    [-0.8,-0.8,0.5],
    [-0.7,-0.9,0.5],
    [-0.099999994,-0.975,0.5],
    [-0.9,-0.099999994,0.5],
    [-0.975,-0.025000006,0.5],
    [-0.9,-0.025000006,0.5],
    [-0.8,-0.199999988,0.5],
    [-0.8,-0.099999994,0.5],
    [-0.8,-0.025000006,0.5],
    [-0.7,-0.8,0.5],
    [-0.099999994,-0.9,0.5],
    [-0.025000006,-0.975,0.5],
    [-0.025000006,-0.9,0.5],
    [-0.099999994,-0.8,0.5],
    [-0.025000006,-0.8,0.5],
    [-0.7,-0.199999988,0.5],
    [-0.7,-0.099999994,0.5],
    [-0.7,-0.025000006,0.5],
    [-0.099999994,-0.199999988,0.5],
    [-0.025000006,-0.199999988,0.5],
    [-0.099999994,-0.099999994,0.5],
    [-0.099999994,-0.025000006,0.5],
    [-0.025000006,-0.099999994,0.5],
    [-0.025000006,-0.025000006,0.5],
    [0.0,-0.099999994,-0.45],
    [0.0,-0.025000006,-0.45],
    [0.0,-0.025000006,-0.3],
    [0.0,-0.099999994,-0.3],
    [0.0,-0.199999988,-0.45],
    [0.0,-0.025000006,-0.100000024],
    [0.0,-0.199999988,-0.3],
    [0.0,-0.8,-0.45],
    [0.0,-0.099999994,-0.100000024],
    [0.0,-0.025000006,0.100000024],
    [0.0,-0.8,-0.3],
    [0.0,-0.9,-0.45],
    [0.0,-0.099999994,0.100000024],
    [0.0,-0.025000006,0.3],
    [0.0,-0.199999988,-0.100000024],
    [0.0,-0.9,-0.3],
    [0.0,-0.975,-0.45],
    [0.0,-0.975,-0.3],
    [0.0,-0.099999994,0.3],
    [0.0,-0.025000006,0.45],
    [0.0,-0.099999994,0.45],
    [0.0,-0.199999988,0.100000024],
    [0.0,-0.8,-0.100000024],
    [0.0,-0.9,-0.100000024],
    [0.0,-0.975,-0.100000024],
    [0.0,-0.199999988,0.3],
    [0.0,-0.199999988,0.45],
    [0.0,-0.8,0.100000024],
    [0.0,-0.9,0.100000024],
    [0.0,-0.975,0.100000024],
    [0.0,-0.8,0.3],
    [0.0,-0.8,0.45],
    [0.0,-0.9,0.3],
    [0.0,-0.975,0.3],
    [0.0,-0.9,0.45],
    [0.0,-0.975,0.45],
    [-0.975,-1.0,0.3],
    [-0.9,-1.0,0.45],
    [-0.975,-1.0,0.45],
    [-0.9,-1.0,0.3],
    [-0.975,-1.0,0.100000024],
    [-0.8,-1.0,0.45],
    [-0.9,-1.0,0.100000024],
    [-0.975,-1.0,-0.100000024],
    [-0.8,-1.0,0.3],
    [-0.7,-1.0,0.45],
    [-0.9,-1.0,-0.100000024],
    [-0.975,-1.0,-0.3],
    [-0.7,-1.0,0.3],
    [-0.099999994,-1.0,0.45],
    [-0.8,-1.0,0.100000024],
    [-0.9,-1.0,-0.3],
    [-0.975,-1.0,-0.45],
    [-0.9,-1.0,-0.45],
    [-0.099999994,-1.0,0.3],
    [-0.025000006,-1.0,0.45],
    [-0.025000006,-1.0,0.3],
    [-0.7,-1.0,0.100000024],
    [-0.8,-1.0,-0.100000024],
    [-0.8,-1.0,-0.3],
    [-0.8,-1.0,-0.45],
    [-0.099999994,-1.0,0.100000024],
    [-0.025000006,-1.0,0.100000024],
    [-0.7,-1.0,-0.100000024],
    [-0.7,-1.0,-0.3],
    [-0.7,-1.0,-0.45],
    [-0.099999994,-1.0,-0.100000024],
    [-0.025000006,-1.0,-0.100000024],
    [-0.099999994,-1.0,-0.3],
    [-0.099999994,-1.0,-0.45],
    [-0.025000006,-1.0,-0.3],
    [-0.025000006,-1.0,-0.45],
    [-0.025000006,-0.9,-0.5],
    [-0.025000006,-0.975,-0.5],
    [-0.099999994,-0.975,-0.5],
    [-0.099999994,-0.9,-0.5],
    [-0.025000006,-0.8,-0.5],
    [-0.7,-0.975,-0.5],
    [-0.099999994,-0.8,-0.5],
    [-0.025000006,-0.199999988,-0.5],
    [-0.7,-0.9,-0.5],
    [-0.8,-0.975,-0.5],
    [-0.099999994,-0.199999988,-0.5],
    [-0.025000006,-0.099999994,-0.5],
    [-0.7,-0.8,-0.5],
    [-0.8,-0.9,-0.5],
    [-0.9,-0.975,-0.5],
    [-0.099999994,-0.099999994,-0.5],
    [-0.025000006,-0.025000006,-0.5],
    [-0.099999994,-0.025000006,-0.5],
    [-0.7,-0.199999988,-0.5],
    [-0.7,-0.099999994,-0.5],
    [-0.7,-0.025000006,-0.5],
    [-0.8,-0.8,-0.5],
    [-0.9,-0.9,-0.5],
    [-0.975,-0.975,-0.5],
    [-0.975,-0.9,-0.5],
    [-0.9,-0.8,-0.5],
    [-0.975,-0.8,-0.5],
    [-0.8,-0.199999988,-0.5],
    [-0.8,-0.099999994,-0.5],
    [-0.8,-0.025000006,-0.5],
    [-0.9,-0.199999988,-0.5],
    [-0.975,-0.199999988,-0.5],
    [-0.9,-0.099999994,-0.5],
    [-0.9,-0.025000006,-0.5],
    [-0.975,-0.099999994,-0.5],
    [-0.975,-0.025000006,-0.5],
    [0.0,-0.975,-0.45],
    [-0.0073223114,-0.9926777,-0.45],
    [-0.0105662346,-0.989433765,-0.4788675],
    [-0.0073223114,-0.975,-0.485355347],
    [-0.025000006,-0.975,-0.5],
    [-0.0073223114,-0.975,-0.485355347],
    [-0.0105662346,-0.989433765,-0.4788675],
    [-0.025000006,-0.9926777,-0.485355347],
    [-0.025000006,-1.0,-0.45],
    [-0.025000006,-0.9926777,-0.485355347],
    [-0.0105662346,-0.989433765,-0.4788675],
    [-0.0073223114,-0.9926777,-0.45],
    [0.0,-0.975,0.45],
    [-0.0073223114,-0.975,0.485355377],
    [-0.0105662346,-0.989433765,0.478867531],
    [-0.0073223114,-0.9926777,0.45],
    [-0.025000006,-1.0,0.45],
    [-0.0073223114,-0.9926777,0.45],
    [-0.0105662346,-0.989433765,0.478867531],
    [-0.025000006,-0.9926777,0.485355377],
    [-0.025000006,-0.975,0.5],
    [-0.025000006,-0.9926777,0.485355377],
    [-0.0105662346,-0.989433765,0.478867531],
    [-0.0073223114,-0.975,0.485355377],
    [0.0,-0.025000006,-0.45],
    [-0.0073223114,-0.025000006,-0.485355347],
    [-0.0105662346,-0.0105662346,-0.4788675],
    [-0.0073223114,-0.0073223114,-0.45],
    [-0.025000006,0.0,-0.45],
    [-0.0073223114,-0.0073223114,-0.45],
    [-0.0105662346,-0.0105662346,-0.4788675],
    [-0.025000006,-0.0073223114,-0.485355347],
    [-0.025000006,-0.025000006,-0.5],
    [-0.025000006,-0.0073223114,-0.485355347],
    [-0.0105662346,-0.0105662346,-0.4788675],
    [-0.0073223114,-0.025000006,-0.485355347],
    [0.0,-0.025000006,0.45],
    [-0.0073223114,-0.0073223114,0.45],
    [-0.0105662346,-0.0105662346,0.478867531],
    [-0.0073223114,-0.025000006,0.485355377],
    [-0.025000006,-0.025000006,0.5],
    [-0.0073223114,-0.025000006,0.485355377],
    [-0.0105662346,-0.0105662346,0.478867531],
    [-0.025000006,-0.0073223114,0.485355377],
    [-0.025000006,0.0,0.45],
    [-0.025000006,-0.0073223114,0.485355377],
    [-0.0105662346,-0.0105662346,0.478867531],
    [-0.0073223114,-0.0073223114,0.45],
    [-1.0,-0.975,-0.45],
    [-0.9926777,-0.975,-0.485355347],
    [-0.989433765,-0.989433765,-0.4788675],
    [-0.9926777,-0.9926777,-0.45],
    [-0.975,-1.0,-0.45],
    [-0.9926777,-0.9926777,-0.45],
    [-0.989433765,-0.989433765,-0.4788675],
    [-0.975,-0.9926777,-0.485355347],
    [-0.975,-0.975,-0.5],
    [-0.975,-0.9926777,-0.485355347],
    [-0.989433765,-0.989433765,-0.4788675],
    [-0.9926777,-0.975,-0.485355347],
    [-1.0,-0.975,0.45],
    [-0.9926777,-0.9926777,0.45],
    [-0.989433765,-0.989433765,0.478867531],
    [-0.9926777,-0.975,0.485355377],
    [-0.975,-0.975,0.5],
    [-0.9926777,-0.975,0.485355377],
    [-0.989433765,-0.989433765,0.478867531],
    [-0.975,-0.9926777,0.485355377],
    [-0.975,-1.0,0.45],
    [-0.975,-0.9926777,0.485355377],
    [-0.989433765,-0.989433765,0.478867531],
    [-0.9926777,-0.9926777,0.45],
    [-0.975,0.0,-0.45],
    [-0.975,-0.0073223114,-0.485355347],
    [-0.989433765,-0.0105662346,-0.4788675],
    [-0.9926777,-0.0073223114,-0.45],
    [-1.0,-0.025000006,-0.45],
    [-0.9926777,-0.0073223114,-0.45],
    [-0.989433765,-0.0105662346,-0.4788675],
    [-0.9926777,-0.025000006,-0.485355347],
    [-0.975,-0.025000006,-0.5],
    [-0.9926777,-0.025000006,-0.485355347],
    [-0.989433765,-0.0105662346,-0.4788675],
    [-0.975,-0.0073223114,-0.485355347],
    [-0.975,0.0,0.45],
    [-0.9926777,-0.0073223114,0.45],
    [-0.989433765,-0.0105662346,0.478867531],
    [-0.975,-0.0073223114,0.485355377],
    [-0.975,-0.025000006,0.5],
    [-0.975,-0.0073223114,0.485355377],
    [-0.989433765,-0.0105662346,0.478867531],
    [-0.9926777,-0.025000006,0.485355377],
    [-1.0,-0.025000006,0.45],
    [-0.9926777,-0.025000006,0.485355377],
    [-0.989433765,-0.0105662346,0.478867531],
    [-0.9926777,-0.0073223114,0.45],
    [-0.025000006,-0.9,-0.5],
    [-0.0073223114,-0.975,-0.485355347],
    [-0.025000006,-0.975,-0.5],
    [-0.0073223114,-0.9,-0.485355347],
    [-0.025000006,-0.8,-0.5],
    [-0.0073223114,-0.8,-0.485355347],
    [-0.025000006,-0.199999988,-0.5],
    [-0.0073223114,-0.199999988,-0.485355347],
    [-0.025000006,-0.099999994,-0.5],
    [-0.0073223114,-0.099999994,-0.485355347],
    [-0.025000006,-0.025000006,-0.5],
    [-0.0073223114,-0.025000006,-0.485355347],
    [-0.0073223114,-0.9,-0.485355347],
    [0.0,-0.975,-0.45],
    [-0.0073223114,-0.975,-0.485355347],
    [0.0,-0.9,-0.45],
    [-0.0073223114,-0.8,-0.485355347],
    [0.0,-0.8,-0.45],
    [-0.0073223114,-0.199999988,-0.485355347],
    [0.0,-0.199999988,-0.45],
    [-0.0073223114,-0.099999994,-0.485355347],
    [0.0,-0.099999994,-0.45],
    [-0.0073223114,-0.025000006,-0.485355347],
    [0.0,-0.025000006,-0.45],
    [-0.025000006,-1.0,0.3],
    [-0.0073223114,-0.9926777,0.45],
    [-0.025000006,-1.0,0.45],
    [-0.0073223114,-0.9926777,0.3],
    [-0.025000006,-1.0,0.100000024],
    [-0.0073223114,-0.9926777,0.100000024],
    [-0.025000006,-1.0,-0.100000024],
    [-0.0073223114,-0.9926777,-0.100000024],
    [-0.025000006,-1.0,-0.3],
    [-0.0073223114,-0.9926777,-0.3],
    [-0.025000006,-1.0,-0.45],
    [-0.0073223114,-0.9926777,-0.45],
    [-0.0073223114,-0.9926777,0.3],
    [0.0,-0.975,0.45],
    [-0.0073223114,-0.9926777,0.45],
    [0.0,-0.975,0.3],
    [-0.0073223114,-0.9926777,0.100000024],
    [0.0,-0.975,0.100000024],
    [-0.0073223114,-0.9926777,-0.100000024],
    [0.0,-0.975,-0.100000024],
    [-0.0073223114,-0.9926777,-0.3],
    [0.0,-0.975,-0.3],
    [-0.0073223114,-0.9926777,-0.45],
    [0.0,-0.975,-0.45],
    [-0.025000006,-0.099999994,0.5],
    [-0.0073223114,-0.025000006,0.485355377],
    [-0.025000006,-0.025000006,0.5],
    [-0.0073223114,-0.099999994,0.485355377],
    [-0.025000006,-0.199999988,0.5],
    [-0.0073223114,-0.199999988,0.485355377],
    [-0.025000006,-0.8,0.5],
    [-0.0073223114,-0.8,0.485355377],
    [-0.025000006,-0.9,0.5],
    [-0.0073223114,-0.9,0.485355377],
    [-0.025000006,-0.975,0.5],
    [-0.0073223114,-0.975,0.485355377],
    [-0.0073223114,-0.099999994,0.485355377],
    [0.0,-0.025000006,0.45],
    [-0.0073223114,-0.025000006,0.485355377],
    [0.0,-0.099999994,0.45],
    [-0.0073223114,-0.199999988,0.485355377],
    [0.0,-0.199999988,0.45],
    [-0.0073223114,-0.8,0.485355377],
    [0.0,-0.8,0.45],
    [-0.0073223114,-0.9,0.485355377],
    [0.0,-0.9,0.45],
    [-0.0073223114,-0.975,0.485355377],
    [0.0,-0.975,0.45],
    [-0.025000006,0.0,-0.3],
    [-0.0073223114,-0.0073223114,-0.45],
    [-0.025000006,0.0,-0.45],
    [-0.0073223114,-0.0073223114,-0.3],
    [-0.025000006,0.0,-0.100000024],
    [-0.0073223114,-0.0073223114,-0.100000024],
    [-0.025000006,0.0,0.100000024],
    [-0.0073223114,-0.0073223114,0.100000024],
    [-0.025000006,0.0,0.3],
    [-0.0073223114,-0.0073223114,0.3],
    [-0.025000006,0.0,0.45],
    [-0.0073223114,-0.0073223114,0.45],
    [-0.0073223114,-0.0073223114,-0.3],
    [0.0,-0.025000006,-0.45],
    [-0.0073223114,-0.0073223114,-0.45],
    [0.0,-0.025000006,-0.3],
    [-0.0073223114,-0.0073223114,-0.100000024],
    [0.0,-0.025000006,-0.100000024],
    [-0.0073223114,-0.0073223114,0.100000024],
    [0.0,-0.025000006,0.100000024],
    [-0.0073223114,-0.0073223114,0.3],
    [0.0,-0.025000006,0.3],
    [-0.0073223114,-0.0073223114,0.45],
    [0.0,-0.025000006,0.45],
    [-0.099999994,-0.025000006,-0.5],
    [-0.025000006,-0.0073223114,-0.485355347],
    [-0.025000006,-0.025000006,-0.5],
    [-0.099999994,-0.0073223114,-0.485355347],
    [-0.7,-0.025000006,-0.5],
    [-0.7,-0.0073223114,-0.485355347],
    [-0.8,-0.025000006,-0.5],
    [-0.8,-0.0073223114,-0.485355347],
    [-0.9,-0.025000006,-0.5],
    [-0.9,-0.0073223114,-0.485355347],
    [-0.975,-0.025000006,-0.5],
    [-0.975,-0.0073223114,-0.485355347],
    [-0.099999994,-0.0073223114,-0.485355347],
    [-0.025000006,0.0,-0.45],
    [-0.025000006,-0.0073223114,-0.485355347],
    [-0.099999994,0.0,-0.45],
    [-0.7,-0.0073223114,-0.485355347],
    [-0.7,0.0,-0.45],
    [-0.8,-0.0073223114,-0.485355347],
    [-0.8,0.0,-0.45],
    [-0.9,-0.0073223114,-0.485355347],
    [-0.9,0.0,-0.45],
    [-0.975,-0.0073223114,-0.485355347],
    [-0.975,0.0,-0.45],
    [-0.9,-0.025000006,0.5],
    [-0.975,-0.0073223114,0.485355377],
    [-0.975,-0.025000006,0.5],
    [-0.9,-0.0073223114,0.485355377],
    [-0.8,-0.025000006,0.5],
    [-0.8,-0.0073223114,0.485355377],
    [-0.7,-0.025000006,0.5],
    [-0.7,-0.0073223114,0.485355377],
    [-0.099999994,-0.025000006,0.5],
    [-0.099999994,-0.0073223114,0.485355377],
    [-0.025000006,-0.025000006,0.5],
    [-0.025000006,-0.0073223114,0.485355377],
    [-0.9,-0.0073223114,0.485355377],
    [-0.975,0.0,0.45],
    [-0.975,-0.0073223114,0.485355377],
    [-0.9,0.0,0.45],
    [-0.8,-0.0073223114,0.485355377],
    [-0.8,0.0,0.45],
    [-0.7,-0.0073223114,0.485355377],
    [-0.7,0.0,0.45],
    [-0.099999994,-0.0073223114,0.485355377],
    [-0.099999994,0.0,0.45],
    [-0.025000006,-0.0073223114,0.485355377],
    [-0.025000006,0.0,0.45],
    [-1.0,-0.025000006,-0.3],
    [-0.9926777,-0.0073223114,-0.45],
    [-1.0,-0.025000006,-0.45],
    [-0.9926777,-0.0073223114,-0.3],
    [-1.0,-0.025000006,-0.100000024],
    [-0.9926777,-0.0073223114,-0.100000024],
    [-1.0,-0.025000006,0.100000024],
    [-0.9926777,-0.0073223114,0.100000024],
    [-1.0,-0.025000006,0.3],
    [-0.9926777,-0.0073223114,0.3],
    [-1.0,-0.025000006,0.45],
    [-0.9926777,-0.0073223114,0.45],
    [-0.9926777,-0.0073223114,-0.3],
    [-0.975,0.0,-0.45],
    [-0.9926777,-0.0073223114,-0.45],
    [-0.975,0.0,-0.3],
    [-0.9926777,-0.0073223114,-0.100000024],
    [-0.975,0.0,-0.100000024],
    [-0.9926777,-0.0073223114,0.100000024],
    [-0.975,0.0,0.100000024],
    [-0.9926777,-0.0073223114,0.3],
    [-0.975,0.0,0.3],
    [-0.9926777,-0.0073223114,0.45],
    [-0.975,0.0,0.45],
    [-0.975,-0.099999994,-0.5],
    [-0.9926777,-0.025000006,-0.485355347],
    [-0.975,-0.025000006,-0.5],
    [-0.9926777,-0.099999994,-0.485355347],
    [-0.975,-0.199999988,-0.5],
    [-0.9926777,-0.199999988,-0.485355347],
    [-0.975,-0.8,-0.5],
    [-0.9926777,-0.8,-0.485355347],
    [-0.975,-0.9,-0.5],
    [-0.9926777,-0.9,-0.485355347],
    [-0.975,-0.975,-0.5],
    [-0.9926777,-0.975,-0.485355347],
    [-0.9926777,-0.099999994,-0.485355347],
    [-1.0,-0.025000006,-0.45],
    [-0.9926777,-0.025000006,-0.485355347],
    [-1.0,-0.099999994,-0.45],
    [-0.9926777,-0.199999988,-0.485355347],
    [-1.0,-0.199999988,-0.45],
    [-0.9926777,-0.8,-0.485355347],
    [-1.0,-0.8,-0.45],
    [-0.9926777,-0.9,-0.485355347],
    [-1.0,-0.9,-0.45],
    [-0.9926777,-0.975,-0.485355347],
    [-1.0,-0.975,-0.45],
    [-0.975,-0.9,0.5],
    [-0.9926777,-0.975,0.485355377],
    [-0.975,-0.975,0.5],
    [-0.9926777,-0.9,0.485355377],
    [-0.975,-0.8,0.5],
    [-0.9926777,-0.8,0.485355377],
    [-0.975,-0.199999988,0.5],
    [-0.9926777,-0.199999988,0.485355377],
    [-0.975,-0.099999994,0.5],
    [-0.9926777,-0.099999994,0.485355377],
    [-0.975,-0.025000006,0.5],
    [-0.9926777,-0.025000006,0.485355377],
    [-0.9926777,-0.9,0.485355377],
    [-1.0,-0.975,0.45],
    [-0.9926777,-0.975,0.485355377],
    [-1.0,-0.9,0.45],
    [-0.9926777,-0.8,0.485355377],
    [-1.0,-0.8,0.45],
    [-0.9926777,-0.199999988,0.485355377],
    [-1.0,-0.199999988,0.45],
    [-0.9926777,-0.099999994,0.485355377],
    [-1.0,-0.099999994,0.45],
    [-0.9926777,-0.025000006,0.485355377],
    [-1.0,-0.025000006,0.45],
    [-0.975,-1.0,-0.3],
    [-0.9926777,-0.9926777,-0.45],
    [-0.975,-1.0,-0.45],
    [-0.9926777,-0.9926777,-0.3],
    [-0.975,-1.0,-0.100000024],
    [-0.9926777,-0.9926777,-0.100000024],
    [-0.975,-1.0,0.100000024],
    [-0.9926777,-0.9926777,0.100000024],
    [-0.975,-1.0,0.3],
    [-0.9926777,-0.9926777,0.3],
    [-0.975,-1.0,0.45],
    [-0.9926777,-0.9926777,0.45],
    [-0.9926777,-0.9926777,-0.3],
    [-1.0,-0.975,-0.45],
    [-0.9926777,-0.9926777,-0.45],
    [-1.0,-0.975,-0.3],
    [-0.9926777,-0.9926777,-0.100000024],
    [-1.0,-0.975,-0.100000024],
    [-0.9926777,-0.9926777,0.100000024],
    [-1.0,-0.975,0.100000024],
    [-0.9926777,-0.9926777,0.3],
    [-1.0,-0.975,0.3],
    [-0.9926777,-0.9926777,0.45],
    [-1.0,-0.975,0.45],
    [-0.9,-0.975,-0.5],
    [-0.975,-0.9926777,-0.485355347],
    [-0.975,-0.975,-0.5],
    [-0.9,-0.9926777,-0.485355347],
    [-0.8,-0.975,-0.5],
    [-0.8,-0.9926777,-0.485355347],
    [-0.7,-0.975,-0.5],
    [-0.7,-0.9926777,-0.485355347],
    [-0.099999994,-0.975,-0.5],
    [-0.099999994,-0.9926777,-0.485355347],
    [-0.025000006,-0.975,-0.5],
    [-0.025000006,-0.9926777,-0.485355347],
    [-0.9,-0.9926777,-0.485355347],
    [-0.975,-1.0,-0.45],
    [-0.975,-0.9926777,-0.485355347],
    [-0.9,-1.0,-0.45],
    [-0.8,-0.9926777,-0.485355347],
    [-0.8,-1.0,-0.45],
    [-0.7,-0.9926777,-0.485355347],
    [-0.7,-1.0,-0.45],
    [-0.099999994,-0.9926777,-0.485355347],
    [-0.099999994,-1.0,-0.45],
    [-0.025000006,-0.9926777,-0.485355347],
    [-0.025000006,-1.0,-0.45],
    [-0.099999994,-0.975,0.5],
    [-0.025000006,-0.9926777,0.485355377],
    [-0.025000006,-0.975,0.5],
    [-0.099999994,-0.9926777,0.485355377],
    [-0.7,-0.975,0.5],
    [-0.7,-0.9926777,0.485355377],
    [-0.8,-0.975,0.5],
    [-0.8,-0.9926777,0.485355377],
    [-0.9,-0.975,0.5],
    [-0.9,-0.9926777,0.485355377],
    [-0.975,-0.975,0.5],
    [-0.975,-0.9926777,0.485355377],
    [-0.099999994,-0.9926777,0.485355377],
    [-0.025000006,-1.0,0.45],
    [-0.025000006,-0.9926777,0.485355377],
    [-0.099999994,-1.0,0.45],
    [-0.7,-0.9926777,0.485355377],
    [-0.7,-1.0,0.45],
    [-0.8,-0.9926777,0.485355377],
    [-0.8,-1.0,0.45],
    [-0.9,-0.9926777,0.485355377],
    [-0.9,-1.0,0.45],
    [-0.975,-0.9926777,0.485355377],
    [-0.975,-1.0,0.45]
  ],
  ""Triangles"": [
    0,
    1,
    2,
    0,
    2,
    3,
    4,
    0,
    3,
    3,
    2,
    5,
    4,
    3,
    6,
    7,
    4,
    6,
    3,
    5,
    8,
    6,
    3,
    8,
    8,
    5,
    9,
    7,
    6,
    10,
    11,
    7,
    10,
    8,
    9,
    12,
    12,
    9,
    13,
    6,
    8,
    14,
    10,
    6,
    14,
    14,
    8,
    12,
    11,
    10,
    15,
    16,
    11,
    15,
    16,
    15,
    17,
    12,
    13,
    18,
    18,
    13,
    19,
    18,
    19,
    20,
    14,
    12,
    21,
    21,
    12,
    18,
    10,
    14,
    22,
    15,
    10,
    22,
    22,
    14,
    21,
    17,
    15,
    23,
    15,
    22,
    23,
    17,
    23,
    24,
    25,
    18,
    20,
    21,
    18,
    25,
    25,
    20,
    26,
    22,
    21,
    27,
    23,
    22,
    27,
    27,
    21,
    25,
    24,
    23,
    28,
    23,
    27,
    28,
    24,
    28,
    29,
    30,
    25,
    26,
    27,
    25,
    30,
    28,
    27,
    30,
    30,
    26,
    31,
    29,
    28,
    32,
    28,
    30,
    32,
    32,
    30,
    31,
    29,
    32,
    33,
    32,
    31,
    34,
    33,
    32,
    34,
    33,
    34,
    35,
    36,
    37,
    38,
    36,
    38,
    39,
    40,
    36,
    39,
    39,
    38,
    41,
    40,
    39,
    42,
    43,
    40,
    42,
    39,
    41,
    44,
    42,
    39,
    44,
    44,
    41,
    45,
    43,
    42,
    46,
    47,
    43,
    46,
    42,
    44,
    48,
    46,
    42,
    48,
    44,
    45,
    49,
    48,
    44,
    49,
    49,
    45,
    50,
    47,
    46,
    51,
    52,
    47,
    51,
    52,
    51,
    53,
    46,
    48,
    54,
    51,
    46,
    54,
    53,
    51,
    55,
    51,
    54,
    55,
    53,
    55,
    56,
    48,
    49,
    57,
    54,
    48,
    57,
    49,
    50,
    58,
    57,
    49,
    58,
    58,
    50,
    59,
    58,
    59,
    60,
    61,
    58,
    60,
    57,
    58,
    61,
    61,
    60,
    62,
    54,
    57,
    63,
    55,
    54,
    63,
    63,
    57,
    61,
    56,
    55,
    64,
    55,
    63,
    64,
    56,
    64,
    65,
    66,
    61,
    62,
    63,
    61,
    66,
    64,
    63,
    66,
    66,
    62,
    67,
    65,
    64,
    68,
    64,
    66,
    68,
    68,
    66,
    67,
    65,
    68,
    69,
    68,
    67,
    70,
    69,
    68,
    70,
    69,
    70,
    71,
    72,
    73,
    74,
    72,
    74,
    75,
    76,
    72,
    75,
    75,
    74,
    77,
    76,
    75,
    78,
    79,
    76,
    78,
    75,
    77,
    80,
    78,
    75,
    80,
    80,
    77,
    81,
    79,
    78,
    82,
    83,
    79,
    82,
    78,
    80,
    84,
    82,
    78,
    84,
    80,
    81,
    85,
    84,
    80,
    85,
    85,
    81,
    86,
    83,
    82,
    87,
    88,
    83,
    87,
    88,
    87,
    89,
    82,
    84,
    90,
    87,
    82,
    90,
    89,
    87,
    91,
    87,
    90,
    91,
    89,
    91,
    92,
    84,
    85,
    93,
    90,
    84,
    93,
    85,
    86,
    94,
    93,
    85,
    94,
    94,
    86,
    95,
    94,
    95,
    96,
    97,
    94,
    96,
    93,
    94,
    97,
    97,
    96,
    98,
    90,
    93,
    99,
    91,
    90,
    99,
    99,
    93,
    97,
    92,
    91,
    100,
    91,
    99,
    100,
    92,
    100,
    101,
    102,
    97,
    98,
    99,
    97,
    102,
    100,
    99,
    102,
    102,
    98,
    103,
    101,
    100,
    104,
    100,
    102,
    104,
    104,
    102,
    103,
    101,
    104,
    105,
    104,
    103,
    106,
    105,
    104,
    106,
    105,
    106,
    107,
    108,
    109,
    110,
    108,
    110,
    111,
    112,
    108,
    111,
    111,
    110,
    113,
    112,
    111,
    114,
    115,
    112,
    114,
    111,
    113,
    116,
    114,
    111,
    116,
    116,
    113,
    117,
    115,
    114,
    118,
    119,
    115,
    118,
    116,
    117,
    120,
    120,
    117,
    121,
    114,
    116,
    122,
    118,
    114,
    122,
    122,
    116,
    120,
    119,
    118,
    123,
    124,
    119,
    123,
    124,
    123,
    125,
    120,
    121,
    126,
    126,
    121,
    127,
    126,
    127,
    128,
    122,
    120,
    129,
    129,
    120,
    126,
    118,
    122,
    130,
    123,
    118,
    130,
    130,
    122,
    129,
    125,
    123,
    131,
    123,
    130,
    131,
    125,
    131,
    132,
    133,
    126,
    128,
    129,
    126,
    133,
    133,
    128,
    134,
    130,
    129,
    135,
    131,
    130,
    135,
    135,
    129,
    133,
    132,
    131,
    136,
    131,
    135,
    136,
    132,
    136,
    137,
    138,
    133,
    134,
    135,
    133,
    138,
    136,
    135,
    138,
    138,
    134,
    139,
    137,
    136,
    140,
    136,
    138,
    140,
    140,
    138,
    139,
    137,
    140,
    141,
    140,
    139,
    142,
    141,
    140,
    142,
    141,
    142,
    143,
    144,
    145,
    146,
    144,
    147,
    145,
    148,
    147,
    144,
    147,
    149,
    145,
    148,
    150,
    147,
    151,
    150,
    148,
    147,
    152,
    149,
    150,
    152,
    147,
    152,
    153,
    149,
    151,
    154,
    150,
    155,
    154,
    151,
    152,
    156,
    153,
    156,
    157,
    153,
    150,
    158,
    152,
    154,
    158,
    150,
    158,
    156,
    152,
    155,
    159,
    154,
    160,
    159,
    155,
    160,
    161,
    159,
    156,
    162,
    157,
    162,
    163,
    157,
    162,
    164,
    163,
    158,
    165,
    156,
    165,
    162,
    156,
    154,
    166,
    158,
    159,
    166,
    154,
    166,
    165,
    158,
    161,
    167,
    159,
    159,
    167,
    166,
    161,
    168,
    167,
    169,
    164,
    162,
    165,
    169,
    162,
    169,
    170,
    164,
    166,
    171,
    165,
    167,
    171,
    166,
    171,
    169,
    165,
    168,
    172,
    167,
    167,
    172,
    171,
    168,
    173,
    172,
    174,
    170,
    169,
    171,
    174,
    169,
    172,
    174,
    171,
    174,
    175,
    170,
    173,
    176,
    172,
    172,
    176,
    174,
    176,
    175,
    174,
    173,
    177,
    176,
    176,
    178,
    175,
    177,
    178,
    176,
    177,
    179,
    178,
    180,
    181,
    182,
    180,
    182,
    183,
    184,
    180,
    183,
    183,
    182,
    185,
    184,
    183,
    186,
    187,
    184,
    186,
    183,
    185,
    188,
    186,
    183,
    188,
    188,
    185,
    189,
    187,
    186,
    190,
    191,
    187,
    190,
    186,
    188,
    192,
    190,
    186,
    192,
    188,
    189,
    193,
    192,
    188,
    193,
    193,
    189,
    194,
    191,
    190,
    195,
    196,
    191,
    195,
    196,
    195,
    197,
    190,
    192,
    198,
    195,
    190,
    198,
    197,
    195,
    199,
    195,
    198,
    199,
    197,
    199,
    200,
    192,
    193,
    201,
    198,
    192,
    201,
    193,
    194,
    202,
    201,
    193,
    202,
    202,
    194,
    203,
    202,
    203,
    204,
    205,
    202,
    204,
    201,
    202,
    205,
    205,
    204,
    206,
    198,
    201,
    207,
    199,
    198,
    207,
    207,
    201,
    205,
    200,
    199,
    208,
    199,
    207,
    208,
    200,
    208,
    209,
    210,
    205,
    206,
    207,
    205,
    210,
    208,
    207,
    210,
    210,
    206,
    211,
    209,
    208,
    212,
    208,
    210,
    212,
    212,
    210,
    211,
    209,
    212,
    213,
    212,
    211,
    214,
    213,
    212,
    214,
    213,
    214,
    215,
    216,
    217,
    218,
    216,
    218,
    219,
    220,
    221,
    222,
    220,
    222,
    223,
    224,
    225,
    226,
    224,
    226,
    227,
    228,
    229,
    230,
    228,
    230,
    231,
    232,
    233,
    234,
    232,
    234,
    235,
    236,
    237,
    238,
    236,
    238,
    239,
    240,
    241,
    242,
    240,
    242,
    243,
    244,
    245,
    246,
    244,
    246,
    247,
    248,
    249,
    250,
    248,
    250,
    251,
    252,
    253,
    254,
    252,
    254,
    255,
    256,
    257,
    258,
    256,
    258,
    259,
    260,
    261,
    262,
    260,
    262,
    263,
    264,
    265,
    266,
    264,
    266,
    267,
    268,
    269,
    270,
    268,
    270,
    271,
    272,
    273,
    274,
    272,
    274,
    275,
    276,
    277,
    278,
    276,
    278,
    279,
    280,
    281,
    282,
    280,
    282,
    283,
    284,
    285,
    286,
    284,
    286,
    287,
    288,
    289,
    290,
    288,
    290,
    291,
    292,
    293,
    294,
    292,
    294,
    295,
    296,
    297,
    298,
    296,
    298,
    299,
    300,
    301,
    302,
    300,
    302,
    303,
    304,
    305,
    306,
    304,
    306,
    307,
    308,
    309,
    310,
    308,
    310,
    311,
    312,
    313,
    314,
    312,
    315,
    313,
    316,
    315,
    312,
    316,
    317,
    315,
    318,
    317,
    316,
    318,
    319,
    317,
    320,
    319,
    318,
    320,
    321,
    319,
    322,
    321,
    320,
    322,
    323,
    321,
    324,
    325,
    326,
    324,
    327,
    325,
    328,
    327,
    324,
    328,
    329,
    327,
    330,
    329,
    328,
    330,
    331,
    329,
    332,
    331,
    330,
    332,
    333,
    331,
    334,
    333,
    332,
    334,
    335,
    333,
    336,
    337,
    338,
    336,
    339,
    337,
    340,
    339,
    336,
    340,
    341,
    339,
    342,
    341,
    340,
    342,
    343,
    341,
    344,
    343,
    342,
    344,
    345,
    343,
    346,
    345,
    344,
    346,
    347,
    345,
    348,
    349,
    350,
    348,
    351,
    349,
    352,
    351,
    348,
    352,
    353,
    351,
    354,
    353,
    352,
    354,
    355,
    353,
    356,
    355,
    354,
    356,
    357,
    355,
    358,
    357,
    356,
    358,
    359,
    357,
    360,
    361,
    362,
    360,
    363,
    361,
    364,
    363,
    360,
    364,
    365,
    363,
    366,
    365,
    364,
    366,
    367,
    365,
    368,
    367,
    366,
    368,
    369,
    367,
    370,
    369,
    368,
    370,
    371,
    369,
    372,
    373,
    374,
    372,
    375,
    373,
    376,
    375,
    372,
    376,
    377,
    375,
    378,
    377,
    376,
    378,
    379,
    377,
    380,
    379,
    378,
    380,
    381,
    379,
    382,
    381,
    380,
    382,
    383,
    381,
    384,
    385,
    386,
    384,
    387,
    385,
    388,
    387,
    384,
    388,
    389,
    387,
    390,
    389,
    388,
    390,
    391,
    389,
    392,
    391,
    390,
    392,
    393,
    391,
    394,
    393,
    392,
    394,
    395,
    393,
    396,
    397,
    398,
    396,
    399,
    397,
    400,
    399,
    396,
    400,
    401,
    399,
    402,
    401,
    400,
    402,
    403,
    401,
    404,
    403,
    402,
    404,
    405,
    403,
    406,
    405,
    404,
    406,
    407,
    405,
    408,
    409,
    410,
    408,
    411,
    409,
    412,
    411,
    408,
    412,
    413,
    411,
    414,
    413,
    412,
    414,
    415,
    413,
    416,
    415,
    414,
    416,
    417,
    415,
    418,
    417,
    416,
    418,
    419,
    417,
    420,
    421,
    422,
    420,
    423,
    421,
    424,
    423,
    420,
    424,
    425,
    423,
    426,
    425,
    424,
    426,
    427,
    425,
    428,
    427,
    426,
    428,
    429,
    427,
    430,
    429,
    428,
    430,
    431,
    429,
    432,
    433,
    434,
    432,
    435,
    433,
    436,
    435,
    432,
    436,
    437,
    435,
    438,
    437,
    436,
    438,
    439,
    437,
    440,
    439,
    438,
    440,
    441,
    439,
    442,
    441,
    440,
    442,
    443,
    441,
    444,
    445,
    446,
    444,
    447,
    445,
    448,
    447,
    444,
    448,
    449,
    447,
    450,
    449,
    448,
    450,
    451,
    449,
    452,
    451,
    450,
    452,
    453,
    451,
    454,
    453,
    452,
    454,
    455,
    453,
    456,
    457,
    458,
    456,
    459,
    457,
    460,
    459,
    456,
    460,
    461,
    459,
    462,
    461,
    460,
    462,
    463,
    461,
    464,
    463,
    462,
    464,
    465,
    463,
    466,
    465,
    464,
    466,
    467,
    465,
    468,
    469,
    470,
    468,
    471,
    469,
    472,
    471,
    468,
    472,
    473,
    471,
    474,
    473,
    472,
    474,
    475,
    473,
    476,
    475,
    474,
    476,
    477,
    475,
    478,
    477,
    476,
    478,
    479,
    477,
    480,
    481,
    482,
    480,
    483,
    481,
    484,
    483,
    480,
    484,
    485,
    483,
    486,
    485,
    484,
    486,
    487,
    485,
    488,
    487,
    486,
    488,
    489,
    487,
    490,
    489,
    488,
    490,
    491,
    489,
    492,
    493,
    494,
    492,
    495,
    493,
    496,
    495,
    492,
    496,
    497,
    495,
    498,
    497,
    496,
    498,
    499,
    497,
    500,
    499,
    498,
    500,
    501,
    499,
    502,
    501,
    500,
    502,
    503,
    501,
    504,
    505,
    506,
    504,
    507,
    505,
    508,
    507,
    504,
    508,
    509,
    507,
    510,
    509,
    508,
    510,
    511,
    509,
    512,
    511,
    510,
    512,
    513,
    511,
    514,
    513,
    512,
    514,
    515,
    513,
    516,
    517,
    518,
    516,
    519,
    517,
    520,
    519,
    516,
    520,
    521,
    519,
    522,
    521,
    520,
    522,
    523,
    521,
    524,
    523,
    522,
    524,
    525,
    523,
    526,
    525,
    524,
    526,
    527,
    525,
    528,
    529,
    530,
    528,
    531,
    529,
    532,
    531,
    528,
    532,
    533,
    531,
    534,
    533,
    532,
    534,
    535,
    533,
    536,
    535,
    534,
    536,
    537,
    535,
    538,
    537,
    536,
    538,
    539,
    537,
    540,
    541,
    542,
    540,
    543,
    541,
    544,
    543,
    540,
    544,
    545,
    543,
    546,
    545,
    544,
    546,
    547,
    545,
    548,
    547,
    546,
    548,
    549,
    547,
    550,
    549,
    548,
    550,
    551,
    549,
    552,
    553,
    554,
    552,
    555,
    553,
    556,
    555,
    552,
    556,
    557,
    555,
    558,
    557,
    556,
    558,
    559,
    557,
    560,
    559,
    558,
    560,
    561,
    559,
    562,
    561,
    560,
    562,
    563,
    561,
    564,
    565,
    566,
    564,
    567,
    565,
    568,
    567,
    564,
    568,
    569,
    567,
    570,
    569,
    568,
    570,
    571,
    569,
    572,
    571,
    570,
    572,
    573,
    571,
    574,
    573,
    572,
    574,
    575,
    573,
    576,
    577,
    578,
    576,
    579,
    577,
    580,
    579,
    576,
    580,
    581,
    579,
    582,
    581,
    580,
    582,
    583,
    581,
    584,
    583,
    582,
    584,
    585,
    583,
    586,
    585,
    584,
    586,
    587,
    585,
    588,
    589,
    590,
    588,
    591,
    589,
    592,
    591,
    588,
    592,
    593,
    591,
    594,
    593,
    592,
    594,
    595,
    593,
    596,
    595,
    594,
    596,
    597,
    595,
    598,
    597,
    596,
    598,
    599,
    597
  ],
  ""Resizer"": {
    ""PivotOffset"": [0.5,0.5,0.5],
    ""ScalingX"": ""SLICE_WITH_ASYMMETRICAL_BORDER"",
    ""BorderXNegative"": 0.25,
    ""BorderXPositive"": 0.75,
    ""ScalingY"": ""SLICE"",
    ""BorderYNegative"": 0.5,
    ""BorderYPositive"": 0.5,
    ""ScalingZ"": ""SCALE"",
    ""BorderZNegative"": 0.5,
    ""BorderZPositive"": 0.5,
    ""StretchCenter"": 0
  }
}

";

    }
}
