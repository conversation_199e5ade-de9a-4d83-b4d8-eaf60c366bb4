%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-2256165467271112851
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a7820776d2dedf42ba036ce519acb73, type: 3}
  m_Name: ConstantMask
  m_EditorClassIdentifier: 
  constant: 0.1
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: efacc41b4d21cfa458fd5af8b13d5803, type: 3}
  m_Name: FloorCeilingWall_GlobalMesh_Staggered
  m_EditorClassIdentifier: 
  Poolsize: 2000
  decorationPrefabs:
  - {fileID: 1847491601760391615, guid: 85f5e53a938c705408ba49d8c1522476, type: 3}
  executeSceneLabels: 7
  targets: 1
  targetPhysicsLayers:
    serializedVersion: 2
    m_Bits: 0
  placement: 0
  placementDirection: {x: 0, y: 0, z: -1}
  selectBehind: 1
  rayOffset: {x: 0, y: 0, z: 0.1}
  spawnHierarchy: 0
  distributionType: 2
  gridDistribution:
    spacingX: 1
    spacingY: 1
  simplexDistribution:
    pointSamplingConfig:
      pointsPerUnitX: 0
      pointsPerUnitY: 0
      noiseOffsetRadius: 0
  staggeredConcentricDistribution:
    stepSize: 0.1
  randomDistribution:
    numPerUnit: 10
  masks:
  - {fileID: -2256165467271112851}
  constraints: []
  modifiers:
  - {fileID: 7886944448432899229}
  - {fileID: 3232008077640052306}
  discardParentScaling: 1
  lifetime: 0
  DrawDebugRaysAndImpactPoints: 0
--- !u!114 &3232008077640052306
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 31688e38539e3ec41b33c3338c2b2203, type: 3}
  m_Name: ScaleUniformModifier
  m_EditorClassIdentifier: 
  enabled: 1
  mask: {fileID: -2256165467271112851}
  limitMin: 0.1
  limitMax: 0.1
  scale: 0.1
  offset: 0
--- !u!114 &7886944448432899229
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5ca82de4c0245b24fb0fa71a8a588cea, type: 3}
  m_Name: KeepUprightWithSurfaceModifier
  m_EditorClassIdentifier: 
  enabled: 1
  uprightAxis: {x: 0, y: 1, z: 0}
