{"CoordinateSystem": "Unity", "Rooms": [{"UUID": "765093AEA8ABFBED886FDAF0CA7AC2BA", "RoomLayout": {"FloorUuid": "BD460F821B48A45B71ED111D91B190FF", "CeilingUuid": "A1FC5FB0FBE83CCC2644868FC4A439A4", "WallsUuid": ["9E0728072A37AD5E52C10630FBD8F046", "9A198B4708653DDB000AAD44F4B9B585", "1DB2D3B732CDFB01E2D645C9E7EF584A", "555C7B6D42DE0C950C6A6C03E437E247", "F59C4D91C8D90D303B9501564B3D9124", "AA65A76759969B77BFA9C374548F78BF"]}, "Anchors": [{"UUID": "07A3E0148FFF9B0CAFC0679C8FED2770", "SemanticClassifications": ["GLOBAL_MESH"], "Transform": {"Translation": [0.181984186, 1.553285, 0.101500571], "Rotation": [270.0, 181.648361, 0.0], "Scale": [1.0, 1.0, 1.0]}, "GlobalMesh": {"Positions": [[1.41433573, 0.145166054, -0.177147523], [1.402782, 0.114477828, 0.422852427], [1.40275478, 0.114405975, 0.622852445], [1.451663, 0.2443096, 0.8228524], [1.43264675, 0.193800017, 0.6728524], [1.40167451, 0.245166153, 0.855911851], [1.39551115, 0.09516582, -0.377147526], [1.32009077, -0.105154991, -0.377147526], [1.31982207, -0.1058684, 0.172852427], [1.3198216, -0.105870485, 0.422852427], [1.20447993, -0.4122253, 0.422852427], [1.37613559, 0.0437033474, 0.5228524], [1.30178, -0.1537902, 0.5728524], [1.22553992, -0.3562892, 0.5228524], [1.20167518, -0.419674933, 0.5728524], [1.39451838, 0.09252936, 0.8228524], [1.35421181, -0.0145272911, 0.7728524], [1.33816361, -0.05715269, 0.8228524], [1.301836, -0.153641552, 0.8228524], [1.24436855, -0.306279063, 0.8228524], [1.225302, -0.356921017, 0.7728524], [1.20721292, -0.4049664, 0.8228524], [1.35167456, 0.295166135, 0.855911851], [1.20167446, 0.0451661646, 0.855911851], [1.3016746, -0.00483384728, 0.855911851], [1.3016746, -0.104833841, 0.855911851], [1.25167441, -0.104833841, 0.855911851], [1.25167441, -0.2048338, 0.855911851], [1.20167446, -0.254833817, 0.855911851], [1.20167446, -0.354833841, 0.855911851], [1.1880796, -0.4557855, -0.22714752], [1.0748024, -0.7566567, -0.177147523], [1.168432, -0.5079704, -0.07714754], [1.06000781, -0.7959531, 0.02285245], [1.13118839, -0.606892943, 0.122852445], [1.00320125, -0.9468346, 0.422852427], [1.131325, -0.6065305, 0.5728524], [1.18802333, -0.455935836, 0.8228524], [1.15177441, -0.5522146, 0.7728524], [1.15178728, -0.5521813, 0.8228524], [1.11294508, -0.6553479, 0.722852349], [1.09401512, -0.705627143, 0.8228524], [1.03749609, -0.855746, 0.8228524], [1.15167451, -0.404833853, 0.855911851], [1.15167451, -0.5048338, 0.855911851], [1.10167456, -0.5048338, 0.855911851], [1.11171532, -0.6586141, 0.8415319], [1.05167437, -0.6548338, 0.855911851], [1.05167437, -0.7048338, 0.855911851], [1.00167441, -0.754833758, 0.855911851], [1.05604482, -0.8064793, 0.8336575], [1.00167441, -0.8548338, 0.855911851], [0.923433065, -1.15870428, 0.172852427], [0.9999089, -0.95557946, 0.8228524], [0.9610956, -1.05867028, 0.6728524], [0.942826748, -1.10719347, 0.8228524], [0.8016746, -0.2048338, 0.855911851], [0.8016746, -0.8048338, 0.855911851], [0.9516747, -0.904833734, 0.855911851], [0.901674747, -1.1048336, 0.855911851], [0.851674557, -1.20483375, 0.855911851], [0.8016765, -1.48209739, -0.377147526], [0.8591566, -1.32942724, -0.07714754], [0.848499537, -1.357733, 0.472852439], [0.8683157, -1.30509973, 0.8228524], [0.849503756, -1.35506535, 0.8228524], [0.82996273, -1.40696788, 0.6728524], [0.8307562, -1.40486026, 0.8228524], [0.8025861, -1.47968173, 0.722852349], [0.851674557, -1.30483365, 0.855911851], [0.8016746, -1.40483379, 0.855911851], [0.810666561, -1.45821929, 0.8375844], [0.7365966, -1.6549542, -0.3271475], [0.7147391, -1.71300912, -0.3271475], [0.6516745, -1.71393251, -0.3271475], [0.713220835, -1.71704173, -0.177147523], [0.7926643, -1.50603509, -0.07714754], [0.75521636, -1.605499, 0.02285245], [0.7135627, -1.71613383, -0.07714754], [0.6516745, -1.723434, -0.07714754], [0.7921982, -1.50727344, 0.172852427], [0.7361057, -1.65625811, 0.222852439], [0.7123141, -1.71944976, 0.172852427], [0.6516745, -1.73203087, 0.172852427], [0.711883545, -1.72059369, 0.222852439], [0.6516745, -1.73762345, 0.322852463], [0.706248045, -1.73556232, 0.422852427], [0.601674557, -1.49830079, 0.5728524], [0.773828, -1.5560658, 0.5228524], [0.7016747, -1.55483389, 0.568540931], [0.6516745, -1.51817679, 0.5728524], [0.751674652, -1.54345536, 0.622852445], [0.7016747, -1.53284621, 0.622852445], [0.754996061, -1.60608435, 0.529886246], [0.7683556, -1.570601, 0.5728524], [0.6516745, -1.65483379, 0.543128133], [0.712121248, -1.71996236, 0.5228524], [0.6516745, -1.73415327, 0.5228524], [0.7016747, -1.53644967, 0.722852349], [0.7725756, -1.559392, 0.8228524], [0.6516745, -1.52427125, 0.8228524], [0.6516745, -1.35483384, 0.855911851], [0.751674652, -1.45483375, 0.855911851], [0.601674557, -1.45483375, 0.855911851], [0.751674652, -1.5048337, 0.855911851], [0.501674652, -1.05483389, 0.855911851], [0.501674652, -1.2548337, 0.855911851], [0.201674461, -0.404833853, 0.855911851], [0.00167441368, -1.20483375, 0.855911851], [0.151674509, -1.2548337, 0.855911851], [-0.148325443, -1.24221706, 0.622852445], [-0.148325443, -1.242696, 0.722852349], [-0.148325443, -1.05483389, 0.855911851], [-0.148325443, -1.20483375, 0.855911851], [-0.0483253, -1.2548337, 0.8303511], [-0.261530876, -1.2548337, -0.377147526], [-0.3483255, -1.21776628, -0.3271475], [-0.393480062, -1.2548337, -0.177147523], [-0.2983253, -1.24994874, -0.12714754], [-0.3483255, -1.22679472, -0.12714754], [-0.284608126, -1.2548337, -0.07714754], [-0.3483255, -1.23263764, -0.07714754], [-0.2893417, -1.20483375, 0.622852445], [-0.3483255, -1.20483375, 0.5957494], [-0.398325443, -1.2548337, 0.5699073], [-0.398325443, -1.13967371, 0.8228524], [-0.2983253, -1.1872313, 0.7728524], [-0.20931077, -1.20483375, 0.8228524], [-0.248325348, -1.20483375, 0.7835369], [-0.398325443, -1.05483389, 0.855911851], [-0.248325348, -1.1048336, 0.855911851], [-0.398325443, -1.1048336, 0.855911851], [-0.248325348, -1.15483379, 0.855911851], [-0.2983253, -1.15483379, 0.855911851], [-0.400437355, -1.2548337, -0.377147526], [-0.399121523, -1.2548337, -0.07714754], [-0.5983255, -1.09061, 0.622852445], [-0.498325348, -1.12604523, 0.622852445], [-0.4483254, -1.20007014, 0.5728524], [-0.498325348, -1.160049, 0.5728524], [-0.5983255, -1.20483375, 0.563984036], [-0.4483254, -1.15700221, 0.622852445], [-0.548325539, -1.09799552, 0.8228524], [-0.4483254, -1.14830065, 0.6728524], [-0.548325539, -0.8548338, 0.855911851], [-0.5983255, -1.0048337, 0.855911851], [-0.548325539, -1.05483389, 0.855911851], [-0.498325348, -1.1048336, 0.855911851], [-0.6983254, -1.04479432, 0.8228524], [-0.6983254, 0.345166147, 0.855911851], [-0.748325348, -0.05483383, 0.855911851], [-0.6983254, -0.4548338, 0.855911851], [-0.748325348, -0.7048338, 0.855911851], [-0.6983254, -1.0048337, 0.855911851], [-0.7983254, -1.0048337, 0.827389956], [-0.962184548, -1.0048337, 0.5728524], [-0.8983253, -0.9909652, 0.622852445], [-0.998325348, -1.20483375, 0.550544739], [-0.998325348, -0.9548338, 0.645352364], [-0.9483253, -0.9527561, 0.8228524], [-0.998325348, 0.195166141, 0.855911851], [-0.8983253, -0.904833734, 0.855911851], [0.501674652, -1.677566, -0.377147526], [0.4016745, -1.68358326, -0.277147532], [0.501674652, -1.68698192, -0.22714752], [0.4016745, -1.68527317, -0.177147523], [0.5516746, -1.73253965, -0.377147526], [0.503814459, -1.7548337, -0.377147526], [0.551673651, -1.7697134, -0.377147526], [0.501674652, -1.68860292, -0.07714754], [0.4016745, -1.68862629, -0.07714754], [0.4016745, -1.69326878, -0.0271475315], [0.451674461, -1.70130539, 0.07285246], [0.399904251, -1.72252774, 0.07285246], [0.5516746, -1.70503283, 0.122852445], [0.451674461, -1.70701313, 0.122852445], [0.5516746, -1.70972681, 0.172852427], [0.400208473, -1.72262239, 0.172852427], [0.451674461, -1.70906448, 0.222852439], [0.5516746, -1.7136898, 0.272852451], [0.4661994, -1.45483375, 0.5728524], [0.451674461, -1.44393253, 0.622852445], [0.5516746, -1.487884, 0.5728524], [0.4016745, -1.5048337, 0.552467465], [0.5516746, -1.47639084, 0.622852445], [0.5516746, -1.55483389, 0.5506965], [0.451674461, -1.65483379, 0.5422187], [0.396502256, -1.72147012, 0.5347334], [0.447148561, -1.73721623, 0.5228524], [0.4016757, -1.72307849, 0.5228524], [0.4773276, -1.45483375, 0.8228524], [0.4016745, -1.42258525, 0.8228524], [0.501674652, -1.45863438, 0.722852349], [0.451674461, -1.35483384, 0.855911851], [0.451674461, -1.40483379, 0.855911851], [0.3016746, -1.47174573, -0.377147526], [0.351674557, -1.53462148, -0.377147526], [0.201674461, -1.53269863, -0.377147526], [0.2516744, -1.52639627, -0.277147532], [0.368625164, -1.55483389, -0.177147523], [0.201674461, -1.52944636, -0.177147523], [0.30167532, -1.691988, -0.22714752], [0.315992355, -1.5048337, -0.12714754], [0.3016746, -1.5048337, -0.06995258], [0.2516744, -1.52366948, -0.12714754], [0.3619411, -1.55483389, -0.07714754], [0.2516744, -1.55483389, -0.104746014], [0.351674557, -1.55483389, -0.06408325], [0.319963932, -1.60483384, -0.12714754], [0.201674461, -1.60483384, -0.111529708], [0.30167532, -1.691988, -0.12714754], [0.351674557, -1.66963124, -0.0271475315], [0.2964213, -1.69035459, -0.0271475315], [0.34961772, -1.70689344, 0.02285245], [0.298413515, -1.690974, 0.02285245], [0.248198032, -1.67536163, 0.07285246], [0.344336271, -1.70525146, 0.122852445], [0.247784138, -1.67523313, 0.122852445], [0.351674318, -1.70753288, 0.222852439], [0.201674461, -1.40483379, 0.5651655], [0.201674461, -1.36279035, 0.622852445], [0.351674557, -1.43079114, 0.5728524], [0.3016746, -1.55483389, 0.5513356], [0.3016746, -1.65483379, 0.543156147], [0.201674461, -1.66089725, 0.5228524], [0.351674318, -1.70753288, 0.5228524], [0.201674461, -1.35240459, 0.8228524], [0.3016746, -1.38601923, 0.8228524], [0.3016746, -1.30483365, 0.855911851], [0.351674557, -1.35483384, 0.855911851], [0.2516744, -1.35483384, 0.855911851], [0.0162420273, -1.35483384, -0.3271475], [0.00167441368, -1.42934346, -0.377147526], [0.051674366, -1.42126822, -0.277147532], [0.02474475, -1.45483375, -0.22714752], [0.00167441368, -1.42519212, -0.22714752], [0.00167441368, -1.422008, -0.177147523], [0.151674509, -1.47674727, -0.377147526], [0.151674509, -1.47461247, -0.277147532], [0.101674557, -1.47957158, -0.277147532], [0.051674366, -1.48176336, -0.22714752], [0.151674509, -1.47039318, -0.177147523], [0.051674366, -1.48020959, -0.177147523], [0.03535676, -1.40483379, -0.07714754], [0.101674557, -1.419203, -0.12714754], [0.167984009, -1.45483375, -0.07714754], [0.101674557, -1.47291636, -0.12714754], [0.00167441368, -1.48206472, -0.12714754], [0.101674557, -1.5048337, -0.104051083], [0.00167441368, -1.5048337, -0.108759582], [0.151674509, -1.53100491, -0.12714754], [0.151674509, -1.55483389, -0.108012021], [0.051674366, -1.55483389, -0.11123845], [0.04554224, -1.61235523, -0.0271475315], [0.144566536, -1.64314246, -0.0271475315], [0.147778273, -1.64414072, 0.122852445], [0.09888482, -1.62893963, 0.122852445], [0.151502371, -1.64529872, 0.172852427], [0.101675272, -1.62980723, 0.422852427], [0.00167441368, -1.29283857, 0.622852445], [0.051674366, -1.33961892, 0.5728524], [0.151674509, -1.37096071, 0.5728524], [0.151674509, -1.45483375, 0.5558795], [0.021348238, -1.60483336, 0.5228524], [0.151674509, -1.60483384, 0.5459237], [0.09459472, -1.62760592, 0.5413058], [0.151674271, -1.64535213, 0.5228524], [0.101675272, -1.62980723, 0.5228524], [0.00167441368, -1.29083776, 0.7728524], [0.151674509, -1.34564972, 0.7728524], [0.101674557, -1.30483365, 0.855911851], [-0.1983254, -1.29395986, -0.177147523], [-0.1983254, -1.32233381, -0.377147526], [-0.1983254, -1.31811547, -0.277147532], [-0.022718668, -1.35483384, -0.22714752], [-0.09832525, -1.3755734, -0.3271475], [-0.148325443, -1.38039327, -0.377147526], [-0.148325443, -1.378474, -0.277147532], [-0.09832525, -1.37083626, -0.22714752], [-0.1983254, -1.38135314, -0.22714752], [-0.148325443, -1.3759551, -0.177147523], [-0.0483253, -1.43202686, -0.3271475], [-0.0483253, -1.42915988, -0.22714752], [-0.09832525, -1.43228412, -0.177147523], [-0.148325443, -1.32619762, -0.07714754], [-0.148325443, -1.37384629, -0.12714754], [-0.1983254, -1.37913561, -0.12714754], [-0.148325443, -1.40483379, -0.1052022], [-0.1983254, -1.40483379, -0.108584523], [-0.0483253, -1.42598557, -0.12714754], [-0.09832525, -1.43044424, -0.12714754], [-0.0483253, -1.45483375, -0.105166852], [-0.148325443, -1.5048337, -0.1138466], [-0.131928921, -1.55717874, -0.0271475315], [-0.04832697, -1.58317089, -0.12714754], [-0.04832697, -1.58317089, 0.172852427], [-0.09832525, -1.28505206, 0.5728524], [-0.148325443, -1.30483365, 0.5672661], [-0.1983254, -1.26996446, 0.5728524], [-0.1983254, -1.45483375, 0.5551398], [-0.0483253, -1.5048337, 0.556218], [-0.148325443, -1.5048337, 0.5473622], [-0.148643732, -1.55198193, 0.5228524], [-0.0483253, -1.55483389, 0.545176744], [-0.3483255, -1.27005672, -0.377147526], [-0.366705179, -1.30483365, -0.377147526], [-0.2983253, -1.33017254, -0.377147526], [-0.3483255, -1.33332634, -0.3271475], [-0.364804268, -1.35483384, -0.3271475], [-0.248325348, -1.323693, -0.22714752], [-0.3483255, -1.33219075, -0.177147523], [-0.3946991, -1.47548246, -0.377147526], [-0.2615137, -1.45483375, -0.277147532], [-0.2983253, -1.41759467, -0.22714752], [-0.248325825, -1.52099061, -0.22714752], [-0.3483255, -1.30483365, -0.06305915], [-0.248325348, -1.32154775, -0.12714754], [-0.3483255, -1.33207107, -0.12714754], [-0.3643844, -1.35483384, -0.12714754], [-0.248325348, -1.35483384, -0.105526626], [-0.3483255, -1.35483384, -0.1104196], [-0.2983253, -1.40483379, -0.1126571], [-0.261379242, -1.45483375, -0.12714754], [-0.398088455, -1.47442865, -0.12714754], [-0.248325348, -1.45483375, -0.112538189], [-0.248325348, -1.45483375, -0.0442889631], [-0.403859615, -1.47263455, -0.0450864732], [-0.398323774, -1.4743557, -0.12714754], [-0.398323774, -1.4743557, -0.0271475315], [-0.248325825, -1.52099061, -0.12714754], [-0.348325968, -1.48990011, 0.372852474], [-0.248325348, -1.35483384, 0.5619842], [-0.398325443, -1.40483379, 0.560197353], [-0.3483255, -1.45483375, 0.54858005], [-0.398323774, -1.4743557, 0.5228524], [-0.298498869, -1.5053916, 0.5236875], [-0.4141009, -1.30483365, -0.177147523], [-0.429986715, -1.40483379, -0.22714752], [-0.423896074, -1.35483384, -0.07714754], [-0.430952549, -1.40483379, -0.12714754], [-0.4447887, -1.45990944, -0.07714754], [-0.5483265, -1.42771912, 0.07285246], [-0.498325348, -1.35483384, 0.561178446], [-0.498325348, -1.40483379, 0.5528002], [-0.5548136, -1.42570233, 0.5478611], [-0.5483265, -1.42771912, 0.472852439], [-0.449452639, -1.45845938, 0.5291791], [-0.448325157, -1.45880985, 0.5228524], [-0.6983242, -1.38108444, -0.277147532], [-0.6983254, -1.30483365, 0.5561435], [-0.7986529, -1.34989166, 0.5228524], [-0.9983245, -1.287813, -0.3271475], [-0.848325253, -1.30483365, 0.5446948], [-0.8983245, -1.31890345, 0.5228524], [-1.13487816, 0.345166147, -0.377147526], [-1.14832532, 0.339154273, -0.3271475], [-1.12308741, 0.345166147, -0.277147532], [-1.14832532, 0.340139419, -0.22714752], [-1.16084456, 0.345166147, -0.177147523], [-1.1983254, 0.3333818, -0.177147523], [-1.20204413, -1.22447562, -0.3271475], [-1.19127285, 0.345166147, -0.07714754], [-1.19832337, -1.22563267, -0.12714754], [-1.12797511, 0.295166135, 0.472852439], [-1.14832532, 0.341083646, 0.472852439], [-1.1983254, 0.345166147, 0.456124753], [-1.1292578, 0.345166147, 0.5228524], [-1.07955, 0.295166135, 0.622852445], [-1.11636519, 0.345166147, 0.622852445], [-1.13399947, 0.245166153, 0.5728524], [-1.19773209, 0.195166141, 0.472852439], [-1.18769765, 0.145166144, 0.5228524], [-1.17392969, -0.904833734, 0.5728524], [-1.106684, -0.904833734, 0.622852445], [-1.14832532, -0.88577956, 0.622852445], [-1.076592, -0.9548338, 0.5728524], [-1.1983254, -1.0048337, 0.5591445], [-1.14832532, -1.20483375, 0.546238542], [-1.10440052, -1.25483346, 0.5228524], [-1.19832337, -1.22563267, 0.5228524], [-1.09871519, 0.295166135, 0.722852349], [-1.126285, 0.345166147, 0.7728524], [-1.161584, 0.295166135, 0.8228524], [-1.11046147, 0.245166153, 0.6728524], [-1.14832532, 0.245166153, 0.79156816], [-1.17854893, 0.245166153, 0.8228524], [-1.15465236, 0.145166144, 0.6728524], [-1.1560545, 0.145166144, 0.722852349], [-1.167692, 0.09516616, 0.722852349], [-1.1983254, 0.09516616, 0.786044955], [-1.14832532, -0.8953243, 0.722852349], [-1.09832537, -0.9039685, 0.8228524], [-1.0483253, -0.9292998, 0.7728524], [-1.09832537, 0.295166135, 0.855911851], [-1.14832532, 0.345166147, 0.855911851], [-1.14832532, 0.295166135, 0.855911851], [-1.0483253, 0.245166153, 0.855911851], [-1.14832532, 0.245166153, 0.855911851], [-1.14832532, 0.195166141, 0.855911851], [-1.1983254, 0.195166141, 0.855911851], [-1.14832532, 0.145166144, 0.855911851], [-1.1983254, 0.145166144, 0.855911851], [-1.0483253, -0.00483384728, 0.855911851], [-1.14832532, -0.254833817, 0.855911851], [-1.0483253, -0.5548338, 0.855911851], [-1.0483253, -0.754833758, 0.855911851], [-1.14832532, -0.8048338, 0.855911851], [-1.09832537, -0.8548338, 0.855911851], [-1.1983254, -0.8548338, 0.855911851], [-1.26148272, 0.315643668, -0.3271475], [-1.38976049, -0.0548334122, -0.177147523], [-1.28510082, -0.2048338, -0.3271475], [-1.31682944, -0.2048338, -0.377147526], [-1.28235054, -0.2048338, -0.22714752], [-1.29832542, -0.254833817, -0.3713206], [-1.29832542, -0.2291016, -0.377147526], [-1.34832537, -0.254833817, -0.350685418], [-1.34832537, -0.229608953, -0.3271475], [-1.34832537, -0.225570619, -0.177147523], [-1.37449145, -0.4548338, -0.22714752], [-1.39832544, -0.4548338, -0.233818173], [-1.38375378, -0.5048338, -0.277147532], [-1.37648034, -0.5048338, -0.22714752], [-1.38171291, -0.5548338, -0.277147532], [-1.34832537, -1.12159657, -0.377147526], [-1.39832544, -1.13538, -0.3271475], [-1.29832542, -1.15483379, -0.304789782], [-1.3551755, -1.17686653, -0.281951785], [-1.30305123, -1.19307232, -0.277147532], [-1.298326, -1.19454145, -0.22714752], [-1.26857281, 0.29516685, -0.0271475315], [-1.25094628, 0.346073657, 0.0155518055], [-1.33782291, 0.09516662, -0.07714754], [-1.372448, -0.004832983, 0.02285245], [-1.34832537, -0.181389272, -0.12714754], [-1.29832542, -0.2048338, -0.08944622], [-1.34832537, -0.2048338, -0.09395254], [-1.39832544, -0.2048338, -0.09379768], [-1.34832537, -0.24020195, -0.12714754], [-1.359311, -0.254833817, -0.12714754], [-1.31100786, -0.254833817, -0.07714754], [-1.34832537, -0.222864628, -0.07714754], [-1.39832544, -0.21921593, -0.07714754], [-1.30715919, -0.254833817, -0.0271475315], [-1.34832537, -0.239542425, -0.0271475315], [-1.39832544, -0.223736048, -0.0271475315], [-1.39832544, -0.254833817, -0.00120607018], [-1.39832544, -0.2883941, -0.12714754], [-1.34832537, -0.273329973, -0.07714754], [-1.34832537, -0.281038, -0.0271475315], [-1.363663, -0.304833829, -0.0271475315], [-1.39832544, -0.304833829, -0.0577715933], [-1.3279103, -0.304833829, 0.02285245], [-1.39832544, -0.278425753, 0.02285245], [-1.36342931, -0.354833841, 0.02285245], [-1.25126064, 0.345166057, 0.07285246], [-1.28588545, 0.245166689, 0.172852427], [-1.28588545, 0.245166689, 0.222852439], [-1.34832609, 0.0648329854, 0.172852427], [-1.38785923, -0.0493421853, 0.122852445], [-1.38976049, -0.0548334122, 0.222852439], [-1.39832544, -0.354833841, 0.0471817851], [-1.36731613, -0.404833853, 0.07285246], [-1.39832544, -0.5048338, 0.06066826], [-1.39832544, -0.5048338, 0.07951763], [-1.39832544, -0.555017, 0.07285246], [-1.21796989, 0.295166135, 0.322852463], [-1.25094628, 0.346073657, 0.288929433], [-1.26640546, 0.301426381, 0.275515], [-1.249108, 0.351383, 0.422852427], [-1.2108556, 0.295166135, 0.422852427], [-1.30267656, 0.196672723, 0.2802203], [-1.27895629, 0.145166144, 0.322852463], [-1.28501523, 0.145166144, 0.422852427], [-1.30319512, 0.09516616, 0.422852427], [-1.31287014, 0.0451661646, 0.322852463], [-1.32900929, -0.00483384728, 0.372852474], [-1.38966942, -0.05457008, 0.272852451], [-1.35591567, -0.104833841, 0.322852463], [-1.34164846, -0.104833841, 0.422852427], [-1.395105, -0.2048338, 0.372852474], [-1.38816833, -0.2048338, 0.422852427], [-1.38799191, -0.254833817, 0.322852463], [-1.39495635, -0.254833817, 0.372852474], [-1.39832544, -0.304833829, 0.321138471], [-1.24832535, 0.145166144, 0.465984732], [-1.2084825, 0.09516616, 0.5228524], [-1.24832535, 0.09516616, 0.4778615], [-1.29832542, 0.0451661646, 0.477534384], [-1.21398258, 0.0451661646, 0.5728524], [-1.20210207, 0.0451661646, 0.622852445], [-1.24801242, -0.00483384728, 0.5228524], [-1.34832537, -0.05483383, 0.4778349], [-1.26976812, -0.05483383, 0.5728524], [-1.36833143, -0.104833841, 0.472852439], [-1.29832542, -0.0794481039, 0.5228524], [-1.34832537, -0.111556828, 0.5228524], [-1.42159581, -0.146776468, 0.484096259], [-1.29832542, -0.107244372, 0.622852445], [-1.38104486, -0.254833817, 0.472852439], [-1.36848831, -0.254833817, 0.5228524], [-1.39832544, -0.254833817, 0.5313505], [-1.38226783, -0.304833829, 0.472852439], [-1.3762542, -0.304833829, 0.5228524], [-1.39832544, -0.304833829, 0.5325737], [-1.39832544, -0.310449541, 0.472852439], [-1.39832544, -0.7872545, 0.622852445], [-1.24832535, -0.8481775, 0.622852445], [-1.24832535, -0.887402, 0.5728524], [-1.39832544, -0.904833734, 0.560938954], [-1.39832544, -1.05483389, 0.54870975], [-1.298326, -1.19454145, 0.472852439], [-1.353144, -1.1774981, 0.5228524], [-1.24981463, -1.20962381, 0.5279089], [-1.21524394, 0.145166144, 0.8228524], [-1.2370069, 0.09516616, 0.8228524], [-1.20074761, 0.0451661646, 0.722852349], [-1.25801408, -0.00483384728, 0.6728524], [-1.26186824, -0.00483384728, 0.722852349], [-1.27865541, -0.00483384728, 0.7728524], [-1.29832542, 0.004509121, 0.8228524], [-1.24832535, -0.05483383, 0.6330991], [-1.27475011, -0.05483383, 0.6728524], [-1.28502393, -0.05483383, 0.7728524], [-1.31130075, -0.05483383, 0.8228524], [-1.31502557, -0.104833841, 0.6728524], [-1.352906, -0.104833841, 0.722852349], [-1.33339441, -0.104833841, 0.7728524], [-1.34832537, -0.127734691, 0.6728524], [-1.34832537, -0.107042789, 0.8228524], [-1.4171797, -0.134022444, 0.8228524], [-1.39832544, -0.7882183, 0.7728524], [-1.29832542, -0.829716861, 0.8228524], [-1.24832535, 0.0451661646, 0.855911851], [-1.29832542, -0.104833841, 0.855911851], [-1.34832537, -0.154833823, 0.855911851], [-1.39832544, -0.154833823, 0.855911851], [-1.39832544, -0.2048338, 0.855911851], [-1.39832544, -0.304833829, 0.855911851], [-1.34832537, -0.4548338, 0.855911851], [-1.24832535, -0.8048338, 0.855911851], [-1.42438543, -0.154832929, -0.377147526], [-1.44325888, -0.304833829, -0.3271475], [-1.47523558, -0.301692545, -0.22714752], [-1.47414863, -0.2985534, -0.177147523], [-1.43926024, -0.354833841, -0.377147526], [-1.4308331, -0.354833841, -0.3271475], [-1.44519544, -0.354833841, -0.277147532], [-1.46047425, -0.404833853, -0.377147526], [-1.45601714, -0.404833853, -0.3271475], [-1.43000782, -0.404833853, -0.22714752], [-1.44411135, -0.404833853, -0.177147523], [-1.52667582, -0.450256228, -0.3271475], [-1.44832528, -0.41098696, -0.277147532], [-1.46953845, -0.4548338, -0.277147532], [-1.49832535, -0.4548338, -0.309847057], [-1.44832528, -0.4548338, -0.209162], [-1.54557335, -0.504834056, -0.377147526], [-1.54557335, -0.504834056, -0.3271475], [-1.44832528, -0.5048338, -0.296044677], [-1.49832535, -0.5048338, -0.202028468], [-1.54557335, -0.504834056, -0.177147523], [-1.51925957, -0.5548338, -0.3271475], [-1.561327, -0.550332, -0.365809321], [-1.44832528, -0.5548338, -0.2945011], [-1.49832535, -0.5548338, -0.238405481], [-1.561189, -0.5499337, -0.22714752], [-1.41181815, -0.6048338, -0.3271475], [-1.44832528, -0.6048338, -0.337477416], [-1.49832535, -0.6048338, -0.355597556], [-1.58019793, -0.6048327, -0.377147526], [-1.49832535, -0.6048338, -0.298697472], [-1.537108, -0.6048338, -0.277147532], [-1.5483253, -0.6048338, -0.255831063], [-1.58019793, -0.6048327, -0.22714752], [-1.44832528, -0.6548338, -0.346451759], [-1.48196387, -0.6548338, -0.377147526], [-1.5483253, -0.6215343, -0.377147526], [-1.49832535, -0.6548338, -0.305860579], [-1.5483253, -0.6548338, -0.292319864], [-1.59751081, -0.6548338, -0.277147532], [-1.59751081, -0.6548338, -0.177147523], [-1.44832528, -0.7048338, -0.37452507], [-1.44832528, -0.6854449, -0.3271475], [-1.45944846, -0.7048338, -0.3271475], [-1.48121858, -0.7048338, -0.277147532], [-1.5483253, -0.7048338, -0.297818065], [-1.44832528, -0.7048338, -0.2416063], [-1.447701, -0.7048338, -0.177147523], [-1.44832528, -0.7041952, -0.177147523], [-1.45146036, -0.7048338, -0.177147523], [-1.46141994, -0.754833758, -0.377147526], [-1.49582458, -0.754833758, -0.3271475], [-1.50893044, -0.754833758, -0.277147532], [-1.5483253, -0.7244448, -0.277147532], [-1.61568832, -0.7073322, -0.277147532], [-1.49832535, -0.744978368, -0.22714752], [-1.61584854, -0.707794249, -0.177147523], [-1.49832535, -0.7636592, -0.377147526], [-1.53838015, -0.8048338, -0.377147526], [-1.5483253, -0.7787599, -0.3271475], [-1.5483253, -0.777777851, -0.277147532], [-1.49832535, -0.755840957, -0.22714752], [-1.49832535, -0.7555546, -0.177147523], [-1.58635342, -0.8048338, -0.177147523], [-1.59832525, -0.830353439, -0.3271475], [-1.59832525, -0.8250585, -0.22714752], [-1.59832525, -0.8207063, -0.177147523], [-1.45161366, -1.14688373, -0.377147526], [-1.4983238, -1.13236141, -0.377147526], [-1.4483248, -1.1479063, -0.277147532], [-1.42438543, -0.154832929, -0.12714754], [-1.44169807, -0.204833388, -0.07714754], [-1.44070375, -0.2019614, 0.02285245], [-1.45815969, -0.252375782, 0.02285245], [-1.4265511, -0.304833829, -0.07714754], [-1.45623934, -0.354833841, -0.0271475315], [-1.45607793, -0.404833853, 0.02285245], [-1.49861908, -0.4548338, -0.0271475315], [-1.52109635, -0.434142, 0.02285245], [-1.49832535, -0.460590959, -0.12714754], [-1.54557335, -0.504834056, -0.12714754], [-1.54449689, -0.5017252, -0.07714754], [-1.54499876, -0.5031745, -0.0271475315], [-1.56276679, -0.554490268, -0.12714754], [-1.56225014, -0.5529981, 0.02285245], [-1.58013952, -0.604663849, -0.07714754], [-1.58019793, -0.6048327, -0.0271475315], [-1.44832528, -0.6048338, 0.0143553317], [-1.49832535, -0.6048338, 0.00119400024], [-1.49832535, -0.5793539, 0.02285245], [-1.59751081, -0.6548338, -0.07714754], [-1.4603312, -0.6548338, -0.0271475315], [-1.49832535, -0.640312254, -0.0271475315], [-1.5261687, -0.6548338, -0.0271475315], [-1.59743941, -0.6546276, -0.0271475315], [-1.48058164, -0.6548338, 0.02285245], [-1.5483253, -0.6548338, -0.0178205371], [-1.44832528, -0.700475037, -0.12714754], [-1.49832535, -0.7046537, -0.12714754], [-1.59832549, -0.657186449, -0.12714754], [-1.44832528, -0.7048338, -0.0852747858], [-1.49832535, -0.694948733, -0.07714754], [-1.5483253, -0.7048338, -0.102565974], [-1.5483253, -0.679622233, -0.07714754], [-1.61305714, -0.69973284, -0.09587076], [-1.5483253, -0.6658004, -0.0271475315], [-1.49832535, -0.664144456, 0.02285245], [-1.5483253, -0.7048338, 0.00225245953], [-1.5483253, -0.686642349, 0.02285245], [-1.56760645, -0.7048338, 0.02285245], [-1.53391087, -0.754833758, -0.12714754], [-1.5483253, -0.720892966, -0.12714754], [-1.49832535, -0.7075996, -0.07714754], [-1.53953934, -0.754833758, -0.07714754], [-1.49832535, -0.712332666, -0.0271475315], [-1.59832525, -0.7427409, 0.02285245], [-1.5483253, -0.7799688, -0.12714754], [-1.59832525, -0.7889051, -0.12714754], [-1.5483253, -0.7782373, -0.07714754], [-1.59832525, -0.775134, -0.0271475315], [-1.42438543, -0.154832929, 0.222852439], [-1.44155037, -0.204406619, 0.222852439], [-1.47568154, -0.3029806, 0.07285246], [-1.474153, -0.298566282, 0.172852427], [-1.49337792, -0.354089379, 0.07285246], [-1.49246168, -0.3514433, 0.172852427], [-1.44832528, -0.404833853, 0.03291765], [-1.47910047, -0.404833853, 0.122852445], [-1.44832528, -0.4548338, 0.04843098], [-1.51403463, -0.4137475, 0.122852445], [-1.51453626, -0.41519624, 0.222852439], [-1.44832528, -0.5548338, 0.034938693], [-1.49832535, -0.5548338, 0.0393131673], [-1.49832535, -0.5548338, 0.105616719], [-1.44832528, -0.567622244, 0.07285246], [-1.48656416, -0.6048338, 0.07285246], [-1.49832535, -0.6048338, 0.09899095], [-1.58019793, -0.6048327, 0.122852445], [-1.49832535, -0.6548338, 0.0460461676], [-1.5483253, -0.6548338, 0.05799392], [-1.61305714, -0.69973284, 0.0661192238], [-1.59832549, -0.657186449, 0.07285246], [-1.4983238, -1.13236141, 0.07285246], [-1.42328787, -0.151663333, 0.272852451], [-1.474092, -0.2983899, 0.272852451], [-1.40442562, -0.304833829, 0.372852474], [-1.47909093, -0.404833853, 0.272852451], [-1.44832528, -0.404833853, 0.316312045], [-1.44832528, -0.3710003, 0.372852474], [-1.50721943, -0.3940646, 0.372852474], [-1.5109483, -0.4048339, 0.422852427], [-1.468231, -0.4548338, 0.322852463], [-1.52826047, -0.4548328, 0.372852474], [-1.54557335, -0.504834056, 0.322852463], [-1.42438543, -0.154832929, 0.5228524], [-1.40757787, -0.2048338, 0.472852439], [-1.44169807, -0.204833388, 0.5228524], [-1.44169807, -0.204833388, 0.622852445], [-1.4299264, -0.254833817, 0.5728524], [-1.42197049, -0.304833829, 0.5728524], [-1.44832528, -0.304833829, 0.608052135], [-1.476323, -0.304832876, 0.622852445], [-1.4259038, -0.354833841, 0.5728524], [-1.44832528, -0.354833841, 0.6044065], [-1.49363565, -0.354833484, 0.622852445], [-1.44832528, -0.378567278, 0.472852439], [-1.44832528, -0.382489979, 0.5728524], [-1.5109483, -0.4048339, 0.5728524], [-1.612623, -0.6984791, 0.622852445], [-1.5483253, -0.754833758, 0.570573568], [-1.59832525, -0.8048338, 0.556025147], [-1.59832525, -1.0048337, 0.5451194], [-1.49832535, -1.05483389, 0.5367675], [-1.58686578, -1.10483313, 0.472852439], [-1.49832535, -1.10057855, 0.5228524], [-1.5483253, -1.08375621, 0.5228524], [-1.42438543, -0.154832929, 0.722852349], [-1.42431748, -0.154636621, 0.8228524], [-1.4413116, -0.203716874, 0.8228524], [-1.49363565, -0.354833484, 0.7728524], [-1.498327, -0.3683827, 0.8228524], [-1.5453403, -0.5041608, 0.8228524], [-1.59751081, -0.6548338, 0.722852349], [-1.59743917, -0.6546268, 0.8228524], [-1.58500862, -0.7048338, 0.8228524], [-1.49832535, -0.738791168, 0.8228524], [-1.45786691, -0.251530051, 0.83498776], [-1.49832535, -0.4548338, 0.855911851], [-1.49832535, -0.5548338, 0.855911851], [-1.5483253, -0.6548338, 0.855911851], [-1.44832528, -0.7048338, 0.855911851], [-1.61482358, -0.7048343, -0.177147523], [-1.666761, -0.854834259, -0.377147526], [-1.701386, -0.9548339, -0.277147532], [-1.7186985, -1.00483418, -0.377147526], [-1.7186985, -1.00483418, -0.177147523], [-1.72806263, -1.06093454, -0.377147526], [-1.72806263, -1.06093454, -0.277147532], [-1.69832408, -1.07018042, -0.177147523], [-1.63213611, -0.754834354, 0.02285245], [-1.64944839, -0.804833949, -0.07714754], [-1.6491133, -0.8038661, -0.0271475315], [-1.666761, -0.854834259, -0.12714754], [-1.72806263, -1.06093454, -0.0271475315], [-1.61482358, -0.7048343, 0.07285246], [-1.666761, -0.854834259, 0.07285246], [-1.72806263, -1.06093454, 0.322852463], [-1.69832408, -1.07018042, 0.372852474], [-1.61412859, -0.702827156, 0.5728524], [-1.63213611, -0.754834354, 0.5228524], [-1.66478729, -0.8491338, 0.537091851], [-1.7186985, -1.00483418, 0.472852439], [-1.7186985, -1.00483418, 0.5228524], [-1.64832532, -1.0048337, 0.5450784], [-1.72648406, -1.06142545, 0.5228524], [-1.64995146, -1.08521962, 0.5228524], [-1.69873285, -1.07005334, 0.5228524], [1.62349939, 0.895168543, -0.22714752], [1.60460424, 0.902607262, -0.221856728], [1.65294313, 0.883576751, -0.377147526], [1.67788315, 0.8451654, -0.277147532], [1.67788315, 0.8451654, -0.177147523], [1.65294313, 0.883576751, -0.177147523], [1.60167456, 0.8451661, -0.212196812], [1.65784931, 0.7919532, -0.177147523], [1.640234, 0.7451656, -0.3271475], [1.65167475, 0.775553644, -0.22714752], [1.640234, 0.7451656, -0.22714752], [1.60167456, 0.7451661, -0.215046778], [1.62140894, 0.6951654, -0.377147526], [1.60167456, 0.733743668, -0.3271475], [1.60167456, 0.730849266, -0.277147532], [1.60167456, 0.7151509, -0.22714752], [1.60285854, 0.645894647, -0.177147523], [1.60442972, 0.902675867, -0.07714754], [1.67462969, 0.8365235, -0.160178155], [1.67462969, 0.8365235, -0.0831128955], [1.67702723, 0.8428918, -0.0271475315], [1.60501909, 0.9024437, -0.0271475315], [1.67729807, 0.8436108, 0.02285245], [1.65905857, 0.795165062, -0.07714754], [1.60167456, 0.795166135, -0.0953293741], [1.65905857, 0.795165062, -0.0271475315], [1.640234, 0.7451656, -0.07714754], [1.60167456, 0.7451661, -0.108973056], [1.62140894, 0.6951654, -0.07714754], [1.60605288, 0.9020369, 0.07285246], [1.67675686, 0.842173338, 0.322852463], [1.6772275, 0.8434237, 0.622852445], [1.65905857, 0.795165062, 0.5728524], [1.61020422, 0.900402546, 0.6728524], [1.65518141, 0.8826957, 0.7728524], [1.67750645, 0.844164, 0.8228524], [1.65905857, 0.795165062, 0.722852349], [1.6588645, 0.794651, 0.8228524], [1.60460424, 0.902607262, 0.8388866], [1.65167427, 0.8451661, 0.855911851], [1.65814209, 0.792731047, 0.833594441], [1.60167456, 0.7451661, 0.855911851], [1.40410376, 0.9451661, -0.377147526], [1.40167451, 0.9514013, -0.377147526], [1.40657, 0.9805701, -0.3271475], [1.45539, 0.9613506, -0.277147532], [1.47987032, 0.9517128, -0.22714752], [1.40682364, 0.98047024, -0.22714752], [1.55167675, 0.923443854, -0.377147526], [1.55167675, 0.923443854, -0.3271475], [1.54301643, 0.8951661, -0.3271475], [1.50167513, 0.9431287, -0.377147526], [1.486835, 0.8951661, -0.377147526], [1.55167675, 0.923443854, -0.277147532], [1.44790363, 0.8951661, -0.277147532], [1.4959631, 0.8951661, -0.22714752], [1.48615742, 0.8451661, -0.3271475], [1.48719931, 0.795166135, -0.377147526], [1.430335, 0.795166135, -0.3271475], [1.41041279, 0.795166135, -0.177147523], [1.4296937, 0.7451661, -0.3271475], [1.42665148, 0.7451661, -0.277147532], [1.50167465, 0.7451661, -0.207886], [1.50167465, 0.7294222, -0.3271475], [1.45167446, 0.722579241, -0.277147532], [1.4196701, 0.6951661, -0.177147523], [1.40167451, 0.6951661, -0.20069164], [1.55167437, 0.6754312, -0.377147526], [1.50167465, 0.676860332, -0.377147526], [1.60118008, 0.641436, -0.277147532], [1.50167465, 0.6739517, -0.22714752], [1.55167437, 0.667907953, -0.177147523], [1.45167446, 0.689033866, -0.177147523], [1.58256578, 0.5919956, -0.377147526], [1.58363056, 0.5948235, -0.3271475], [1.55167437, 0.6058116, -0.377147526], [1.55167437, 0.61033535, -0.3271475], [1.50167465, 0.600770354, -0.377147526], [1.50167465, 0.6224787, -0.3271475], [1.45167446, 0.5963293, -0.377147526], [1.40167451, 0.627364337, -0.377147526], [1.40167451, 0.638418436, -0.3271475], [1.50167465, 0.6379856, -0.277147532], [1.45167446, 0.642372131, -0.277147532], [1.56493425, 0.545164, -0.3271475], [1.45423222, 0.961806357, -0.12714754], [1.40673733, 0.9805042, -0.12714754], [1.48403788, 0.950072, 0.02285245], [1.55167437, 0.8951661, -0.133952171], [1.50386381, 0.9422671, -0.07714754], [1.570921, 0.8451661, -0.12714754], [1.50167465, 0.8451661, -0.155194223], [1.55167437, 0.838088036, -0.12714754], [1.50167465, 0.795166135, -0.11444968], [1.43310881, 0.7451661, -0.12714754], [1.55167437, 0.7451661, -0.105136007], [1.50167465, 0.6927076, -0.12714754], [1.55167484, 0.5099482, -0.0271475315], [1.54611015, 0.495166719, 0.122852445], [1.46969748, 0.955717742, 0.272852451], [1.55391932, 0.922561, 0.5228524], [1.56439066, 0.5437204, 0.5728524], [1.50241518, 0.9428375, 0.8228524], [1.600349, 0.6392296, 0.8228524], [1.58315229, 0.593553066, 0.722852349], [1.56438065, 0.5436943, 0.722852349], [1.54403925, 0.489666045, 0.8228524], [1.50990677, 0.399008632, 0.8228524], [1.40167451, 0.9451661, 0.855911851], [1.55167437, 0.8951661, 0.855911851], [1.50167465, 0.8951661, 0.855911851], [1.40167451, 0.795166135, 0.855911851], [1.45167446, 0.645166159, 0.855911851], [1.50167465, 0.545166135, 0.855911851], [1.45167446, 0.395166129, 0.855911851], [1.35301208, 1.0016551, -0.3271475], [1.30458736, 1.02071893, -0.277147532], [1.2547617, 1.04033458, -0.277147532], [1.260809, 1.03795385, -0.22714752], [1.3016746, 0.994654536, -0.22714752], [1.38804936, 0.8951661, -0.3271475], [1.3494544, 0.8451661, -0.377147526], [1.34865141, 0.8451661, -0.3271475], [1.33592367, 0.795166135, -0.377147526], [1.3330152, 0.795166135, -0.3271475], [1.34977746, 0.795166135, -0.277147532], [1.37763739, 0.795166135, -0.22714752], [1.32492661, 0.7451661, -0.3271475], [1.36072826, 0.7451661, -0.22714752], [1.38784671, 0.7451661, -0.177147523], [1.34106493, 0.6951661, -0.377147526], [1.35945368, 0.9991191, -0.07714754], [1.35677958, 1.00017166, 0.172852427], [1.23044443, 1.0499078, 0.372852474], [1.365428, 0.996767163, 0.722852349], [1.2529912, 1.0410316, 0.8228524], [1.3540659, 1.00124025, 0.832474], [1.25167441, 0.9951661, 0.855911851], [1.3016746, 0.9451661, 0.855911851], [1.10851073, 1.09791124, -0.12714754], [1.17224407, 1.045166, 0.02285245], [1.106988, 1.0985105, 0.122852445], [1.00435925, 1.13891387, 0.322852463], [1.00464129, 1.138803, 0.8228524], [1.15344262, 1.08022237, 0.722852349], [1.10399151, 1.09969032, 0.8228524], [1.00167441, 1.09516609, 0.855911851], [1.00167441, 1.045166, 0.855911851], [1.10167456, 0.8451661, 0.855911851], [0.853147268, 1.19844365, 0.472852439], [0.906475067, 1.17744935, 0.7728524], [0.9566133, 1.15771079, 0.8291472], [0.851674557, 1.14516616, 0.855911851], [0.8016746, 0.9451661, 0.855911851], [0.607465744, 1.29516482, 0.02285245], [0.60693, 1.29537559, 0.322852463], [0.6516745, 1.24516606, 0.418902367], [0.7528193, 1.23794127, 0.372852474], [0.6585083, 1.27507007, 0.722852349], [0.6943555, 1.26095748, 0.8228524], [0.5544276, 1.31604481, 0.372852474], [0.501674652, 1.29978681, 0.422852427], [0.563149452, 1.3126111, 0.472852439], [0.509088755, 1.333894, 0.5728524], [0.4016745, 1.34516609, 0.668019652], [0.5372093, 1.295166, 0.8228524], [0.4016745, 1.34371519, 0.8228524], [0.451674461, 1.295166, 0.855911851], [0.5516746, 1.24516606, 0.855911851], [0.4016745, 1.24516606, 0.855911851], [0.5516746, 1.19516611, 0.855911851], [0.451674461, 1.045166, 0.855911851], [0.2264452, 1.44516635, -0.07714754], [0.2264452, 1.44516635, 0.222852439], [0.303702116, 1.41475129, 0.322852463], [0.2533512, 1.43457365, 0.372852474], [0.30862236, 1.41281438, 0.3883042], [0.201674461, 1.407078, 0.422852427], [0.352771759, 1.39543366, 0.372852474], [0.3016746, 1.3709662, 0.422852427], [0.2516744, 1.38233185, 0.422852427], [0.351674557, 1.34438324, 0.422852427], [0.208930492, 1.45206141, 0.472852439], [0.309387684, 1.412513, 0.472852439], [0.201674461, 1.42367315, 0.6728524], [0.274404764, 1.39516616, 0.8228524], [0.351674557, 1.36191821, 0.7728524], [0.351674557, 1.34516609, 0.855911851], [0.201674461, 1.045166, 0.855911851], [0.1527605, 1.47417474, 0.322852463], [0.10211277, 1.49411392, 0.372852474], [0.08457255, 1.44516611, 0.422852427], [0.108480692, 1.491607, 0.472852439], [0.00167441368, 1.50671482, 0.6728524], [0.151674509, 1.44521308, 0.8228524], [0.051674366, 1.48500061, 0.8228524], [0.101674557, 1.44516611, 0.855911851], [0.151674509, 1.34516609, 0.855911851], [-0.1483252, 1.5927074, -0.177147523], [-0.0483262539, 1.55333948, 0.372852474], [-0.0483253, 1.493911, 0.422852427], [-0.140826225, 1.589755, 0.472852439], [-0.1983254, 1.58521175, 0.5228524], [-0.148325443, 1.565532, 0.622852445], [-0.0777232647, 1.56491256, 0.722852349], [-0.1983254, 1.58227444, 0.7728524], [-0.141733646, 1.59011245, 0.8228524], [-0.1983254, 1.545166, 0.855911851], [-0.09832525, 1.49516606, 0.855911851], [-0.0483253, 0.645166159, 0.855911851], [-0.1983254, 0.395166129, 0.855911851], [-0.34432888, 1.66987062, -0.3271475], [-0.398325443, 1.64516592, -0.300116], [-0.398325443, 1.66296816, -0.277147532], [-0.339368343, 1.667918, -0.2307083], [-0.390649319, 1.68810654, -0.22714752], [-0.340331316, 1.668297, -0.177147523], [-0.398325443, 1.64774346, -0.177147523], [-0.246206045, 1.63124156, -0.22714752], [-0.398325443, 1.64457679, -0.177147523], [-0.3310907, 1.545166, -0.377147526], [-0.3483255, 1.56366181, -0.377147526], [-0.3483255, 1.56958461, -0.3271475], [-0.398325443, 1.55859661, -0.377147526], [-0.3310542, 1.545166, -0.277147532], [-0.3483255, 1.56718206, -0.277147532], [-0.398325443, 1.55921412, -0.277147532], [-0.3483255, 1.57189584, -0.22714752], [-0.330325365, 1.545166, -0.177147523], [-0.328769445, 1.49516606, -0.3271475], [-0.398325443, 1.52473187, -0.377147526], [-0.3683207, 1.49516606, -0.22714752], [-0.398325443, 1.52493382, -0.177147523], [-0.3483255, 1.46933007, -0.277147532], [-0.3483255, 1.404099, -0.377147526], [-0.2983253, 1.38229394, -0.3271475], [-0.398325443, 1.64516592, -0.0966121256], [-0.248068333, 1.6319747, -0.12714754], [-0.398325443, 1.63833117, -0.12714754], [-0.325189829, 1.595166, -0.0271475315], [-0.3483255, 1.595166, -0.0485873222], [-0.3483255, 1.62837768, -0.0271475315], [-0.398325443, 1.63792253, -0.0271475315], [-0.3260336, 1.595166, 0.02285245], [-0.3483255, 1.62638974, 0.02285245], [-0.398325443, 1.63597322, 0.02285245], [-0.3483255, 1.56528878, -0.12714754], [-0.3295064, 1.545166, -0.07714754], [-0.398325443, 1.5590899, -0.07714754], [-0.2744112, 1.545166, -0.0271475315], [-0.32806325, 1.49516606, -0.12714754], [-0.3256259, 1.49516606, 0.02285245], [-0.3483255, 1.468874, -0.07714754], [-0.3483255, 1.46759033, 0.02285245], [-0.2983253, 1.38047624, -0.07714754], [-0.398325443, 1.64516592, 0.11337018], [-0.398325443, 1.65655661, 0.122852445], [-0.339368343, 1.667918, 0.149438649], [-0.342399836, 1.66911149, 0.172852427], [-0.398325443, 1.64516592, 0.155419856], [-0.398325443, 1.595166, 0.03708604], [-0.247776031, 1.63185978, 0.172852427], [-0.272547722, 1.595166, 0.172852427], [-0.321375847, 1.545166, 0.07285246], [-0.3483255, 1.57202721, 0.07285246], [-0.3228352, 1.545166, 0.122852445], [-0.3483255, 1.56878877, 0.122852445], [-0.398325443, 1.559875, 0.172852427], [-0.280537128, 1.545166, 0.222852439], [-0.2983253, 1.59089851, 0.222852439], [-0.3846345, 1.545166, 0.222852439], [-0.398325443, 1.52361345, 0.07285246], [-0.326635838, 1.49516606, 0.172852427], [-0.278864622, 1.49516606, 0.222852439], [-0.327271461, 1.49516606, 0.222852439], [-0.398325443, 1.52434754, 0.222852439], [-0.3483255, 1.46792054, 0.172852427], [-0.3483255, 1.46818972, 0.222852439], [-0.2983253, 1.385386, 0.07285246], [-0.34092617, 1.66853118, 0.272852451], [-0.334452152, 1.66598248, 0.322852463], [-0.398325443, 1.64516592, 0.3295116], [-0.3938005, 1.68934679, 0.372852474], [-0.398325443, 1.66135716, 0.422852427], [-0.247914314, 1.63191414, 0.272852451], [-0.3483255, 1.63349032, 0.272852451], [-0.2983253, 1.595166, 0.283741027], [-0.290345669, 1.64861846, 0.322852463], [-0.3097191, 1.595166, 0.322852463], [-0.3483255, 1.64422584, 0.322852463], [-0.2457385, 1.63105726, 0.372852474], [-0.289598942, 1.64832473, 0.372852474], [-0.3483255, 1.64419937, 0.372852474], [-0.398325443, 1.63880181, 0.422852427], [-0.292340279, 1.545166, 0.272852451], [-0.398325443, 1.56090379, 0.272852451], [-0.384150982, 1.545166, 0.322852463], [-0.398325443, 1.56038332, 0.372852474], [-0.248325348, 1.56953645, 0.422852427], [-0.2983253, 1.57821441, 0.422852427], [-0.305621862, 1.545166, 0.422852427], [-0.383396626, 1.545166, 0.422852427], [-0.380959034, 1.49516606, 0.272852451], [-0.280402184, 1.49516606, 0.322852463], [-0.380877733, 1.49516606, 0.322852463], [-0.277763367, 1.49516606, 0.422852427], [-0.398325443, 1.46135807, 0.272852451], [-0.398325443, 1.46222162, 0.372852474], [-0.3483255, 1.39763856, 0.422852427], [-0.2983253, 1.38262248, 0.422852427], [-0.374838352, 1.64516592, 0.472852439], [-0.3898635, 1.68779707, 0.472852439], [-0.3836944, 1.64516592, 0.5228524], [-0.398325443, 1.6566515, 0.5228524], [-0.339563131, 1.6679945, 0.5728524], [-0.398325443, 1.63199806, 0.472852439], [-0.2983253, 1.595166, 0.4787449], [-0.248325348, 1.60232925, 0.5728524], [-0.398325443, 1.595166, 0.54165554], [-0.3483255, 1.63228345, 0.622852445], [-0.311375856, 1.545166, 0.472852439], [-0.398325443, 1.545166, 0.437754184], [-0.3483255, 1.545166, 0.5133827], [-0.306398153, 1.49516606, 0.472852439], [-0.3483255, 1.49516606, 0.4921262], [-0.2983253, 1.44349885, 0.472852439], [-0.398325443, 1.44005156, 0.472852439], [-0.3483255, 1.63276958, 0.8228524], [-0.2983253, 1.34516609, 0.855911851], [-0.398325443, 0.395166129, 0.855911851], [-0.5380862, 1.74614978, -0.277147532], [-0.535588264, 1.74516678, -0.22714752], [-0.422806978, 1.70076632, -0.377147526], [-0.425229073, 1.70171976, -0.3271475], [-0.44727087, 1.71039748, -0.377147526], [-0.4481275, 1.71073484, -0.3271475], [-0.446849585, 1.71023154, -0.377147526], [-0.449391127, 1.71123219, -0.3271475], [-0.535752535, 1.69516611, -0.377147526], [-0.52966094, 1.69516611, -0.3271475], [-0.448186636, 1.710758, -0.277147532], [-0.5353708, 1.69516611, -0.277147532], [-0.498325348, 1.69516611, -0.236494854], [-0.498324633, 1.73049641, -0.22714752], [-0.4478197, 1.71061349, -0.177147523], [-0.4511931, 1.71194148, -0.177147523], [-0.498325348, 1.69516611, -0.209876835], [-0.5260639, 1.69516611, -0.177147523], [-0.441401, 1.70808673, -0.377147526], [-0.5351455, 1.64516592, -0.377147526], [-0.441208124, 1.70801067, -0.277147532], [-0.441691637, 1.70820093, -0.22714752], [-0.498325348, 1.68561983, -0.22714752], [-0.53512, 1.64516592, -0.22714752], [-0.4348948, 1.595166, -0.377147526], [-0.498325348, 1.608346, -0.377147526], [-0.498325348, 1.60860538, -0.3271475], [-0.4483254, 1.55844, -0.3271475], [-0.498325348, 1.57585907, -0.3271475], [-0.5983255, 1.56387067, -0.3271475], [-0.4483254, 1.5201509, -0.177147523], [-0.5483248, 1.75018072, 0.02285245], [-0.424830675, 1.70156288, -0.12714754], [-0.447391272, 1.71044469, -0.07714754], [-0.450799227, 1.71178651, -0.07714754], [-0.4212842, 1.700167, -0.0271475315], [-0.5276332, 1.69516611, -0.0271475315], [-0.4217806, 1.70036244, 0.02285245], [-0.44722414, 1.71037889, 0.02285245], [-0.454483271, 1.71323681, 0.02285245], [-0.520637751, 1.69516611, 0.02285245], [-0.440583467, 1.70776463, 0.02285245], [-0.5341706, 1.64516592, 0.02285245], [-0.498325348, 1.6087122, -0.07714754], [-0.4483254, 1.61155915, -0.0271475315], [-0.5673454, 1.595166, -0.0271475315], [-0.4483254, 1.61322165, 0.02285245], [-0.4483254, 1.5588479, -0.12714754], [-0.4483254, 1.55887532, -0.07714754], [-0.498325348, 1.57588625, -0.07714754], [-0.5983255, 1.56368423, -0.07714754], [-0.498325348, 1.57522941, -0.0271475315], [-0.548325539, 1.57074738, -0.0271475315], [-0.4483254, 1.51914954, -0.07714754], [-0.4483254, 1.51861835, 0.02285245], [-0.5983255, 1.52189946, 0.02285245], [-0.578496933, 1.762059, 0.07285246], [-0.598324537, 1.769865, 0.07285246], [-0.5483248, 1.75018072, 0.122852445], [-0.536285639, 1.7454412, 0.172852427], [-0.5359051, 1.74529123, 0.222852439], [-0.598324537, 1.769865, 0.222852439], [-0.4287448, 1.70310426, 0.07285246], [-0.445534229, 1.70971394, 0.07285246], [-0.451005459, 1.71186781, 0.07285246], [-0.4229343, 1.70081639, 0.122852445], [-0.491287947, 1.72772622, 0.122852445], [-0.5460341, 1.749279, 0.122852445], [-0.5845885, 1.69516611, 0.122852445], [-0.422165632, 1.70051384, 0.172852427], [-0.447030544, 1.71030283, 0.172852427], [-0.459987879, 1.715404, 0.172852427], [-0.498325348, 1.69516611, 0.135897011], [-0.546036243, 1.74927974, 0.172852427], [-0.584625244, 1.69516611, 0.172852427], [-0.420577526, 1.69988871, 0.222852439], [-0.498325348, 1.69516611, 0.2180765], [-0.498325348, 1.70234346, 0.222852439], [-0.5983255, 1.73772669, 0.222852439], [-0.441210747, 1.70801163, 0.07285246], [-0.5844958, 1.64516592, 0.07285246], [-0.498325348, 1.69227457, 0.122852445], [-0.440118551, 1.70758176, 0.172852427], [-0.439196825, 1.70721889, 0.222852439], [-0.5845978, 1.64516592, 0.222852439], [-0.498325348, 1.60912776, 0.07285246], [-0.548325539, 1.60899591, 0.07285246], [-0.5983255, 1.62596774, 0.07285246], [-0.498325348, 1.60911822, 0.172852427], [-0.4483254, 1.61996174, 0.222852439], [-0.4483254, 1.55911827, 0.07285246], [-0.548325539, 1.56979418, 0.07285246], [-0.498325348, 1.5750432, 0.122852445], [-0.5983255, 1.56461763, 0.122852445], [-0.4483254, 1.55941939, 0.172852427], [-0.548325539, 1.57108474, 0.172852427], [-0.4483254, 1.51942754, 0.122852445], [-0.498325348, 1.51130223, 0.172852427], [-0.498325348, 1.51379275, 0.222852439], [-0.5693791, 1.49516606, 0.222852439], [-0.5440109, 1.74848247, 0.272852451], [-0.59605, 1.7689693, 0.272852451], [-0.537641048, 1.74597454, 0.322852463], [-0.5475571, 1.74987841, 0.322852463], [-0.5466161, 1.7495079, 0.333924323], [-0.5646467, 1.75660634, 0.372852474], [-0.5899067, 1.76655078, 0.364904374], [-0.598324537, 1.769865, 0.372852474], [-0.5466161, 1.7495079, 0.411780566], [-0.444006681, 1.70911241, 0.272852451], [-0.498325348, 1.69516611, 0.226782948], [-0.5983255, 1.73777342, 0.272852451], [-0.4175694, 1.69870448, 0.322852463], [-0.498325348, 1.69516611, 0.317388982], [-0.498325348, 1.70321012, 0.322852463], [-0.546004057, 1.7492671, 0.322852463], [-0.4129238, 1.69687533, 0.372852474], [-0.4465499, 1.71011353, 0.372852474], [-0.493232727, 1.69516611, 0.372852474], [-0.58443, 1.69516611, 0.372852474], [-0.4429922, 1.708713, 0.404716164], [-0.495453358, 1.72936606, 0.422852427], [-0.584325552, 1.69516611, 0.422852427], [-0.4420123, 1.70832729, 0.272852451], [-0.4483254, 1.68378186, 0.322852463], [-0.5844488, 1.64516592, 0.322852463], [-0.4483254, 1.68204, 0.372852474], [-0.5847883, 1.64516592, 0.372852474], [-0.407254219, 1.64516592, 0.422852427], [-0.498325348, 1.68510628, 0.422852427], [-0.498325348, 1.60909939, 0.322852463], [-0.498325348, 1.60923338, 0.372852474], [-0.548325539, 1.60870314, 0.372852474], [-0.548325539, 1.60884786, 0.422852427], [-0.5983255, 1.56740332, 0.272852451], [-0.4483254, 1.55946422, 0.322852463], [-0.548325539, 1.57291651, 0.372852474], [-0.5983255, 1.56710839, 0.372852474], [-0.4483254, 1.55941939, 0.422852427], [-0.548325539, 1.57381558, 0.422852427], [-0.4483254, 1.52087784, 0.272852451], [-0.4483254, 1.52144074, 0.322852463], [-0.422357082, 1.49516606, 0.372852474], [-0.498325348, 1.515578, 0.372852474], [-0.4483254, 1.5213511, 0.422852427], [-0.46352458, 1.44516611, 0.372852474], [-0.46677804, 1.44516611, 0.422852427], [-0.548325539, 1.47549081, 0.422852427], [-0.540732861, 1.74719191, 0.472852439], [-0.5421977, 1.74776864, 0.5228524], [-0.598324537, 1.769865, 0.5228524], [-0.4429922, 1.708713, 0.4538544], [-0.5983255, 1.69516611, 0.436757356], [-0.5983255, 1.73764348, 0.472852439], [-0.4429922, 1.708713, 0.511204243], [-0.498325348, 1.69516611, 0.4913223], [-0.545940638, 1.74924207, 0.5228524], [-0.5983255, 1.73212481, 0.5228524], [-0.548325539, 1.69516611, 0.566296], [-0.548325539, 1.71498871, 0.5728524], [-0.5983255, 1.69516611, 0.5554426], [-0.420046329, 1.64516592, 0.472852439], [-0.4414022, 1.70808721, 0.472852439], [-0.5983255, 1.64516592, 0.436968237], [-0.548325539, 1.64516592, 0.5175655], [-0.4483254, 1.68192434, 0.5728524], [-0.4483254, 1.67356086, 0.622852445], [-0.498325348, 1.683444, 0.622852445], [-0.498325348, 1.595166, 0.4372969], [-0.548325539, 1.595166, 0.4383873], [-0.4483254, 1.595166, 0.5157372], [-0.548325539, 1.545166, 0.490146846], [-0.5983255, 1.545166, 0.48270753], [-0.4483254, 1.49516606, 0.487425417], [-0.498325348, 1.49516606, 0.475655764], [-0.518780947, 1.69516611, 0.722852349], [-0.548325539, 1.69516611, 0.823743939], [-0.4483254, 1.64516592, 0.855911851], [-0.498325348, 1.64516592, 0.855911851], [-0.4483254, 1.595166, 0.855911851], [-0.5983255, 1.295166, 0.855911851], [-0.548325539, 0.645166159, 0.855911851], [-0.789599657, 1.84516668, -0.22714752], [-0.748325348, 1.6754024, -0.377147526], [-0.748325348, 1.6748836, -0.22714752], [-0.7983254, 1.66823411, -0.177147523], [-0.6199577, 1.595166, -0.377147526], [-0.6483253, 1.62340713, -0.3271475], [-0.6983254, 1.61595774, -0.22714752], [-0.6483253, 1.62341881, -0.177147523], [-0.6549071, 1.545166, -0.22714752], [-0.748325348, 1.67447376, -0.07714754], [-0.7983254, 1.66629744, -0.0271475315], [-0.748325348, 1.67281961, 0.02285245], [-0.7983254, 1.612931, -0.12714754], [-0.6483253, 1.62205648, -0.07714754], [-0.6983254, 1.615663, -0.07714754], [-0.6983247, 1.80923343, 0.122852445], [-0.6345514, 1.69516611, 0.222852439], [-0.6983254, 1.61502242, 0.07285246], [-0.6483253, 1.62276459, 0.122852445], [-0.6983254, 1.61844492, 0.222852439], [-0.789599657, 1.84516668, 0.372852474], [-0.6983247, 1.80923343, 0.372852474], [-0.7483244, 1.82891726, 0.422852427], [-0.6356497, 1.74516606, 0.322852463], [-0.6483251, 1.78954935, 0.322852463], [-0.6344768, 1.69516611, 0.322852463], [-0.748325348, 1.67520022, 0.322852463], [-0.7983254, 1.67101383, 0.322852463], [-0.748325348, 1.67500353, 0.372852474], [-0.7983254, 1.67160439, 0.372852474], [-0.6983254, 1.67877293, 0.422852427], [-0.6483253, 1.62384892, 0.372852474], [-0.6983254, 1.61899161, 0.372852474], [-0.6483253, 1.624619, 0.422852427], [-0.748325348, 1.57237887, 0.322852463], [-0.686419, 1.545166, 0.372852474], [-0.748325348, 1.553132, 0.422852427], [-0.6483253, 1.51832581, 0.422852427], [-0.789599657, 1.84516668, 0.472852439], [-0.789599657, 1.84516668, 0.5228524], [-0.6983247, 1.80923343, 0.472852439], [-0.697773337, 1.80901647, 0.472852439], [-0.748325348, 1.795166, 0.433189958], [-0.6625905, 1.7951653, 0.5228524], [-0.6983247, 1.80923343, 0.5228524], [-0.6980282, 1.8091166, 0.5228524], [-0.748325348, 1.795166, 0.572764635], [-0.7983254, 1.795166, 0.5645659], [-0.6483251, 1.78954935, 0.472852439], [-0.6602671, 1.74516606, 0.472852439], [-0.7983254, 1.74516606, 0.4365802], [-0.6483253, 1.74516606, 0.48626706], [-0.7983254, 1.74516606, 0.505286455], [-0.6483253, 1.75715041, 0.5728524], [-0.6983254, 1.74516606, 0.5655793], [-0.6983254, 1.76989746, 0.5728524], [-0.6483253, 1.69516611, 0.436100572], [-0.6483253, 1.73369408, 0.472852439], [-0.748325348, 1.69516611, 0.4376305], [-0.6483253, 1.69516611, 0.5219151], [-0.6483253, 1.69631529, 0.5228524], [-0.6983254, 1.69516611, 0.5058912], [-0.6983254, 1.71413875, 0.5228524], [-0.748325348, 1.69516611, 0.500168443], [-0.748325348, 1.7403791, 0.5228524], [-0.6483253, 1.731425, 0.622852445], [-0.6483253, 1.64516592, 0.437890977], [-0.748325348, 1.64516592, 0.493164152], [-0.7983254, 1.64516592, 0.488997132], [-0.748325348, 1.595166, 0.4292052], [-0.6483253, 1.595166, 0.490322322], [-0.6983254, 1.595166, 0.478493363], [-0.6483253, 1.545166, 0.427101284], [-0.748325348, 1.7718153, 0.6728524], [-0.7983254, 1.78825235, 0.8228524], [-0.748325348, 1.74516606, 0.855911851], [-0.6483253, 1.69516611, 0.855911851], [-0.7983254, 1.69516611, 0.855911851], [-0.6983254, 1.545166, 0.855911851], [-0.6983254, 0.9451661, 0.855911851], [-0.748325348, 0.645166159, 0.855911851], [-0.9483243, 1.907654, -0.3271475], [-0.9695318, 1.89715314, -0.22714752], [-0.9483243, 1.907654, -0.177147523], [-0.9865817, 1.84516716, -0.277147532], [-0.9483253, 1.778799, -0.3271475], [-1.00352263, 1.79351425, -0.277147532], [-0.9483253, 1.778707, -0.22714752], [-0.848325253, 1.72747707, -0.377147526], [-0.998325348, 1.71186185, -0.3271475], [-0.998325348, 1.70122433, -0.22714752], [-0.8983253, 1.72184587, -0.177147523], [-0.998325348, 1.70210934, -0.177147523], [-0.848325253, 1.62826586, -0.22714752], [-0.9695318, 1.89715314, -0.07714754], [-0.9865817, 1.84516716, 0.02285245], [-0.998325348, 1.809361, -0.07714754], [-0.9483253, 1.77707863, -0.07714754], [-1.00378776, 1.79270554, -0.07714754], [-1.00371945, 1.79291415, -0.0271475315], [-0.9483253, 1.77692556, 0.02285245], [-0.848325253, 1.72447371, -0.0271475315], [-0.998325348, 1.71444654, 0.02285245], [-0.9483253, 1.68393993, -0.12714754], [-0.848325253, 1.63603449, 0.02285245], [-0.9483243, 1.907654, 0.07285246], [-0.9695318, 1.89715314, 0.07285246], [-0.9483243, 1.907654, 0.222852439], [-0.9695318, 1.89715314, 0.222852439], [-0.8983253, 1.720227, 0.07285246], [-0.998325348, 1.71003079, 0.122852445], [-0.848325253, 1.72541213, 0.172852427], [-0.998325348, 1.70706964, 0.222852439], [-0.9483253, 1.6850071, 0.172852427], [-0.9943682, 0.5951661, 0.122852445], [-0.998325348, 0.545166135, 0.0475093722], [-0.9762584, 0.545166135, 0.122852445], [-0.9941747, 0.545166135, 0.172852427], [-0.9786602, 0.545166135, 0.222852439], [-0.998325348, 0.571628, 0.222852439], [-0.998325348, 0.5104218, 0.07285246], [-0.9567771, 0.495166123, 0.222852439], [-0.91660285, 1.89516592, 0.372852474], [-0.9695318, 1.89715314, 0.372852474], [-0.9483243, 1.907654, 0.422852427], [-0.898325443, 1.88797021, 0.422852427], [-0.998325348, 1.809361, 0.422852427], [-1.0037334, 1.79287124, 0.272852451], [-0.9483253, 1.77781439, 0.372852474], [-0.8983253, 1.780962, 0.422852427], [-0.9483253, 1.77865386, 0.422852427], [-0.848325253, 1.72655249, 0.322852463], [-0.8983253, 1.7237711, 0.372852474], [-0.998325348, 1.69516611, 0.352826327], [-0.848325253, 1.66541672, 0.372852474], [-0.998325348, 1.68415236, 0.422852427], [-0.815314054, 1.595166, 0.422852427], [-0.848325253, 1.60790467, 0.422852427], [-0.9805623, 0.645166159, 0.322852463], [-0.998325348, 0.65891695, 0.322852463], [-0.9647758, 0.5951661, 0.272852451], [-0.998325348, 0.6387117, 0.272852451], [-0.9483253, 0.5951661, 0.3322648], [-0.9483253, 0.642450869, 0.372852474], [-0.982251048, 0.5951661, 0.372852474], [-0.9303937, 0.545166135, 0.272852451], [-0.970975637, 0.545166135, 0.322852463], [-0.998325348, 0.545166135, 0.325607866], [-0.91660285, 1.89516592, 0.472852439], [-0.9695318, 1.89715314, 0.472852439], [-0.91660285, 1.89516592, 0.5228524], [-0.947302461, 1.90725183, 0.5228524], [-0.8483261, 1.86828637, 0.472852439], [-0.8983253, 1.845166, 0.432957739], [-0.9483253, 1.845166, 0.432961076], [-0.8483261, 1.86828637, 0.5228524], [-0.8608831, 1.845166, 0.5228524], [-0.8983253, 1.845166, 0.563671947], [-0.894435048, 1.88643885, 0.5728524], [-0.9483253, 1.845166, 0.549369335], [-0.9483253, 1.86635423, 0.5728524], [-0.984462857, 1.85162759, 0.5728524], [-0.8983253, 1.848371, 0.622852445], [-0.848325253, 1.795166, 0.435897976], [-0.848325253, 1.83857107, 0.472852439], [-0.9483253, 1.795166, 0.436143547], [-0.848325253, 1.795166, 0.5066619], [-0.8983253, 1.795166, 0.50437355], [-0.8983253, 1.82354951, 0.5228524], [-0.9483253, 1.83735776, 0.5228524], [-0.9483253, 1.8433907, 0.622852445], [-1.00127089, 1.80037975, 0.622852445], [-0.848325253, 1.74516606, 0.436978], [-0.9483253, 1.74516606, 0.504119039], [-0.9483253, 1.70483232, 0.472852439], [-0.848325253, 1.69516611, 0.4970015], [-0.8983253, 1.69516611, 0.488328427], [-0.998325348, 0.6951661, 0.426322848], [-0.954015255, 1.845166, 0.6728524], [-0.9483253, 1.84779263, 0.7728524], [-0.957211137, 1.845166, 0.8228524], [-1.0012517, 1.80043793, 0.6728524], [-0.8983253, 1.83667088, 0.722852349], [-0.8983253, 1.83351183, 0.8228524], [-1.00074255, 1.80199051, 0.8228524], [-0.848325253, 1.795166, 0.855911851], [-0.8983253, 1.795166, 0.855911851], [-0.9483253, 1.795166, 0.855911851], [-0.8983253, 1.69516611, 0.855911851], [-0.998325348, 1.69516611, 0.855911851], [-0.998325348, 1.595166, 0.855911851], [-0.8983253, 0.495166123, 0.855911851], [-1.03558409, 1.69575763, -0.3271475], [-1.03430057, 1.69967151, -0.177147523], [-1.04832542, 1.65690875, -0.3271475], [-1.04832542, 1.65690875, -0.22714752], [-1.1983254, 0.865713, -0.377147526], [-1.13367474, 0.795166135, -0.377147526], [-1.13875067, 0.795166135, -0.3271475], [-1.14832532, 0.8116149, -0.377147526], [-1.19178271, 0.795166135, -0.177147523], [-1.09462488, 0.7451661, -0.3271475], [-1.0988735, 0.7451661, -0.377147526], [-1.09832537, 0.7451661, -0.307984531], [-1.11361361, 0.7451661, -0.277147532], [-1.11768913, 0.7451661, -0.22714752], [-1.05994153, 0.6951661, -0.3271475], [-1.09832537, 0.6951661, -0.303076029], [-1.14832532, 0.6951661, -0.32192564], [-1.14832532, 0.7265701, -0.277147532], [-1.09949362, 0.6951661, -0.22714752], [-1.14832532, 0.6951661, -0.2505135], [-1.13242149, 0.6951661, -0.177147523], [-1.06331146, 0.645166159, -0.377147526], [-1.09832537, 0.6597111, -0.3271475], [-1.1983254, 0.645166159, -0.357463479], [-1.114208, 0.645166159, -0.22714752], [-1.05830765, 0.545166135, -0.377147526], [-1.14832532, 0.5637432, -0.377147526], [-1.12936211, 0.545166135, -0.22714752], [-1.1983254, 0.545166135, -0.242302656], [-1.1656034, 0.545166135, -0.177147523], [-1.09832537, 0.533794761, -0.377147526], [-1.14832532, 0.495166123, -0.22946775], [-1.14287341, 0.495166123, -0.177147523], [-1.1983254, 0.445166141, -0.256613255], [-1.16244543, 0.395166129, -0.377147526], [-1.1983254, 0.4290317, -0.377147526], [-1.1983254, 0.430260122, -0.3271475], [-1.1983254, 0.427739143, -0.277147532], [-1.14832532, 0.395166129, -0.23384957], [-1.14832532, 0.395166129, -0.18497467], [-1.14832532, 0.352632046, -0.3271475], [-1.14832532, 0.351497442, -0.277147532], [-1.00280464, 1.79570293, 0.02285245], [-1.03513145, 1.69713783, -0.12714754], [-1.04832542, 1.65690875, -0.12714754], [-1.18025744, 0.6951661, -0.12714754], [-1.1983254, 0.7082779, 0.02285245], [-1.1469003, 0.645166159, -0.12714754], [-1.14832532, 0.645166159, -0.174918473], [-1.17109585, 0.645166159, -0.07714754], [-1.14832532, 0.645166159, -0.03891754], [-1.14832532, 0.6691952, 0.02285245], [-1.177422, 0.5951661, -0.12714754], [-1.14832532, 0.5951661, -0.0453140438], [-1.09832537, 0.5951661, -0.0169113278], [-1.09832537, 0.6203387, 0.02285245], [-1.18058109, 0.545166135, -0.12714754], [-1.18382943, 0.545166135, -0.07714754], [-1.1983254, 0.5873918, -0.07714754], [-1.178929, 0.545166135, -0.0271475315], [-1.0483253, 0.545166135, 0.007276565], [-1.0483253, 0.565616, 0.02285245], [-1.14832532, 0.545166135, -0.0136930943], [-1.13501954, 0.495166123, -0.07714754], [-1.14253342, 0.495166123, -0.0271475315], [-1.02197707, 0.495166123, 0.02285245], [-1.09832537, 0.495166123, 0.00519788265], [-1.14278162, 0.445166141, -0.07714754], [-1.14832532, 0.445631027, -0.0271475315], [-1.06608415, 0.445166141, 0.02285245], [-1.09832537, 0.445166141, 0.006058365], [-1.1631074, 0.395166129, -0.12714754], [-1.14832532, 0.42760253, 0.02285245], [-1.1983254, 0.395166129, -0.00637382269], [-1.1983254, 0.4224546, 0.02285245], [-1.00277841, 1.795783, 0.172852427], [-1.03514957, 1.69708252, 0.07285246], [-1.03544366, 1.69618583, 0.172852427], [-1.08497381, 1.54516649, 0.122852445], [-1.15056849, 1.34516573, 0.222852439], [-1.1983254, 0.8451661, 0.180874616], [-1.14670551, 0.795166135, 0.172852427], [-1.1983254, 0.830625653, 0.172852427], [-1.12135661, 0.795166135, 0.222852439], [-1.1983254, 0.7683276, 0.07285246], [-1.09173739, 0.7451661, 0.172852427], [-1.14832532, 0.7451661, 0.1498554], [-1.14832532, 0.7205574, 0.07285246], [-1.09712434, 0.6951661, 0.122852445], [-1.1983254, 0.7240709, 0.122852445], [-1.03482628, 0.6951661, 0.222852439], [-1.14832532, 0.71318984, 0.222852439], [-1.05786884, 0.645166159, 0.07285246], [-1.0407548, 0.645166159, 0.122852445], [-1.1983254, 0.6628322, 0.122852445], [-1.0483253, 0.692663431, 0.172852427], [-1.1300441, 0.645166159, 0.172852427], [-1.14832532, 0.6613327, 0.172852427], [-1.0389868, 0.645166159, 0.222852439], [-1.1983254, 0.661155939, 0.222852439], [-1.1983254, 0.5951661, 0.0554713], [-1.01731479, 0.5951661, 0.172852427], [-1.0734241, 0.5951661, 0.172852427], [-1.04030466, 0.5951661, 0.222852439], [-1.05474985, 0.5951661, 0.222852439], [-1.09832537, 0.620593, 0.222852439], [-1.12826169, 0.545166135, 0.07285246], [-1.07356715, 0.545166135, 0.122852445], [-1.07147288, 0.545166135, 0.172852427], [-1.07854271, 0.545166135, 0.222852439], [-1.12312531, 0.495166123, 0.07285246], [-1.1983254, 0.495166123, 0.0566858649], [-1.01035953, 0.495166123, 0.122852445], [-1.09832537, 0.5192566, 0.122852445], [-1.12241578, 0.495166123, 0.122852445], [-0.998937964, 0.495166123, 0.172852427], [-1.09832537, 0.5241661, 0.172852427], [-1.14832532, 0.495166123, 0.140007883], [-1.14832532, 0.5280107, 0.172852427], [-1.1983254, 0.528403938, 0.222852439], [-1.1983254, 0.4767285, 0.07285246], [-1.14832532, 0.4739375, 0.122852445], [-1.0483253, 0.445166141, 0.1928049], [-1.14832532, 0.452138364, 0.222852439], [-1.1983254, 0.459085256, 0.222852439], [-1.09832537, 0.413507164, 0.07285246], [-1.09832537, 0.408245027, 0.122852445], [-1.14832532, 0.4223829, 0.122852445], [-1.1983254, 0.4088612, 0.122852445], [-1.14832532, 0.4267978, 0.172852427], [-1.1983254, 0.4233398, 0.172852427], [-1.04832542, 1.65690875, 0.322852463], [-1.18967021, 0.8451661, 0.322852463], [-1.1983254, 0.862551868, 0.372852474], [-1.125166, 0.795166135, 0.422852427], [-1.09832537, 0.768861532, 0.372852474], [-1.0483253, 0.755640864, 0.422852427], [-1.03636611, 0.6951661, 0.272852451], [-1.12619019, 0.6951661, 0.272852451], [-1.14832532, 0.711914539, 0.322852463], [-0.9999931, 0.6951661, 0.372852474], [-1.14832532, 0.6951661, 0.3429753], [-1.1983254, 0.7139333, 0.372852474], [-1.1983254, 0.6951661, 0.390116364], [-1.07140231, 0.645166159, 0.272852451], [-1.08103228, 0.645166159, 0.322852463], [-1.1983254, 0.6626065, 0.322852463], [-1.0483253, 0.645166159, 0.38612178], [-1.0483253, 0.6823829, 0.422852427], [-1.09832537, 0.645166159, 0.400289685], [-1.080575, 0.5951661, 0.272852451], [-1.09832537, 0.5951661, 0.343034834], [-1.1983254, 0.5951661, 0.338542134], [-1.14832532, 0.5951661, 0.396862358], [-1.07457244, 0.545166135, 0.272852451], [-1.14832532, 0.545166135, 0.343440682], [-1.1983254, 0.545166135, 0.339587241], [-1.0483253, 0.545166135, 0.3752354], [-1.09832537, 0.545166135, 0.381442755], [-1.14832532, 0.545166135, 0.389624268], [-1.01641083, 0.495166123, 0.272852451], [-1.09832537, 0.517719746, 0.272852451], [-1.1983254, 0.5249139, 0.322852463], [-1.09832537, 0.495166123, 0.375516027], [-1.14832532, 0.495166123, 0.413009673], [-1.0483253, 0.475613475, 0.272852451], [-1.09832537, 0.4515003, 0.272852451], [-1.14832532, 0.474184841, 0.272852451], [-1.1983254, 0.460998833, 0.272852451], [-1.0483253, 0.488365054, 0.322852463], [-1.09832537, 0.454378664, 0.322852463], [-1.14832532, 0.44703418, 0.322852463], [-1.09832537, 0.445166141, 0.3719971], [-1.1983254, 0.463817537, 0.422852427], [-1.1983254, 0.422522277, 0.372852474], [-1.01854527, 1.74770975, 0.5228524], [-1.0353334, 1.69652224, 0.472852439], [-1.04832542, 1.65690875, 0.472852439], [-1.05119586, 1.6481564, 0.5728524], [-1.13297117, 1.39882112, 0.5728524], [-1.16618454, 1.29755163, 0.5728524], [-1.1983254, 0.8451661, 0.427689165], [-1.14832532, 0.795166135, 0.46448043], [-1.09832537, 0.7451661, 0.440572053], [-1.1983254, 0.7451661, 0.443907529], [-1.09832537, 0.6951661, 0.439968079], [-1.14832532, 0.645166159, 0.441286176], [-1.1983254, 0.5951661, 0.44297], [-1.1983254, 0.545166135, 0.438455671], [-1.1983254, 0.495166123, 0.445753664], [-1.1983254, 0.359813571, 0.472852439], [-1.0353781, 1.69638586, 0.7728524], [-1.08456516, 1.54641247, 0.8228524], [-1.11764586, 1.445548, 0.8228524], [-1.15056849, 1.34516573, 0.722852349], [-1.18308246, 1.2460295, 0.8228524], [-1.1983254, 0.380159318, 0.6728524], [-1.1983254, 0.3863544, 0.8228524], [-1.0483253, 1.545166, 0.855911851], [-1.09832537, 1.295166, 0.855911851], [-1.14832532, 1.24516606, 0.855911851], [-1.1983254, 1.14516616, 0.855911851], [-1.1983254, 1.045166, 0.855911851], [-1.1983254, 0.8951661, 0.855911851], [-1.1983254, 0.6951661, 0.855911851], [-1.1983254, 0.495166123, 0.855911851], [-1.0483253, 0.395166129, 0.855911851], [-1.26535952, 0.995163739, -0.22714752], [-1.28035009, 0.949457169, -0.22714752], [-1.22846663, 0.8951661, -0.377147526], [-1.22669613, 0.8951661, -0.277147532], [-1.23103642, 0.8951661, -0.22714752], [-1.31003368, 0.858951, -0.3271475], [-1.31455493, 0.845165, -0.3271475], [-1.30958319, 0.8603243, -0.277147532], [-1.312299, 0.85204345, -0.22714752], [-1.31455493, 0.845165, -0.22714752], [-1.31297851, 0.84997195, -0.2190016], [-1.23043549, 0.795166135, -0.3271475], [-1.24832535, 0.795166135, -0.346283048], [-1.29832542, 0.795166135, -0.3379837], [-1.33095324, 0.7951664, -0.3271475], [-1.22982943, 0.795166135, -0.277147532], [-1.33050644, 0.796528339, -0.277147532], [-1.28769779, 0.795166135, -0.22714752], [-1.28136945, 0.795166135, -0.177147523], [-1.33095324, 0.7951664, -0.177147523], [-1.24832535, 0.7580905, -0.3271475], [-1.24832535, 0.7750037, -0.277147532], [-1.28412211, 0.7451661, -0.22714752], [-1.29832542, 0.7451661, -0.242931545], [-1.28188145, 0.7451661, -0.177147523], [-1.24832535, 0.7271705, -0.3271475], [-1.32011163, 0.6951661, -0.3271475], [-1.22903681, 0.6951661, -0.277147532], [-1.36225235, 0.6997338, -0.245212212], [-1.34832549, 0.742197633, -0.22714752], [-1.279896, 0.6951661, -0.177147523], [-1.29832542, 0.6951661, -0.1914208], [-1.34832549, 0.742197633, -0.177147523], [-1.29832542, 0.645166159, -0.365024656], [-1.37771022, 0.6526023, -0.3271475], [-1.29832542, 0.645166159, -0.300043821], [-1.34832537, 0.645166159, -0.2923121], [-1.38014913, 0.645166039, -0.22714752], [-1.24832535, 0.645166159, -0.191888347], [-1.34832537, 0.5951661, -0.364109874], [-1.36389482, 0.5951661, -0.3271475], [-1.34832537, 0.5951661, -0.304676265], [-1.21740806, 0.5951661, -0.177147523], [-1.34832537, 0.5951661, -0.190546915], [-1.39654779, 0.5951662, -0.177147523], [-1.36964953, 0.545166135, -0.3271475], [-1.29832542, 0.5821314, -0.277147532], [-1.34832537, 0.545166135, -0.300306261], [-1.29832542, 0.545166135, -0.2655544], [-1.3983252, 0.5897465, -0.22714752], [-1.218673, 0.545166135, -0.177147523], [-1.24832535, 0.545166135, -0.192548051], [-1.34832537, 0.545166135, -0.191689432], [-1.33067131, 0.495166123, -0.377147526], [-1.31304765, 0.495166123, -0.277147532], [-1.39458632, 0.484268427, -0.277147532], [-1.41679788, 0.5334229, -0.277147532], [-1.29832542, 0.495166123, -0.269949257], [-1.39846385, 0.485647142, -0.22714752], [-1.24832535, 0.495166123, -0.1935079], [-1.28461814, 0.445167363, -0.377147526], [-1.3500942, 0.46844846, -0.3271475], [-1.40030181, 0.486300647, -0.277147532], [-1.25239837, 0.433711022, -0.2723213], [-1.39832413, 0.485597432, -0.22714752], [-1.28461814, 0.445167363, -0.177147523], [-1.348325, 0.4678194, -0.177147523], [-1.24494421, 0.431060582, -0.177147523], [-1.23256183, 1.09516549, -0.0271475315], [-1.26535952, 0.995163739, -0.07714754], [-1.26535952, 0.995163739, 0.02285245], [-1.26593363, 0.8951661, -0.12714754], [-1.29691255, 0.898957431, -0.07714754], [-1.298156, 0.895166039, 0.02285245], [-1.27185917, 0.8451661, -0.07714754], [-1.278385, 0.8451661, 0.02285245], [-1.23047519, 0.795166135, -0.12714754], [-1.29832542, 0.795166135, -0.161371142], [-1.25862634, 0.795166135, -0.0271475315], [-1.21811807, 0.7451661, -0.12714754], [-1.29832542, 0.7451661, -0.161758274], [-1.24272609, 0.7451661, -0.07714754], [-1.2247839, 0.7451661, -0.0271475315], [-1.24832535, 0.779101551, 0.02285245], [-1.281003, 0.6951661, -0.12714754], [-1.29832542, 0.7179035, -0.07714754], [-1.28242862, 0.6951661, -0.0271475315], [-1.34832549, 0.742197633, -0.0271475315], [-1.29832542, 0.714967847, 0.02285245], [-1.27786064, 0.645166159, -0.12714754], [-1.28055191, 0.645166159, -0.0271475315], [-1.24832535, 0.645166159, 0.005079031], [-1.24832535, 0.668479562, 0.02285245], [-1.267373, 0.5951661, -0.12714754], [-1.26666713, 0.5951661, -0.07714754], [-1.27362692, 0.5951661, -0.0271475315], [-1.23094416, 0.5951661, 0.02285245], [-1.24832535, 0.545166135, -0.161683038], [-1.28286088, 0.545166135, -0.12714754], [-1.28131962, 0.545166135, -0.0271475315], [-1.2335099, 0.545166135, 0.02285245], [-1.22540343, 0.495166123, -0.12714754], [-1.2246685, 0.495166123, -0.0271475315], [-1.24832535, 0.5295602, 0.02285245], [-1.29832542, 0.495166123, -0.010931015], [-1.34832537, 0.495166123, -0.0154976547], [-1.34832537, 0.5266283, 0.02285245], [-1.242623, 0.4302352, -0.07714754], [-1.29832554, 0.450041175, -0.07714754], [-1.39832413, 0.485597432, -0.07714754], [-1.24832535, 0.474813253, -0.0271475315], [-1.348325, 0.4678194, -0.0271475315], [-1.24494421, 0.431060582, -0.12714754], [-1.23394787, 0.395166725, 0.02285245], [-1.26535952, 0.995163739, 0.172852427], [-1.28091085, 0.94774735, 0.172852427], [-1.28085411, 0.9479204, 0.222852439], [-1.29773724, 0.896443069, 0.07285246], [-1.24462354, 0.8951661, 0.172852427], [-1.22953022, 0.8951661, 0.222852439], [-1.24832535, 0.922426939, 0.222852439], [-1.31240916, 0.8517078, 0.07285246], [-1.29850078, 0.8941149, 0.122852445], [-1.31365275, 0.8479158, 0.122852445], [-1.24832535, 0.8889979, 0.172852427], [-1.28288615, 0.8451661, 0.222852439], [-1.30299759, 0.880404234, 0.222852439], [-1.31455493, 0.845165, 0.222852439], [-1.28854036, 0.795166135, 0.122852445], [-1.33095324, 0.7951664, 0.172852427], [-1.22759044, 0.795166135, 0.222852439], [-1.24832535, 0.791823, 0.07285246], [-1.27860391, 0.7451661, 0.122852445], [-1.23750031, 0.7451661, 0.172852427], [-1.34734523, 0.745186, 0.172852427], [-1.22439611, 0.7451661, 0.222852439], [-1.34735179, 0.7451662, 0.222852439], [-1.34832549, 0.742197633, 0.07285246], [-1.36375, 0.695167661, 0.07285246], [-1.36225235, 0.6997338, 0.080422014], [-1.24832535, 0.7303228, 0.172852427], [-1.29970133, 0.6951661, 0.172852427], [-1.28310454, 0.6951661, 0.222852439], [-1.36375, 0.695167661, 0.222852439], [-1.24832535, 0.6646013, 0.07285246], [-1.29832542, 0.6627639, 0.07285246], [-1.33710229, 0.645166159, 0.222852439], [-1.34832537, 0.645166159, 0.208214313], [-1.37882054, 0.6492171, 0.222852439], [-1.24832535, 0.6136454, 0.122852445], [-1.29832542, 0.6117183, 0.122852445], [-1.34832537, 0.6144344, 0.172852427], [-1.39654779, 0.5951662, 0.172852427], [-1.29832542, 0.6113455, 0.222852439], [-1.3942157, 0.6022768, 0.222852439], [-1.24832535, 0.579578, 0.222852439], [-1.34832537, 0.545166135, 0.199503928], [-1.24832535, 0.5295483, 0.07285246], [-1.34832537, 0.5193871, 0.07285246], [-1.29832542, 0.5283282, 0.122852445], [-1.41431952, 0.540979445, 0.122852445], [-1.24832535, 0.529101, 0.172852427], [-1.34832537, 0.521447659, 0.172852427], [-1.41510749, 0.538576961, 0.172852427], [-1.25239837, 0.433711022, 0.164714783], [-1.28461814, 0.445167363, 0.222852439], [-1.23394787, 0.395166725, 0.07285246], [-1.23394787, 0.395166725, 0.122852445], [-1.23394787, 0.395166725, 0.172852427], [-1.24494421, 0.431060582, 0.222852439], [-1.26535952, 0.995163739, 0.372852474], [-1.28151059, 0.9459188, 0.372852474], [-1.23347783, 0.8951661, 0.272852451], [-1.309801, 0.8596604, 0.272852451], [-1.30874312, 0.8628857, 0.322852463], [-1.31455493, 0.845165, 0.322852463], [-1.31268609, 0.8508634, 0.372852474], [-1.24832535, 0.8824189, 0.422852427], [-1.22337389, 0.795166135, 0.272852451], [-1.24832535, 0.836824656, 0.272852451], [-1.22852707, 0.795166135, 0.322852463], [-1.24832535, 0.839054048, 0.322852463], [-1.33095324, 0.7951664, 0.322852463], [-1.29832542, 0.795166135, 0.3619896], [-1.34735179, 0.7451662, 0.272852451], [-1.24832535, 0.7451661, 0.273896366], [-1.24832535, 0.776819766, 0.322852463], [-1.29832542, 0.7451661, 0.29123798], [-1.36216474, 0.7000011, 0.272852451], [-1.29832542, 0.7115987, 0.372852474], [-1.36049247, 0.70509994, 0.372852474], [-1.36375, 0.695167661, 0.372852474], [-1.29832542, 0.6951661, 0.3893247], [-1.36164093, 0.701598, 0.422852427], [-1.34832549, 0.742197633, 0.422852427], [-1.36375, 0.695167661, 0.422852427], [-1.38014913, 0.645166039, 0.272852451], [-1.24832535, 0.662321568, 0.322852463], [-1.34832537, 0.6591357, 0.322852463], [-1.38014913, 0.645166039, 0.322852463], [-1.23095155, 0.645166159, 0.372852474], [-1.29832542, 0.645166159, 0.357364565], [-1.24832535, 0.645166159, 0.388566226], [-1.28261161, 0.645166159, 0.422852427], [-1.38014913, 0.645166039, 0.422852427], [-1.24832535, 0.610440254, 0.272852451], [-1.29832542, 0.610654, 0.322852463], [-1.39463055, 0.601011634, 0.322852463], [-1.23263574, 0.5951661, 0.372852474], [-1.36699915, 0.5951661, 0.372852474], [-1.24832535, 0.5951661, 0.38844654], [-1.33588552, 0.5951661, 0.422852427], [-1.34832537, 0.576520145, 0.272852451], [-1.24832535, 0.5797772, 0.322852463], [-1.24832535, 0.5796899, 0.372852474], [-1.29832542, 0.579643, 0.372852474], [-1.34832537, 0.579140067, 0.372852474], [-1.34832537, 0.5726489, 0.422852427], [-1.41151035, 0.549544752, 0.422852427], [-1.2612282, 0.495166123, 0.272852451], [-1.29832542, 0.495166123, 0.241556078], [-1.34832537, 0.495166123, 0.227653235], [-1.34832537, 0.5060465, 0.272852451], [-1.29832542, 0.5095775, 0.322852463], [-1.40134573, 0.486671865, 0.294588476], [-1.40467346, 0.487855077, 0.322852463], [-1.29832542, 0.495166123, 0.340234548], [-1.33828866, 0.495166123, 0.372852474], [-1.40321171, 0.487335265, 0.372852474], [-1.32159221, 0.495166123, 0.422852427], [-1.34832537, 0.51094234, 0.422852427], [-1.24832535, 0.467731237, 0.272852451], [-1.28461814, 0.445167363, 0.272852451], [-1.34905207, 0.468077928, 0.272852451], [-1.29832554, 0.450041175, 0.322852463], [-1.25437093, 0.43441242, 0.372852474], [-1.28461814, 0.445167363, 0.372852474], [-1.34931636, 0.4681719, 0.372852474], [-1.39832413, 0.485597432, 0.372852474], [-1.25468838, 0.434525281, 0.422852427], [-1.348325, 0.4678194, 0.422852427], [-1.23394787, 0.395166725, 0.272852451], [-1.24494421, 0.431060582, 0.322852463], [-1.19973481, 1.19525635, 0.5228524], [-1.2651782, 0.995717, 0.5728524], [-1.28175712, 0.945166767, 0.472852439], [-1.24832535, 0.8451661, 0.4499869], [-1.31297851, 0.84997195, 0.43932], [-1.24832535, 0.795166135, 0.439827234], [-1.33095324, 0.7951664, 0.472852439], [-1.24832535, 0.7451661, 0.449261934], [-1.29832542, 0.7451661, 0.460080236], [-1.34732866, 0.7452368, 0.472852439], [-1.34734011, 0.745202065, 0.5228524], [-1.29832542, 0.6951661, 0.46292457], [-1.32586789, 0.6951661, 0.472852439], [-1.36375, 0.695167661, 0.5228524], [-1.36375, 0.695167661, 0.5728524], [-1.24832535, 0.645166159, 0.451899916], [-1.32457244, 0.645166159, 0.472852439], [-1.34832537, 0.645166159, 0.443412572], [-1.38014913, 0.645166039, 0.5228524], [-1.29832542, 0.5951661, 0.4596441], [-1.34832537, 0.596814454, 0.472852439], [-1.39412928, 0.602540135, 0.472852439], [-1.24832535, 0.545166135, 0.4450076], [-1.29832542, 0.545166135, 0.4660097], [-1.34832537, 0.545166135, 0.4584529], [-1.29832542, 0.495166123, 0.440122157], [-1.28461814, 0.445167363, 0.472852439], [-1.39832413, 0.485597432, 0.5728524], [-1.23394787, 0.395166725, 0.472852439], [-1.23311341, 0.397576958, 0.5228524], [-1.24747515, 0.4319605, 0.5228524], [-1.24919844, 0.432573259, 0.5728524], [-1.216163, 1.14516568, 0.722852349], [-1.21600342, 1.14565277, 0.8228524], [-1.28146255, 0.9460654, 0.8228524], [-1.2983253, 0.8946499, 0.7728524], [-1.33095324, 0.7951664, 0.722852349], [-1.34832549, 0.742197633, 0.8228524], [-1.37951076, 0.64711225, 0.8228524], [-1.39631319, 0.5958816, 0.8228524], [-1.3983252, 0.5897465, 0.7728524], [-1.348325, 0.4678194, 0.7728524], [-1.28461814, 0.445167363, 0.8228524], [-1.25012171, 0.432901531, 0.8228524], [-1.29832542, 0.8451661, 0.855911851], [-1.29832542, 0.7451661, 0.855911851], [-1.34832537, 0.645166159, 0.855911851], [-1.34832537, 0.545166135, 0.855911851], [-1.29832542, 0.495166123, 0.855911851], [-1.34832537, 0.495166123, 0.855911851], [-1.40134573, 0.486671865, 0.8296379], [-1.25239837, 0.433711022, 0.8426881], [-1.412946, 0.5451673, -0.22714752], [-1.41473687, 0.491433263, -0.277147532], [-1.41473687, 0.491433263, -0.12714754], [-1.41473687, 0.491433263, 0.02285245], [-1.412946, 0.5451673, 0.07285246], [-1.412946, 0.5451673, 0.172852427], [-1.41473687, 0.491433263, 0.322852463], [-1.41473687, 0.491433263, 0.372852474], [-1.412946, 0.5451673, 0.472852439], [-1.412946, 0.5451673, 0.5728524], [-1.41473687, 0.491433263, 0.5228524], [-1.41166162, 0.5490835, 0.8228524], [-1.41473687, 0.491433263, 0.6728524], [-1.41473687, 0.491433263, 0.7728524], [1.489532, 0.344891369, -1.52714753], [1.45167494, 0.244341388, -1.37714744], [1.43316031, 0.19516404, -1.37714744], [1.41433573, 0.145166054, -1.52714753], [1.41433573, 0.145166054, -1.42714751], [1.45198536, 0.245165884, -1.32714748], [1.40167451, 0.195166141, -1.36381], [1.40167451, 0.226783961, -1.32714748], [1.40167451, 0.226233631, -1.17714751], [1.45167446, 0.345166147, -1.14890873], [1.46843481, 0.28885597, -1.16589367], [1.40167451, 0.345166147, -1.13941419], [1.48957825, 0.34501493, -1.07714748], [1.45167446, 0.345166147, -1.10477841], [1.47233438, 0.299213767, -1.07714748], [1.40167451, 0.345166147, -1.10885477], [1.47081089, 0.295167148, -1.02714753], [1.40167451, 0.295166135, -1.06900728], [1.45198536, 0.245165884, -1.02714753], [1.40167451, 0.245166153, -1.05620956], [1.45167494, 0.244341388, -1.12714744], [1.41276407, 0.140990987, -1.05072069], [1.40167475, 0.1115368, -0.8771475], [1.47081089, 0.295167148, -0.72714746], [1.3016746, 0.295166135, -1.559637], [1.25167441, 0.145166144, -1.559637], [1.35167456, 0.145166144, -1.40528417], [1.3016746, 0.145166144, -1.412142], [1.3016746, 0.180160642, -1.37714744], [1.25167441, 0.145166144, -1.41307056], [1.35167456, 0.09516616, -1.559637], [1.25167441, 0.09516616, -1.41389084], [1.21493125, 0.09516616, -1.37714744], [1.37668586, 0.0451650023, -1.52714753], [1.35167456, 0.0451661646, -1.559637], [1.37668586, 0.0451650023, -1.42714751], [1.37358117, 0.03691843, -1.41440237], [1.3016746, 0.0451661646, -1.41198647], [1.25167441, 0.0451661646, -1.41452813], [1.214294, 0.0451661646, -1.37714744], [1.35837984, -0.003456831, -1.37714744], [1.3016746, 0.01032722, -1.37714744], [1.25167441, 0.0077855587, -1.37714744], [1.3016746, -0.05483383, -1.559637], [1.35167336, -0.0212691128, -1.37714744], [1.32021165, -0.104834169, -1.52714753], [1.3016746, -0.104833841, -1.559637], [1.23619246, -0.104833841, -1.37714744], [1.20167446, -0.104833841, -1.41166544], [1.20167446, -0.07031587, -1.37714744], [1.30167508, -0.154068768, -1.52714753], [1.25167441, -0.154833823, -1.559637], [1.30167508, -0.154068768, -1.37714744], [1.28256226, -0.204833984, -1.52714753], [1.23454976, -0.2048338, -1.37714744], [1.20167446, -0.2048338, -1.40344954], [1.20167446, -0.304833829, -1.559637], [1.22608781, -0.35483402, -1.52714753], [1.206569, -0.40667665, -1.53864336], [1.38499427, 0.295166135, -1.32714748], [1.35167456, 0.295166135, -1.3604672], [1.35167456, 0.3284859, -1.32714748], [1.3016746, 0.295166135, -1.3600136], [1.3016746, 0.328032255, -1.32714748], [1.25167441, 0.295166135, -1.360233], [1.3826015, 0.295166135, -1.27714741], [1.35167456, 0.326093018, -1.27714741], [1.25167441, 0.345166147, -1.30878425], [1.37806535, 0.345166147, -1.22714746], [1.29299378, 0.295166135, -1.22714746], [1.25167441, 0.3195947, -1.22714746], [1.370126, 0.345166147, -1.17714751], [1.30993915, 0.295166135, -1.17714751], [1.3016746, 0.315104574, -1.17714751], [1.3826015, 0.245166153, -1.32714748], [1.35167456, 0.245166153, -1.35736489], [1.25167441, 0.245166153, -1.35859287], [1.3016746, 0.251595616, -1.27714741], [1.20167446, 0.245166153, -1.298461], [1.37826037, 0.245166153, -1.22714746], [1.25167441, 0.245166153, -1.26922917], [1.20167446, 0.245166153, -1.25105393], [1.37445164, 0.245166153, -1.17714751], [1.35167456, 0.195166141, -1.34373939], [1.3016746, 0.195166141, -1.348601], [1.25167441, 0.195166141, -1.35701478], [1.20167446, 0.195166141, -1.35713279], [1.34917665, 0.195166141, -1.27714741], [1.3016746, 0.195166141, -1.30140507], [1.25167441, 0.195166141, -1.26924121], [1.20167446, 0.145166144, -1.35894752], [1.36529732, 0.145166144, -1.27714741], [1.35167456, 0.145166144, -1.3237977], [1.20167446, 0.145166144, -1.25269377], [1.35419035, 0.09516616, -1.32714748], [1.3016746, 0.09516616, -1.33445847], [1.25167441, 0.09516616, -1.30300951], [1.22094512, 0.09516616, -1.27714741], [1.36398411, 0.09516616, -1.22714746], [1.36163712, 0.09516616, -1.17714751], [1.3016746, 0.07322517, -1.32714748], [1.20167446, 0.0614679158, -1.32714748], [1.35167456, 0.08119246, -1.27714741], [1.3016746, 0.0451661646, -1.28580058], [1.25167441, 0.0451661646, -1.2869401], [1.35167456, 0.06658515, -1.22714746], [1.32919931, 0.0451661646, -1.22714746], [1.23703766, 0.0451661646, -1.22714746], [1.20167446, 0.0451661646, -1.25951934], [1.35167456, 0.0741072, -1.17714751], [1.3016746, 0.04848805, -1.17714751], [1.22818589, 0.0451661646, -1.17714751], [1.20167446, 0.0559831262, -1.17714751], [1.3016746, -0.00483384728, -1.3285538], [1.25167441, 0.0133338571, -1.32714748], [1.29499936, -0.00483384728, -1.27714741], [1.36177492, 0.00556093454, -1.22714746], [1.23563457, -0.00483384728, -1.22714746], [1.20167446, -0.00483384728, -1.26110744], [1.35786128, -0.004834175, -1.17714751], [1.360775, 0.002904892, -1.17714751], [1.3016746, 0.04060936, -1.17714751], [1.23840046, -0.00483384728, -1.17714751], [1.33903718, -0.0548327267, -1.32714748], [1.33903718, -0.0548327267, -1.22714746], [1.20167446, -0.03879389, -1.22714746], [1.20167446, -0.0415599346, -1.17714751], [1.20167446, -0.104833841, -1.34262955], [1.32021165, -0.104834169, -1.17714751], [1.20167446, -0.124996305, -1.32714748], [1.23415709, -0.154833823, -1.27714741], [1.20167446, -0.1231806, -1.27714741], [1.2326889, -0.2048338, -1.22714746], [1.20167446, -0.180548072, -1.22714746], [1.26158834, -0.260541677, -1.32714748], [1.20167446, -0.254833817, -1.36006355], [1.26129985, -0.261307657, -1.22714746], [1.20167446, -0.254833817, -1.199211], [1.20167446, -0.290041327, -1.32714748], [1.35167456, 0.345166147, -1.12524235], [1.35167456, 0.295166135, -1.09416544], [1.33422828, 0.245166153, -1.12714744], [1.37177253, 0.195166141, -1.07714748], [1.35546279, 0.145166144, -1.12714744], [1.33445287, 0.145166144, -1.07714748], [1.35167456, 0.145166144, -1.045602], [1.20167446, 0.1607936, -0.97714746], [1.39434123, 0.09205848, -1.02714753], [1.35167456, 0.09516616, -1.0383563], [1.34250116, 0.09516616, -1.02714753], [1.3016746, 0.135101646, -1.02714753], [1.25167441, 0.115109, -1.02714753], [1.20167446, 0.09516616, -1.06450462], [1.20167446, 0.116334811, -1.02714753], [1.33444381, 0.09516616, -0.97714746], [1.3016746, 0.14115186, -0.97714746], [1.25167441, 0.1153871, -0.97714746], [1.37601662, 0.043386966, -1.12714744], [1.37358117, 0.03691843, -1.154022], [1.3016746, 0.07364738, -1.12714744], [1.2678411, 0.0451661646, -1.12714744], [1.23202467, 0.0451661646, -1.12714744], [1.20167446, 0.061983496, -1.12714744], [1.37358117, 0.03691843, -1.1183455], [1.3016746, 0.09504059, -1.07714748], [1.25167441, 0.08078259, -1.07714748], [1.216058, 0.0451661646, -1.07714748], [1.37668586, 0.0451650023, -1.02714753], [1.37358117, 0.03691843, -1.03594947], [1.34216547, 0.0451661646, -1.02714753], [1.22514153, 0.0451661646, -1.02714753], [1.35167456, 0.0556076765, -0.97714746], [1.20167446, 0.0451661646, -0.9954953], [1.3016746, 0.01133275, -1.12714744], [1.20167446, 0.0148158669, -1.12714744], [1.35786128, -0.004834175, -1.07714748], [1.25167441, 0.009549707, -1.07714748], [1.3016746, 0.0118668079, -1.02714753], [1.25167441, 0.0186332166, -1.02714753], [1.20167446, -0.00483384728, -1.05684507], [1.20167446, 0.02486372, -1.02714753], [1.35786128, -0.004834175, -0.97714746], [1.362839, 0.008387029, -0.97714746], [1.285527, -0.00483384728, -0.97714746], [1.25167441, -0.00483384728, -1.011], [1.23130131, -0.05483383, -1.02714753], [1.20167446, -0.05483383, -1.05677438], [1.23424888, -0.05483383, -0.97714746], [1.32021165, -0.104834169, -1.02714753], [1.20167446, -0.0844607055, -1.02714753], [1.30167508, -0.154068768, -0.97714746], [1.28256226, -0.204833984, -1.07714748], [1.25167441, -0.171471357, -1.02714753], [1.25167441, -0.175240874, -0.97714746], [1.20167446, -0.254833817, -1.04809463], [1.20167446, -0.254833817, -0.996771932], [1.20167446, -0.24424541, -0.97714746], [1.24491262, -0.3048339, -1.07714748], [1.20491147, -0.304833829, -0.97714746], [1.22608781, -0.35483402, -1.07714748], [1.22305679, -0.362884223, -1.06962109], [1.20721173, -0.404969156, -1.02714753], [1.39544725, 0.0949963, -0.9271475], [1.25167441, 0.121734276, -0.9271475], [1.3016746, 0.101721913, -0.8771475], [1.39472413, 0.09307599, -0.8271475], [1.25167441, 0.113774225, -0.8271475], [1.3016746, 0.09516616, -0.821542561], [1.25757337, 0.09516616, -0.777147532], [1.20167446, 0.103864849, -0.777147532], [1.3016746, 0.08927739, -0.9271475], [1.35167456, 0.08470988, -0.8771475], [1.35167456, 0.084125936, -0.8271475], [1.20167446, 0.0603120327, -0.777147532], [1.35906267, -0.00164374709, -0.9271475], [1.3016746, 0.0127111375, -0.9271475], [1.20167446, 0.038410604, -0.9271475], [1.3016746, 0.01300931, -0.8771475], [1.28206253, -0.00483384728, -0.8771475], [1.3016746, -0.00483384728, -0.858508468], [1.35167336, -0.0212691128, -0.8771475], [1.22957516, -0.05483383, -0.8771475], [1.33903718, -0.0548327267, -0.8271475], [1.277436, -0.05483383, -0.8271475], [1.3016746, -0.05483383, -0.8077894], [1.20167446, -0.05483383, -0.8018381], [1.221925, -0.104833841, -0.9271475], [1.20167446, -0.104833841, -0.947398067], [1.29002738, -0.104833841, -0.8771475], [1.31791019, -0.110946536, -0.8149662], [1.20167446, -0.104833841, -0.81575], [1.25167441, -0.139339834, -0.9271475], [1.30167508, -0.154068768, -0.8771475], [1.20167446, -0.154833823, -0.8703553], [1.20167446, -0.2048338, -0.85877955], [1.25583482, -0.2048338, -0.777147532], [1.2027328, -0.254833817, -0.9271475], [1.21727538, -0.254833817, -0.8771475], [1.20167446, -0.224884152, -0.8771475], [1.24432564, -0.306392848, -0.777147532], [1.22324514, -0.362383962, -0.777147532], [1.39551115, 0.09516582, -0.72714746], [1.20167446, 0.0964314044, -0.72714746], [1.25167441, 0.0451661646, -0.735710859], [1.35709333, -0.00687396526, -0.7351659], [1.3016746, -0.05483383, -0.7395594], [1.25167441, -0.104833841, -0.7488467], [1.300942, -0.156015992, -0.72714746], [1.25167441, -0.154833823, -0.7624818], [1.24491262, -0.3048339, -0.6271475], [1.223614, -0.361404777, -0.72714746], [1.20167446, -0.344298124, -0.6271475], [1.22601342, -0.355031073, -0.5771475], [1.20717692, -0.405061841, -0.5771475], [1.2031846, -0.415666163, -0.5771475], [1.22608781, -0.35483402, -0.527147532], [1.20301127, -0.41612643, -0.527147532], [1.05167437, 0.345166147, -1.559637], [1.00167441, 0.295166135, -1.559637], [1.00167441, 0.295166135, -1.511304], [1.02284646, 0.345166147, -1.42714751], [1.08255672, 0.345166147, -1.37714744], [1.00167441, 0.245166153, -1.559637], [1.0344758, 0.195166141, -1.47714746], [1.00167441, 0.09516616, -1.559637], [1.05167437, -0.05483383, -1.40832], [1.05167437, -0.0236614347, -1.37714744], [1.00167441, -0.0266855061, -1.37714744], [1.00167441, -0.104833841, -1.453834], [1.10167456, -0.104833841, -1.406985], [1.10167456, -0.07499632, -1.37714744], [1.10167456, -0.154833823, -1.559637], [1.00167441, -0.154833823, -1.559637], [1.10167456, -0.154833823, -1.39192927], [1.05167437, -0.154833823, -1.39698172], [1.034219, -0.2048338, -1.42714751], [1.15167451, -0.2048338, -1.397495], [1.05167437, -0.2048338, -1.40118051], [1.00167441, -0.254833817, -1.559637], [1.05167437, -0.228866816, -1.37714744], [1.05167437, -0.404833853, -1.559637], [1.18836808, -0.4550197, -1.52714753], [1.15167451, -0.4548338, -1.559637], [1.13196373, -0.6048334, -1.52714753], [1.10167456, -0.6048338, -1.559637], [1.00167441, -0.6548338, -1.40923417], [1.00167441, -0.7048338, -1.559637], [1.10167575, -0.6852804, -1.42714751], [1.05167437, -0.6995055, -1.37714744], [1.00167441, -0.7048338, -1.39751649], [1.05167437, -0.754833758, -1.559637], [1.07253242, -0.7626868, -1.41200757], [1.00167441, -0.754833758, -1.4088254], [1.05666423, -0.8048337, -1.52714753], [1.05604482, -0.8064793, -1.53767025], [1.03778863, -0.8549686, -1.52714753], [1.00167441, -0.8548338, -1.559637], [1.03783941, -0.8548338, -1.42714751], [1.00167441, -0.8548338, -1.40958333], [1.00167584, -0.9508864, -1.42714751], [1.08169889, 0.345166147, -1.32714748], [1.10167456, 0.345166147, -1.30301666], [1.10167456, 0.295166135, -1.30434048], [1.00208569, 0.345166147, -1.27714741], [1.18792224, 0.345166147, -1.17714751], [1.15167451, 0.345166147, -1.21208346], [1.15167451, 0.295166135, -1.22162485], [1.05167437, 0.345166147, -1.223966], [1.03107572, 0.245166153, -1.32714748], [1.05167437, 0.245166153, -1.29215491], [1.00167441, 0.283589065, -1.22714746], [1.15167451, 0.245166153, -1.20993245], [1.05167437, 0.245166153, -1.20323288], [1.15167451, 0.195166141, -1.29595935], [1.10167456, 0.195166141, -1.2999562], [1.02978778, 0.195166141, -1.27714741], [1.15167451, 0.195166141, -1.24814832], [1.05167437, 0.195166141, -1.24891579], [1.10167456, 0.195166141, -1.20193076], [1.00167441, 0.230144247, -1.17714751], [1.10167456, 0.145166144, -1.30284476], [1.17269754, 0.145166144, -1.22714746], [1.05167437, 0.145166144, -1.26249623], [1.00167441, 0.164948747, -1.22714746], [1.10167456, 0.186075225, -1.17714751], [1.15167451, 0.09516616, -1.3104943], [1.178102, 0.09516616, -1.22714746], [1.15522909, 0.09516616, -1.17714751], [1.00167441, 0.11190778, -1.17714751], [1.15167451, 0.0618192852, -1.27714741], [1.15167451, 0.0451661646, -1.26382172], [1.10167456, 0.0589401424, -1.22714746], [1.06625247, 0.0451661646, -1.17714751], [1.00167441, 0.02821666, -1.32714748], [1.15167451, 0.008491904, -1.22714746], [1.00167441, 0.03052023, -1.22714746], [1.15167451, 0.009090006, -1.17714751], [1.10167456, 0.009744287, -1.17714751], [1.13548875, -0.05483383, -1.32714748], [1.05167437, -0.0272783339, -1.32714748], [1.10167456, -0.0213726163, -1.27714741], [1.13826156, -0.05483383, -1.17714751], [1.10167456, -0.01824689, -1.17714751], [1.182193, -0.104833841, -1.32714748], [1.15167451, -0.07431528, -1.32714748], [1.18679976, -0.104833841, -1.27714741], [1.15167451, -0.0697085261, -1.27714741], [1.07029366, -0.104833841, -1.27714741], [1.13768411, -0.104833841, -1.22714746], [1.05167437, -0.09991735, -1.22714746], [1.15393448, -0.154833823, -1.32714748], [1.15167451, -0.154833823, -1.33259487], [1.10167456, -0.154833823, -1.35473871], [1.00167441, -0.154833823, -1.35607147], [1.15314293, -0.154833823, -1.27714741], [1.15167451, -0.1537525, -1.27714741], [1.05167437, -0.154833823, -1.32372332], [1.10167456, -0.136715263, -1.22714746], [1.11143374, -0.154833823, -1.17714751], [1.10167456, -0.183873236, -1.32714748], [1.17268348, -0.2048338, -1.27714741], [1.15167451, -0.2048338, -1.31964409], [1.15388155, -0.2048338, -1.22714746], [1.05167437, -0.2048338, -1.2560426], [1.05167437, -0.254833817, -1.36241722], [1.00167441, -0.230844378, -1.32714748], [1.15167451, -0.219296515, -1.27714741], [1.10167456, -0.225845635, -1.22714746], [1.10167456, -0.22073257, -1.17714751], [1.05167437, -0.226318061, -1.17714751], [1.00167441, -0.2122696, -1.17714751], [1.15167451, -0.289369345, -1.32714748], [1.05167437, -0.2901036, -1.32714748], [1.00167441, -0.288051, -1.27714741], [1.15167451, -0.279501319, -1.17714751], [1.00167441, -0.5548338, -1.21451247], [1.00167441, -0.5174688, -1.17714751], [1.03960538, -0.6048338, -1.32714748], [1.00167441, -0.566902757, -1.32714748], [1.03905487, -0.6048338, -1.27714741], [1.00167441, -0.5674532, -1.27714741], [1.09213448, -0.6048338, -1.22714746], [1.05167437, -0.5695165, -1.22714746], [1.13196373, -0.6048334, -1.17714751], [1.0907011, -0.6048338, -1.17714751], [1.05167437, -0.571406066, -1.17714751], [1.1131382, -0.6548349, -1.32714748], [1.0308454, -0.6548338, -1.32714748], [1.00167441, -0.6548338, -1.30741918], [1.00167441, -0.633811355, -1.27714741], [1.05167437, -0.6386535, -1.22714746], [1.11171532, -0.6586141, -1.18408585], [1.09431386, -0.7048333, -1.22714746], [1.07498074, -0.756183565, -1.32714748], [1.07253242, -0.7626868, -1.34030187], [1.07481623, -0.7566206, -1.17714751], [1.05604482, -0.8064793, -1.33137631], [1.00167441, -0.8548338, -1.33133352], [1.03783941, -0.8548338, -1.27714741], [1.03783941, -0.8548338, -1.22714746], [1.01686168, -0.9105517, -1.30062366], [1.00621057, -0.9388419, -1.27714741], [1.05167437, 0.195166141, -1.15117264], [1.10167456, 0.145166144, -1.16004682], [1.05167437, 0.176572829, -1.12714744], [1.05167437, 0.1670439, -1.07714748], [1.00167441, 0.153091833, -1.07714748], [1.05167437, 0.156027347, -1.02714753], [1.05167437, 0.155830249, -0.97714746], [1.02435708, 0.145166144, -0.97714746], [1.12329721, 0.09516616, -1.12714744], [1.10167456, 0.130955875, -1.12714744], [1.02524948, 0.09516616, -1.12714744], [1.00167441, 0.116994515, -1.12714744], [1.1430819, 0.09516616, -1.07714748], [1.10167456, 0.106546536, -1.07714748], [1.15167451, 0.123463988, -1.02714753], [1.15167451, 0.06646246, -1.12714744], [1.15167451, 0.08996412, -1.07714748], [1.05167437, 0.0675005, -1.07714748], [1.03340125, 0.0451661646, -1.07714748], [1.00167441, 0.0451661646, -1.10887432], [1.15167451, 0.0451661646, -1.04118586], [1.15167451, 0.05803138, -1.02714753], [1.139163, 0.0451661646, -1.02714753], [1.03465438, 0.0451661646, -1.02714753], [1.15167451, 0.0451661646, -1.01393354], [1.03445411, 0.0451661646, -0.97714746], [1.10167456, 0.01251784, -1.12714744], [1.00167441, 0.0292198956, -1.12714744], [1.15167451, 0.00920456648, -1.07714748], [1.1719768, -0.00483384728, -1.02714753], [1.10167456, 0.00767761469, -1.02714753], [1.16104269, -0.00483384728, -0.97714746], [1.088629, -0.05483383, -1.12714744], [1.05167437, -0.0178793967, -1.12714744], [1.08847761, -0.05483383, -1.07714748], [1.05167437, -0.0180307627, -1.07714748], [1.17204762, -0.05483383, -1.02714753], [1.13828778, -0.104833841, -1.12714744], [1.10167456, -0.104833841, -1.09053421], [1.087291, -0.104833841, -1.07714748], [1.00167441, -0.104833841, -1.12662387], [1.05167437, -0.0677886, -1.02714753], [1.136997, -0.154833823, -1.12714744], [1.05167437, -0.14952293, -1.12714744], [1.10167456, -0.154833823, -1.09182489], [1.08615136, -0.154833823, -1.07714748], [1.05167437, -0.154833823, -1.1215539], [1.00167441, -0.154833823, -1.05710232], [1.08639455, -0.154833823, -0.97714746], [1.05167437, -0.1201137, -0.97714746], [1.10167456, -0.2048338, -1.14174628], [1.09582424, -0.2048338, -1.12714744], [1.13836622, -0.2048338, -1.07714748], [1.10167456, -0.168142259, -1.07714748], [1.05167437, -0.2048338, -1.09497535], [1.15167451, -0.2048338, -1.05948138], [1.15167451, -0.1724999, -1.02714753], [1.15167451, -0.174223, -0.97714746], [1.10167456, -0.170742929, -0.97714746], [1.13421178, -0.254833817, -1.12714744], [1.05167437, -0.2331273, -1.12714744], [1.00167441, -0.215099871, -1.12714744], [1.13852835, -0.254833817, -1.07714748], [1.00167441, -0.227392435, -1.07714748], [1.15167451, -0.254833817, -1.05869317], [1.00167441, -0.209273279, -1.02714753], [1.15167451, -0.24880594, -0.97714746], [1.10167456, -0.287371159, -1.12714744], [1.10167456, -0.304833829, -1.06227279], [1.05167437, -0.288974047, -1.02714753], [1.10167456, -0.354833841, -1.01117635], [1.15167451, -0.404833853, -1.00767529], [1.10167456, -0.404833853, -1.01084578], [1.18843746, -0.454835653, -1.02714753], [1.01662517, -0.4548338, -0.97714746], [1.10167456, -0.5048338, -1.01044679], [1.05167437, -0.5048338, -1.01236415], [1.03818178, -0.5548338, -1.12714744], [1.00167441, -0.5183264, -1.12714744], [1.15167546, -0.5524777, -1.07714748], [1.15167546, -0.5524777, -1.02714753], [1.10167456, -0.5548338, -1.06380117], [1.10167456, -0.5190181, -1.02714753], [1.05167437, -0.51945883, -1.02714753], [1.13196373, -0.6048334, -1.07714748], [1.05167437, -0.6048338, -1.11694741], [1.00167441, -0.587142944, -1.07714748], [1.05167437, -0.6048338, -1.06085134], [1.00167441, -0.5870928, -1.02714753], [1.00167441, -0.588562667, -0.97714746], [1.00167441, -0.616446, -1.12714744], [1.11290121, -0.655464232, -1.07714748], [1.05167437, -0.633765042, -0.97714746], [1.093473, -0.707067668, -1.02714753], [1.19884539, 0.145166144, -0.8771475], [1.00167441, 0.145166144, -0.9211534], [1.15167451, 0.154681608, -0.8271475], [1.05167437, 0.147221953, -0.8271475], [1.15412259, 0.145166144, -0.777147532], [1.10167456, 0.09516616, -0.830712736], [1.05167437, 0.0451661646, -0.9627892], [1.15167451, 0.0451661646, -0.9067105], [1.15167451, 0.0944110453, -0.8771475], [1.185765, 0.0451661646, -0.8271475], [1.15167451, 0.08083308, -0.8271475], [1.10167456, 0.09326628, -0.777147532], [1.05167437, 0.0451661646, -0.807143748], [1.10167456, -0.00483384728, -0.9640602], [1.05167437, -0.00483384728, -0.9642823], [1.15167451, -0.00483384728, -0.9092835], [1.08522487, -0.00483384728, -0.8771475], [1.00167441, -0.00483384728, -0.9104943], [1.170141, -0.00483384728, -0.8271475], [1.02940679, -0.00483384728, -0.8271475], [1.10167456, -0.05483383, -0.942721248], [1.113996, -0.05483383, -0.8771475], [1.10167456, -0.05483383, -0.8964516], [1.05167437, -0.05483383, -0.8374424], [1.15167451, -0.05483383, -0.801108837], [1.15167451, -0.104833841, -0.9511392], [1.05167437, -0.104833841, -0.963821769], [1.15167451, -0.104833841, -0.878455639], [1.09329033, -0.104833841, -0.8771475], [1.00167441, -0.104833841, -0.911379337], [1.10167456, -0.05699718, -0.8271475], [1.00167441, -0.104833841, -0.803298652], [1.10167456, -0.154833823, -0.940180659], [1.10167456, -0.154833823, -0.896720767], [1.06571984, -0.154833823, -0.8271475], [1.10167456, -0.162091076, -0.8771475], [1.15167451, -0.2048338, -0.8491119], [1.05167437, -0.197835565, -0.777147532], [1.00167441, -0.2030549, -0.777147532], [1.15167451, -0.226068139, -0.9271475], [1.10167456, -0.2344166, -0.9271475], [1.00167441, -0.254833817, -0.8780285], [1.10167456, -0.220675886, -0.8271475], [1.00167441, -0.213634312, -0.8271475], [1.05167437, -0.209765732, -0.777147532], [1.11045384, -0.304833829, -0.9271475], [1.00167441, -0.304833829, -0.9535792], [1.189925, -0.304833829, -0.8771475], [1.18536115, -0.354833841, -0.9271475], [1.15167451, -0.354833841, -0.943025], [1.05167437, -0.354833841, -0.953193069], [1.00167441, -0.354833841, -0.9544542], [1.05167437, -0.354833841, -0.898742437], [1.171483, -0.354833841, -0.8271475], [1.2046721, -0.411714733, -0.9271475], [1.05167437, -0.404833853, -0.953760862], [1.10167456, -0.404833853, -0.9187956], [1.05167437, -0.404833853, -0.8947245], [1.00167441, -0.404833853, -0.87315017], [1.15167451, -0.4548338, -0.9392741], [1.00167441, -0.4548338, -0.957403064], [1.18715429, -0.45824343, -0.8771475], [1.18807173, -0.455806673, -0.777147532], [1.167386, -0.510749161, -0.928856134], [1.08680892, -0.5048338, -0.9271475], [1.00167441, -0.5048338, -0.958109856], [1.05167437, -0.5048338, -0.900792956], [1.00167441, -0.5048338, -0.8736048], [1.10167456, -0.5548338, -0.930233], [1.1304388, -0.608884037, -0.9271475], [1.10167456, -0.6048338, -0.9516405], [1.05167437, -0.6048338, -0.9524399], [1.05167437, -0.5631937, -0.9271475], [1.00167441, -0.5733045, -0.9271475], [1.13123679, -0.6067644, -0.777147532], [1.1125834, -0.6563086, -0.9271475], [1.11171532, -0.6586141, -0.9621979], [1.0751574, -0.7557146, -0.8771475], [1.10167456, 0.145166144, -0.7649391], [1.05167437, 0.09516616, -0.762710869], [1.00377488, 0.09516616, -0.72714746], [1.00167441, 0.0451661646, -0.764097], [1.00167441, -0.154833823, -0.771750331], [1.19561434, -0.354833841, -0.6771475], [1.139586, -0.354833841, -0.6271475], [1.2055614, -0.409353435, -0.72714746], [1.17444491, -0.404833853, -0.6271475], [1.15167451, -0.404833853, -0.592448], [1.18843746, -0.454835653, -0.6771475], [1.18552041, -0.462583542, -0.6271475], [1.15167451, -0.4548338, -0.5980486], [1.12272239, -0.4548338, -0.5771475], [1.16961312, -0.5048338, -0.6771475], [1.14273453, -0.5048338, -0.6271475], [1.16855216, -0.5076517, -0.5771475], [1.10945964, -0.5548338, -0.6771475], [1.12184072, -0.5548338, -0.6271475], [1.150785, -0.5548424, -0.5771475], [1.13086939, -0.607740343, -0.6271475], [1.07534981, -0.7552034, -0.5771475], [1.00167584, -0.9508864, -0.72714746], [1.15167451, -0.404833853, -0.560061932], [1.18827868, -0.455257058, -0.527147532], [1.15167451, -0.4548338, -0.5553104], [1.16958165, -0.504917264, -0.4271475], [1.153588, -0.5473976, -0.527147532], [0.9833379, 0.295166135, -1.52714753], [0.9516747, 0.345166147, -1.559637], [0.931201935, 0.345166147, -1.52714753], [0.9730482, 0.345166147, -1.47714746], [0.851674557, 0.345166147, -1.493003], [0.851674557, 0.295166135, -1.4944756], [0.8016746, 0.295166135, -1.49731517], [0.901674747, 0.345166147, -1.45554841], [0.901674747, 0.245166153, -1.48721051], [0.9516747, 0.267533422, -1.42714751], [0.9610548, 0.245166153, -1.37714744], [0.9807677, 0.195166141, -1.52714753], [0.8016746, 0.195166141, -1.52541208], [0.9385054, 0.195166141, -1.42714751], [0.9813421, 0.145166144, -1.47714746], [0.901674747, 0.145166144, -1.51535773], [0.989408, 0.09516616, -1.52714753], [0.851674557, 0.09516616, -1.559637], [0.8016746, 0.09516616, -1.559637], [0.985456944, 0.09516616, -1.42714751], [0.901674747, 0.110252053, -1.37714744], [0.9516747, 0.0451661646, -1.559637], [0.8750174, 0.0451661646, -1.47714746], [0.922621965, 0.0451661646, -1.42714751], [0.927629, 0.0451661646, -1.37714744], [0.851674557, -0.00483384728, -1.559637], [0.9516747, -0.05483383, -1.559637], [0.8278487, -0.05483383, -1.52714753], [0.8016746, -0.05483383, -1.559637], [0.926788568, -0.05483383, -1.47714746], [0.8630967, -0.05483383, -1.42714751], [0.988986969, -0.104833841, -1.52714753], [0.8391576, -0.104833841, -1.52714753], [0.8016746, -0.104833841, -1.559637], [0.980110645, -0.104833841, -1.47714746], [0.8578117, -0.104833841, -1.47714746], [0.8984945, -0.104833841, -1.42714751], [0.901674747, -0.08027694, -1.37714744], [0.9799843, -0.154833823, -1.52714753], [0.901674747, -0.154833823, -1.559637], [0.851674557, -0.154833823, -1.559637], [0.851674557, -0.116689712, -1.52714753], [0.963932753, -0.154833823, -1.47714746], [0.937525749, -0.154833823, -1.47714746], [0.950516462, -0.154833823, -1.42714751], [0.975254536, -0.2048338, -1.52714753], [0.901674747, -0.2048338, -1.559637], [0.851674557, -0.2048338, -1.559637], [0.9716196, -0.2048338, -1.47714746], [0.9307709, -0.2048338, -1.47714746], [0.9342725, -0.2048338, -1.37714744], [0.99058795, -0.254833817, -1.52714753], [0.851674557, -0.254833817, -1.559637], [0.8016746, -0.254833817, -1.559637], [0.88082695, -0.254833817, -1.47714746], [0.986816168, -0.254833817, -1.42714751], [0.9386072, -0.304833829, -1.52714753], [0.8299794, -0.304833829, -1.52714753], [0.8016746, -0.304833829, -1.559637], [0.852492332, -0.304833829, -1.47714746], [0.9516747, -0.28928715, -1.37714744], [0.9516747, -0.354833841, -1.559637], [0.940966845, -0.354833841, -1.52714753], [0.9373915, -0.354833841, -1.42714751], [0.901674747, -0.404833853, -1.559637], [0.901674747, -0.390881538, -1.47714746], [0.8757241, -0.404833853, -1.47714746], [0.8016746, -0.404833853, -1.52509427], [0.8835254, -0.4548338, -1.52714753], [0.8206396, -0.4548338, -1.47714746], [0.8284776, -0.4548338, -1.37714744], [0.901674747, -0.5048338, -1.559637], [0.8285301, -0.5048338, -1.47714746], [0.8016746, -0.5048338, -1.52317476], [0.882654667, -0.5048338, -1.42714751], [0.879005432, -0.5548338, -1.52714753], [0.875031948, -0.5548338, -1.47714746], [0.8235619, -0.5548338, -1.47714746], [0.8016746, -0.5548338, -1.51843643], [0.901674747, -0.5548338, -1.41385639], [0.826586962, -0.5548338, -1.37714744], [0.901674747, -0.6048338, -1.559637], [0.8856499, -0.6048338, -1.52714753], [0.873207569, -0.6048338, -1.42714751], [0.807637, -0.6048338, -1.42714751], [0.9516747, -0.6048338, -1.41472709], [0.9516747, -0.5672542, -1.37714744], [0.901674747, -0.6048338, -1.41208124], [0.8380041, -0.6048338, -1.37714744], [0.8391657, -0.6548338, -1.52714753], [0.885035038, -0.6548338, -1.47714746], [0.881718636, -0.6548338, -1.42714751], [0.9516747, -0.6548338, -1.40783143], [0.901674747, -0.6548338, -1.38964748], [0.8016746, -0.7048338, -1.559637], [0.8016746, -0.692324936, -1.52714753], [0.851674557, -0.6881942, -1.47714746], [0.8016746, -0.684828937, -1.47714746], [0.851674557, -0.6848777, -1.42714751], [0.831718445, -0.7048338, -1.42714751], [0.901674747, -0.7048338, -1.40893936], [0.851674557, -0.7048338, -1.40667248], [0.8016746, -0.754833758, -1.559637], [0.836274147, -0.754833758, -1.47714746], [0.8016746, -0.754833758, -1.51592517], [0.8320155, -0.754833758, -1.42714751], [0.9516747, -0.754833758, -1.4107281], [0.8827863, -0.754833758, -1.37714744], [0.8342192, -0.8048338, -1.47714746], [0.8862102, -0.8048338, -1.42714751], [0.882385969, -0.8048338, -1.37714744], [0.884619236, -0.8548338, -1.42714751], [0.901674747, -0.8548338, -1.41232133], [0.8016746, -0.904833734, -1.51014018], [0.9516747, -0.904833734, -1.4115653], [0.813854, -0.904833734, -1.37714744], [0.9516747, -0.9548338, -1.559637], [0.8016746, -0.9548338, -1.559637], [0.8273792, -0.9548338, -1.47714746], [0.8825302, -0.9548338, -1.42714751], [1.00018978, -0.954834044, -1.37714744], [0.901674747, -0.9548338, -1.40603924], [0.9813645, -1.00483489, -1.52714753], [0.9516747, -1.0048337, -1.559637], [0.851674557, -1.0048337, -1.559637], [0.839753866, -1.0048337, -1.52714753], [0.8016746, -0.9667545, -1.52714753], [0.9611912, -1.05841684, -1.54551351], [0.826120853, -1.05483389, -1.47714746], [0.8851626, -1.05483389, -1.42714751], [0.901674747, -1.1048336, -1.559637], [0.851674557, -1.1048336, -1.559637], [0.8355918, -1.1048336, -1.52714753], [0.822978, -1.1048336, -1.42714751], [0.9248903, -1.154834, -1.52714753], [0.905520439, -1.20628166, -1.5366466], [0.851674557, -1.20483375, -1.559637], [0.838827848, -1.20483375, -1.52714753], [0.906065464, -1.20483422, -1.37714744], [0.8242283, -1.20483375, -1.37714744], [0.851674557, -1.2548337, -1.559637], [0.8016746, -1.2548337, -1.559637], [0.8393748, -1.2548337, -1.47714746], [0.90167594, -1.21649265, -1.42714751], [0.8872409, -1.2548337, -1.42714751], [0.851674557, -1.22261477, -1.37714744], [0.9974222, 0.345166147, -1.32714748], [0.986636639, 0.295166135, -1.27714741], [0.9328921, 0.195166141, -1.32714748], [0.929496765, 0.195166141, -1.22714746], [0.985041857, 0.145166144, -1.32714748], [0.983932, 0.145166144, -1.22714746], [0.986314535, 0.09516616, -1.32714748], [0.883914, 0.09516616, -1.27714741], [0.9858515, 0.09516616, -1.22714746], [0.9516747, 0.114463985, -1.17714751], [0.924356461, 0.09516616, -1.17714751], [0.8748486, 0.0451661646, -1.32714748], [0.9304681, 0.0451661646, -1.27714741], [0.9516747, 0.0609892, -1.22714746], [0.9293039, 0.0451661646, -1.17714751], [0.8592992, 0.0451661646, -1.17714751], [0.88035655, -0.00483384728, -1.32714748], [0.9516747, 0.0230070651, -1.22714746], [0.888236046, -0.00483384728, -1.17714751], [0.901674747, -0.026855886, -1.27714741], [0.989388, -0.05483383, -1.22714746], [0.9516747, -0.203423083, -1.27714741], [0.8759482, -0.254833817, -1.32714748], [0.9332454, -0.304833829, -1.32714748], [0.852375031, -0.304833829, -1.27714741], [0.9516747, -0.287205756, -1.22714746], [0.9260731, -0.304833829, -1.17714751], [0.8409741, -0.354833841, -1.27714741], [0.875126839, -0.404833853, -1.32714748], [0.901674747, -0.3864953, -1.27714741], [0.93345046, -0.404833853, -1.22714746], [0.873158, -0.4548338, -1.27714741], [0.826885462, -0.4548338, -1.27714741], [0.8674426, -0.4548338, -1.22714746], [0.934850454, -0.4548338, -1.17714751], [0.901674747, -0.4548338, -1.21032333], [0.8791685, -0.5048338, -1.32714748], [0.815968037, -0.5048338, -1.32714748], [0.9370444, -0.5048338, -1.27714741], [0.9370496, -0.5048338, -1.22714746], [0.901674747, -0.469458818, -1.22714746], [0.937013149, -0.5048338, -1.17714751], [0.82403636, -0.5548338, -1.32714748], [0.9356911, -0.5548338, -1.27714741], [0.9516747, -0.517837942, -1.22714746], [0.863273144, -0.5548338, -1.17714751], [0.9516747, -0.5675079, -1.32714748], [0.9516747, -0.567988336, -1.27714741], [0.901674747, -0.6548338, -1.347761], [0.8016746, -0.6548338, -1.3394953], [0.9516747, -0.6548338, -1.31811988], [0.9516747, -0.6355433, -1.27714741], [0.901674747, -0.614994168, -1.22714746], [0.901674747, -0.6656347, -1.32714748], [0.851674557, -0.658528149, -1.32714748], [0.9516747, -0.7048338, -1.31211376], [0.901674747, -0.7048338, -1.28144634], [0.851674557, -0.7048338, -1.2875489], [0.901674747, -0.754833758, -1.35366023], [0.8016746, -0.754833758, -1.33682883], [0.9516747, -0.754833758, -1.29705775], [0.8500612, -0.754833758, -1.27714741], [0.9958544, -0.8048338, -1.32714748], [0.901674747, -0.8048338, -1.35469067], [0.901674747, -0.8048338, -1.27529943], [0.851674557, -0.8048338, -1.27599788], [0.8298662, -0.8548338, -1.32714748], [0.901674747, -0.8548338, -1.28206849], [0.851674557, -0.8548338, -1.30448294], [0.851674557, -0.904833734, -1.34336829], [0.901674747, -0.904833734, -1.31902552], [0.9516747, -0.9548338, -1.3491354], [0.834095, -0.9548338, -1.32714748], [0.9516747, -0.9548338, -1.275747], [1.00016546, -0.95489794, -1.17714751], [0.901674747, -1.0048337, -1.35949659], [0.9516747, -1.0048337, -1.31309], [0.851674557, -0.9564788, -1.27714741], [0.8183484, -1.0048337, -1.22714746], [0.851674557, -0.9685394, -1.17714751], [0.901674747, -1.037183, -1.32714748], [0.8840995, -1.05483389, -1.32714748], [0.9625404, -1.05483317, -1.27714741], [0.8065617, -1.05483389, -1.27714741], [0.882367849, -1.05483389, -1.22714746], [0.825825453, -1.05483389, -1.17714751], [0.943714857, -1.10483432, -1.27714741], [0.885812044, -1.1048336, -1.27714741], [0.8146484, -1.1048336, -1.27714741], [0.9516747, -1.08369279, -1.17714751], [0.943714857, -1.10483432, -1.17714751], [0.9248903, -1.154834, -1.32714748], [0.8894074, -1.15483379, -1.32714748], [0.851674557, -1.15483379, -1.35575247], [0.901674747, -1.11606, -1.27714741], [0.8275802, -1.15483379, -1.27714741], [0.901674747, -1.11689162, -1.17714751], [0.905520439, -1.20628166, -1.37549949], [0.851674557, -1.20483375, -1.35189307], [0.8016746, -1.20483375, -1.32497549], [0.8847256, -1.26151466, -1.27714741], [0.885754347, -1.25878143, -1.22714746], [0.9414804, 0.195166141, -1.12714744], [0.94449234, 0.145166144, -1.07714748], [0.901674747, 0.145166144, -1.11757815], [0.9028921, 0.09516616, -1.07714748], [0.9615538, 0.09516616, -1.02714753], [0.9516747, 0.09516616, -1.04761147], [0.8594794, 0.0451661646, -1.12714744], [0.8903239, 0.0451661646, -1.07714748], [0.9303534, 0.0451661646, -1.02714753], [0.9314623, 0.0451661646, -0.97714746], [0.8966048, -0.00483384728, -1.07714748], [0.977005959, -0.00483384728, -1.02714753], [0.922114134, -0.00483384728, -1.02714753], [0.901674747, -0.00483384728, -1.06921661], [0.9516747, -0.0316487849, -1.12714744], [0.9516747, -0.05483383, -1.08008862], [0.9359336, -0.104833841, -1.02714753], [0.94084096, -0.154833823, -0.97714746], [0.980190754, -0.2048338, -0.97714746], [0.9516747, -0.216701388, -1.12714744], [0.888643742, -0.254833817, -1.12714744], [0.9516747, -0.215251684, -1.07714748], [0.9516747, -0.227974892, -1.02714753], [0.901674747, -0.238787591, -1.02714753], [0.9516747, -0.234847367, -0.97714746], [0.8935456, -0.254833817, -0.97714746], [0.9516747, -0.2844708, -1.02714753], [0.924117565, -0.304833829, -1.02714753], [0.8618252, -0.354833841, -1.12714744], [0.9249711, -0.404833853, -0.97714746], [0.854826, -0.404833853, -0.97714746], [0.8447335, -0.4548338, -1.07714748], [0.8460262, -0.4548338, -0.97714746], [0.84532547, -0.5048338, -1.12714744], [0.8448465, -0.5048338, -1.07714748], [0.9332702, -0.5048338, -1.02714753], [0.8706629, -0.5548338, -1.12714744], [0.881118059, -0.5548338, -1.07714748], [0.9516747, -0.520136535, -1.02714753], [0.9516747, -0.5974817, -1.12714744], [0.9516747, -0.5704572, -1.02714753], [0.901674747, -0.562376, -1.02714753], [0.901674747, -0.560428, -0.97714746], [0.9998224, -0.955809653, -0.97714746], [0.9516747, -0.974253, -1.12714744], [0.8240149, -1.0048337, -1.12714744], [0.9516747, -0.978709042, -1.02714753], [0.901674747, -0.9727822, -1.02714753], [0.851674557, -0.9719357, -1.02714753], [0.8217349, -1.0048337, -1.02714753], [0.901674747, -1.0048337, -0.9821626], [0.851674557, -1.0048337, -1.00042176], [0.9625404, -1.05483317, -1.12714744], [0.901674747, -1.0349648, -1.12714744], [0.88246727, -1.05483389, -1.12714744], [0.963845253, -1.05136752, -1.07714748], [0.8078439, -1.05483389, -1.02714753], [0.923047543, -1.05483389, -0.97714746], [0.851674557, -1.05483389, -1.0110997], [0.828783035, -1.1048336, -1.12714744], [0.8334143, -1.1048336, -1.07714748], [0.9419339, -1.1095655, -0.97714746], [0.8771558, -1.15483379, -1.07714748], [0.8743501, -1.20483375, -1.12714744], [0.851674557, -1.17447329, -1.12714744], [0.9053509, -1.20673227, -1.02714753], [0.9516747, 0.09516616, -0.929406762], [0.951991558, 0.09516616, -0.8771475], [0.956098557, 0.09516616, -0.8271475], [0.9707172, 0.09516616, -0.777147532], [0.928315639, 0.0451661646, -0.9271475], [0.933079243, 0.0451661646, -0.8271475], [0.9516747, 0.06563634, -0.777147532], [0.9195912, -0.00483384728, -0.777147532], [0.89796567, -0.05483383, -0.9271475], [0.9027288, -0.05483383, -0.8271475], [0.911896467, -0.05483383, -0.777147532], [0.9817283, -0.104833841, -0.9271475], [0.9244752, -0.104833841, -0.777147532], [0.924336433, -0.154833823, -0.9271475], [0.901674747, -0.105997831, -0.8771475], [0.9404354, -0.154833823, -0.777147532], [0.9614856, -0.2048338, -0.9271475], [0.9673257, -0.2048338, -0.8771475], [0.9516747, -0.2247647, -0.9271475], [0.909898043, -0.254833817, -0.9271475], [0.9516747, -0.254833817, -0.889408], [0.9516747, -0.3299508, -0.8771475], [0.9516747, -0.404833853, -0.9515617], [0.901674747, -0.404833853, -0.8886477], [0.9516747, -0.404833853, -0.867422938], [0.9516747, -0.4548338, -0.954038], [0.865664244, -0.4548338, -0.9271475], [0.9013796, -0.4548338, -0.8771475], [0.875424147, -0.5048338, -0.9271475], [0.9516747, -0.5048338, -0.8642138], [0.901674747, -0.5048338, -0.8752835], [0.9516747, -0.5548338, -0.8935422], [0.9516747, -0.526453435, -0.8771475], [0.9516747, -1.0048337, -0.967956662], [0.96026206, -1.06088471, -0.9271475], [0.9244952, -1.15588284, -0.8271475], [0.9486103, 0.0451661646, -0.72714746], [0.9516747, -0.00483384728, -0.7702937], [0.9516747, -0.05483383, -0.7691102], [0.9516747, -0.104833841, -0.7564318], [0.9516747, -0.154833823, -0.745924], [0.9244647, -1.15596366, -0.527147532], [0.7016747, 0.295166135, -1.559637], [0.6516745, 0.295166135, -1.559637], [0.7016747, 0.245166153, -1.559637], [0.601674557, 0.195166141, -1.559637], [0.6516745, 0.145166144, -1.559637], [0.751674652, 0.09516616, -1.559637], [0.601674557, -0.00483384728, -1.559637], [0.601674557, -0.104833841, -1.559637], [0.7016747, -0.254833817, -1.559637], [0.751674652, -0.404833853, -1.559637], [0.751674652, -0.5048338, -1.559637], [0.7016747, -0.6048338, -1.559637], [0.7016747, -0.6548338, -1.559637], [0.6516745, -0.6548338, -1.559637], [0.7234733, -0.6548338, -1.47714746], [0.7870414, -0.7048338, -1.52714753], [0.6915624, -0.7048338, -1.52714753], [0.6516745, -0.7048338, -1.559637], [0.601674557, -0.7048338, -1.559637], [0.7776921, -0.7048338, -1.47714746], [0.7095058, -0.7048338, -1.47714746], [0.751674652, -0.7048338, -1.37989652], [0.788632631, -0.754833758, -1.52714753], [0.678926468, -0.754833758, -1.52714753], [0.6516745, -0.754833758, -1.559637], [0.751674652, -0.8048338, -1.559637], [0.6514039, -0.8048338, -1.52714753], [0.601674557, -0.8048338, -1.559637], [0.751674652, -0.8048338, -1.50624061], [0.6790118, -0.8048338, -1.47714746], [0.751674652, -0.8048338, -1.42936611], [0.734796762, -0.8548338, -1.52714753], [0.6516745, -0.8548338, -1.4853487], [0.601674557, -0.904833734, -1.559637], [0.775141, -0.904833734, -1.42714751], [0.7016747, -0.904833734, -1.45173764], [0.751674652, -0.9548338, -1.559637], [0.7360358, -0.9548338, -1.52714753], [0.751674652, -0.9548338, -1.50247192], [0.6339743, -0.9548338, -1.47714746], [0.601674557, -0.9548338, -1.49735093], [0.751674652, -0.9548338, -1.44122314], [0.751674652, -0.9691753, -1.52714753], [0.6516745, -1.0048337, -1.48160326], [0.601674557, -1.0048337, -1.49952757], [0.7862084, -1.0048337, -1.42714751], [0.751674652, -1.0048337, -1.45671725], [0.751674652, -1.05483389, -1.49061024], [0.7973714, -1.05483389, -1.37714744], [0.768564, -1.1048336, -1.47714746], [0.751674652, -1.1048336, -1.50292051], [0.7016747, -1.1048336, -1.51033819], [0.6516745, -1.1048336, -1.50427115], [0.751674652, -1.15483379, -1.49397933], [0.7016747, -1.15483379, -1.51886535], [0.601674557, -1.15483379, -1.52291763], [0.7417252, -1.20483375, -1.47714746], [0.7016747, -1.20483375, -1.51014042], [0.770292044, -1.20483375, -1.37714744], [0.7831335, -1.2548337, -1.52714753], [0.6516745, -1.2548337, -1.51126945], [0.601674557, -1.2548337, -1.51118553], [0.7511256, -1.2548337, -1.37714744], [0.796837568, -0.7048338, -1.32714748], [0.7971189, -1.1048336, -1.32714748], [0.7927592, -1.2548337, -1.32714748], [0.868415833, -1.30483317, -1.52714753], [0.851674557, -1.30483365, -1.559637], [0.865628242, -1.3122375, -1.47714746], [0.851674557, -1.34929991, -1.52714753], [0.8016746, -1.35483384, -1.559637], [0.8016746, -1.35483384, -1.51777422], [0.847783566, -1.35963511, -1.37714744], [0.8016746, -1.33258271, -1.37714744], [0.8307662, -1.40483308, -1.52714753], [0.8016746, -1.40483379, -1.559637], [0.8016746, -1.40483379, -1.390033], [0.810666561, -1.45821929, -1.544855], [0.8119414, -1.45483351, -1.47714746], [0.866904259, -1.30884838, -1.32714748], [0.867896557, -1.30621314, -1.22714746], [0.849591, -1.35483408, -1.32714748], [0.849591, -1.35483408, -1.27714741], [0.851674557, -1.34929991, -1.17714751], [0.8029618, -1.40483379, -1.32714748], [0.8016746, -1.38817453, -1.27714741], [0.830587149, -1.405309, -1.17714751], [0.824093342, -1.42255688, -1.22714746], [0.8681762, -1.30547023, -1.02714753], [0.8307662, -1.40483308, -1.07714748], [0.8144293, -1.4482255, -1.12714744], [0.8116145, -1.45570183, -1.07714748], [0.8092108, -1.46208644, -0.97714746], [0.8307662, -1.40483308, -0.8771475], [0.809067249, -1.46246743, -0.8771475], [0.8307662, -1.40483308, -0.5771475], [0.8119414, -1.45483351, -0.72714746], [0.8058605, -1.47098494, -0.5771475], [0.8016765, -1.48209739, -0.47714752], [0.7016747, -1.30483365, -1.51225078], [0.6516745, -1.30483365, -1.507088], [0.601674557, -1.30483365, -1.47900259], [0.736047, -1.30483365, -1.42714751], [0.766337156, -1.30483365, -1.37714744], [0.7079308, -1.35483384, -1.47714746], [0.6516745, -1.34545827, -1.47714746], [0.7608843, -1.35483384, -1.42714751], [0.751674652, -1.35483384, -1.45501661], [0.6516745, -1.35483384, -1.46479166], [0.601674557, -1.32524967, -1.42714751], [0.6370661, -1.35483384, -1.37714744], [0.787443638, -1.40483379, -1.52714753], [0.601674557, -1.39020872, -1.52714753], [0.751674652, -1.38281512, -1.42714751], [0.6516745, -1.37030482, -1.42714751], [0.630696535, -1.40483379, -1.42714751], [0.601674557, -1.40483379, -1.4434104], [0.601674557, -1.37981844, -1.42714751], [0.751674652, -1.39874029, -1.37714744], [0.7016747, -1.3873589, -1.37714744], [0.751674652, -1.45483375, -1.559637], [0.751674652, -1.43873167, -1.52714753], [0.6516745, -1.45483375, -1.559637], [0.751674652, -1.42548943, -1.47714746], [0.7016747, -1.42577052, -1.47714746], [0.601674557, -1.438571, -1.47714746], [0.751674652, -1.45483375, -1.45687521], [0.7016747, -1.42372847, -1.42714751], [0.6516745, -1.42581177, -1.42714751], [0.7929516, -1.50527191, -1.52714753], [0.79311657, -1.50483394, -1.42714751], [0.751674652, -1.4875319, -1.42714751], [0.751674652, -1.55483389, -1.559637], [0.7016747, -1.60483384, -1.559637], [0.7516742, -1.61490726, -1.52714753], [0.7016747, -1.65483379, -1.559637], [0.6516745, -1.70483375, -1.559637], [0.701674461, -1.74770951, -1.52714753], [0.6516745, -1.7548337, -1.559637], [0.651316643, -1.800693, -1.52714753], [0.601674557, -1.31186342, -1.27714741], [0.641422033, -1.35483384, -1.22714746], [0.601674557, -1.308485, -1.17714751], [0.751674652, -1.40187407, -1.32714748], [0.601674557, -1.38968945, -1.32714748], [0.6516745, -1.38221693, -1.27714741], [0.635771036, -1.40483379, -1.27714741], [0.6516745, -1.38083434, -1.22714746], [0.601674557, -1.40483379, -1.265882], [0.7016747, -1.42003441, -1.32714748], [0.6516745, -1.42073727, -1.27714741], [0.6516745, -1.42366338, -1.22714746], [0.601674557, -1.44356823, -1.22714746], [0.751674652, -1.42166257, -1.17714751], [0.601674557, -1.44367886, -1.17714751], [0.751674652, -1.48626828, -1.32714748], [0.7016747, -1.48503208, -1.27714741], [0.7016747, -1.48358321, -1.22714746], [0.751674652, -1.48006749, -1.17714751], [0.6516745, -1.48622012, -1.17714751], [0.6108911, -1.30483365, -1.07714748], [0.637268543, -1.35483384, -1.02714753], [0.6516745, -1.39363456, -1.12714744], [0.6516745, -1.394202, -0.97714746], [0.7016747, -1.413923, -1.02714753], [0.79311657, -1.50483394, -1.07714748], [0.7016747, -1.480217, -1.02714753], [0.774291039, -1.55483556, -1.02714753], [0.7747624, -1.55358362, -1.02714753], [0.6516743, -1.80080414, -1.07714748], [0.6516745, -1.38384581, -0.8771475], [0.7016747, -1.4009707, -0.8271475], [0.703611135, -1.40483379, -0.777147532], [0.6516745, -1.36275, -0.777147532], [0.601674557, -1.44204092, -0.8771475], [0.8094301, -1.46150422, -0.777147532], [0.6516745, -1.43383384, -0.777147532], [0.6516745, -1.48544455, -0.9271475], [0.6516745, -1.48762059, -0.8271475], [0.7016747, -1.4845314, -0.777147532], [0.774291039, -1.55483556, -0.8771475], [0.6989896, -1.75484133, -0.9271475], [0.618427038, -1.30483365, -0.72714746], [0.601674557, -1.26433516, -0.6771475], [0.6516745, -1.33745527, -0.6771475], [0.601674557, -1.33425021, -0.6271475], [0.6516745, -1.35483384, -0.612635136], [0.601674557, -1.35483384, -0.602572858], [0.7016747, -1.38196421, -0.6771475], [0.601674557, -1.38416243, -0.6771475], [0.601674557, -1.40483379, -0.6498657], [0.601674557, -1.44204879, -0.72714746], [0.751674652, -1.42724371, -0.6771475], [0.6516745, -1.433075, -0.6771475], [0.751674652, -1.43741512, -0.6271475], [0.601674557, -1.45483375, -0.6558748], [0.751674652, -1.44963837, -0.5771475], [0.7016747, -1.41363764, -0.5771475], [0.7016747, -1.48647881, -0.6771475], [0.7016747, -1.48475051, -0.6271475], [0.6516745, -1.5048337, -0.607712269], [0.601674557, -1.5048337, -0.6062866], [0.774291039, -1.55483556, -0.6771475], [0.774502754, -1.55427337, -0.6771475], [0.751674652, -1.53988457, -0.5771475], [0.7016747, -1.5335722, -0.5771475], [0.601674557, -1.55483389, -0.6086516], [0.7516742, -1.61490726, -0.5771475], [0.601674557, -1.65483379, -0.611553431], [0.701674461, -1.74770951, -0.5771475], [0.6516745, -1.40483379, -0.55234766], [0.601674557, -1.40483379, -0.552503049], [0.751674652, -1.48496962, -0.527147532], [0.79290843, -1.50538635, -0.47714752], [0.6516745, -1.48062682, -0.47714752], [0.601674557, -1.5048337, -0.483893275], [0.7016747, -1.55483389, -0.5577949], [0.6516745, -1.55483389, -0.5577031], [0.77148366, -1.56229186, -0.479046166], [0.7016747, -1.55483389, -0.4699552], [0.601674557, -1.60483384, -0.478580028], [0.754996061, -1.60608435, -0.43332243], [0.6516745, -1.60483384, -0.45875752], [0.7016747, -1.65483379, -0.5673522], [0.6516745, -1.65483379, -0.5628941], [0.6516745, -1.65321231, -0.47714752], [0.601674557, -1.65483379, -0.4840322], [0.7016747, -1.65483379, -0.447649956], [0.6516745, -1.70483375, -0.5631091], [0.601674557, -1.70483375, -0.558145165], [0.601674557, -1.67927337, -0.47714752], [0.7233305, -1.6901896, -0.4271475], [0.6516745, -1.69913411, -0.4271475], [0.601674557, -1.6922276, -0.4271475], [0.6989896, -1.75484133, -0.527147532], [0.6516745, -1.74207187, -0.527147532], [0.601674557, -1.741699, -0.527147532], [0.6989896, -1.75484133, -0.47714752], [0.6516745, -1.72189736, -0.47714752], [0.601674557, -1.72103286, -0.47714752], [0.6516745, -1.7548337, -0.45257473], [0.601674557, -1.7548337, -0.45996958], [0.6516743, -1.80080414, -0.527147532], [0.6516743, -1.80080414, -0.47714752], [0.601674557, -1.785259, -0.47714752], [0.5516746, 0.345166147, -1.559637], [0.451674461, 0.295166135, -1.559637], [0.4016745, 0.345166147, -1.559637], [0.501674652, 0.245166153, -1.559637], [0.5516746, 0.195166141, -1.559637], [0.501674652, 0.195166141, -1.52926087], [0.451674461, 0.195166141, -1.53303814], [0.4016745, 0.195166141, -1.559637], [0.451674461, 0.145166144, -1.559637], [0.4016745, 0.145166144, -1.559637], [0.501674652, 0.09516616, -1.559637], [0.4016745, 0.0451661646, -1.559637], [0.451674461, -0.254833817, -1.559637], [0.4016745, -0.254833817, -1.559637], [0.4016745, -0.404833853, -1.559637], [0.501674652, -0.4548338, -1.559637], [0.5516746, -0.5048338, -1.559637], [0.451674461, -0.754833758, -1.559637], [0.5516746, -0.904833734, -1.559637], [0.5516746, -0.9548338, -1.559637], [0.5516746, -1.05483389, -1.559637], [0.501674652, -1.05483389, -1.559637], [0.501674652, -1.1048336, -1.52943027], [0.483078241, -1.15483379, -1.52714753], [0.4016745, -1.15483379, -1.559637], [0.5516746, -1.15483379, -1.526272], [0.5516746, -1.20483375, -1.50781047], [0.501674652, -1.20483375, -1.47856379], [0.451674461, -1.18827629, -1.47714746], [0.4016745, -1.17338729, -1.47714746], [0.451674461, -1.17091393, -1.37714744], [0.4016745, -1.2548337, -1.559637], [0.4016745, -1.23305178, -1.47714746], [0.5516746, -1.22046566, -1.42714751], [0.4016745, -1.2356174, -1.42714751], [0.573938847, -1.2548337, -1.37714744], [0.4016745, -1.15104079, -1.17714751], [0.501674652, -1.17721462, -1.17714751], [0.518715858, -1.2548337, -1.32714748], [0.501674652, -1.22789288, -1.32714748], [0.4016745, -1.235796, -1.32714748], [0.5191374, -1.2548337, -1.22714746], [0.501674652, -1.226728, -1.17714751], [0.4016745, -1.23360515, -1.17714751], [0.557933331, -1.20483375, -1.12714744], [0.5516746, -1.19936776, -1.07714748], [0.451674461, -1.16858459, -0.97714746], [0.5692308, -1.2548337, -1.12714744], [0.522696257, -1.2548337, -1.02714753], [0.501674652, -1.2243309, -1.02714753], [0.5236554, -1.2548337, -0.97714746], [0.5516746, -1.20483375, -0.8830837], [0.451674461, -1.17435265, -0.777147532], [0.5663116, -1.2548337, -0.9271475], [0.451674461, -1.22675681, -0.9271475], [0.4016745, -1.23129916, -0.9271475], [0.5660262, -1.2548337, -0.8771475], [0.472139359, -1.2548337, -0.8271475], [0.451674461, -1.22481179, -0.777147532], [0.501674652, -1.18676281, -0.72714746], [0.4016745, -1.15500832, -0.6771475], [0.501674652, -1.20483375, -0.630533], [0.4016745, -1.20483375, -0.619039655], [0.5697236, -1.2548337, -0.72714746], [0.5516746, -1.21604371, -0.6771475], [0.4016745, -1.22928786, -0.6771475], [0.5516746, -1.2548337, -0.634031057], [0.4016745, -1.2548337, -0.6417619], [0.501674652, -1.2548337, -0.623981833], [0.201674461, 0.295166135, -1.559637], [0.351674557, -0.00483384728, -1.559637], [0.3016746, -0.2048338, -1.559637], [0.2516744, -0.2048338, -1.559637], [0.3016746, -1.0048337, -1.559637], [0.201674461, -1.0048337, -1.559637], [0.2516744, -1.05483389, -1.559637], [0.351674557, -1.1048336, -1.559637], [0.201674461, -1.06831884, -1.52714753], [0.201674461, -1.07698917, -1.47714746], [0.351674557, -1.14980912, -1.52714753], [0.3016746, -1.108449, -1.52714753], [0.216016054, -1.15483379, -1.52714753], [0.201674461, -1.15483379, -1.559637], [0.358423948, -1.15483379, -1.47714746], [0.3016746, -1.12236881, -1.47714746], [0.201674461, -1.12778616, -1.47714746], [0.201674461, -1.12768745, -1.42714751], [0.3016746, -1.11945724, -1.37714744], [0.3016746, -1.20483375, -1.559637], [0.3016746, -1.18385267, -1.52714753], [0.2516744, -1.20483375, -1.559637], [0.2516744, -1.19049215, -1.52714753], [0.3016746, -1.18063164, -1.42714751], [0.3016746, -1.179887, -1.37714744], [0.351674557, -1.2548337, -1.559637], [0.351674557, -1.23801661, -1.47714746], [0.351674557, -1.23807836, -1.37714744], [0.201674461, -1.12084317, -1.27714741], [0.351674557, -1.173151, -1.32714748], [0.2516744, -1.18394089, -1.32714748], [0.2516744, -1.18293524, -1.27714741], [0.201674461, -1.186454, -1.22714746], [0.201674461, -1.12172937, -1.07714748], [0.201674461, -1.18550849, -1.12714744], [0.2516744, -1.18237066, -1.07714748], [0.2516744, -1.09974194, -0.777147532], [0.201674461, -1.12110925, -0.9271475], [0.351674557, -1.1699605, -0.9271475], [0.3016746, -1.177295, -0.8771475], [0.2516744, -1.181067, -0.8271475], [0.351674557, -1.234235, -0.8771475], [0.273991585, -1.1048336, -0.6771475], [0.201674461, -1.08330441, -0.6271475], [0.201674461, -1.12289762, -0.6771475], [0.201674461, -1.15483379, -0.643318534], [0.3016746, -1.17614722, -0.72714746], [0.2516744, -1.18192458, -0.6771475], [0.351674557, -1.16343379, -0.6271475], [0.2516744, -1.20483375, -0.645186543], [0.2516744, -1.20483375, -0.599055767], [0.351674557, -1.233758, -0.72714746], [0.3016746, -1.23541689, -0.6771475], [0.3016746, -1.2548337, -0.574095845], [0.151674509, 0.145166144, -1.559637], [0.00167441368, -0.05483383, -1.559637], [0.101674557, -0.304833829, -1.559637], [0.101674557, -0.9548338, -1.559637], [0.101674557, -1.0048337, -1.559637], [0.00167441368, -1.0048337, -1.559637], [0.151674509, -1.05483389, -1.559637], [0.10722661, -1.05483389, -1.52714753], [0.051674366, -1.03389525, -1.52714753], [0.051674366, -1.03135324, -1.42714751], [0.051674366, -1.1048336, -1.559637], [0.051674366, -1.07356524, -1.52714753], [0.151674509, -1.06779432, -1.47714746], [0.051674366, -1.07671833, -1.42714751], [0.151674509, -1.06369352, -1.37714744], [0.101674557, -1.15483379, -1.559637], [0.101674557, -1.1360147, -1.47714746], [0.101674557, -1.13562632, -1.37714744], [0.00167441368, -1.0053072, -1.32714748], [0.151674509, -1.06947184, -1.17714751], [0.151674509, -1.12799, -1.22714746], [0.00167441368, -1.0814805, -0.97714746], [0.151674509, -1.12905455, -1.12714744], [0.101674557, -1.13317847, -1.07714748], [0.101674557, -1.042819, -0.9271475], [0.051674366, -1.07543659, -0.9271475], [0.00167441368, -1.08110142, -0.8771475], [0.101674557, -1.13280582, -0.8771475], [0.00167441368, -0.994386852, -0.6771475], [0.00167441368, -1.0048337, -0.627016962], [0.07275009, -1.1048336, -0.72714746], [0.051674366, -1.07723, -0.6771475], [0.00167441368, -1.080795, -0.6771475], [0.051674366, -1.1048336, -0.6481049], [0.00167441368, -1.1048336, -0.648451], [0.151674509, -1.1048336, -0.6108616], [0.051674366, -1.1048336, -0.5937199], [0.101674557, -1.13265061, -0.6771475], [0.101674557, -1.20483375, -0.5819403], [0.101674557, -1.2548337, -0.6572592], [0.051674366, -1.2548337, -0.5948193], [0.00167441368, -1.15483379, -0.5747858], [-0.148325443, 0.345166147, -1.559637], [-0.148325443, 0.195166141, -1.559637], [-0.148325443, -0.154833823, -1.559637], [-0.09832525, -0.5548338, -1.559637], [-0.0483253, -0.754833758, -1.559637], [-0.09832525, -0.9548338, -1.559637], [-0.148325443, -0.9548338, -1.559637], [-0.1983254, -0.9548338, -1.52756822], [-0.1983254, -0.9368251, -1.47714746], [-0.148325443, -0.9461712, -1.42714751], [-0.148325443, -0.9451162, -1.37714744], [-0.148325443, -0.9678733, -1.52714753], [-0.148325443, -0.9656146, -1.47714746], [-0.09832525, -0.970407546, -1.42714751], [-0.1983254, -0.9757913, -1.37714744], [-0.0483253, -1.0087688, -1.52714753], [-0.137348175, -1.05483389, -1.52714753], [-0.148325443, -1.037673, -1.52714753], [-0.148325443, -1.03565359, -1.47714746], [-0.09832525, -1.03030539, -1.42714751], [-0.148325443, -1.03444028, -1.37714744], [-0.0483253, -1.09072781, -1.52714753], [-0.09832525, -1.1048336, -1.559637], [-0.09832525, -1.08942151, -1.47714746], [-0.0483253, -1.08636284, -1.42714751], [-0.136169434, -0.9548338, -1.17714751], [-0.1983254, -0.9739223, -1.22714746], [-0.09832525, -1.02830315, -1.32714748], [-0.0483253, -1.08522153, -1.22714746], [-0.1983254, -0.922818244, -1.12714744], [-0.1983254, -0.975073636, -1.02714753], [-0.148325443, -1.03294706, -1.12714744], [-0.07814288, -1.05483389, -1.02714753], [-0.09832525, -1.02791071, -1.02714753], [-0.0483253, -1.02146649, -0.97714746], [-0.116458893, -0.9548338, -0.9271475], [-0.1983254, -0.9731633, -0.9271475], [-0.1983254, -0.9741941, -0.8271475], [-0.148325443, -1.03242612, -0.9271475], [-0.1983254, -0.916529953, -0.6771475], [-0.1983254, -0.933788836, -0.6271475], [-0.148325443, -0.9548338, -0.619676], [-0.1983254, -0.974844754, -0.6771475], [-0.1983254, -1.0048337, -0.646200955], [-0.0483253, -1.02228856, -0.6771475], [-0.148325443, -1.03235841, -0.6771475], [-0.0483253, -1.05483389, -0.643457651], [-0.148325443, -1.05483389, -0.6517836], [-0.148325443, -1.05483389, -0.5845219], [-0.1983254, -1.05483389, -0.5936103], [-0.1983254, -1.15483379, -0.584446132], [-0.09832525, -1.20483375, -0.6600136], [-0.1983254, -1.20483375, -0.6583631], [-0.148325443, -1.2548337, -0.585850954], [-0.0483253, -1.1048336, -0.571393132], [-0.0483253, -1.20483375, -0.564853668], [-0.0483253, -1.2548337, -0.566934], [-0.3483255, 0.09516616, -1.559637], [-0.248325348, -0.354833841, -1.559637], [-0.3483255, -0.754833758, -1.559637], [-0.3257501, -0.8548338, -1.52714753], [-0.398325443, -0.8154494, -1.52714753], [-0.3483255, -0.844370067, -1.47714746], [-0.398325443, -0.8548338, -1.49542987], [-0.248325348, -0.904833734, -1.559637], [-0.2859435, -0.904833734, -1.52714753], [-0.2983253, -0.8826863, -1.47714746], [-0.239320517, -0.904833734, -1.42714751], [-0.3483255, -0.856457531, -1.42714751], [-0.3483255, -0.9548338, -1.559637], [-0.248325348, -0.9209135, -1.47714746], [-0.3483255, -0.9354903, -1.47714746], [-0.36396718, -0.9548338, -1.47714746], [-0.2983253, -0.926660359, -1.37714744], [-0.2983253, -0.990569055, -1.52714753], [-0.2983253, -0.988558, -1.47714746], [-0.248325348, -0.9830865, -1.37714744], [-0.362668753, -1.0048337, -1.37714744], [-0.248325348, -1.05483389, -1.559637], [-0.248325348, -1.04030728, -1.47714746], [-0.2983253, -1.20483375, -1.559637], [-0.2983253, -0.8704217, -1.32714748], [-0.3483255, -0.87220186, -1.27714741], [-0.2983253, -0.8749729, -1.22714746], [-0.248325348, -0.900739849, -1.17714751], [-0.398325443, -0.9024914, -1.17714751], [-0.2983253, -0.92672807, -1.32714748], [-0.3483255, -0.932900131, -1.32714748], [-0.3656404, -0.9548338, -1.22714746], [-0.2983253, -0.923027933, -1.17714751], [-0.3622682, -1.0048337, -1.17714751], [-0.3483255, -0.8789887, -1.07714748], [-0.398325443, -0.9046256, -1.07714748], [-0.2983253, -0.875891268, -0.97714746], [-0.3483255, -0.8711384, -0.97714746], [-0.398325443, -0.899778068, -0.97714746], [-0.2983253, -0.923358, -1.07714748], [-0.315191031, -0.9548338, -1.07714748], [-0.3483255, -0.9284485, -1.02714753], [-0.366083384, -0.9548338, -1.02714753], [-0.2983253, -0.923015535, -0.97714746], [-0.362568855, -1.0048337, -1.07714748], [-0.248325348, -0.980000556, -0.97714746], [-0.3622303, -1.0048337, -0.97714746], [-0.248325348, -0.904833734, -0.904983163], [-0.3483255, -0.8686717, -0.8771475], [-0.2983253, -0.876612842, -0.777147532], [-0.3483255, -0.928809, -0.9271475], [-0.3659916, -0.9548338, -0.9271475], [-0.3483255, -0.9276516, -0.8271475], [-0.2983253, -0.922455847, -0.777147532], [-0.2983253, -0.9837038, -0.9271475], [-0.398325443, -1.0048337, -0.941052437], [-0.398325443, -0.987167656, -0.9271475], [-0.2983253, -0.983769, -0.8271475], [-0.398325443, -0.986237, -0.8271475], [-0.248325348, -0.9799629, -0.777147532], [-0.364246845, -1.0048337, -0.777147532], [-0.398325443, -0.889756143, -0.6771475], [-0.239649057, -0.904833734, -0.6271475], [-0.3483255, -0.871583164, -0.6271475], [-0.398325443, -0.904833734, -0.622020364], [-0.366663933, -0.9548338, -0.72714746], [-0.2983253, -0.9221391, -0.6771475], [-0.3483255, -0.926900566, -0.6771475], [-0.2983253, -0.9548338, -0.6382494], [-0.3483255, -0.9548338, -0.635862052], [-0.2983253, -0.9548338, -0.616222], [-0.248325348, -0.980090559, -0.6771475], [-0.3665173, -1.0048337, -0.6771475], [-0.248325348, -1.0048337, -0.6456293], [-0.3483255, -1.0048337, -0.6402057], [-0.248325348, -1.0048337, -0.60478425], [-0.398325443, -1.05483389, -0.641163349], [-0.398325443, -1.05483389, -0.6099785], [-0.2983253, -1.1048336, -0.589039266], [-0.3483255, -1.1048336, -0.6001952], [-0.398325443, -1.1048336, -0.5941138], [-0.398325443, -1.15483379, -0.6515378], [-0.248325348, -1.15483379, -0.5985404], [-0.2983253, -1.15483379, -0.5791001], [-0.3483255, -1.14394236, -0.5771475], [-0.398325443, -1.15483379, -0.5874882], [-0.248325348, -1.20483375, -0.5995467], [-0.2983253, -1.20483375, -0.590255857], [-0.398325443, -1.2548337, -0.6603509], [-0.2983253, -1.2548337, -0.598704159], [-0.3138132, -1.2548337, -0.5771475], [-0.3483255, -1.19007277, -0.527147532], [-0.3590815, -1.20483375, -0.527147532], [-0.398325443, -1.20483375, -0.5618293], [-0.3483255, -1.208231, -0.47714752], [-0.3483255, -1.23424554, -0.4271475], [-0.398325443, -1.25151277, -0.4271475], [-0.5983255, 0.0451661646, -1.559637], [-0.5983255, -0.7048338, -1.559637], [-0.4483254, -0.754833758, -1.559637], [-0.4483254, -0.7983988, -1.52714753], [-0.498325348, -0.8048338, -1.559637], [-0.5983255, -0.8548338, -1.559637], [-0.498325348, -0.8548338, -1.4810257], [-0.5983255, -0.904833734, -1.559637], [-0.4483254, -0.904833734, -1.48510039], [-0.498325348, -0.904833734, -1.5023104], [-0.548325539, -0.904833734, -1.4817071], [-0.412023783, -0.904833734, -1.42714751], [-0.498325348, -0.9548338, -1.495072], [-0.5434406, -0.9548338, -1.47714746], [-0.5983255, -0.9548338, -1.52210367], [-0.548325539, -0.9548338, -1.46836734], [-0.4102354, -1.0048337, -1.52714753], [-0.4895985, -1.0048337, -1.42714751], [-0.498325348, -1.0048337, -1.45738053], [-0.548325539, -1.0048337, -1.435742], [-0.4483254, -0.9643796, -1.37714744], [-0.4483254, -1.05483389, -1.559637], [-0.4483254, -1.04292369, -1.52714753], [-0.498325348, -1.03432941, -1.52714753], [-0.4118538, -1.05483389, -1.47714746], [-0.4483254, -1.05483389, -1.51743209], [-0.498325348, -1.05483389, -1.508722], [-0.5983255, -1.00910354, -1.47714746], [-0.498325348, -1.04074979, -1.42714751], [-0.520194054, -1.05483389, -1.42714751], [-0.548325539, -1.05483389, -1.44506013], [-0.498325348, -1.05483389, -1.39098275], [-0.587728262, -1.05483389, -1.37714744], [-0.465387821, -1.1048336, -1.47714746], [-0.498325348, -1.1048336, -1.51455], [-0.548325539, -1.1048336, -1.50213575], [-0.465239763, -1.1048336, -1.42714751], [-0.559088945, -1.1048336, -1.42714751], [-0.5983255, -1.05628681, -1.42714751], [-0.4483254, -1.09154248, -1.37714744], [-0.537584066, -1.1048336, -1.37714744], [-0.5983255, -1.15483379, -1.559637], [-0.466285944, -1.15483379, -1.47714746], [-0.498325348, -1.15483379, -1.51379156], [-0.5983255, -1.15483379, -1.399143], [-0.5983255, -1.10511184, -1.37714744], [-0.498325348, -1.1868732, -1.47714746], [-0.5983255, -1.17773247, -1.47714746], [-0.498325348, -1.18774986, -1.42714751], [-0.548325539, -1.20483375, -1.4572978], [-0.5983255, -1.20483375, -1.4529953], [-0.5983255, -1.2548337, -1.45110619], [-0.5747278, -1.2548337, -1.37714744], [-0.5386226, -0.8548338, -1.17714751], [-0.5983255, -0.8548338, -1.19775021], [-0.5983255, -0.8421939, -1.17714751], [-0.403210878, -0.904833734, -1.32714748], [-0.548325539, -0.904833734, -1.24909866], [-0.5983255, -0.904833734, -1.24075532], [-0.531358, -0.904833734, -1.17714751], [-0.5407231, -0.9548338, -1.22714746], [-0.5983255, -0.9625221, -1.27714741], [-0.467424154, -1.0048337, -1.22714746], [-0.5521741, -1.0048337, -1.22714746], [-0.4483254, -0.9688464, -1.17714751], [-0.486198664, -1.05483389, -1.27714741], [-0.410991669, -1.05483389, -1.17714751], [-0.4850235, -1.05483389, -1.17714751], [-0.548325539, -1.0080173, -1.17714751], [-0.4717121, -1.1048336, -1.32714748], [-0.498325348, -1.07114482, -1.32714748], [-0.584682941, -1.1048336, -1.32714748], [-0.5983255, -1.05580473, -1.32714748], [-0.498325348, -1.08777666, -1.27714741], [-0.5983255, -1.07211637, -1.27714741], [-0.4483254, -1.09223747, -1.22714746], [-0.5215485, -1.1048336, -1.22714746], [-0.482189417, -1.1048336, -1.17714751], [-0.5983255, -1.06318569, -1.17714751], [-0.469207525, -1.15483379, -1.32714748], [-0.548325539, -1.15483379, -1.29770815], [-0.4661529, -1.15483379, -1.17714751], [-0.533684254, -1.15483379, -1.17714751], [-0.498325348, -1.18395162, -1.32714748], [-0.522223949, -1.20483375, -1.32714748], [-0.5983255, -1.19181585, -1.32714748], [-0.498325348, -1.1892693, -1.27714741], [-0.525680065, -1.20483375, -1.27714741], [-0.548325539, -1.16354251, -1.27714741], [-0.5983255, -1.16346741, -1.27714741], [-0.548325539, -1.20483375, -1.24286175], [-0.498325348, -1.187006, -1.17714751], [-0.5921693, -1.2548337, -1.32714748], [-0.548325539, -1.2548337, -1.23648143], [-0.5983255, -1.2548337, -1.27623653], [-0.5983255, -0.7797802, -1.07714748], [-0.548325539, -0.8048338, -1.07463562], [-0.548325539, -0.7953462, -1.02714753], [-0.548325539, -0.7872117, -0.97714746], [-0.5983255, -0.7613806, -0.97714746], [-0.5275428, -0.8548338, -1.12714744], [-0.4995141, -0.8548338, -1.02714753], [-0.5983255, -0.83102566, -0.97714746], [-0.5983255, -0.8863295, -1.12714744], [-0.5121038, -0.904833734, -1.07714748], [-0.57919, -0.904833734, -1.07714748], [-0.5774219, -0.904833734, -1.02714753], [-0.4915104, -0.904833734, -0.97714746], [-0.498325348, -0.904833734, -1.00335], [-0.5797794, -0.904833734, -0.97714746], [-0.4237492, -0.9548338, -1.12714744], [-0.574434042, -0.9548338, -1.12714744], [-0.5983255, -0.9548338, -1.14562666], [-0.5318396, -0.9548338, -1.07714748], [-0.570796, -0.9548338, -1.07714748], [-0.426392555, -0.9548338, -1.02714753], [-0.5796354, -0.9548338, -1.02714753], [-0.4483254, -0.9548338, -0.992491961], [-0.5983255, -0.9725304, -1.12714744], [-0.4711125, -1.0048337, -1.07714748], [-0.5983255, -0.975318968, -1.07714748], [-0.4853871, -1.0048337, -1.02714753], [-0.498325348, -1.0048337, -1.0136131], [-0.5807805, -1.0048337, -0.97714746], [-0.481558084, -1.05483389, -1.12714744], [-0.5983255, -1.03598619, -1.12714744], [-0.4837315, -1.05483389, -1.07714748], [-0.548325539, -1.007561, -1.07714748], [-0.5983255, -1.04804945, -1.07714748], [-0.498325348, -1.05483389, -1.04366863], [-0.548325539, -1.05483389, -1.03594565], [-0.5983255, -1.02009654, -0.97714746], [-0.498325348, -1.1048336, -1.13769341], [-0.4483254, -1.09182119, -1.02714753], [-0.468223333, -1.1048336, -1.02714753], [-0.498325348, -1.1048336, -1.06662488], [-0.548325539, -1.1048336, -1.04183781], [-0.4483254, -1.090374, -0.97714746], [-0.498325348, -1.08440971, -0.97714746], [-0.5983255, -1.1048336, -1.01802266], [-0.5048549, -1.15483379, -1.12714744], [-0.522258759, -1.15483379, -1.07714748], [-0.4647448, -1.15483379, -1.02714753], [-0.5983255, -1.15483379, -1.052834], [-0.537587166, -1.20483375, -1.12714744], [-0.548325539, -1.20483375, -1.14591575], [-0.548325539, -1.19431233, -1.12714744], [-0.5983255, -1.20483375, -1.14548516], [-0.5983255, -1.1913588, -1.12714744], [-0.5176141, -1.20483375, -1.07714748], [-0.519554138, -1.20483375, -1.02714753], [-0.5983255, -1.22517061, -1.12714744], [-0.548325539, -1.23554492, -1.07714748], [-0.548325539, -1.23360515, -1.02714753], [-0.5983255, -1.24017739, -1.02714753], [-0.552676439, -0.754833758, -0.8771475], [-0.5983255, -0.73854, -0.8271475], [-0.548325539, -0.754833758, -0.821247339], [-0.498325348, -0.8011618, -0.9271475], [-0.491675854, -0.8048338, -0.8271475], [-0.498325348, -0.8048338, -0.8099149], [-0.5983255, -0.8331786, -0.9271475], [-0.4747455, -0.8548338, -0.8771475], [-0.522109032, -0.8548338, -0.8771475], [-0.548325539, -0.8284059, -0.8771475], [-0.548325539, -0.8548338, -0.8439627], [-0.5983255, -0.8548338, -0.841210663], [-0.5983255, -0.8548338, -0.810171843], [-0.5250585, -0.904833734, -0.9271475], [-0.4167962, -0.904833734, -0.8771475], [-0.458935738, -0.904833734, -0.8771475], [-0.462083578, -0.904833734, -0.8271475], [-0.4123702, -0.904833734, -0.777147532], [-0.498325348, -0.904833734, -0.809360266], [-0.4483254, -0.928186834, -0.9271475], [-0.5282912, -0.9548338, -0.9271475], [-0.548325539, -0.9548338, -0.94390285], [-0.4483254, -0.9175051, -0.8771475], [-0.5258069, -0.9548338, -0.8771475], [-0.4483254, -0.9137803, -0.8271475], [-0.4483254, -0.950727761, -0.777147532], [-0.498325348, -0.9548338, -0.799904346], [-0.548325539, -0.9548338, -0.8018161], [-0.4483254, -1.0048337, -0.943250537], [-0.4483254, -0.9803495, -0.9271475], [-0.498325348, -1.0048337, -0.947399735], [-0.548325539, -1.0048337, -0.9446925], [-0.4483254, -0.9779019, -0.8771475], [-0.498325348, -0.982315242, -0.8771475], [-0.422175169, -1.0048337, -0.8271475], [-0.4483254, -1.0048337, -0.8532977], [-0.498325348, -1.0048337, -0.850599647], [-0.498325348, -1.05483389, -0.9475714], [-0.548325539, -1.05483389, -0.9574472], [-0.5983255, -1.05483389, -0.942410231], [-0.417605162, -1.05483389, -0.777147532], [-0.487549782, -1.05483389, -0.777147532], [-0.548325539, -1.1048336, -0.94496727], [-0.5983255, -1.1048336, -0.941536069], [-0.4483254, -1.1048336, -0.8587016], [-0.498325348, -1.1048336, -0.841430068], [-0.492537737, -1.1048336, -0.777147532], [-0.498325348, -1.1048336, -0.7964035], [-0.5983255, -1.1048336, -0.795675159], [-0.548325539, -1.15483379, -0.9464817], [-0.5983255, -1.15483379, -0.9476738], [-0.413674116, -1.15483379, -0.8271475], [-0.548325539, -1.15483379, -0.849747062], [-0.498325348, -1.20483375, -0.8568047], [-0.5983255, -1.20483375, -0.8529453], [-0.548325539, -1.18191338, -0.777147532], [-0.5983255, -1.16358781, -0.777147532], [-0.498325348, -1.23449087, -0.8271475], [-0.548325539, -1.2548337, -0.8498384], [-0.5983255, -1.23063159, -0.8271475], [-0.5983255, -1.22041464, -0.777147532], [-0.439712763, -0.9548338, -0.72714746], [-0.4483254, -0.944569767, -0.6771475], [-0.4782579, -1.0048337, -0.72714746], [-0.4483254, -1.0048337, -0.6231827], [-0.413216352, -1.05483389, -0.72714746], [-0.498115063, -1.05483389, -0.72714746], [-0.487683773, -1.05483389, -0.6771475], [-0.5330329, -1.1048336, -0.72714746], [-0.522037745, -1.1048336, -0.6771475], [-0.4483254, -1.1048336, -0.59938395], [-0.548325539, -1.15483379, -0.7496437], [-0.41501236, -1.15483379, -0.6771475], [-0.533361435, -1.15483379, -0.6771475], [-0.498325348, -1.15483379, -0.581372559], [-0.463973045, -1.20483375, -0.72714746], [-0.5983255, -1.18196368, -0.72714746], [-0.4634807, -1.20483375, -0.6771475], [-0.5789652, -1.20483375, -0.6771475], [-0.4483254, -1.20483375, -0.6555494], [-0.5232494, -1.2548337, -0.72714746], [-0.462344646, -1.2548337, -0.6771475], [-0.4483254, -1.2548337, -0.6574789], [-0.5983255, -1.2548337, -0.6698813], [-0.4483254, -1.20714808, -0.5771475], [-0.5195973, -1.2548337, -0.5771475], [-0.4979391, -1.20483375, -0.47714752], [-0.4323976, -1.2548337, -0.527147532], [-0.4483254, -1.2548337, -0.5448687], [-0.459186316, -1.2548337, -0.527147532], [-0.498325348, -1.20912933, -0.527147532], [-0.513764858, -1.2548337, -0.527147532], [-0.7983254, -0.2048338, -1.559637], [-0.748325348, -0.5048338, -1.559637], [-0.7983254, -0.5048338, -1.559637], [-0.7983254, -0.5548338, -1.559637], [-0.7983254, -0.6048338, -1.559637], [-0.748325348, -0.6548338, -1.559637], [-0.780311, -0.6548338, -1.47714746], [-0.7647251, -0.7048338, -1.52714753], [-0.7983254, -0.694091141, -1.37714744], [-0.748325348, -0.754833758, -1.559637], [-0.7814926, -0.754833758, -1.52714753], [-0.6483253, -0.904833734, -1.559637], [-0.748325348, -0.904833734, -1.559637], [-0.6983254, -0.9548338, -1.559637], [-0.7983254, -0.9548338, -1.559637], [-0.6483253, -1.0048337, -1.559637], [-0.748325348, -1.0048337, -1.559637], [-0.6983254, -1.05483389, -1.559637], [-0.748325348, -1.1048336, -1.559637], [-0.7983254, -1.1048336, -1.559637], [-0.6483253, -1.06278992, -1.47714746], [-0.6821455, -1.1048336, -1.47714746], [-0.6756574, -1.1048336, -1.42714751], [-0.6483253, -1.08175206, -1.37714744], [-0.7983254, -1.15483379, -1.559637], [-0.734881043, -1.15483379, -1.47714746], [-0.748325348, -1.15483379, -1.51113594], [-0.7450602, -1.15483379, -1.42714751], [-0.7168858, -1.15483379, -1.37714744], [-0.6483253, -1.19030428, -1.52714753], [-0.7983254, -1.19021249, -1.52714753], [-0.6483253, -1.18083024, -1.47714746], [-0.748325348, -1.17417693, -1.47714746], [-0.7983254, -1.18020153, -1.47714746], [-0.6483253, -1.20483375, -1.45595181], [-0.7983254, -1.20483375, -1.4521246], [-0.7669641, -1.20483375, -1.37714744], [-0.6983254, -1.2548337, -1.559637], [-0.6983254, -1.23970151, -1.52714753], [-0.748325348, -1.22457886, -1.47714746], [-0.7983254, -1.22985649, -1.47714746], [-0.622284055, -1.2548337, -1.42714751], [-0.6483253, -1.233638, -1.42714751], [-0.6983254, -1.2548337, -1.43035424], [-0.75148356, -1.2548337, -1.42714751], [-0.675763369, -1.2548337, -1.37714744], [-0.6983254, -1.22171593, -1.37714744], [-0.7983254, -1.2548337, -1.40681481], [-0.7983254, -1.22516632, -1.37714744], [-0.7983254, -0.753306, -1.22714746], [-0.7801691, -0.754833758, -1.17714751], [-0.7983254, -0.729629457, -1.17714751], [-0.7983254, -0.8048338, -1.24985528], [-0.6483253, -0.8048338, -1.17930174], [-0.748325348, -0.8048338, -1.21931982], [-0.748325348, -0.8190101, -1.22714746], [-0.7983254, -0.8548338, -1.252599], [-0.773946762, -0.904833734, -1.27714741], [-0.748325348, -0.904833734, -1.1984092], [-0.7983254, -0.904833734, -1.19622946], [-0.6983254, -0.9508706, -1.27714741], [-0.748325348, -0.9548338, -1.29199362], [-0.6483253, -0.9548338, -1.24841309], [-0.6483253, -0.9368575, -1.17714751], [-0.6710186, -0.9548338, -1.17714751], [-0.6483253, -1.0048337, -1.296994], [-0.6983254, -1.0048337, -1.28883207], [-0.748325348, -1.0048337, -1.32131612], [-0.7983254, -1.0048337, -1.29036331], [-0.7322202, -1.0048337, -1.22714746], [-0.7983254, -0.9899582, -1.22714746], [-0.6483253, -0.97578007, -1.17714751], [-0.7983254, -0.9811153, -1.17714751], [-0.6483253, -1.04121637, -1.32714748], [-0.711275, -1.05483389, -1.32714748], [-0.748325348, -1.05483389, -1.29210246], [-0.7983254, -1.01804948, -1.27714741], [-0.758988142, -1.05483389, -1.22714746], [-0.7095883, -1.05483389, -1.17714751], [-0.748325348, -1.02608681, -1.17714751], [-0.6983254, -1.1048336, -1.336698], [-0.605831742, -1.1048336, -1.27714741], [-0.7693752, -1.1048336, -1.27714741], [-0.627861142, -1.1048336, -1.22714746], [-0.748325348, -1.1048336, -1.25609756], [-0.6483253, -1.1048336, -1.18995321], [-0.6483253, -1.09954643, -1.17714751], [-0.6983254, -1.1048336, -1.19896424], [-0.628790736, -1.15483379, -1.32714748], [-0.7665874, -1.15483379, -1.32714748], [-0.777195334, -1.15483379, -1.27714741], [-0.6986457, -1.15483379, -1.22714746], [-0.610347152, -1.20483375, -1.32714748], [-0.6483253, -1.20483375, -1.35349035], [-0.6483253, -1.17312026, -1.32714748], [-0.6983254, -1.20483375, -1.33733344], [-0.6983254, -1.17306209, -1.32714748], [-0.748325348, -1.20483375, -1.35850871], [-0.606141448, -1.20483375, -1.27714741], [-0.748325348, -1.18370366, -1.27714741], [-0.6044817, -1.2548337, -1.32714748], [-0.6483253, -1.2548337, -1.34970939], [-0.748325348, -1.2548337, -1.34611094], [-0.7983254, -1.2548337, -1.34748018], [-0.6684978, -1.2548337, -1.27714741], [-0.616619, -1.2548337, -1.22714746], [-0.6483253, -1.24604249, -1.22714746], [-0.6109685, -1.2548337, -1.17714751], [-0.6483253, -1.2314949, -1.17714751], [-0.680027843, -1.2548337, -1.17714751], [-0.748325348, -0.737904966, -1.12714744], [-0.6483253, -0.7524273, -1.07714748], [-0.7983254, -0.714195549, -1.07714748], [-0.748325348, -0.707694232, -1.02714753], [-0.748325348, -0.783132732, -1.07714748], [-0.7983254, -0.785840333, -1.07714748], [-0.7983254, -0.78857094, -0.97714746], [-0.6983254, -0.8548338, -1.14960432], [-0.6983254, -0.8351179, -1.12714744], [-0.748325348, -0.8548338, -1.14605331], [-0.748325348, -0.8356084, -1.12714744], [-0.7983254, -0.8370449, -1.12714744], [-0.6307124, -0.8548338, -1.07714748], [-0.6483253, -0.8330055, -1.07714748], [-0.6983254, -0.835563242, -1.07714748], [-0.6483253, -0.832841337, -1.02714753], [-0.6983254, -0.838078439, -0.97714746], [-0.6483253, -0.904833734, -1.14512372], [-0.6983254, -0.9548338, -1.14984071], [-0.7983254, -0.9548338, -1.150866], [-0.6483253, -0.976838052, -1.12714744], [-0.6983254, -1.0048337, -1.15234745], [-0.748325348, -1.0048337, -1.1558944], [-0.6483253, -0.973115, -1.07714748], [-0.6800442, -1.0048337, -1.07714748], [-0.6483253, -0.9679211, -1.02714753], [-0.6852381, -1.0048337, -1.02714753], [-0.6483253, -1.0048337, -0.990234733], [-0.6983254, -1.02164412, -1.12714744], [-0.73151505, -1.05483389, -1.12714744], [-0.6483253, -1.05483389, -1.09935462], [-0.6483253, -1.03655267, -1.07714748], [-0.6983254, -1.05483389, -1.09395778], [-0.634788156, -1.05483389, -1.02714753], [-0.6483253, -1.01989436, -0.97714746], [-0.683264732, -1.05483389, -0.97714746], [-0.6983254, -1.08802342, -1.12714744], [-0.633936763, -1.1048336, -0.97714746], [-0.631326556, -1.15483379, -1.02714753], [-0.627799034, -1.15483379, -0.97714746], [-0.658015251, -1.20483375, -1.07714748], [-0.6336689, -1.20483375, -1.02714753], [-0.6483253, -1.20483375, -1.06745756], [-0.6483253, -1.2548337, -1.14544511], [-0.6983254, -0.703976, -0.9271475], [-0.7983254, -0.684673965, -0.9271475], [-0.748325348, -0.6661852, -0.8271475], [-0.6983254, -0.7048338, -0.81780225], [-0.7983254, -0.7048338, -0.8096715], [-0.7983254, -0.754833758, -0.9434104], [-0.7983254, -0.730773866, -0.9271475], [-0.6483253, -0.7182533, -0.8771475], [-0.7247503, -0.754833758, -0.8771475], [-0.748325348, -0.754833758, -0.89428854], [-0.748325348, -0.727433264, -0.8771475], [-0.6776897, -0.754833758, -0.8271475], [-0.748325348, -0.754833758, -0.8371377], [-0.748325348, -0.754833758, -0.815075457], [-0.6983254, -0.783703744, -0.9271475], [-0.748325348, -0.7876927, -0.9271475], [-0.7233101, -0.8048338, -0.8771475], [-0.748325348, -0.8048338, -0.840280533], [-0.6483253, -0.8048338, -0.8182845], [-0.6483253, -0.836625755, -0.9271475], [-0.6983254, -0.829818547, -0.8771475], [-0.6983254, -0.8548338, -0.841020465], [-0.748325348, -0.904833734, -0.8146287], [-0.7983254, -1.0048337, -0.8415929], [-0.6483253, -1.05483389, -0.9422082], [-0.6483253, -1.05483389, -0.804017663], [-0.6483253, -1.1048336, -0.8489061], [-0.7983254, -1.1048336, -0.808774], [-0.748325348, -1.15483379, -0.84277904], [-0.6483253, -1.15483379, -0.797537565], [-0.748325348, -1.20483375, -0.9093971], [-0.748325348, -1.172584, -0.8771475], [-0.7983254, -1.17279434, -0.8771475], [-0.6983254, -1.20483375, -0.846622], [-0.6483253, -1.18902588, -0.777147532], [-0.6983254, -1.20483375, -0.8091178], [-0.6483253, -1.2548337, -0.8575542], [-0.6983254, -1.2548337, -0.849012852], [-0.617993951, -1.2548337, -0.777147532], [-0.7015486, -1.2548337, -0.777147532], [-0.748325348, -1.2548337, -0.8153349], [-0.6590179, -1.20483375, -0.72714746], [-0.6483253, -1.20511079, -0.6771475], [-0.9483253, 0.295166135, -1.559637], [-0.998325348, 0.345166147, -1.559637], [-0.998325348, 0.295166135, -1.52460825], [-0.8983253, 0.245166153, -1.559637], [-0.998325348, 0.245166153, -1.53203523], [-0.9483253, 0.145166144, -1.559637], [-0.9483253, -0.254833817, -1.559637], [-0.998325348, -0.354833841, -1.559637], [-0.8983253, -0.404833853, -1.559637], [-0.9483253, -0.404833853, -1.559637], [-0.998325348, -0.404833853, -1.51718462], [-0.8871814, -0.4548338, -1.52714753], [-0.9483253, -0.4204352, -1.52714753], [-0.8922374, -0.4548338, -1.47714746], [-0.9483253, -0.436322451, -1.47714746], [-0.998325348, -0.4548338, -1.436734], [-0.9483253, -0.4866411, -1.52714753], [-0.998325348, -0.5048338, -1.559637], [-0.848325253, -0.494465947, -1.47714746], [-0.8983253, -0.478694916, -1.42714751], [-0.848325253, -0.5048338, -1.39956069], [-0.813527, -0.5548338, -1.52714753], [-0.8799759, -0.5548338, -1.52714753], [-0.8983253, -0.5548338, -1.559637], [-0.8983253, -0.539009154, -1.52714753], [-0.8749653, -0.5548338, -1.47714746], [-0.8983253, -0.5326267, -1.47714746], [-0.9483253, -0.529485643, -1.47714746], [-0.83129406, -0.5548338, -1.37714744], [-0.998325348, -0.5064348, -1.37714744], [-0.883968353, -0.6048338, -1.52714753], [-0.806072831, -0.6048338, -1.47714746], [-0.8742087, -0.6048338, -1.42714751], [-0.8983253, -0.5820268, -1.42714751], [-0.9483253, -0.5803933, -1.37714744], [-0.998325348, -0.5795872, -1.37714744], [-0.8750979, -0.7048338, -1.42714751], [-0.887316465, -0.754833758, -1.52714753], [-0.8983253, -0.754833758, -1.559637], [-0.877368331, -0.754833758, -1.37714744], [-0.9483253, -0.8048338, -1.559637], [-0.8314227, -0.8048338, -1.42714751], [-0.933773041, -0.8048338, -1.42714751], [-0.9320495, -0.8048338, -1.37714744], [-0.848325253, -0.8548338, -1.559637], [-0.86677, -0.8548338, -1.52714753], [-0.926100731, -0.8548338, -1.52714753], [-0.9483253, -0.8548338, -1.559637], [-0.93271935, -0.8548338, -1.42714751], [-0.8526894, -0.8548338, -1.37714744], [-0.848325253, -0.904833734, -1.559637], [-0.978321552, -0.904833734, -1.52714753], [-0.998325348, -0.904833734, -1.559637], [-0.876296163, -0.904833734, -1.47714746], [-0.9831867, -0.904833734, -1.42714751], [-0.984315753, -0.904833734, -1.37714744], [-0.848325253, -0.9548338, -1.559637], [-0.8983253, -0.9520566, -1.52714753], [-0.966732264, -0.9548338, -1.52714753], [-0.998325348, -0.9548338, -1.559637], [-0.9215816, -0.9548338, -1.47714746], [-0.96973896, -0.9548338, -1.47714746], [-0.902872562, -0.9548338, -1.42714751], [-0.998325348, -0.9548338, -1.46510684], [-0.983325362, -0.9548338, -1.37714744], [-0.998325348, -0.9548338, -1.389188], [-0.8983253, -1.0048337, -1.559637], [-0.935799241, -1.0048337, -1.52714753], [-0.9601737, -1.0048337, -1.52714753], [-0.9483253, -0.9937306, -1.47714746], [-0.937678456, -1.0048337, -1.42714751], [-0.9483253, -1.0048337, -1.462221], [-0.998325348, -1.0048337, -1.46360135], [-0.8983253, -0.9613789, -1.37714744], [-0.9483253, -1.0048337, -1.41042662], [-0.9483253, -0.9898338, -1.37714744], [-0.848325253, -1.05483389, -1.559637], [-0.998325348, -1.01971221, -1.52714753], [-0.9483253, -1.05483389, -1.4939574], [-0.998325348, -1.0256207, -1.47714746], [-0.9483253, -1.03502464, -1.42714751], [-0.998325348, -1.05483389, -1.44793427], [-0.998325348, -1.04128766, -1.42714751], [-0.998325348, -1.04354978, -1.37714744], [-0.8983253, -1.1048336, -1.559637], [-0.937248349, -1.1048336, -1.52714753], [-0.998325348, -1.1048336, -1.5012641], [-0.998325348, -1.15483379, -1.559637], [-0.9483253, -1.15483379, -1.50992715], [-0.848325253, -1.18174338, -1.52714753], [-0.8983253, -1.178313, -1.52714753], [-0.998325348, -1.20483375, -1.559637], [-0.848325253, -1.20483375, -1.51738334], [-0.8983253, -1.20483375, -1.51663637], [-0.9483253, -1.20483375, -1.50195062], [-0.848325253, -1.2548337, -1.559637], [-0.848325253, -1.21842337, -1.52714753], [-0.9483253, -1.2548337, -1.559637], [-0.998325348, -1.2548337, -1.559637], [-0.827992558, -1.2548337, -1.37714744], [-0.8983253, -0.5548338, -1.31035459], [-0.9483253, -0.5548338, -1.29460025], [-0.848325253, -0.6048338, -1.331699], [-0.9483253, -0.6048338, -1.35030377], [-0.998325348, -0.6048338, -1.35155368], [-0.8983253, -0.6048338, -1.29164553], [-0.998325348, -0.6548338, -1.35491014], [-0.8983253, -0.6548338, -1.28029656], [-0.998325348, -0.6548338, -1.25246882], [-0.821333051, -0.7048338, -1.32714748], [-0.848325253, -0.6577653, -1.32714748], [-0.9483253, -0.7048338, -1.3548739], [-0.998325348, -0.7048338, -1.29871976], [-0.9483253, -0.7048338, -1.23351467], [-0.998325348, -0.6771105, -1.22714746], [-0.8241333, -0.754833758, -1.27714741], [-0.848325253, -0.707508862, -1.27714741], [-0.9483253, -0.754833758, -1.30818033], [-0.998325348, -0.754833758, -1.3077122], [-0.848325253, -0.7202192, -1.17714751], [-0.9319191, -0.8048338, -1.32714748], [-0.877941966, -0.8048338, -1.27714741], [-0.8983253, -0.7836819, -1.27714741], [-0.998325348, -0.785398543, -1.27714741], [-0.8789177, -0.8048338, -1.22714746], [-0.8983253, -0.782389343, -1.22714746], [-0.8256383, -0.8048338, -1.17714751], [-0.848325253, -0.7800457, -1.17714751], [-0.9316586, -0.8548338, -1.32714748], [-0.819597363, -0.8548338, -1.27714741], [-0.875929832, -0.8548338, -1.27714741], [-0.8217603, -0.8548338, -1.17714751], [-0.848325253, -0.8548338, -1.20145011], [-0.846899152, -0.904833734, -1.32714748], [-0.929402, -0.904833734, -1.32714748], [-0.8749386, -0.904833734, -1.22714746], [-0.848325253, -0.904833734, -1.20053411], [-0.9483253, -0.904833734, -1.20114744], [-0.9723253, -0.904833734, -1.17714751], [-0.8760605, -0.9548338, -1.32714748], [-0.980404139, -0.9548338, -1.32714748], [-0.848325253, -0.9548338, -1.2945956], [-0.9483253, -0.9169887, -1.27714741], [-0.8983253, -0.9303399, -1.22714746], [-0.9483253, -0.9269809, -1.22714746], [-0.976178169, -0.9548338, -1.22714746], [-0.848325253, -0.9249863, -1.17714751], [-0.8328676, -1.0048337, -1.32714748], [-0.8983253, -1.0048337, -1.34511209], [-0.9483253, -0.986912549, -1.32714748], [-0.998325348, -1.0048337, -1.3384316], [-0.848325253, -1.0048337, -1.26300764], [-0.848325253, -0.984867752, -1.22714746], [-0.8983253, -1.0048337, -1.27708817], [-0.912466645, -1.0048337, -1.22714746], [-0.971990943, -1.0048337, -1.22714746], [-0.848325253, -0.9846812, -1.17714751], [-0.8983253, -0.9813196, -1.17714751], [-0.93223834, -1.0048337, -1.17714751], [-0.9644122, -1.0048337, -1.17714751], [-0.848325253, -1.05483389, -1.34369159], [-0.8983253, -1.03175044, -1.32714748], [-0.848325253, -1.05483389, -1.29524517], [-0.848325253, -1.027931, -1.27714741], [-0.9483253, -1.02324271, -1.27714741], [-0.8983253, -1.05483389, -1.2549479], [-0.9483253, -1.02849936, -1.22714746], [-0.86554563, -1.05483389, -1.17714751], [-0.8983253, -1.05483389, -1.20992708], [-0.9813547, -1.05483389, -1.17714751], [-0.8983253, -1.07703328, -1.27714741], [-0.8983253, -1.08761334, -1.17714751], [-0.9483253, -1.08786321, -1.17714751], [-0.998325348, -0.691996157, -1.07714748], [-0.9483253, -0.692911565, -0.97714746], [-0.9483253, -0.715921, -1.12714744], [-0.9483253, -0.7123875, -1.07714748], [-0.830536366, -0.8048338, -1.12714744], [-0.998325348, -0.7857873, -1.12714744], [-0.998325348, -0.787664, -1.02714753], [-0.8983253, -0.78912, -0.97714746], [-0.8983253, -0.904833734, -1.17021871], [-0.848325253, -0.9548338, -1.1473], [-0.8983253, -0.9548338, -1.15066171], [-0.9483253, -0.9548338, -1.15957022], [-0.9483253, -1.0048337, -1.16106057], [-0.8983253, -1.05483389, -1.14436781], [-0.9483253, -1.05483389, -1.14411807], [-0.848325253, -0.675461948, -0.8771475], [-0.998325348, -0.6774352, -0.8771475], [-0.848325253, -0.6577113, -0.8271475], [-0.998325348, -0.6984877, -0.8271475], [-0.8983253, -0.7048338, -0.805619], [-0.9483253, -0.7048338, -0.8226563], [-0.8983253, -0.732321441, -0.9271475], [-0.9483253, -0.754833758, -0.9445286], [-0.9483253, -0.725426257, -0.9271475], [-0.848325253, -0.7346614, -0.8771475], [-0.998325348, -0.7258529, -0.8771475], [-0.8983253, -0.754833758, -0.8469595], [-0.9483253, -0.754833758, -0.844269156], [-0.8983253, -0.754833758, -0.805482864], [-0.9483253, -0.754833758, -0.808975339], [-0.9483253, -0.8048338, -0.8423482], [-0.998325348, -0.8048338, -0.810806155], [-0.9483253, -0.8548338, -0.8112869], [-0.848325253, -0.904833734, -0.8409885], [-0.9483253, -0.9548338, -0.8152354], [-0.998325348, -1.05483389, -0.9088825], [-0.998325348, -1.02309871, -0.8771475], [-0.9483253, -1.05483389, -0.841600657], [-0.848325253, -1.1048336, -0.845167756], [-0.8983253, -1.1048336, -0.842266738], [-0.848325253, -1.15483379, -0.9095042], [-0.8983253, -1.15483379, -0.820153952], [-0.8983253, -1.20483375, -0.833486438], [-0.998325348, -1.20483375, -0.829417348], [-0.9483253, -1.2548337, -0.9069613], [-0.8983253, -1.25237775, -0.777147532], [-0.998325348, -1.20906448, -0.777147532], [-0.9483253, -1.22258782, -0.5771475], [-0.991282344, -1.2548337, -0.527147532], [0.501674652, -1.28979564, -1.52714753], [0.451674461, -1.30483365, -1.559637], [0.51405, -1.30483365, -1.47714746], [0.451674461, -1.29090977, -1.47714746], [0.5139241, -1.30483365, -1.37714744], [0.501674652, -1.28951955, -1.37714744], [0.501674652, -1.35483384, -1.559637], [0.5133445, -1.35483384, -1.47714746], [0.5516746, -1.40483379, -1.559637], [0.5516746, -1.39316368, -1.47714746], [0.5516746, -1.55483389, -1.559637], [0.4016745, -1.65483379, -1.559637], [0.5516746, -1.70483375, -1.559637], [0.4016745, -1.70483375, -1.559637], [0.5516746, -1.7548337, -1.559637], [0.4516735, -1.7386229, -1.52714753], [0.5132067, -1.35483384, -1.27714741], [0.5516746, -1.39361763, -1.32714748], [0.5516746, -1.39343667, -1.22714746], [0.5132327, -1.35483384, -1.12714744], [0.595175266, -1.30483365, -0.9271475], [0.516965866, -1.30483365, -0.9271475], [0.517442942, -1.30483365, -0.8771475], [0.5997548, -1.30483365, -0.8271475], [0.5177021, -1.30483365, -0.8271475], [0.5178158, -1.30483365, -0.777147532], [0.514209032, -1.35483384, -0.9271475], [0.5144515, -1.35483384, -0.777147532], [0.564695358, -1.40483379, -0.777147532], [0.5181403, -1.30483365, -0.72714746], [0.451674461, -1.30483365, -0.641845763], [0.566785336, -1.35483384, -0.72714746], [0.5516746, -1.35483384, -0.709312141], [0.564459562, -1.40483379, -0.72714746], [0.5516746, -1.40483379, -0.6501089], [0.5516746, -1.40483379, -0.5910364], [0.451674461, -1.40483379, -0.600402951], [0.546916246, -1.45483375, -0.5771475], [0.501674652, -1.45483375, -0.596068], [0.5516746, -1.5048337, -0.6549764], [0.4016745, -1.5048337, -0.6577858], [0.451674461, -1.5048337, -0.5818739], [0.5516746, -1.55483389, -0.6582939], [0.451674461, -1.55483389, -0.6558858], [0.4016745, -1.55483389, -0.659131646], [0.5516746, -1.60483384, -0.6039398], [0.451674461, -1.60483384, -0.599874735], [0.4016745, -1.60483384, -0.607326567], [0.5516746, -1.65483379, -0.605388761], [0.451674461, -1.65483379, -0.6081452], [0.4206767, -1.65483379, -0.5771475], [0.501674652, -1.75416875, -0.5771475], [0.4516735, -1.7386229, -0.5771475], [0.4016745, -1.5048337, -0.559442639], [0.5516746, -1.55483389, -0.541256964], [0.501674652, -1.55483389, -0.5640255], [0.593549967, -1.55483389, -0.47714752], [0.4016745, -1.53478765, -0.47714752], [0.501674652, -1.60483384, -0.5395321], [0.451674461, -1.56554651, -0.527147532], [0.451674461, -1.56332016, -0.47714752], [0.451674461, -1.60483384, -0.455668867], [0.501674652, -1.619329, -0.527147532], [0.420381546, -1.65483379, -0.527147532], [0.5516746, -1.65483379, -0.5107014], [0.4016745, -1.65483379, -0.5030451], [0.5516746, -1.70483375, -0.5493282], [0.451674461, -1.70483375, -0.565750062], [0.5516746, -1.68012762, -0.47714752], [0.451674461, -1.66879272, -0.4271475], [0.5516746, -1.74169517, -0.527147532], [0.503814459, -1.7548337, -0.527147532], [0.4016757, -1.72307849, -0.527147532], [0.5516746, -1.7347517, -0.47714752], [0.503814459, -1.7548337, -0.47714752], [0.5516746, -1.733382, -0.4271475], [0.551673651, -1.7697134, -0.4271475], [0.201674461, -1.40483379, -1.559637], [0.2516744, -1.60483384, -1.559637], [0.350909233, -1.70729518, -1.53355944], [0.251673222, -1.67644215, -1.42714751], [0.351674557, -1.30483365, -0.652604461], [0.2516744, -1.30483365, -0.655307531], [0.3016746, -1.35483384, -0.6591554], [0.346953154, -1.35483384, -0.5771475], [0.2516744, -1.35483384, -0.6006855], [0.201674461, -1.35483384, -0.604024947], [0.351674557, -1.45483375, -0.66085875], [0.3016746, -1.45483375, -0.6630219], [0.2516744, -1.45483375, -0.6089717], [0.351674557, -1.5048337, -0.5938209], [0.3016746, -1.5048337, -0.593283534], [0.2516744, -1.5048337, -0.6079941], [0.201674461, -1.5048337, -0.608345866], [0.351674557, -1.55483389, -0.604103], [0.3016746, -1.52096987, -0.5771475], [0.2516744, -1.53568029, -0.5771475], [0.201674461, -1.5360322, -0.5771475], [0.2516744, -1.35483384, -0.540640354], [0.201674461, -1.40483379, -0.5401281], [0.351674557, -1.45483375, -0.568026543], [0.3016746, -1.45483375, -0.568105459], [0.2516744, -1.45483375, -0.5357981], [0.201674461, -1.454678, -0.527147532], [0.351674557, -1.49675012, -0.527147532], [0.2516744, -1.474448, -0.527147532], [0.351674557, -1.49681258, -0.47714752], [0.3016746, -1.4785099, -0.47714752], [0.333019257, -1.5048337, -0.4271475], [0.3016746, -1.46666336, -0.4271475], [0.201674461, -1.46981883, -0.4271475], [0.3016746, -1.51197672, -0.527147532], [0.2516744, -1.52293515, -0.527147532], [0.2516744, -1.53000808, -0.47714752], [0.201674461, -1.53384447, -0.47714752], [0.2516744, -1.52889967, -0.4271475], [0.351674557, -1.57966471, -0.527147532], [0.351674557, -1.60483384, -0.508800745], [0.3200214, -1.60483384, -0.47714752], [0.312842369, -1.65483379, -0.47714752], [0.39907074, -1.65483379, -0.4271475], [0.30167532, -1.691988, -0.527147532], [0.350909233, -1.70729518, -0.5261916], [0.30167532, -1.691988, -0.4271475], [0.00167441368, -1.55483389, -1.559637], [0.151674509, -1.60483384, -1.559637], [0.0490014553, -1.61343074, -1.544121], [0.101675272, -1.62980723, -1.52714753], [0.151674509, -1.30483365, -0.6620201], [0.051674366, -1.30483365, -0.5918665], [0.0206503868, -1.30483365, -0.5771475], [0.101674557, -1.35483384, -0.6606951], [0.051674366, -1.35483384, -0.6537809], [0.151674509, -1.35483384, -0.6057301], [0.061923027, -1.35483384, -0.5771475], [0.00167441368, -1.35483384, -0.5802753], [0.101674557, -1.40483379, -0.661781669], [0.151674509, -1.40483379, -0.6081452], [0.051674366, -1.43418336, -0.6271475], [0.151674509, -1.45483375, -0.60881716], [0.101674557, -1.45483375, -0.606985033], [0.00167441368, -1.42454529, -0.5771475], [0.151674509, -1.48650336, -0.5771475], [0.051674366, -1.48330379, -0.5771475], [0.151674509, -1.30483365, -0.5403562], [0.151674509, -1.35483384, -0.5346803], [0.00167441368, -1.34385729, -0.4271475], [0.101674557, -1.40483379, -0.529804], [0.051674366, -1.38301873, -0.527147532], [0.00167441368, -1.36519837, -0.527147532], [0.151674509, -1.40878153, -0.47714752], [0.00167441368, -1.43017983, -0.47714752], [0.051674366, -1.42457891, -0.4271475], [0.00167441368, -1.43052387, -0.4271475], [0.151674509, -1.47015238, -0.527147532], [0.101674557, -1.47938323, -0.527147532], [0.151674509, -1.47901416, -0.47714752], [0.051674366, -1.48473072, -0.47714752], [0.101674557, -1.482014, -0.4271475], [0.151674271, -1.64535213, -0.527147532], [-0.148325443, -1.5048337, -1.559637], [-0.09832525, -1.55483389, -1.559637], [-0.198324919, -1.536536, -1.52714753], [-0.04832697, -1.58317089, -1.52714753], [-0.0983250141, -1.56762648, -1.32714748], [-0.04832697, -1.58317089, -0.8771475], [-0.0483253, -1.30483365, -0.6616596], [-0.09832525, -1.35483384, -0.6609553], [-0.09832525, -1.32539654, -0.5771475], [-0.1983254, -1.30772781, -0.5771475], [-0.0483253, -1.40483379, -0.661856234], [-0.09832525, -1.3886416, -0.6271475], [-0.09832525, -1.36674953, -0.5771475], [-0.0483253, -1.43954253, -0.6271475], [-0.0483253, -1.42833757, -0.5771475], [-0.0983250141, -1.56762648, -0.72714746], [-0.0483253, -1.30483365, -0.567606449], [-0.0483253, -1.34881, -0.527147532], [-0.09832525, -1.30827045, -0.4271475], [-0.07656646, -1.40483379, -0.47714752], [-0.09832525, -1.37512755, -0.47714752], [-0.1983254, -1.38243842, -0.47714752], [-0.0483253, -1.37257051, -0.4271475], [-0.0483253, -1.433075, -0.47714752], [-0.2983253, -1.35483384, -1.559637], [-0.3483255, -1.45483375, -1.559637], [-0.248325348, -1.5048337, -1.559637], [-0.2983253, -1.30483365, -0.6469261], [-0.3483255, -1.30483365, -0.6594585], [-0.2983253, -1.29981327, -0.5771475], [-0.248325348, -1.35483384, -0.654109538], [-0.2983253, -1.32461238, -0.6271475], [-0.3483255, -1.33714485, -0.6271475], [-0.2983253, -1.3084085, -0.5771475], [-0.398325443, -1.33835483, -0.5771475], [-0.248325348, -1.38179588, -0.6271475], [-0.2983253, -1.50544548, -0.5771475], [-0.248325348, -1.29203391, -0.527147532], [-0.2983253, -1.28988814, -0.527147532], [-0.2983253, -1.25629807, -0.47714752], [-0.248325348, -1.259265, -0.4271475], [-0.2983253, -1.31604147, -0.527147532], [-0.3483255, -1.33475041, -0.527147532], [-0.3631785, -1.35483384, -0.527147532], [-0.398325443, -1.35483384, -0.5622944], [-0.248325348, -1.32482266, -0.47714752], [-0.248325348, -1.32701445, -0.4271475], [-0.3483255, -1.33438945, -0.4271475], [-0.363878, -1.35483384, -0.4271475], [-0.248325348, -1.38293529, -0.527147532], [-0.262450933, -1.45483375, -0.527147532], [-0.2983253, -1.45483375, -0.5633592], [-0.4044404, -1.47245407, -0.47714752], [-0.2614715, -1.45483375, -0.4271475], [-0.398323774, -1.4743557, -0.4271475], [-0.248325825, -1.52099061, -0.527147532], [-0.5983255, -1.27879238, -1.42714751], [-0.5983255, -1.27843142, -1.37714744], [-0.498325348, -1.40483379, -1.559637], [-0.4986415, -1.44316649, -1.52714753], [-0.5734713, -1.30483365, -1.32714748], [-0.548325539, -1.27724862, -1.27714741], [-0.572642565, -1.30483365, -1.27714741], [-0.588242531, -1.30483365, -1.22714746], [-0.5983255, -1.32968783, -1.32714748], [-0.5983255, -1.33051658, -1.27714741], [-0.579795361, -1.35483384, -1.22714746], [-0.5983255, -1.35483384, -1.20861721], [-0.603133559, -1.41067958, -1.22714746], [-0.5483265, -1.42771912, -1.02714753], [-0.548325539, -1.27752471, -0.8271475], [-0.548325539, -1.27001977, -0.777147532], [-0.5483265, -1.42771912, -0.777147532], [-0.548325539, -1.27990985, -0.72714746], [-0.5983255, -1.27802515, -0.72714746], [-0.5124705, -1.30483365, -0.6771475], [-0.548325539, -1.30483365, -0.713002443], [-0.5983255, -1.30483365, -0.6949794], [-0.5753751, -1.30483365, -0.5771475], [-0.548325539, -1.35483384, -0.7127841], [-0.5983255, -1.35483384, -0.6883553], [-0.498325348, -1.33788419, -0.6271475], [-0.5140271, -1.35483384, -0.6271475], [-0.5983255, -1.34985518, -0.6271475], [-0.515968561, -1.35483384, -0.5771475], [-0.5983255, -1.35362077, -0.5771475], [-0.5233047, -1.40483379, -0.6771475], [-0.5548136, -1.42570233, -0.708511353], [-0.6004065, -1.4115274, -0.715970159], [-0.5151317, -1.40483379, -0.5771475], [-0.5548136, -1.42570233, -0.615569234], [-0.4983244, -1.443265, -0.6771475], [-0.4983244, -1.443265, -0.5771475], [-0.574177265, -1.30483365, -0.527147532], [-0.430957317, -1.30483365, -0.47714752], [-0.4483254, -1.30483365, -0.501031756], [-0.4787724, -1.30483365, -0.47714752], [-0.548325539, -1.30483365, -0.5065842], [-0.498325348, -1.30483365, -0.471576124], [-0.4483254, -1.35483384, -0.5582242], [-0.498325348, -1.35483384, -0.558397532], [-0.452636242, -1.35483384, -0.47714752], [-0.498325348, -1.35483384, -0.499945432], [-0.424684048, -1.35483384, -0.4271475], [-0.4483254, -1.40483379, -0.5587678], [-0.498325348, -1.40483379, -0.5631102], [-0.5983255, -1.38011146, -0.527147532], [-0.440341949, -1.40483379, -0.47714752], [-0.424709558, -1.40483379, -0.4271475], [-0.449452639, -1.45845938, -0.48025], [-0.5483265, -1.42771912, -0.47714752], [-0.7983254, -1.30483365, -1.559637], [-0.6144031, -1.30483365, -1.37714744], [-0.6483253, -1.30483365, -1.41106975], [-0.748325348, -1.2792058, -1.37714744], [-0.7983254, -1.28625631, -1.37714744], [-0.6483253, -1.35483384, -1.559637], [-0.7513602, -1.36459517, -1.54557431], [-0.7987186, -1.3498714, -1.52714753], [-0.782755136, -1.35483432, -1.47714746], [-0.758721, -1.36230659, -1.42714751], [-0.804775834, -1.34798813, -1.42714751], [-0.632386446, -1.35483384, -1.37714744], [-0.6483253, -1.35483384, -1.39562178], [-0.6724595, -1.35483384, -1.37714744], [-0.6487528, -1.396496, -1.52714753], [-0.621937752, -1.40483308, -1.42714751], [-0.6483252, -1.3966291, -1.42714751], [-0.6983242, -1.38108444, -1.37714744], [-0.748325944, -1.3655386, -1.37714744], [-0.6483253, -1.27534986, -1.32714748], [-0.677809358, -1.30483365, -1.32714748], [-0.681105, -1.30483365, -1.27714741], [-0.671854734, -1.30483365, -1.22714746], [-0.6118269, -1.30483365, -1.17714751], [-0.6848239, -1.30483365, -1.17714751], [-0.6483253, -1.35483384, -1.3586731], [-0.6483253, -1.33697462, -1.32714748], [-0.8026966, -1.34863448, -1.32714748], [-0.6483253, -1.34005737, -1.27714741], [-0.6483253, -1.33117247, -1.22714746], [-0.6483253, -1.34343219, -1.17714751], [-0.606551051, -1.409617, -1.32714748], [-0.6535121, -1.39501643, -1.32714748], [-0.6983242, -1.38108444, -1.27714741], [-0.6483253, -1.30483365, -1.140649], [-0.748325348, -1.30483365, -0.909564853], [-0.6983254, -1.30483365, -0.860344052], [-0.7983254, -1.25977826, -0.8271475], [-0.6157286, -1.30483365, -0.777147532], [-0.748325348, -1.28807735, -0.777147532], [-0.7983254, -1.28119016, -0.777147532], [-0.7983259, -1.34999347, -0.9271475], [-0.752775669, -1.36415529, -0.8771475], [-0.6983254, -1.34471059, -0.8271475], [-0.782755136, -1.35483432, -0.8271475], [-0.6483253, -1.33988857, -0.777147532], [-0.748325348, -1.333955, -0.777147532], [-0.6183107, -1.30483365, -0.72714746], [-0.6983254, -1.26424646, -0.72714746], [-0.6983254, -1.270272, -0.6771475], [-0.748325348, -1.29242849, -0.6771475], [-0.7983254, -1.28681016, -0.6771475], [-0.6983254, -1.30483365, -0.6427494], [-0.747963667, -1.30483365, -0.6271475], [-0.6983254, -1.34242249, -0.72714746], [-0.782755136, -1.35483432, -0.72714746], [-0.6483253, -1.35483384, -0.70005554], [-0.6983254, -1.35483384, -0.708275437], [-0.6483253, -1.34715629, -0.6271475], [-0.6983254, -1.327899, -0.6271475], [-0.748325348, -1.30999279, -0.5771475], [-0.6983242, -1.38108444, -0.72714746], [-0.6483253, -1.355721, -0.5771475], [-0.800428152, -1.34933972, -0.527147532], [-0.6483252, -1.3966291, -0.47714752], [-0.848325253, -1.30483365, -1.559637], [-0.9983245, -1.287813, -1.52714753], [-0.848326564, -1.33444786, -1.37714744], [-0.9483255, -1.30335808, -1.17714751], [-0.848326564, -1.33444786, -1.27714741], [-0.9983245, -1.287813, -0.9271475], [-0.848325253, -1.30483365, -0.913060546], [-0.848325253, -1.26536393, -0.6771475], [-0.9492818, -1.30306053, -0.527147532], [-0.9483255, -1.30335808, -0.47714752], [-0.8523388, -1.33320045, -0.527147532], [-0.848326564, -1.33444786, -0.47714752], [-1.09832537, 0.345166147, -1.521945], [-1.14832532, 0.345166147, -1.48653209], [-1.15595973, 0.295166135, -1.47714746], [-1.17500186, 0.295166135, -1.42714751], [-1.1954627, 0.345166147, -1.37714744], [-1.06033158, 0.245166153, -1.47714746], [-1.09832537, 0.249205291, -1.42714751], [-1.0483253, 0.195166141, -1.52944326], [-1.1983254, 0.195166141, -1.559637], [-1.08131969, 0.195166141, -1.47714746], [-1.17886245, 0.195166141, -1.47714746], [-1.08469534, 0.195166141, -1.42714751], [-1.174932, 0.195166141, -1.37714744], [-1.1983254, 0.210051641, -1.37714744], [-1.0483253, 0.145166144, -1.559637], [-1.09832537, 0.145166144, -1.559637], [-1.10982335, 0.145166144, -1.52714753], [-1.18080318, 0.145166144, -1.52714753], [-1.1718291, 0.145166144, -1.42714751], [-1.16739631, 0.145166144, -1.37714744], [-1.09832537, 0.09516616, -1.559637], [-1.13046169, 0.09516616, -1.52714753], [-1.13223755, 0.09516616, -1.42714751], [-1.14089108, 0.09516616, -1.37714744], [-1.1983254, 0.1267782, -1.37714744], [-1.14832532, 0.0451661646, -1.53238678], [-1.14832532, -0.00483384728, -1.559637], [-1.14832532, 0.01966402, -1.47714746], [-1.09832537, -0.05483383, -1.559637], [-1.16995513, -0.05483383, -1.52714753], [-1.17980731, -0.05483383, -1.42714751], [-1.14832532, -0.104833841, -1.559637], [-1.16911006, -0.104833841, -1.52714753], [-1.178042, -0.104833841, -1.47714746], [-1.180172, -0.104833841, -1.37714744], [-1.14832532, -0.154833823, -1.559637], [-1.0483253, -0.254833817, -1.52930248], [-1.1983254, -0.254833817, -1.559637], [-1.09832537, -0.304833829, -1.52867424], [-1.1983254, -0.304833829, -1.5247246], [-1.09832537, -0.404833853, -1.52392757], [-1.14832532, -0.404833853, -1.51554334], [-1.14832532, -0.4548338, -1.48609483], [-1.1983254, -0.4548338, -1.52481508], [-1.09832537, -0.4548338, -1.424258], [-1.0483253, -0.5048338, -1.559637], [-1.0483253, -0.4888668, -1.52714753], [-1.1983254, -0.4870422, -1.47714746], [-1.14832532, -0.5048338, -1.41098309], [-1.09832537, -0.5548338, -1.559637], [-1.09832537, -0.5429131, -1.52714753], [-1.14832532, -0.5548338, -1.559637], [-1.14832532, -0.5429077, -1.52714753], [-1.1983254, -0.538905, -1.52714753], [-1.0483253, -0.5343075, -1.47714746], [-1.09832537, -0.534809351, -1.47714746], [-1.09832537, -0.5548338, -1.45763814], [-1.14832532, -0.5548338, -1.39101481], [-1.0483253, -0.5859976, -1.42714751], [-1.09832537, -0.6048338, -1.46263647], [-1.14832532, -0.6048338, -1.45883358], [-1.14832532, -0.577879846, -1.37714744], [-1.1983254, -0.589481652, -1.37714744], [-1.1983254, -0.6548338, -1.559637], [-1.1983254, -0.627750456, -1.42714751], [-1.09832537, -0.686896861, -1.37714744], [-1.1983254, -0.6858315, -1.37714744], [-1.14832532, -0.9548338, -1.559637], [-1.03628469, -0.9548338, -1.42714751], [-1.03477919, -1.0048337, -1.42714751], [-1.03704119, -1.0048337, -1.37714744], [-1.0483253, -1.05483389, -1.559637], [-1.02753854, -1.05483389, -1.47714746], [-1.02946246, -1.1048336, -1.52714753], [-1.14832532, -1.15483379, -1.559637], [-1.0483253, -1.20483375, -1.559637], [-1.14832532, -1.20483375, -1.559637], [-1.0483253, -1.2548337, -1.559637], [-1.09886086, -1.256556, -1.53181458], [-1.19832337, -1.22563267, -1.47714746], [-1.19048846, 0.295166135, -1.32714748], [-1.109954, 0.245166153, -1.32714748], [-1.14477444, 0.245166153, -1.27714741], [-1.10276818, 0.195166141, -1.32714748], [-1.17789125, 0.195166141, -1.32714748], [-1.1983254, 0.215879351, -1.32714748], [-1.09227169, 0.195166141, -1.27714741], [-1.1983254, 0.195166141, -1.29247344], [-1.1983254, 0.195166141, -1.24877751], [-1.12230134, 0.145166144, -1.32714748], [-1.17088819, 0.145166144, -1.32714748], [-1.12521851, 0.145166144, -1.27714741], [-1.1983254, 0.145166144, -1.29391551], [-1.14832532, 0.145166144, -1.253909], [-1.1983254, 0.1243899, -1.32714748], [-1.161364, 0.0451661646, -1.27714741], [-1.1983254, 0.0451661646, -1.25425327], [-1.1983254, -0.00483384728, -1.2607913], [-1.18965876, -0.05483383, -1.27714741], [-1.181954, -0.104833841, -1.32714748], [-1.1983254, -0.104833841, -1.26603317], [-1.0483253, -0.548825741, -1.32714748], [-1.19761384, -0.6048338, -1.32714748], [-1.1983254, -0.5961247, -1.22714746], [-1.0483253, -0.6375166, -1.27714741], [-1.09832537, -0.641685665, -1.22714746], [-1.0483253, -0.6809004, -1.32714748], [-1.09832537, -0.678883255, -1.32714748], [-1.0483253, -0.7048338, -1.30458546], [-1.14832532, -0.6789734, -1.27714741], [-1.0483253, -0.660645068, -1.22714746], [-1.14832532, -0.676336944, -1.22714746], [-1.0483253, -0.73227185, -1.27714741], [-1.09832537, -0.7361607, -1.27714741], [-1.0483253, -0.730356753, -1.17714751], [-1.0779897, -0.6548338, -1.12714744], [-1.14832532, -0.625134, -1.02714753], [-1.0483253, -0.6643006, -1.02714753], [-1.14832532, -0.673610032, -0.97714746], [-1.0483253, -0.7308654, -1.02714753], [-1.09832537, -0.733983457, -1.02714753], [-1.07663226, -0.6548338, -0.9271475], [-1.14832532, -0.633949161, -0.9271475], [-1.14832532, -0.639996767, -0.8771475], [-1.14832532, -0.650182962, -0.8271475], [-1.1983254, -0.649909437, -0.8271475], [-1.14832532, -0.6725579, -0.9271475], [-1.1983254, -0.679787934, -0.9271475], [-1.14832532, -0.7048338, -0.9058968], [-1.09832537, -0.673440635, -0.8271475], [-1.09832537, -0.7048338, -0.8102589], [-1.09832537, -0.7345213, -0.9271475], [-1.09832537, -0.730437, -0.8771475], [-1.0483253, -0.754833758, -0.841453433], [-1.09832537, -0.754833758, -0.847114444], [-1.1983254, -0.754833758, -0.8483298], [-1.0483253, -0.754833758, -0.808875859], [-1.0483253, -0.8048338, -0.8436094], [-1.14832532, -0.8548338, -0.7876476], [-1.1983254, -0.904833734, -0.778609753], [-1.17729712, -0.9548338, -0.777147532], [-1.0483253, -1.0048337, -0.8486903], [-1.14832532, -1.0048337, -0.853490651], [-1.0483253, -1.0048337, -0.80567193], [-1.14832532, -1.05483389, -0.9127487], [-1.1983254, -1.05483389, -0.856190443], [-1.1983254, -1.05483389, -0.785822153], [-1.0483253, -1.06827164, -0.8271475], [-1.13204181, -1.1048336, -0.8271475], [-1.196653, -1.1048336, -0.777147532], [-1.0483253, -1.15483379, -0.908171535], [-1.14832532, -1.15483379, -0.906136751], [-1.17731464, -1.15483379, -0.8771475], [-1.0483253, -1.15483379, -0.8328757], [-1.1150316, -1.15483379, -0.8271475], [-1.1983254, -1.15483379, -0.8460541], [-1.14171445, -1.15483379, -0.777147532], [-1.09832537, -1.20483375, -0.905563235], [-1.14832532, -1.20483375, -0.910323], [-1.1983254, -1.18507051, -0.8271475], [-1.18959332, -1.22834682, -0.777147532], [-1.14832532, -1.24117708, -0.9271475], [-1.05326784, -1.270731, -0.916743159], [-1.10440052, -1.25483346, -0.8771475], [-1.19832337, -1.22563267, -0.8771475], [-1.09977818, -1.25627065, -0.777147532], [-1.1983254, -1.09549928, -0.6271475], [-1.1983254, -1.18263793, -0.72714746], [-1.0483253, -1.19554615, -0.6771475], [-1.14832532, -1.15556979, -0.6271475], [-1.09832537, -1.17649579, -0.5771475], [-1.19832337, -1.22563267, -0.72714746], [-1.14832532, 0.345166147, -0.430939078], [-1.1983254, 0.329417676, -0.4271475], [-1.18151629, -1.15483379, -0.527147532], [-1.14832532, -1.17652678, -0.527147532], [-1.14832532, -1.20483375, -0.47906962], [-1.1983254, -1.18989253, -0.47714752], [-1.0483253, -1.22068119, -0.527147532], [-1.05326784, -1.270731, -0.5093209], [-1.08746648, -1.26009846, -0.47714752], [-1.10120666, -1.25582647, -0.4271475], [-1.26857281, 0.29516685, -1.52714753], [-1.25029385, 0.347958416, -1.42714751], [-1.22843957, 0.295166135, -1.42714751], [-1.26857281, 0.29516685, -1.42714751], [-1.23822677, 0.245166153, -1.52714753], [-1.24832535, 0.245166153, -1.559637], [-1.24832535, 0.2592768, -1.37714744], [-1.30267656, 0.196672723, -1.5375545], [-1.29832613, 0.209237263, -1.37714744], [-1.24832535, 0.09516616, -1.559637], [-1.29832542, 0.09516616, -1.559637], [-1.33769512, 0.0955357254, -1.52714753], [-1.22875082, 0.09516616, -1.42714751], [-1.22993743, 0.09516616, -1.37714744], [-1.33782291, 0.09516662, -1.37714744], [-1.22808313, 0.0451661646, -1.47714746], [-1.24832535, -0.00483384728, -1.559637], [-1.34832537, -0.00483384728, -1.559637], [-1.372448, -0.004832983, -1.42714751], [-1.22551048, -0.05483383, -1.52714753], [-1.38961029, -0.05439937, -1.52714753], [-1.21076524, -0.05483383, -1.37714744], [-1.38976049, -0.0548334122, -1.37714744], [-1.24832535, -0.06649867, -1.52714753], [-1.29832542, -0.104833841, -1.51766062], [-1.29832542, -0.06807339, -1.47714746], [-1.24832535, -0.0718326, -1.42714751], [-1.24832535, -0.07270032, -1.37714744], [-1.29832542, -0.06896913, -1.37714744], [-1.20125926, -0.154833823, -1.52714753], [-1.34832537, -0.154833823, -1.559637], [-1.24107039, -0.154833823, -1.47714746], [-1.389256, -0.154833823, -1.47714746], [-1.38952887, -0.154833823, -1.42714751], [-1.34832537, -0.119453609, -1.37714744], [-1.2153815, -0.2048338, -1.52714753], [-1.38207579, -0.2048338, -1.52714753], [-1.39832544, -0.2048338, -1.559637], [-1.37483, -0.2048338, -1.47714746], [-1.3134166, -0.2048338, -1.42714751], [-1.37708485, -0.254833817, -1.52714753], [-1.24832535, -0.254833817, -1.52197683], [-1.31966543, -0.254833817, -1.47714746], [-1.37301648, -0.304833829, -1.52714753], [-1.39832544, -0.304833829, -1.559637], [-1.32901275, -0.304833829, -1.47714746], [-1.29832542, -0.354833841, -1.49908066], [-1.36098933, -0.354833841, -1.42714751], [-1.25810719, -0.404833853, -1.52714753], [-1.35090554, -0.404833853, -1.47714746], [-1.38228726, -0.404833853, -1.42714751], [-1.29832542, -0.4548338, -1.49856973], [-1.34984326, -0.4548338, -1.47714746], [-1.35845017, -0.4548338, -1.37714744], [-1.24832535, -0.468056262, -1.52714753], [-1.24832535, -0.498380244, -1.47714746], [-1.29832542, -0.463475943, -1.47714746], [-1.2049768, -0.5048338, -1.42714751], [-1.24832535, -0.490187645, -1.42714751], [-1.29832542, -0.469735622, -1.42714751], [-1.24832535, -0.523268938, -1.52714753], [-1.29832542, -0.5548338, -1.559637], [-1.34832537, -0.5337697, -1.52714753], [-1.24832535, -0.5548338, -1.51165438], [-1.27790141, -0.5548338, -1.47714746], [-1.29832542, -0.530516744, -1.47714746], [-1.34832537, -0.526833832, -1.47714746], [-1.39832544, -0.5548338, -1.50530148], [-1.39832544, -0.527488232, -1.47714746], [-1.21226025, -0.5548338, -1.37714744], [-1.29832542, -0.538818955, -1.37714744], [-1.39832544, -0.6048338, -1.559637], [-1.39832544, -0.5820384, -1.52714753], [-1.27658975, -0.6048338, -1.42714751], [-1.27296937, -0.6048338, -1.37714744], [-1.24832535, -0.6330981, -1.42714751], [-1.39832544, -0.7048338, -1.559637], [-1.34832537, -1.1048336, -1.559637], [-1.29832542, -1.15483379, -1.559637], [-1.40076852, -1.16269159, -1.54313266], [-1.24981463, -1.20962381, -1.53827834], [-1.20815623, 0.295166135, -1.27714741], [-1.21580207, 0.345166147, -1.22714746], [-1.23041379, 0.295166135, -1.22714746], [-1.26542258, 0.3042651, -1.17714751], [-1.21947, 0.245166153, -1.22714746], [-1.2553246, 0.245166153, -1.17714751], [-1.24832535, 0.213890418, -1.32714748], [-1.29832613, 0.209237263, -1.32714748], [-1.24832535, 0.195166141, -1.30022442], [-1.24832535, 0.195166141, -1.21056175], [-1.32051039, 0.145166919, -1.32714748], [-1.24832535, 0.145166144, -1.29599488], [-1.24832535, 0.145166144, -1.243741], [-1.31813562, 0.152025446, -1.21623147], [-1.320429, 0.145402163, -1.17714751], [-1.24832535, 0.09516616, -1.3587842], [-1.24832535, 0.126802936, -1.32714748], [-1.29832542, 0.09516616, -1.26513088], [-1.337645, 0.0956804752, -1.22714746], [-1.3544066, 0.0472717881, -1.370155], [-1.29832542, 0.0451661646, -1.28760219], [-1.24832535, 0.0451661646, -1.26356924], [-1.36986578, 0.00262451172, -1.35305071], [-1.29832542, -0.00483384728, -1.29264486], [-1.36986578, 0.00262451172, -1.29387629], [-1.24832535, -0.00483384728, -1.26684833], [-1.372448, -0.004832983, -1.22714746], [-1.372448, -0.004832983, -1.17714751], [-1.24832535, -0.05483383, -1.35715234], [-1.29832542, -0.05483383, -1.35733616], [-1.29832542, -0.05483383, -1.291099], [-1.34832537, -0.05483383, -1.28157663], [-1.38813758, -0.0501460433, -1.22714746], [-1.389492, -0.0540579855, -1.17714751], [-1.34832537, -0.104833841, -1.35152173], [-1.40613663, -0.102129191, -1.37247086], [-1.29832542, -0.104833841, -1.27825546], [-1.30291164, -0.104833841, -1.27714741], [-1.24832535, -0.104833841, -1.260921], [-1.35837936, -0.104833841, -1.22714746], [-1.40489173, -0.09853366, -1.17714751], [-1.25488985, -0.154833823, -1.32714748], [-1.388716, -0.154833823, -1.32714748], [-1.26757383, -0.154833823, -1.27714741], [-1.29832542, -0.141738445, -1.22714746], [-1.29832542, -0.154833823, -1.19932652], [-1.33413553, -0.154833823, -1.17714751], [-1.29832542, -0.1976623, -1.27714741], [-1.3785696, -0.2048338, -1.27714741], [-1.39832544, -0.19045788, -1.27714741], [-1.30216014, -0.2048338, -1.22714746], [-1.39832544, -0.18522799, -1.22714746], [-1.30971122, -0.2048338, -1.17714751], [-1.39832544, -0.2048338, -1.20873511], [-1.34110165, -0.254833817, -1.22714746], [-1.34114122, -0.254833817, -1.17714751], [-1.34832537, -0.304833829, -1.36294413], [-1.35863984, -0.304833829, -1.22714746], [-1.35516822, -0.354833841, -1.32714748], [-1.38271952, -0.354833841, -1.17714751], [-1.35858953, -0.404833853, -1.32714748], [-1.36181319, -0.404833853, -1.27714741], [-1.37409151, -0.404833853, -1.22714746], [-1.39767253, -0.404833853, -1.17714751], [-1.33959627, -0.4548338, -1.27714741], [-1.3671447, -0.4548338, -1.22714746], [-1.21337235, -0.5048338, -1.32714748], [-1.24832535, -0.484946072, -1.32714748], [-1.21489334, -0.5048338, -1.27714741], [-1.24832535, -0.470212936, -1.27714741], [-1.21496868, -0.5048338, -1.22714746], [-1.24832535, -0.47207284, -1.22714746], [-1.34832537, -0.465403616, -1.22714746], [-1.34832537, -0.4962005, -1.17714751], [-1.39832544, -0.475082457, -1.17714751], [-1.27090657, -0.5548338, -1.32714748], [-1.39832544, -0.5351274, -1.32714748], [-1.39832544, -0.5306603, -1.27714741], [-1.21137106, -0.5548338, -1.22714746], [-1.27452421, -0.5548338, -1.22714746], [-1.29832542, -0.536881149, -1.22714746], [-1.39832544, -0.52638644, -1.22714746], [-1.29832542, -0.5548338, -1.20941246], [-1.34832537, -0.5548338, -1.21191871], [-1.39832544, -0.5548338, -1.20229161], [-1.27370858, -0.6048338, -1.27714741], [-1.27071238, -0.6048338, -1.17714751], [-1.34832537, -0.6048338, -1.21342885], [-1.39832544, -0.6048338, -1.21379566], [-1.2820431, -0.6548338, -1.32714748], [-1.28050554, -0.6548338, -1.17714751], [-1.24832535, -0.6885515, -1.32714748], [-1.21687436, 0.345166147, -1.12714744], [-1.25126064, 0.345166057, -0.97714746], [-1.28587949, 0.245184168, -1.07714748], [-1.32051039, 0.145166919, -1.07714748], [-1.37233555, -0.00450828671, -1.12714744], [-1.38773859, -0.0489939153, -0.97714746], [-1.40525031, -0.09956923, -1.07714748], [-1.3918798, -0.154833823, -1.12714744], [-1.32924247, -0.2048338, -1.12714744], [-1.34832537, -0.2048338, -1.11135364], [-1.39832544, -0.2048338, -1.08548427], [-1.342035, -0.254833817, -1.07714748], [-1.39832544, -0.254833817, -1.05932963], [-1.35963106, -0.304833829, -1.07714748], [-1.39832544, -0.304833829, -1.03357959], [-1.39585662, -0.354833841, -1.12714744], [-1.38370252, -0.354833841, -1.02714753], [-1.38326156, -0.404833853, -0.97714746], [-1.38618231, -0.4548338, -0.97714746], [-1.24832535, -0.5048338, -1.09803832], [-1.39832544, -0.5548338, -1.14379406], [-1.29832542, -0.5376526, -1.07714748], [-1.20230949, -0.6048338, -1.12714744], [-1.39832544, -0.6048338, -1.08762407], [-1.22097659, -0.6048338, -1.02714753], [-1.24832535, -0.573363364, -1.02714753], [-1.35774791, -0.6048338, -1.02714753], [-1.29832542, -0.6355904, -1.12714744], [-1.29832542, -0.6328177, -1.07714748], [-1.24832535, -0.6118422, -0.97714746], [-1.371365, -0.6548338, -0.97714746], [-1.39832544, -0.6548338, -1.01102829], [-1.34832537, -0.6908147, -1.12714744], [-1.39832544, -0.6926572, -1.12714744], [-1.24832535, -0.6865117, -1.07714748], [-1.39832544, -0.7048338, -1.114613], [-1.29832542, -0.6854537, -1.02714753], [-1.31104672, -0.7048338, -1.02714753], [-1.34832537, -0.7048338, -1.06442606], [-1.24832535, -0.6835832, -0.97714746], [-1.39128447, -0.7048338, -0.97714746], [-1.35978, -0.754833758, -1.07714748], [-1.39832544, -0.754833758, -1.115693], [-1.31084454, -0.754833758, -1.02714753], [-1.31151378, -0.754833758, -0.97714746], [-1.3594743, -0.8048338, -1.02714753], [-1.39832544, -0.8048338, -1.06599855], [-1.362775, -0.8048338, -0.97714746], [-1.36218321, -0.8548338, -0.97714746], [-1.39832544, -0.8548338, -1.01328969], [-1.25126064, 0.345166057, -0.8271475], [-1.372448, -0.004832983, -0.8771475], [-1.372448, -0.004832983, -0.8271475], [-1.38828266, -0.0505651236, -0.9271475], [-1.37602162, -0.404833853, -0.9271475], [-1.39832544, -0.374367177, -0.8771475], [-1.3246, -0.404833853, -0.8271475], [-1.34832537, -0.391635478, -0.8271475], [-1.39832544, -0.372667074, -0.8271475], [-1.33893883, -0.404833853, -0.777147532], [-1.39832544, -0.404833853, -0.7840564], [-1.34535491, -0.4548338, -0.8771475], [-1.32271051, -0.4548338, -0.8271475], [-1.34832537, -0.4548338, -0.796481967], [-1.39832544, -0.507230639, -0.8271475], [-1.24832535, -0.6159723, -0.9271475], [-1.29832542, -0.635688961, -0.9271475], [-1.29832542, -0.6526829, -0.8771475], [-1.24832535, -0.681626141, -0.9271475], [-1.36834443, -0.7048338, -0.9271475], [-1.31524754, -0.7048338, -0.8771475], [-1.26137924, -0.754833758, -0.9271475], [-1.29832542, -0.7519832, -0.8271475], [-1.24832535, -0.754833758, -0.8022792], [-1.36369288, -0.8048338, -0.8271475], [-1.29832542, -0.8048338, -0.794997752], [-1.34832537, -0.8048338, -0.7933843], [-1.36111832, -0.8548338, -0.9271475], [-1.39832544, -0.8350654, -0.8271475], [-1.24832535, -0.8548338, -0.796499968], [-1.34832537, -0.8548338, -0.7878896], [-1.36670947, -0.8548338, -0.777147532], [-1.39832544, -0.847053349, -0.777147532], [-1.34832537, -0.904833734, -0.8530663], [-1.39832544, -0.904833734, -0.855526567], [-1.39832544, -0.9332128, -0.8271475], [-1.29832542, -1.0048337, -0.8575542], [-1.24832535, -1.0048337, -0.7775519], [-1.29832542, -1.05483389, -0.857516646], [-1.32271564, -1.05483389, -0.777147532], [-1.27648544, -1.1048336, -0.8271475], [-1.217232, -1.15483379, -0.8271475], [-1.27866733, -1.15483379, -0.777147532], [-1.250934, -1.20927572, -0.777147532], [-1.25126064, 0.345166057, -0.6771475], [-1.25126064, 0.345166057, -0.5771475], [-1.39832544, -0.852747262, -0.72714746], [-1.38937926, -0.8548338, -0.6771475], [-1.37355649, -0.8548338, -0.6271475], [-1.39832544, -0.845888555, -0.6271475], [-1.34832537, -0.8355203, -0.5771475], [-1.39832544, -0.8285261, -0.5771475], [-1.34832537, -0.904833734, -0.7722281], [-1.36979651, -0.904833734, -0.72714746], [-1.37030256, -0.904833734, -0.6771475], [-1.33204687, -0.904833734, -0.5771475], [-1.31756675, -0.9548338, -0.6271475], [-1.3303231, -0.9548338, -0.5771475], [-1.29832542, -0.973921359, -0.72714746], [-1.37293792, -1.0048337, -0.6771475], [-1.29832542, -0.980035841, -0.6271475], [-1.3724159, -1.0048337, -0.6271475], [-1.29832542, -1.0048337, -0.599204838], [-1.37690806, -1.05483389, -0.6271475], [-1.20264471, -1.1048336, -0.72714746], [-1.26868367, -1.1048336, -0.72714746], [-1.270354, -1.1048336, -0.6771475], [-1.29832542, -1.07893229, -0.6771475], [-1.24832535, -1.07105374, -0.5771475], [-1.27950716, -1.15483379, -0.6771475], [-1.2784152, -1.15483379, -0.6271475], [-1.29832542, -1.13643909, -0.6271475], [-1.29832542, -1.13027859, -0.5771475], [-1.34832537, -1.1408751, -0.5771475], [-1.25090122, -1.209286, -0.72714746], [-1.34832585, -1.17899609, -0.72714746], [-1.24632621, -1.21070838, -0.6771475], [-1.25759578, 0.326869577, -0.47714752], [-1.26857281, 0.29516685, -0.4271475], [-1.30783355, -0.254833817, -0.4271475], [-1.34832537, -0.2368108, -0.4271475], [-1.39832544, -0.254833817, -0.4593371], [-1.39832544, -0.275262177, -0.47714752], [-1.34832537, -0.304833829, -0.4573956], [-1.34832537, -0.28295666, -0.4271475], [-1.39832544, -0.304833829, -0.437235534], [-1.39832544, -0.352667153, -0.527147532], [-1.36587679, -0.354833841, -0.47714752], [-1.39832544, -0.354833841, -0.470131576], [-1.39748621, -0.404833853, -0.527147532], [-1.33181047, -0.8548338, -0.527147532], [-1.39832544, -0.837310255, -0.527147532], [-1.33999646, -0.904833734, -0.527147532], [-1.36811817, -0.9548338, -0.527147532], [-1.39832544, -0.9548338, -0.516291261], [-1.34832537, -1.0048337, -0.5564951], [-1.39832544, -1.0048337, -0.5240541], [-1.39832544, -1.05483389, -0.5177225], [-1.29832542, -1.06868982, -0.527147532], [-1.39832544, -1.1048336, -0.5489935], [-1.34832537, -1.1048336, -0.484779775], [-1.29832542, -1.15483379, -0.5537929], [-1.3551755, -1.17686653, -0.563372], [-1.39832544, -1.11595011, -0.4271475], [-1.24981463, -1.20962381, -0.5726227], [-1.24832535, -1.15675473, -0.47714752], [-1.42438543, -0.154832929, -1.52714753], [-1.45786691, -0.251530051, -1.545426], [-1.45730448, -0.249906182, -1.47714746], [-1.476323, -0.304832876, -1.52714753], [-1.42785239, -0.354833841, -1.52714753], [-1.44832528, -0.354833841, -1.559637], [-1.42049909, -0.354833841, -1.47714746], [-1.42676055, -0.354833841, -1.42714751], [-1.509597, -0.40093106, -1.54739892], [-1.40843737, -0.404833853, -1.47714746], [-1.44832528, -0.404833853, -1.50340593], [-1.41781116, -0.404833853, -1.37714744], [-1.44832528, -0.4548338, -1.50242531], [-1.47562706, -0.5048338, -1.52714753], [-1.49832535, -0.5048338, -1.559637], [-1.54547155, -0.504539669, -1.52714753], [-1.42097974, -0.5048338, -1.47714746], [-1.44832528, -0.5048338, -1.505205], [-1.42090654, -0.5048338, -1.42714751], [-1.47268736, -0.5548338, -1.52714753], [-1.49832535, -0.5548338, -1.559637], [-1.43085754, -0.5548338, -1.47714746], [-1.465793, -0.5548338, -1.47714746], [-1.44832528, -0.5299565, -1.42714751], [-1.47320259, -0.5548338, -1.42714751], [-1.44832528, -0.6048338, -1.559637], [-1.44832528, -0.579195857, -1.52714753], [-1.44832528, -0.572301567, -1.47714746], [-1.5483253, -0.6548338, -1.559637], [-1.59747422, -0.654728, -1.52714753], [-1.59832525, -0.754833758, -1.559637], [-1.59832525, -1.05483389, -1.559637], [-1.44832528, -1.1048336, -1.559637], [-1.49832535, -1.1048336, -1.559637], [-1.59865856, -1.101167, -1.52714753], [-1.44875836, -1.14777136, -1.52714753], [-1.54832566, -1.11681557, -1.52714753], [-1.44169807, -0.204833388, -1.27714741], [-1.45729446, -0.2498768, -1.22714746], [-1.44832754, -0.223979473, -1.22714746], [-1.45728076, -0.2498374, -1.17714751], [-1.43027782, -0.304833829, -1.17714751], [-1.476323, -0.304832876, -1.17714751], [-1.49363565, -0.354833484, -1.22714746], [-1.44832528, -0.354833841, -1.20871115], [-1.44832528, -0.3180948, -1.17714751], [-1.41667318, -0.404833853, -1.27714741], [-1.50875044, -0.3984862, -1.22714746], [-1.508504, -0.397774577, -1.17714751], [-1.498327, -0.3683827, -1.17714751], [-1.52826047, -0.4548328, -1.27714741], [-1.48064125, -0.4548338, -1.22714746], [-1.47739041, -0.4548338, -1.17714751], [-1.42861879, -0.5048338, -1.32714748], [-1.42415178, -0.5048338, -1.27714741], [-1.477036, -0.5048338, -1.22714746], [-1.47025549, -0.5048338, -1.17714751], [-1.54557335, -0.504834056, -1.17714751], [-1.42363429, -0.5548338, -1.32714748], [-1.44832528, -0.530142844, -1.32714748], [-1.47301626, -0.5548338, -1.32714748], [-1.44832528, -0.5548338, -1.3024565], [-1.44832528, -0.5321531, -1.22714746], [-1.44832528, -0.5548338, -1.19821775], [-1.44832528, -0.579524755, -1.32714748], [-1.44832528, -0.6048338, -1.20423841], [-1.42248368, -0.1493409, -1.07714748], [-1.422269, -0.148720831, -0.97714746], [-1.42798948, -0.254833817, -1.02714753], [-1.43191731, -0.304833829, -1.12714744], [-1.476323, -0.304832876, -1.12714744], [-1.41848922, -0.304833829, -1.02714753], [-1.44719028, -0.304833829, -0.97714746], [-1.43253529, -0.354833841, -0.97714746], [-1.40850234, -0.404833853, -1.07714748], [-1.50887847, -0.398855746, -1.07714748], [-1.498327, -0.3683827, -1.07714748], [-1.50863087, -0.39814055, -0.97714746], [-1.5109483, -0.4048339, -0.97714746], [-1.47243869, -0.4548338, -1.12714744], [-1.52826047, -0.4548328, -1.12714744], [-1.40233386, -0.4548338, -1.02714753], [-1.52826047, -0.4548328, -1.02714753], [-1.49832535, -0.5048338, -1.15869856], [-1.49832535, -0.4683509, -1.12714744], [-1.43636286, -0.5048338, -1.07714748], [-1.49832535, -0.464218318, -0.97714746], [-1.43572915, -0.5548338, -1.12714744], [-1.49832535, -0.5548338, -1.15469062], [-1.54832518, -0.5127814, -1.12714744], [-1.44832528, -0.5548338, -1.10470593], [-1.45383465, -0.5548338, -1.02714753], [-1.5602119, -0.5471116, -1.02714753], [-1.44832528, -0.5453168, -0.97714746], [-1.49832535, -0.6048338, -1.15174425], [-1.52914536, -0.6048338, -1.12714744], [-1.44832528, -0.6048338, -1.1095103], [-1.46212566, -0.6048338, -1.07714748], [-1.47777486, -0.6048338, -0.97714746], [-1.44832528, -0.6548338, -1.15584159], [-1.49832535, -0.6548338, -1.15116394], [-1.44832528, -0.6350311, -1.07714748], [-1.5483253, -0.6548338, -1.1090982], [-1.426417, -0.6548338, -1.02714753], [-1.44832528, -0.6548338, -1.06590736], [-1.48424053, -0.6548338, -1.02714753], [-1.53509438, -0.6548338, -0.97714746], [-1.59832549, -0.657186449, -1.12714744], [-1.49832535, -0.7048338, -1.09593558], [-1.61305714, -0.69973284, -1.12036049], [-1.43586588, -0.7048338, -1.02714753], [-1.44832528, -0.7048338, -1.05602717], [-1.49832535, -0.7048338, -1.04103029], [-1.5672996, -0.7048338, -0.97714746], [-1.47594762, -0.754833758, -1.12714744], [-1.59832525, -0.754833758, -1.16489613], [-1.44832528, -0.754833758, -1.10351789], [-1.44047987, -0.754833758, -1.02714753], [-1.44832528, -0.754833758, -1.04368], [-1.49832535, -0.754833758, -1.06599438], [-1.5483253, -0.754833758, -1.05734515], [-1.58460176, -0.754833758, -1.02714753], [-1.47171867, -0.8048338, -1.12714744], [-1.44832528, -0.7812043, -1.07714748], [-1.48530889, -0.8048338, -1.07714748], [-1.53801548, -0.8048338, -1.07714748], [-1.5483253, -0.798273, -1.07714748], [-1.44832528, -0.8048338, -1.06187332], [-1.48823965, -0.8048338, -1.02714753], [-1.49832535, -0.8048338, -1.05815244], [-1.59607708, -0.8048338, -1.02714753], [-1.45272684, -0.8048338, -0.97714746], [-1.587125, -0.8048338, -0.97714746], [-1.5483253, -0.8548338, -1.15280867], [-1.59832525, -0.8548338, -1.13063645], [-1.46986663, -0.8548338, -1.07714748], [-1.59832525, -0.8548338, -1.11979294], [-1.59832525, -0.825811446, -1.07714748], [-1.49832535, -0.8548338, -1.04504061], [-1.44832528, -0.8548338, -1.01158893], [-1.47558033, -0.8548338, -0.97714746], [-1.51913321, -0.904833734, -1.12714744], [-1.5483253, -0.904833734, -1.15633953], [-1.59832525, -0.904833734, -1.14518654], [-1.54763341, -0.904833734, -1.07714748], [-1.59832525, -0.904833734, -1.09046829], [-1.49832535, -0.904833734, -1.06201446], [-1.49832535, -0.8669422, -1.02714753], [-1.53905928, -0.904833734, -1.02714753], [-1.44832528, -0.904833734, -1.01238012], [-1.53433633, -0.904833734, -0.97714746], [-1.57282424, -0.9548338, -1.12714744], [-1.59832525, -0.9548338, -1.15264857], [-1.59832525, -0.9250111, -1.07714748], [-1.5483253, -0.9548338, -1.05894744], [-1.5483253, -0.916371167, -1.02714753], [-1.59832525, -0.9494316, -1.02714753], [-1.467041, -0.9548338, -0.97714746], [-1.49832535, -0.9548338, -1.00843179], [-1.5483253, -0.9188679, -0.97714746], [-1.59832525, -0.9433363, -0.97714746], [-1.59832525, -0.9803348, -1.12714744], [-1.59832525, -0.9664597, -1.07714748], [-1.5483253, -0.9866337, -1.02714753], [-1.49832535, -0.986118, -0.97714746], [-1.42438543, -0.154832929, -0.8271475], [-1.43942428, -0.1982665, -0.9271475], [-1.45901072, -0.254833639, -0.8271475], [-1.47413731, -0.2985208, -0.9271475], [-1.43464637, -0.354833841, -0.9271475], [-1.44832528, -0.354833841, -0.8660673], [-1.49323606, -0.3536796, -0.8271475], [-1.49363565, -0.354833484, -0.777147532], [-1.44832528, -0.368367255, -0.8271475], [-1.509597, -0.40093106, -0.788174748], [-1.5109483, -0.4048339, -0.777147532], [-1.52826047, -0.4548328, -0.9271475], [-1.52568281, -0.447388351, -0.8271475], [-1.44832528, -0.4548338, -0.803897738], [-1.40183747, -0.5048338, -0.8771475], [-1.49832535, -0.5048338, -0.840518], [-1.54557335, -0.504834056, -0.8271475], [-1.54557335, -0.504834056, -0.777147532], [-1.44832528, -0.5450509, -0.9271475], [-1.48468423, -0.5548338, -0.9271475], [-1.44832528, -0.530823231, -0.8771475], [-1.4730314, -0.5548338, -0.8771475], [-1.44832528, -0.548974037, -0.8271475], [-1.4519732, -0.5548338, -0.8271475], [-1.49832535, -0.5243561, -0.8271475], [-1.561327, -0.550332, -0.7961705], [-1.56288588, -0.5548341, -0.777147532], [-1.5248735, -0.6048338, -0.9271475], [-1.47557628, -0.6048338, -0.777147532], [-1.54419458, -0.6548338, -0.9271475], [-1.54408324, -0.6548338, -0.8771475], [-1.50652266, -0.6548338, -0.777147532], [-1.41018033, -0.754833758, -0.9271475], [-1.58858621, -0.754833758, -0.8771475], [-1.59832525, -0.754833758, -0.83271575], [-1.4839325, -0.8548338, -0.9271475], [-1.44832528, -0.848414242, -0.777147532], [-1.49832535, -0.8754768, -0.8771475], [-1.579537, -0.904833734, -0.777147532], [-1.5483253, -0.906955659, -0.9271475], [-1.44832528, -0.940718, -0.8771475], [-1.59832525, -0.916180551, -0.8771475], [-1.49832535, -0.93998605, -0.8271475], [-1.44832528, -0.9340152, -0.777147532], [-1.59832525, -0.921354949, -0.777147532], [-1.5483253, -0.989986, -0.9271475], [-1.59832525, -0.9643243, -0.9271475], [-1.49832535, -0.9918048, -0.8771475], [-1.5483253, -0.99105984, -0.8271475], [-1.59832525, -0.979947746, -0.8271475], [-1.54832566, -1.11681557, -0.9271475], [-1.45901072, -0.254833639, -0.5771475], [-1.49363565, -0.354833484, -0.5771475], [-1.498327, -0.3683827, -0.6771475], [-1.51091838, -0.4047476, -0.6271475], [-1.46517229, -0.404833853, -0.5771475], [-1.46465611, -0.4548338, -0.5771475], [-1.47532427, -0.5048338, -0.5771475], [-1.54832518, -0.5127814, -0.6771475], [-1.49832535, -0.5548338, -0.59484303], [-1.561327, -0.550332, -0.621402144], [-1.49832535, -0.6048338, -0.739325762], [-1.5483253, -0.6048338, -0.7505361], [-1.58011734, -0.6045997, -0.72714746], [-1.5788089, -0.6008207, -0.6271475], [-1.5483253, -0.6048338, -0.6149833], [-1.49097848, -0.6548338, -0.72714746], [-1.49832535, -0.6265474, -0.72714746], [-1.5483253, -0.6288367, -0.72714746], [-1.49832535, -0.6548338, -0.700239062], [-1.5483253, -0.6548338, -0.7062523], [-1.55946124, -0.6548338, -0.5771475], [-1.52016056, -0.7048338, -0.6771475], [-1.5483253, -0.7048338, -0.6604278], [-1.6121403, -0.697084963, -0.6271475], [-1.61199367, -0.6966612, -0.5771475], [-1.55342114, -0.754833758, -0.6771475], [-1.579189, -0.8048338, -0.6271475], [-1.59832525, -0.8048338, -0.603308558], [-1.44832528, -0.8470873, -0.6771475], [-1.58618844, -0.8548338, -0.6271475], [-1.44832528, -0.8428064, -0.5771475], [-1.49832535, -0.8513281, -0.5771475], [-1.5483253, -0.841297, -0.5771475], [-1.5483253, -0.885783732, -0.72714746], [-1.58624923, -0.904833734, -0.72714746], [-1.49832535, -0.877768, -0.6771475], [-1.59832525, -0.8738474, -0.6771475], [-1.49832535, -0.870376647, -0.6271475], [-1.5483253, -0.8709071, -0.6271475], [-1.44832528, -0.9331556, -0.72714746], [-1.49832535, -0.9395024, -0.72714746], [-1.42658961, -0.9548338, -0.6271475], [-1.5483253, -0.9377784, -0.6271475], [-1.59832525, -0.9317438, -0.6271475], [-1.44832528, -0.939150155, -0.5771475], [-1.59832525, -0.9385902, -0.5771475], [-1.5483253, -0.9894332, -0.6771475], [-1.42425835, -1.0048337, -0.5771475], [-1.4260484, -1.15483212, -0.5771475], [-1.407073, -0.104833424, -0.4271475], [-1.44832754, -0.223979473, -0.47714752], [-1.45901072, -0.254833639, -0.47714752], [-1.47590888, -0.303636968, -0.527147532], [-1.42067575, -0.304833829, -0.4271475], [-1.44832528, -0.354833841, -0.5628923], [-1.45887852, -0.354833841, -0.4271475], [-1.44832528, -0.361057222, -0.47714752], [-1.474769, -0.404833853, -0.47714752], [-1.49832535, -0.4548338, -0.5020112], [-1.52716851, -0.451678932, -0.47714752], [-1.44832528, -0.487894535, -0.527147532], [-1.49832535, -0.5048338, -0.5309133], [-1.54557335, -0.504834056, -0.47714752], [-1.47483289, -0.5548338, -0.527147532], [-1.49832535, -0.524681866, -0.527147532], [-1.561327, -0.550332, -0.516944468], [-1.48742115, -0.6048338, -0.527147532], [-1.49832535, -0.6048338, -0.499322325], [-1.5483253, -0.6048338, -0.495805353], [-1.58019793, -0.6048327, -0.47714752], [-1.52422643, -0.6548338, -0.527147532], [-1.59750271, -0.654810369, -0.4271475], [-1.55982721, -0.7048338, -0.527147532], [-1.49832535, -0.6595909, -0.47714752], [-1.5335027, -0.7048338, -0.47714752], [-1.50071752, -0.7048338, -0.4271475], [-1.58296633, -0.754833758, -0.47714752], [-1.501036, -0.754833758, -0.4271475], [-1.49832535, -0.8048338, -0.433395654], [-1.59832525, -0.8048338, -0.467904836], [-1.49832535, -0.8473218, -0.527147532], [-1.5483253, -0.840697467, -0.527147532], [-1.59832525, -0.843886435, -0.527147532], [-1.44832528, -0.8548338, -0.5215324], [-1.49832535, -0.8548338, -0.5033703], [-1.5483253, -0.8381496, -0.47714752], [-1.5375092, -0.8548338, -0.4271475], [-1.59832525, -0.8548338, -0.4009143], [-1.49832535, -0.904833734, -0.51024127], [-1.54146481, -0.904833734, -0.47714752], [-1.44832528, -0.9548338, -0.5469038], [-1.5483253, -0.9548338, -0.5507537], [-1.5483253, -0.9548338, -0.5022071], [-1.49832535, -1.0048337, -0.506649733], [-1.44832528, -1.05483389, -0.5451303], [-1.49832535, -1.05483389, -0.5483095], [-1.5483253, -1.05483389, -0.490309983], [-1.44832528, -1.1044364, -0.47714752], [-1.49832535, -1.1048336, -0.485767365], [-1.55233681, -1.1155684, -0.47714752], [-1.5030514, -1.13089132, -0.47714752], [-1.666761, -0.854834259, -1.52714753], [-1.64832532, -0.904833734, -1.559637], [-1.69832563, -0.945995033, -1.42714751], [-1.6983254, -1.0048337, -1.559637], [-1.7186985, -1.00483418, -1.52714753], [-1.7186985, -1.00483418, -1.37714744], [-1.6983254, -1.05483389, -1.559637], [-1.72806263, -1.06093454, -1.42714751], [-1.69832408, -1.07018042, -1.52714753], [-1.63213611, -0.754834354, -1.17714751], [-1.64832509, -0.801589668, -1.22714746], [-1.68407357, -0.90483433, -1.17714751], [-1.7186985, -1.00483418, -1.17714751], [-1.72806263, -1.06093454, -1.22714746], [-1.69832408, -1.07018042, -1.27714741], [-1.61548162, -0.8548338, -1.07714748], [-1.64832532, -0.904833734, -1.1533103], [-1.61068034, -0.904833734, -1.07714748], [-1.62543023, -0.904833734, -1.02714753], [-1.64832532, -0.9548338, -1.14909947], [-1.69832563, -0.945995033, -1.12714744], [-1.7006495, -0.952707, -1.07714748], [-1.64832532, -0.975415647, -1.07714748], [-1.7186985, -1.00483418, -1.02714753], [-1.72806263, -1.06093454, -1.07714748], [-1.69832408, -1.07018042, -1.07714748], [-1.60861182, -0.8048338, -0.8271475], [-1.60491228, -0.8548338, -0.9271475], [-1.61469412, -0.904833734, -0.9271475], [-1.616877, -0.904833734, -0.8271475], [-1.70071507, -0.9528963, -0.9271475], [-1.64832532, -0.9825055, -0.8771475], [-1.72806263, -1.06093454, -0.8771475], [-1.69832408, -1.07018042, -0.8771475], [-1.60219944, -0.754833758, -0.6271475], [-1.62261569, -0.8048338, -0.5771475], [-1.61103487, -0.904833734, -0.72714746], [-1.69832563, -0.945995033, -0.6771475], [-1.64832532, -0.937296331, -0.6271475], [-1.701386, -0.9548339, -0.5771475], [-1.7186985, -1.00483418, -0.72714746], [-1.64832532, -0.986421049, -0.6771475], [-1.7186985, -1.00483418, -0.5771475], [-1.72806263, -1.06093454, -0.6271475], [-1.69832408, -1.07018042, -0.5771475], [-1.64932823, -0.8044866, -0.5289279], [-1.66607356, -0.8528485, -0.527147532], [-1.68407357, -0.90483433, -0.4271475], [-1.70105827, -0.9538875, -0.4298895], [-1.64832532, -1.0048337, -0.5586265], [-1.71640539, -0.9982113, -0.47714752], [-1.702676, -1.06882739, -0.464637756], [-1.72806263, -1.06093454, -0.4271475], [-1.65505552, -1.08363271, -0.47714752], [-1.64832616, -1.08572507, -0.4271475], [-1.09832525, -1.25672245, -1.02714753], [-1.04832542, -1.27226758, -0.9271475], [-1.09832525, -1.25672245, -0.8771475], [-1.04832542, -1.27226758, -0.47714752], [1.60460424, 0.902607262, -1.54281735], [1.67788315, 0.8451654, -1.52714753], [1.65167427, 0.8451661, -1.559637], [1.65294313, 0.883576751, -1.52714753], [1.64011621, 0.744853854, -1.52714753], [1.60167456, 0.7451661, -1.559637], [1.60247135, 0.6448661, -1.52946317], [1.62349939, 0.895168543, -1.32714748], [1.65294313, 0.883576751, -1.27714741], [1.67788315, 0.8451654, -1.22714746], [1.67788315, 0.8451654, -1.17714751], [1.65805387, 0.8815648, -1.17714751], [1.640234, 0.7451656, -1.17714751], [1.60258389, 0.645166039, -1.22714746], [1.61241531, 0.899532259, -1.12714744], [1.61317682, 0.8992323, -1.02714753], [1.60167408, 0.9037608, -0.97714746], [1.62876749, 0.8451661, -1.12714744], [1.63030529, 0.8451661, -1.07714748], [1.63868523, 0.8451661, -0.97714746], [1.62640285, 0.795166135, -1.12714744], [1.60167456, 0.805255532, -1.07714748], [1.65760469, 0.7913041, -1.02714753], [1.640234, 0.7451656, -1.12714744], [1.60167456, 0.7451661, -1.115886], [1.63836336, 0.740197539, -1.02714753], [1.62140894, 0.6951654, -1.12714744], [1.60167456, 0.7188201, -1.02714753], [1.62139082, 0.695117, -0.97714746], [1.61895943, 0.6886585, -1.00418055], [1.60184574, 0.643203855, -0.97714746], [1.61678028, 0.8978136, -0.9271475], [1.62349939, 0.895168543, -0.777147532], [1.628613, 0.8451661, -0.9271475], [1.65895319, 0.7948855, -0.9271475], [1.65894508, 0.7948646, -0.777147532], [1.60167551, 0.6427519, -0.9271475], [1.62349939, 0.895168543, -0.72714746], [1.60208273, 0.9035999, -0.72714746], [1.62214756, 0.8957005, -0.6771475], [1.6020627, 0.903607845, -0.5771475], [1.67686749, 0.8424669, -0.72714746], [1.67686653, 0.84246397, -0.6771475], [1.65463257, 0.8829118, -0.6771475], [1.67490053, 0.837242961, -0.6271475], [1.65872049, 0.79426837, -0.6771475], [1.67254162, 0.830977559, -0.5771475], [1.65395212, 0.7816025, -0.5771475], [1.62140894, 0.6951654, -0.5771475], [1.60355473, 0.903020442, -0.4271475], [1.60167456, 0.8451661, -0.564161956], [1.61041021, 0.8451661, -0.47714752], [1.67788315, 0.8451654, -0.4271475], [1.65294313, 0.883576751, -0.4271475], [1.65814209, 0.792731047, -0.5741697], [1.60167456, 0.795166135, -0.5764043], [1.65814209, 0.792731047, -0.43013528], [1.60167456, 0.795166135, -0.390310466], [1.63866949, 0.7410103, -0.527147532], [1.60167456, 0.7451661, -0.505668342], [1.640234, 0.7451656, -0.4271475], [1.60167456, 0.7451661, -0.45107463], [1.62138462, 0.695100248, -0.527147532], [1.61895943, 0.6886585, -0.5055172], [1.61895943, 0.6886585, -0.383215725], [1.45167446, 0.9451661, -1.559637], [1.40167451, 0.9451661, -1.559637], [1.40433979, 0.981448233, -1.37714744], [1.50191975, 0.9430325, -1.52714753], [1.55167675, 0.923443854, -1.42714751], [1.50167513, 0.9431287, -1.37714744], [1.50167465, 0.495166123, -1.559637], [1.54611015, 0.495166719, -1.42714751], [1.5272851, 0.445166469, -1.52714753], [1.40167451, 0.9451661, -1.34985733], [1.45768976, 0.9604451, -1.28348708], [1.40167451, 0.9451661, -1.30023634], [1.55167675, 0.923443854, -1.32714748], [1.51075649, 0.9395534, -1.32714748], [1.48917055, 0.8951661, -1.32714748], [1.45167446, 0.8951661, -1.36354733], [1.40167451, 0.8951661, -1.36528826], [1.50394773, 0.9422339, -1.27714741], [1.48868132, 0.8951661, -1.27714741], [1.55734539, 0.9212124, -1.22714746], [1.50167513, 0.9431287, -1.22714746], [1.40167451, 0.926129639, -1.22714746], [1.55167675, 0.923443854, -1.17714751], [1.40167451, 0.8951661, -1.20664], [1.45167446, 0.858766258, -1.32714748], [1.40167451, 0.8570254, -1.32714748], [1.45167446, 0.859281957, -1.27714741], [1.40167451, 0.8686399, -1.22714746], [1.5272851, 0.445166469, -1.17714751], [1.49649858, 0.9451665, -1.02714753], [1.440064, 0.8451661, -1.12714744], [1.40167451, 0.8786892, -1.12714744], [1.42517662, 0.8451661, -1.02714753], [1.40167451, 0.877480745, -1.02714753], [1.40167451, 0.874777, -0.97714746], [1.40167451, 0.8067765, -1.12714744], [1.43887377, 0.795166135, -1.07714748], [1.55167437, 0.8138455, -1.02714753], [1.50167465, 0.795166135, -1.06408525], [1.50167465, 0.8255678, -1.02714753], [1.45167446, 0.8191509, -1.02714753], [1.52765942, 0.795166135, -0.97714746], [1.50167465, 0.8189915, -0.97714746], [1.45167446, 0.8341042, -0.97714746], [1.55167437, 0.7451661, -1.1138736], [1.55167437, 0.7818922, -1.07714748], [1.40167451, 0.757967, -1.07714748], [1.58941269, 0.7451661, -1.02714753], [1.50167465, 0.7451661, -1.06361032], [1.45167446, 0.7451661, -1.0650357], [1.40167451, 0.757967, -1.02714753], [1.55167437, 0.7451661, -1.00693178], [1.52079916, 0.7451661, -0.97714746], [1.40167451, 0.7451661, -1.01358342], [1.55167437, 0.6951661, -1.11351109], [1.50167465, 0.6951661, -1.05800307], [1.50167465, 0.6951661, -0.9775913], [1.40167451, 0.6951661, -1.0135293], [1.55167437, 0.645166159, -1.05500567], [1.45167446, 0.645166159, -1.05736482], [1.55167437, 0.645166159, -0.9939269], [1.40167451, 0.645166159, -1.00869322], [1.58375931, 0.5951656, -1.07714748], [1.55167437, 0.5951661, -1.06369019], [1.40167451, 0.5951661, -1.0585084], [1.50167465, 0.6121838, -0.97714746], [1.56493425, 0.545164, -1.07714748], [1.50167465, 0.545166135, -1.05553794], [1.40167451, 0.545166135, -1.05119693], [1.55167484, 0.5099482, -1.12714744], [1.50167465, 0.528096557, -1.07714748], [1.56058168, 0.533605337, -1.02714753], [1.48539162, 0.495166123, -0.97714746], [1.45167446, 0.445166141, -1.16117013], [1.40167451, 0.478773028, -1.12714744], [1.5249095, 0.438856781, -1.02714753], [1.5272851, 0.445166469, -0.97714746], [1.40167451, 0.395166129, -1.15137661], [1.50930643, 0.3974137, -1.07714748], [1.45167446, 0.395166129, -1.09298611], [1.40167451, 0.395166129, -1.0760448], [1.45167494, 0.962813, -0.777147532], [1.512166, 0.795166135, -0.9271475], [1.40167451, 0.812587142, -0.9271475], [1.50167465, 0.795166135, -0.8791274], [1.45167446, 0.795166135, -0.910238862], [1.56759357, 0.795166135, -0.8271475], [1.55167437, 0.806892157, -0.8271475], [1.50167465, 0.795166135, -0.8759582], [1.49384165, 0.795166135, -0.8271475], [1.50167465, 0.8095846, -0.777147532], [1.50167465, 0.78979677, -0.9271475], [1.45167446, 0.7451661, -0.912818551], [1.50167465, 0.786544442, -0.8271475], [1.45167446, 0.645166159, -0.958964467], [1.40167451, 0.645166159, -0.9271451], [1.55167437, 0.5951661, -0.967471242], [1.50167465, 0.545166135, -0.9622984], [1.54608965, 0.495112479, -0.9271475], [1.50167465, 0.495166123, -0.972415566], [1.5272851, 0.445166469, -0.8771475], [1.40214348, 0.982312739, -0.5771475], [1.55167437, 0.8164606, -0.72714746], [1.581408, 0.795166135, -0.6771475], [1.49554968, 0.795166135, -0.6771475], [1.56558609, 0.795166135, -0.6271475], [1.50167465, 0.8108915, -0.6271475], [1.55167437, 0.8242142, -0.5771475], [1.55167437, 0.7754381, -0.72714746], [1.50167465, 0.785258651, -0.6271475], [1.55167437, 0.767709255, -0.5771475], [1.45253563, 0.962474167, -0.47714752], [1.45768976, 0.9604451, -0.4655826], [1.41937685, 0.9451661, -0.4271475], [1.40290022, 0.9820147, -0.4271475], [1.60060453, 0.9041819, -0.527147532], [1.59840107, 0.9050493, -0.47714752], [1.55691814, 0.9213805, -0.47714752], [1.50593853, 0.9414504, -0.47714752], [1.55167437, 0.8951661, -0.471208155], [1.57892323, 0.8451661, -0.527147532], [1.58519554, 0.8451661, -0.4271475], [1.45167446, 0.8451661, -0.4470088], [1.55167437, 0.8451661, -0.3936264], [1.40167451, 0.8451661, -0.410422117], [1.55167437, 0.8282057, -0.527147532], [1.50167465, 0.795166135, -0.5716109], [1.50167465, 0.820754766, -0.527147532], [1.48667574, 0.795166135, -0.527147532], [1.55167437, 0.8432515, -0.47714752], [1.45167446, 0.812081933, -0.47714752], [1.4395175, 0.795166135, -0.47714752], [1.40167451, 0.826627731, -0.4271475], [1.50167465, 0.795166135, -0.390251517], [1.50167465, 0.7780996, -0.527147532], [1.55167437, 0.7451661, -0.48309952], [1.50167465, 0.7451661, -0.4926805], [1.40167451, 0.7451661, -0.462588251], [1.55167437, 0.7451661, -0.3907206], [1.5819211, 0.6951661, -0.47714752], [1.50167465, 0.6951661, -0.451855361], [1.404871, 0.6951661, -0.4271475], [1.50167465, 0.6951661, -0.392927855], [1.56888962, 0.645166159, -0.47714752], [1.55167437, 0.645166159, -0.465503335], [1.45167446, 0.645166159, -0.430289358], [1.58375931, 0.5951656, -0.527147532], [1.58312559, 0.5934824, -0.47714752], [1.50167465, 0.5951661, -0.435807824], [1.50167465, 0.5951661, -0.397362083], [1.56471777, 0.544589341, -0.4271475], [1.54611015, 0.495166719, -0.4271475], [1.20167542, 1.06123388, -1.52714753], [1.24249053, 1.04516542, -1.37714744], [1.35167551, 1.00218117, -1.52714753], [1.3016746, 0.9951661, -1.559637], [1.25167441, 0.9951661, -1.559637], [1.35167551, 1.00218117, -1.42714751], [1.3016746, 0.9451661, -1.559637], [1.35167456, 0.8451661, -1.559637], [1.24193048, 1.04538584, -1.17714751], [1.20715141, 1.059078, -1.22534215], [1.35750461, 0.9998866, -1.32714748], [1.31077528, 1.01828289, -1.364578], [1.25167441, 0.9951661, -1.36612475], [1.35227108, 1.00194669, -1.27714741], [1.30884862, 1.01904142, -1.27714741], [1.29783034, 0.9951661, -1.27714741], [1.20167446, 0.9951661, -1.31304133], [1.302388, 1.021585, -1.22714746], [1.25167441, 0.9951661, -1.24622643], [1.25603628, 1.03983283, -1.22714746], [1.36949372, 0.995166361, -1.17714751], [1.20167446, 0.9951661, -1.2257874], [1.35167456, 0.9451661, -1.36307049], [1.31575155, 0.9451661, -1.32714748], [1.35167456, 0.9451661, -1.28960478], [1.3016746, 0.9451661, -1.31286418], [1.25167441, 0.9451661, -1.31413507], [1.20167446, 0.9451661, -1.31396759], [1.36353374, 0.8951661, -1.32714748], [1.35167456, 0.8951661, -1.31280923], [1.35167456, 0.932467, -1.27714741], [1.25167441, 0.8951661, -1.31459761], [1.35167456, 0.9132586, -1.22714746], [1.3016746, 0.922026753, -1.22714746], [1.20167446, 0.8951661, -1.26240659], [1.35167456, 0.8951661, -1.19965029], [1.3016746, 0.8951661, -1.17817879], [1.35167456, 0.859504342, -1.27714741], [1.38900828, 0.8451661, -1.17714751], [1.35167456, 0.8880093, -1.17714751], [1.35167456, 0.8083122, -1.22714746], [1.20167446, 0.8107306, -1.17714751], [1.20167446, 0.395166129, -1.25796711], [1.20167446, 0.4259858, -1.22714746], [1.25167441, 0.425994784, -1.17714751], [1.20167446, 0.424144566, -1.17714751], [1.3016746, 0.3709497, -1.27714741], [1.20167446, 0.375277847, -1.27714741], [1.35167456, 0.371557027, -1.22714746], [1.25167441, 0.37127465, -1.22714746], [1.3016746, 0.910192251, -1.12714744], [1.20167446, 0.8951661, -1.14201081], [1.35167456, 0.8855022, -1.07714748], [1.25167441, 0.8632698, -1.07714748], [1.24678707, 0.8451661, -0.97714746], [1.3016746, 0.8103384, -1.07714748], [1.3016746, 0.811907768, -1.02714753], [1.35167456, 0.795166135, -1.0104332], [1.3016746, 0.795166135, -1.00548089], [1.20167446, 0.7451661, -1.10943556], [1.36523867, 0.7451661, -0.97714746], [1.3016746, 0.7451661, -1.00658], [1.25167441, 0.7451661, -0.9996089], [1.20167446, 0.6951661, -1.05594087], [1.3016746, 0.6951661, -1.00851691], [1.20167446, 0.645166159, -1.05355871], [1.35167456, 0.645166159, -1.01092327], [1.25167441, 0.645166159, -1.003242], [1.25167441, 0.5951661, -1.05496442], [1.20167446, 0.545166135, -1.04936588], [1.3016746, 0.545166135, -0.9873408], [1.20167446, 0.545166135, -0.98478353], [1.3016746, 0.5266451, -1.07714748], [1.20167446, 0.522089243, -1.07714748], [1.35167456, 0.495166123, -1.00259078], [1.25167441, 0.495166123, -0.9964634], [1.35167456, 0.445166141, -1.16004217], [1.20167446, 0.445166141, -1.03683567], [1.3016746, 0.445166141, -1.02531576], [1.3016746, 0.395166129, -1.09635949], [1.25167441, 0.3634115, -1.12714744], [1.20167446, 0.367767423, -1.12714744], [1.35167456, 0.811463833, -0.9271475], [1.25167441, 0.795166135, -0.9313692], [1.20167446, 0.795166135, -0.9734819], [1.3016746, 0.7983942, -0.8771475], [1.35167456, 0.795166135, -0.8697789], [1.35167456, 0.7451661, -0.9566827], [1.25446248, 0.7451661, -0.9271475], [1.3001523, 0.7451661, -0.8771475], [1.35167456, 0.6951661, -0.9579582], [1.39271331, 0.6951661, -0.8771475], [1.35167456, 0.6951661, -0.844659], [1.3016746, 0.6951661, -0.8681699], [1.21044731, 0.645166159, -0.9271475], [1.26579809, 0.645166159, -0.8771475], [1.3016746, 0.645166159, -0.861820161], [1.35167456, 0.5951661, -0.958253264], [1.3016746, 0.596075, -0.9271475], [1.35167456, 0.6385679, -0.8771475], [1.25167441, 0.5951661, -0.921230435], [1.35208154, 1.00202143, -0.4271475], [1.35167456, 0.795166135, -0.3809411], [1.35167456, 0.7451661, -0.3989704], [1.101675, 1.10060251, -1.52714753], [1.05167365, 1.12028718, -1.52714753], [1.0346446, 1.09516609, -1.47714746], [1.05167365, 1.12028718, -1.42714751], [1.03515863, 1.09516609, -1.37714744], [1.15167451, 1.045166, -1.559637], [1.05167437, 1.045166, -1.559637], [1.15167546, 1.080918, -1.42714751], [1.03720951, 1.045166, -1.42714751], [1.037235, 1.045166, -1.37714744], [1.15167451, 0.9951661, -1.559637], [1.05167437, 0.9951661, -1.559637], [1.15167451, 0.6951661, -1.559637], [1.03326988, 0.495166123, -1.42714751], [1.03272486, 0.495166123, -1.37714744], [1.02717543, 0.445166141, -1.42714751], [1.08327818, 0.445166141, -1.37714744], [1.101675, 1.10060251, -1.32714748], [1.11548162, 1.09516692, -1.27714741], [1.101428, 1.10069966, -1.27714741], [1.02005911, 1.09516609, -1.22714746], [1.0879283, 1.045166, -1.32714748], [1.16567755, 1.045166, -1.27714741], [1.15167451, 1.045166, -1.25465465], [1.10167456, 1.045166, -1.2592653], [1.15167451, 1.045166, -1.1987071], [1.15392613, 1.080032, -1.17714751], [1.10167456, 1.06939626, -1.17714751], [1.05167437, 1.00972867, -1.27714741], [1.15167451, 0.9951661, -1.256985], [1.00167441, 0.958552837, -1.32714748], [1.00167441, 0.9600516, -1.27714741], [1.15167451, 0.9451661, -1.26142764], [1.00167441, 0.9639937, -1.22714746], [1.14500833, 0.9451661, -1.17714751], [1.05167437, 0.9451661, -1.20258236], [1.10167456, 0.8951661, -1.26310909], [1.05167437, 0.8951661, -1.20784962], [1.10167456, 0.8451661, -1.21214747], [1.00167441, 0.795166135, -1.199157], [1.00167441, 0.7451661, -1.25290227], [1.05167437, 0.7451661, -1.20399225], [1.00167441, 0.6951661, -1.25198615], [1.07638884, 0.6951661, -1.17714751], [1.00167441, 0.645166159, -1.30517852], [1.00167441, 0.5951661, -1.30089748], [1.07167435, 0.5951661, -1.22714746], [1.066365, 0.5951661, -1.17714751], [1.00167441, 0.6351894, -1.17714751], [1.05167437, 0.545166135, -1.30216265], [1.13046789, 0.545166135, -1.22714746], [1.129962, 0.545166135, -1.17714751], [1.10167456, 0.5734535, -1.17714751], [1.04738188, 0.545166135, -1.17714751], [1.05167437, 0.495166123, -1.30032134], [1.12717557, 0.495166123, -1.22714746], [1.10167456, 0.495166123, -1.25264847], [1.00167441, 0.514099538, -1.22714746], [1.12591982, 0.495166123, -1.17714751], [1.05167437, 0.495166123, -1.21372557], [1.08118916, 0.445166141, -1.32714748], [1.15167451, 0.476407528, -1.22714746], [1.0270853, 0.445166141, -1.22714746], [1.07722235, 0.445166141, -1.17714751], [1.010165, 0.395166129, -1.32714748], [1.07716084, 0.395166129, -1.27714741], [1.01478887, 0.395166129, -1.27714741], [1.10167456, 0.395166129, -1.23392844], [1.10167456, 0.395166129, -1.20160711], [1.15167451, 0.372616559, -1.27714741], [1.10167456, 0.3710353, -1.27714741], [1.10352755, 1.09987307, -1.12833607], [1.06023693, 1.116916, -1.1609292], [1.05167365, 1.12028718, -1.12714744], [1.05167365, 1.12028718, -1.07714748], [1.15167546, 1.080918, -1.12714744], [1.05167437, 1.045166, -1.15113842], [1.00167441, 1.045166, -1.141381], [1.10167456, 0.9951661, -1.17217648], [1.00167441, 0.9951661, -1.13174725], [1.00167441, 0.9451661, -1.1365937], [1.15167451, 0.8951661, -1.14350867], [1.17203522, 0.8451661, -1.07714748], [1.05167437, 0.8451661, -1.09245276], [1.00167441, 0.8451661, -1.09465], [1.15167451, 0.795166135, -1.16081977], [1.10167456, 0.795166135, -1.15832853], [1.0188005, 0.795166135, -1.07714748], [1.00167441, 0.795166135, -1.105353], [1.15167451, 0.795166135, -1.03667033], [1.05167437, 0.795166135, -1.05039692], [1.1313014, 0.7451661, -1.12714744], [1.00167441, 0.7451661, -1.12933493], [1.15167451, 0.7451661, -1.10346329], [1.0268805, 0.7451661, -1.07714748], [1.11415291, 0.7451661, -1.02714753], [1.05167437, 0.7451661, -1.05136788], [1.016937, 0.6951661, -1.12714744], [1.00167441, 0.6951661, -1.14886785], [1.15167451, 0.6951661, -1.10332549], [1.05167437, 0.6978024, -1.07714748], [1.10167456, 0.645166159, -1.1555835], [1.11524487, 0.645166159, -1.02714753], [1.14905071, 0.645166159, -0.97714746], [1.12951541, 0.5951661, -1.12714744], [1.10167456, 0.5951661, -1.15498841], [1.05167437, 0.5951661, -1.0724268], [1.12711692, 0.545166135, -1.12714744], [1.15167451, 0.545166135, -1.00222456], [1.06502318, 0.495166123, -1.12714744], [1.08428741, 0.495166123, -1.07714748], [1.15167451, 0.495166123, -1.02025425], [1.110028, 0.445166141, -1.07714748], [1.11685753, 0.395166129, -1.12714744], [1.15167451, 0.395166129, -1.0863353], [1.15167451, 0.373799056, -1.12714744], [1.15167451, 0.6951661, -0.97243917], [0.852989435, 1.19850588, -1.53552985], [0.953336, 1.15900087, -1.52714753], [0.901674747, 1.14516616, -1.559637], [0.8016746, 1.14516616, -1.559637], [0.98433876, 1.14679575, -1.37714744], [0.9516747, 1.09516609, -1.559637], [0.9763503, 1.09516609, -1.47714746], [0.9516747, 1.045166, -1.530766], [0.9516747, 1.07072556, -1.42714751], [0.9516747, 1.07433987, -1.37714744], [0.9875002, 0.9951661, -1.37714744], [0.895669937, 0.9951661, -1.37714744], [0.901674747, 0.965378165, -1.52714753], [0.851674557, 0.9451661, -1.559637], [0.987835646, 0.9451661, -1.47714746], [0.8837762, 0.9451661, -1.47714746], [0.901674747, 0.9733057, -1.42714751], [0.882227659, 0.9451661, -1.42714751], [0.9883921, 0.9451661, -1.37714744], [0.8826549, 0.9451661, -1.37714744], [0.9516747, 0.8951661, -1.559637], [0.932738066, 0.8951661, -1.52714753], [0.851674557, 0.8951661, -1.559637], [0.8016746, 0.8951661, -1.559637], [0.9362452, 0.8951661, -1.37714744], [0.8609917, 0.8951661, -1.37714744], [0.8016746, 0.8451661, -1.559637], [0.851674557, 0.8451661, -1.51567137], [0.86831665, 0.8451661, -1.42714751], [0.8016746, 0.795166135, -1.559637], [0.859725237, 0.795166135, -1.47714746], [0.9319682, 0.795166135, -1.37714744], [0.866024, 0.795166135, -1.37714744], [0.9516747, 0.7451661, -1.559637], [0.939553261, 0.7451661, -1.52714753], [0.859984636, 0.7451661, -1.42714751], [0.901674747, 0.6951661, -1.559637], [0.881557465, 0.6951661, -1.52714753], [0.93175745, 0.6951661, -1.47714746], [0.8388705, 0.6951661, -1.42714751], [0.9253235, 0.6951661, -1.37714744], [0.9338772, 0.645166159, -1.52714753], [0.8038423, 0.645166159, -1.52714753], [0.9178107, 0.645166159, -1.37714744], [0.9516747, 0.5951661, -1.559637], [0.9296067, 0.5951661, -1.52714753], [0.915063143, 0.5951661, -1.47714746], [0.880635, 0.5951661, -1.47714746], [0.851674557, 0.5951661, -1.511246], [0.920319557, 0.5951661, -1.42714751], [0.851674557, 0.6440869, -1.42714751], [0.921882153, 0.5951661, -1.37714744], [0.882735, 0.5951661, -1.37714744], [0.875925064, 0.545166135, -1.47714746], [0.9836192, 0.545166135, -1.42714751], [0.883989334, 0.545166135, -1.42714751], [0.9516747, 0.575507, -1.37714744], [0.9269419, 0.495166123, -1.52714753], [0.851674557, 0.495166123, -1.51113141], [0.901674747, 0.514606655, -1.37714744], [0.9516747, 0.445166141, -1.559637], [0.8932693, 0.445166141, -1.47714746], [0.9516747, 0.445166141, -1.38153684], [0.851674557, 0.395166129, -1.49485409], [0.92539525, 0.395166129, -1.42714751], [0.97574687, 0.395166129, -1.37714744], [0.9516747, 0.395166129, -1.38973725], [0.9884794, 1.14516568, -1.27714741], [0.9912081, 1.09516609, -1.32714748], [0.931838751, 1.045166, -1.32714748], [0.887918949, 0.9951661, -1.32714748], [0.890753746, 0.9951661, -1.27714741], [0.9516747, 1.01499093, -1.22714746], [0.9516747, 0.9451661, -1.34043], [0.9516747, 0.959564865, -1.32714748], [0.933498859, 0.9451661, -1.32714748], [0.8728447, 0.9451661, -1.32714748], [0.861325264, 0.9451661, -1.27714741], [0.863458633, 0.9451661, -1.22714746], [0.8451886, 0.8951661, -1.32714748], [0.9329076, 0.8951661, -1.27714741], [0.851674557, 0.906284, -1.22714746], [0.9516747, 0.8951661, -1.200496], [0.935722351, 0.8451661, -1.32714748], [0.8728974, 0.8451661, -1.27714741], [0.839272, 0.8451661, -1.27714741], [0.928591251, 0.8451661, -1.22714746], [0.851674557, 0.8451661, -1.1934607], [0.851674557, 0.795166135, -1.335704], [0.931620836, 0.795166135, -1.27714741], [0.841457367, 0.795166135, -1.27714741], [0.9516747, 0.795166135, -1.254077], [0.838928461, 0.795166135, -1.22714746], [0.851674557, 0.795166135, -1.18914711], [0.9270284, 0.7451661, -1.32714748], [0.873693943, 0.7451661, -1.32714748], [0.926435947, 0.7451661, -1.27714741], [0.864066362, 0.7451661, -1.27714741], [0.9516747, 0.7451661, -1.25018], [0.851674557, 0.7535804, -1.22714746], [0.901674747, 0.7451661, -1.18329561], [0.9833853, 0.6951661, -1.32714748], [0.8733289, 0.6951661, -1.32714748], [0.9797411, 0.6951661, -1.27714741], [0.8923762, 0.6951661, -1.22714746], [0.901674747, 0.6951661, -1.21055186], [0.979156, 0.645166159, -1.32714748], [0.901674747, 0.645166159, -1.28955233], [0.9516747, 0.645166159, -1.20123875], [0.9772997, 0.5951661, -1.32714748], [0.9516747, 0.5951661, -1.2323817], [0.977741241, 0.545166135, -1.32714748], [0.901674747, 0.545166135, -1.29380476], [0.9516747, 0.545166135, -1.24819744], [0.9175482, 0.495166123, -1.32714748], [0.9516747, 0.495166123, -1.25154293], [0.9516747, 0.46445322, -1.32714748], [0.951675653, 1.15965474, -1.12714744], [0.9516747, 0.9451661, -1.144475], [0.901674747, 0.9451661, -1.16800308], [0.9305198, 0.8951661, -1.12714744], [0.901674747, 0.8951661, -1.15455937], [0.973964, 0.795166135, -1.12714744], [0.9516747, 0.7451661, -1.16797352], [0.9796281, 1.14865017, -0.4271475], [0.6024511, 1.29713869, -1.53254771], [0.6516745, 1.24516606, -1.559637], [0.70167613, 1.25807548, -1.42714751], [0.751674652, 0.795166135, -1.559637], [0.7016747, 0.795166135, -1.559637], [0.601674557, 0.795166135, -1.559637], [0.751674652, 0.7451661, -1.559637], [0.751674652, 0.5951661, -1.559637], [0.7016747, 0.5951661, -1.559637], [0.7016747, 0.545166135, -1.559637], [0.751674652, 0.495166123, -1.559637], [0.6516745, 0.495166123, -1.559637], [0.601674557, 0.495166123, -1.559637], [0.601674557, 0.445166141, -1.559637], [0.751674652, 0.395166129, -1.559637], [0.4555366, 1.35497665, -1.54593492], [0.5516763, 1.31712806, -1.52714753], [0.501674652, 1.295166, -1.559637], [0.5516746, 1.24516606, -1.559637], [0.501674652, 0.9451661, -1.559637], [0.5516746, 0.6951661, -1.559637], [0.5516746, 0.545166135, -1.559637], [0.451674461, 0.495166123, -1.559637], [0.3016746, 1.39516616, -1.559637], [0.3016746, 1.41554952, -1.52714753], [0.2516744, 1.39516616, -1.559637], [0.3016746, 1.41554952, -1.47714746], [0.3016746, 1.34516609, -1.559637], [0.3016746, 1.24516606, -1.559637], [0.351674557, 0.645166159, -1.559637], [0.201674461, 0.445166141, -1.559637], [0.0516726971, 1.51397133, -1.52714753], [0.151674509, 1.44516611, -1.559637], [0.151674509, 1.47460222, -1.52714753], [0.101674557, 1.44516611, -1.559637], [0.051674366, 1.34516609, -1.559637], [0.00167441368, 0.8451661, -1.559637], [0.151674509, 0.795166135, -1.559637], [0.00167441368, 0.445166141, -1.559637], [0.151674509, 1.47460222, -0.4271475], [-0.1983254, 1.595166, -1.559637], [-0.197802067, 1.61218572, -1.52714753], [-0.0455396175, 1.55224228, -1.54228568], [-0.09832668, 1.5730238, -1.52714753], [-0.148325443, 1.545166, -1.559637], [-0.1983254, 1.44516611, -1.559637], [-0.0483253, 0.6951661, -1.559637], [-0.0483253, 0.495166123, -1.559637], [-0.148325443, 0.445166141, -1.559637], [-0.1983254, 0.445166141, -1.559637], [-0.09832668, 1.5730238, -1.32714748], [-0.198323011, 1.61239076, -0.97714746], [-0.198323011, 1.61239076, -0.6771475], [-0.398325443, 1.64516592, -1.559637], [-0.342688322, 1.669225, -1.42714751], [-0.243897676, 1.63033271, -1.52714753], [-0.2983253, 1.595166, -1.559637], [-0.2983253, 1.61782646, -1.52714753], [-0.3483255, 1.595166, -1.559637], [-0.3483255, 1.62629724, -1.52714753], [-0.248325348, 1.63207579, -1.47714746], [-0.248325348, 1.545166, -1.559637], [-0.398325443, 1.545166, -1.559637], [-0.2983253, 1.545166, -1.48117256], [-0.3483255, 1.58410144, -1.47714746], [-0.2983253, 1.57240748, -1.42714751], [-0.387028933, 1.545166, -1.42714751], [-0.27317524, 1.545166, -1.37714744], [-0.248325348, 1.49516606, -1.559637], [-0.27213645, 1.49516606, -1.47714746], [-0.398325443, 1.52864075, -1.47714746], [-0.3312416, 1.49516606, -1.37714744], [-0.3483255, 1.49516606, -1.391273], [-0.398325443, 1.52881885, -1.37714744], [-0.248325348, 1.44516611, -1.559637], [-0.282921076, 1.44516611, -1.47714746], [-0.3483255, 1.47183275, -1.37714744], [-0.2983253, 1.40073228, -1.52714753], [-0.3483255, 1.39516616, -1.559637], [-0.398325443, 1.39516616, -1.559637], [-0.398325443, 1.42099667, -1.52714753], [-0.3483255, 1.40502429, -1.47714746], [-0.398325443, 1.42885256, -1.47714746], [-0.2983253, 1.40532041, -1.42714751], [-0.2983253, 1.34516609, -1.559637], [-0.3483255, 1.19516611, -1.559637], [-0.2867639, 1.64720845, -1.32714748], [-0.351430178, 1.64516592, -1.32714748], [-0.398324966, 1.69112825, -1.27714741], [-0.3589549, 1.64516592, -1.22714746], [-0.390935183, 1.68821883, -1.22714746], [-0.378577471, 1.64516592, -1.17714751], [-0.248325348, 1.63207579, -1.32714748], [-0.2919407, 1.64924645, -1.27714741], [-0.319290876, 1.595166, -1.27714741], [-0.3483255, 1.595166, -1.29055226], [-0.361730337, 1.595166, -1.27714741], [-0.248325348, 1.63207579, -1.22714746], [-0.291921616, 1.595166, -1.22714746], [-0.398325443, 1.595166, -1.2656672], [-0.398325443, 1.63500929, -1.22714746], [-0.3483255, 1.595166, -1.21235085], [-0.398325443, 1.63824558, -1.17714751], [-0.27761364, 1.545166, -1.32714748], [-0.2983253, 1.58355188, -1.32714748], [-0.3483255, 1.58353591, -1.32714748], [-0.386695147, 1.545166, -1.32714748], [-0.2716365, 1.545166, -1.27714741], [-0.2983253, 1.58891821, -1.27714741], [-0.398325443, 1.545166, -1.26491988], [-0.269184828, 1.545166, -1.17714751], [-0.3322854, 1.49516606, -1.32714748], [-0.398325443, 1.52808928, -1.27714741], [-0.3314457, 1.49516606, -1.22714746], [-0.3483255, 1.50699115, -1.22714746], [-0.3656478, 1.49516606, -1.22714746], [-0.3483255, 1.506685, -1.17714751], [-0.2877319, 1.44516611, -1.27714741], [-0.3483255, 1.4728744, -1.27714741], [-0.3483255, 1.47264123, -1.17714751], [-0.3483255, 1.40509319, -1.32714748], [-0.2983253, 1.393348, -1.27714741], [-0.373423338, 1.64516592, -1.12714744], [-0.398324966, 1.69112825, -1.12714744], [-0.3483231, 1.67144322, -1.07714748], [-0.383405447, 1.64516592, -1.02714753], [-0.398324966, 1.69112825, -1.02714753], [-0.328819036, 1.663765, -0.97714746], [-0.339368343, 1.667918, -0.986927748], [-0.248325348, 1.63207579, -1.12714744], [-0.29034996, 1.595166, -1.12714744], [-0.3483255, 1.595166, -1.146064], [-0.398325443, 1.62938547, -1.12714744], [-0.271607637, 1.595166, -1.07714748], [-0.3226564, 1.595166, -1.07714748], [-0.3483255, 1.595166, -1.11371613], [-0.398325443, 1.633295, -1.07714748], [-0.2461958, 1.63123727, -1.02714753], [-0.3483255, 1.595166, -1.0532192], [-0.329190016, 1.595166, -0.97714746], [-0.3483255, 1.595166, -1.00338066], [-0.398325443, 1.595166, -0.990640044], [-0.269821167, 1.545166, -1.12714744], [-0.269793272, 1.545166, -0.97714746], [-0.332453251, 1.545166, -0.97714746], [-0.398325443, 1.545166, -0.989805937], [-0.3307202, 1.49516606, -1.12714744], [-0.366176367, 1.49516606, -1.12714744], [-0.3483255, 1.50732112, -1.02714753], [-0.330750227, 1.49516606, -0.97714746], [-0.366122, 1.49516606, -0.97714746], [-0.398325443, 1.5273695, -0.97714746], [-0.3483255, 1.47182608, -0.97714746], [-0.398325443, 1.4334929, -1.07714748], [-0.3483231, 1.67144322, -0.9271475], [-0.3483231, 1.67144322, -0.777147532], [-0.242991686, 1.629976, -0.9271475], [-0.3296864, 1.595166, -0.9271475], [-0.243828773, 1.63030553, -0.777147532], [-0.3323543, 1.595166, -0.777147532], [-0.398325443, 1.52705455, -0.9271475], [-0.329779148, 1.49516606, -0.8771475], [-0.3483255, 1.4712677, -0.8771475], [-0.3483255, 1.46979427, -0.777147532], [-0.3483255, 1.40414381, -0.9271475], [-0.2983253, 1.386956, -0.9271475], [-0.2983253, 1.38856077, -0.777147532], [-0.330596447, 1.66446447, -0.6271475], [-0.3483231, 1.67144322, -0.6271475], [-0.374482632, 1.64516592, -0.5771475], [-0.398324966, 1.69112825, -0.5771475], [-0.246137857, 1.63121462, -0.6271475], [-0.331119537, 1.595166, -0.5771475], [-0.32859993, 1.49516606, -0.72714746], [-0.398325443, 1.52579522, -0.72714746], [-0.328713417, 1.49516606, -0.6771475], [-0.36791873, 1.49516606, -0.6771475], [-0.275239, 1.49516606, -0.6271475], [-0.398325443, 1.52736163, -0.5771475], [-0.3483255, 1.46993494, -0.5771475], [-0.2983253, 1.39949989, -0.5771475], [-0.398325443, 1.42210937, -0.5771475], [-0.3724482, 1.64516592, -0.527147532], [-0.398324966, 1.69112825, -0.527147532], [-0.3663683, 1.64516592, -0.4271475], [-0.394366264, 1.68956971, -0.4271475], [-0.398325443, 1.64516592, -0.4026545], [-0.33122015, 1.595166, -0.47714752], [-0.2584455, 1.595166, -0.4271475], [-0.3483255, 1.6288836, -0.4271475], [-0.3483255, 1.595166, -0.4086516], [-0.398325443, 1.595166, -0.413716882], [-0.327538729, 1.49516606, -0.527147532], [-0.283476353, 1.44516611, -0.47714752], [-0.3483255, 1.46803784, -0.47714752], [-0.2983253, 1.3958559, -0.527147532], [-0.3483255, 1.39528751, -0.527147532], [-0.2983253, 1.39571738, -0.47714752], [-0.5983255, 1.74516606, -1.559637], [-0.4085834, 1.69516683, -1.52714753], [-0.4483254, 1.69516611, -1.559637], [-0.448326349, 1.710813, -1.52714753], [-0.498325348, 1.69516611, -1.559637], [-0.448326349, 1.710813, -1.37714744], [-0.5983255, 1.64516592, -1.559637], [-0.548325539, 1.595166, -1.559637], [-0.5602143, 1.595166, -1.52714753], [-0.5983255, 1.63327718, -1.52714753], [-0.4483254, 1.545166, -1.559637], [-0.498325348, 1.58291626, -1.52714753], [-0.548325539, 1.57168555, -1.47714746], [-0.4483254, 1.52894616, -1.52714753], [-0.5983255, 1.49516606, -1.559637], [-0.5983255, 1.52689075, -1.52714753], [-0.523011446, 1.49516606, -1.42714751], [-0.4483254, 1.52219915, -1.37714744], [-0.4483254, 1.4584868, -1.52714753], [-0.498325348, 1.44516611, -1.559637], [-0.5983255, 0.495166123, -1.559637], [-0.5483248, 1.75018072, -1.17714751], [-0.4324026, 1.70454383, -1.32714748], [-0.448326349, 1.710813, -1.27714741], [-0.4843564, 1.69516611, -1.22714746], [-0.498324633, 1.73049641, -1.22714746], [-0.4085834, 1.69516683, -1.17714751], [-0.448326349, 1.710813, -1.17714751], [-0.4483254, 1.64516592, -1.26555014], [-0.4867282, 1.64516592, -1.22714746], [-0.4483254, 1.64516592, -1.18874478], [-0.4483254, 1.595166, -1.26532245], [-0.486500263, 1.595166, -1.22714746], [-0.5983255, 1.62882519, -1.22714746], [-0.4483254, 1.63344932, -1.17714751], [-0.4866085, 1.595166, -1.17714751], [-0.43609786, 1.545166, -1.22714746], [-0.498325348, 1.57701445, -1.17714751], [-0.5983255, 1.52902341, -1.27714741], [-0.4483254, 1.52192545, -1.22714746], [-0.4483254, 1.63332057, -1.12714744], [-0.486479759, 1.595166, -1.12714744], [-0.5983255, 1.62810349, -1.12714744], [-0.4349823, 1.595166, -1.07714748], [-0.4348328, 1.595166, -1.02714753], [-0.435821533, 1.545166, -1.12714744], [-0.548325539, 1.57149553, -1.12714744], [-0.498325348, 1.57726836, -1.07714748], [-0.435667038, 1.545166, -1.02714753], [-0.5983255, 1.52556252, -1.07714748], [-0.4483254, 1.52097106, -1.02714753], [-0.5983255, 1.62802482, -0.8771475], [-0.466436863, 1.545166, -0.9271475], [-0.548325539, 1.57201076, -0.9271475], [-0.4483254, 1.51963615, -0.9271475], [-0.4483254, 1.46231508, -0.777147532], [-0.498324633, 1.73049641, -0.6771475], [-0.5983255, 1.62832117, -0.6771475], [-0.498325348, 1.57694983, -0.72714746], [-0.548325539, 1.5724349, -0.6771475], [-0.498325348, 1.57813191, -0.5771475], [-0.4483254, 1.51928926, -0.72714746], [-0.498325348, 1.470624, -0.5771475], [-0.598324537, 1.769865, -0.527147532], [-0.535588264, 1.74516678, -0.4271475], [-0.498324633, 1.73049641, -0.4271475], [-0.498325348, 1.69516611, -0.418101639], [-0.4483254, 1.64516592, -0.4140853], [-0.498325348, 1.64516592, -0.4139676], [-0.5983255, 1.627902, -0.527147532], [-0.5983255, 1.627651, -0.47714752], [-0.548325539, 1.572115, -0.527147532], [-0.498325348, 1.57636428, -0.47714752], [-0.548325539, 1.57205009, -0.47714752], [-0.5983255, 1.564765, -0.4271475], [-0.4483254, 1.52057886, -0.527147532], [-0.5983255, 1.52143359, -0.47714752], [-0.4483254, 1.44767165, -0.527147532], [-0.6625905, 1.7951653, -1.52714753], [-0.748325348, 1.795166, -1.559637], [-0.748325348, 1.74516606, -1.559637], [-0.7983254, 1.74516606, -1.559637], [-0.6983254, 1.69516611, -1.559637], [-0.748325348, 1.69516611, -1.559637], [-0.7983254, 1.72990322, -1.47714746], [-0.6483253, 1.64516592, -1.559637], [-0.6983254, 1.68341875, -1.52714753], [-0.7983254, 1.66690516, -1.42714751], [-0.6483253, 1.62890649, -1.52714753], [-0.748325348, 1.60260844, -1.52714753], [-0.7983254, 1.595166, -1.559637], [-0.7983254, 1.61784482, -1.52714753], [-0.6483253, 1.62366986, -1.47714746], [-0.74154985, 1.595166, -1.42714751], [-0.6483253, 1.545166, -1.559637], [-0.6483253, 1.55548191, -1.52714753], [-0.6983254, 1.545166, -1.559637], [-0.748325348, 1.49516606, -1.559637], [-0.6483253, 1.295166, -1.559637], [-0.6983254, 1.67956614, -1.17714751], [-0.7983254, 1.66806483, -1.17714751], [-0.6483253, 1.62305474, -1.32714748], [-0.7983254, 1.72968984, -1.12714744], [-0.7983254, 1.7297132, -1.07714748], [-0.748325348, 1.67485356, -1.12714744], [-0.748325348, 1.67505312, -0.97714746], [-0.6483253, 1.62444234, -1.07714748], [-0.7983254, 1.66805577, -0.9271475], [-0.6983254, 1.67894173, -0.8271475], [-0.7983254, 1.66725183, -0.8271475], [-0.6483253, 1.62341881, -0.777147532], [-0.789599657, 1.84516668, -0.72714746], [-0.748325348, 1.67523909, -0.72714746], [-0.7983254, 1.667866, -0.6771475], [-0.6983254, 1.61664939, -0.6771475], [-0.6983254, 1.57018709, -0.72714746], [-0.748325348, 1.676295, -0.47714752], [-0.7983254, 1.66974711, -0.4271475], [-0.6983254, 1.61713719, -0.527147532], [-0.6191863, 1.595166, -0.4271475], [-0.6483253, 1.624305, -0.4271475], [-0.944068551, 1.90597868, -1.54708731], [-0.9483243, 1.907654, -1.52714753], [-0.8983253, 1.845166, -1.559637], [-0.9864813, 1.84547353, -1.52714753], [-0.9865817, 1.84516716, -1.42714751], [-0.9483253, 1.795166, -1.559637], [-0.957300544, 1.795166, -1.52714753], [-0.8983253, 1.74516606, -1.559637], [-0.9483253, 1.77867389, -1.47714746], [-0.998325348, 1.69516611, -1.559637], [-0.998325348, 1.71097183, -1.52714753], [-0.8983253, 1.72097826, -1.47714746], [-0.848325253, 1.72613716, -1.42714751], [-0.8983253, 1.71998143, -1.37714744], [-0.998325348, 1.70428848, -1.37714744], [-0.848325253, 1.64516592, -1.559637], [-0.8983253, 1.66574979, -1.52714753], [-0.9483253, 1.64516592, -1.559637], [-0.998325348, 1.64516592, -1.559637], [-0.850448132, 1.64516592, -1.47714746], [-0.8983253, 1.595166, -1.559637], [-0.9483253, 0.645166159, -1.559637], [-0.998325348, 0.645166159, -1.559637], [-0.8983253, 0.545166135, -1.559637], [-0.9483253, 0.545166135, -1.559637], [-0.998325348, 0.545166135, -1.52024329], [-0.9483253, 0.495166123, -1.559637], [-0.9483253, 1.777606, -1.22714746], [-1.0035764, 1.79335, -1.22714746], [-0.848325253, 1.72588634, -1.22714746], [-0.998325348, 1.70567632, -1.22714746], [-0.8983253, 1.71980214, -1.17714751], [-0.8983253, 1.66380382, -1.17714751], [-0.848325253, 1.642967, -1.27714741], [-0.9695318, 1.89715314, -1.07714748], [-0.9865817, 1.84516716, -1.02714753], [-0.9483253, 1.77688479, -1.12714744], [-0.9163649, 1.74516606, -0.97714746], [-0.998325348, 1.71239471, -1.12714744], [-0.8983253, 1.71748781, -1.07714748], [-0.998325348, 1.71349478, -1.02714753], [-0.8983253, 1.66684079, -1.02714753], [-0.9483243, 1.907654, -0.9271475], [-0.9695318, 1.89715314, -0.8771475], [-0.9483253, 1.7775228, -0.9271475], [-0.848325253, 1.72626019, -0.9271475], [-0.8983253, 1.71974707, -0.9271475], [-0.8983253, 1.72148871, -0.777147532], [-0.9865817, 1.84516716, -0.6271475], [-0.9618714, 1.795166, -0.5771475], [-0.9483253, 1.77852654, -0.72714746], [-1.00345206, 1.79372931, -0.6271475], [-0.848325253, 1.72661138, -0.6771475], [-0.998325348, 1.705137, -0.6771475], [-0.8983253, 1.65432167, -0.6771475], [-0.9695318, 1.89715314, -0.47714752], [-0.9618894, 1.795166, -0.4271475], [-0.8983253, 1.72372627, -0.527147532], [-0.998325348, 1.71285534, -0.4271475], [-0.8983253, 1.64877987, -0.527147532], [-1.00272512, 1.79594541, -1.37714744], [-1.03569329, 1.6954248, -1.52714753], [-1.05180264, 1.64630651, -1.53578937], [-1.04832542, 1.65690875, -1.37714744], [-1.06857514, 1.59516692, -1.47714746], [-1.0483253, 1.545166, -1.559637], [-1.1010766, 1.49606848, -1.53423965], [-1.0483253, 1.44516611, -1.559637], [-1.15056849, 1.34516573, -1.52714753], [-1.14832532, 1.295166, -1.559637], [-1.166967, 1.29516613, -1.52714753], [-1.1833657, 1.24516582, -1.42714751], [-1.0483253, 1.19516611, -1.559637], [-1.1983254, 0.9951661, -1.559637], [-1.09832537, 0.8451661, -1.559637], [-1.1983254, 0.8451661, -1.559637], [-1.09832537, 0.795166135, -1.559637], [-1.14832532, 0.795166135, -1.559637], [-1.1983254, 0.833387733, -1.52714753], [-1.15687728, 0.795166135, -1.42714751], [-1.1983254, 0.822086, -1.42714751], [-1.0483253, 0.7451661, -1.559637], [-1.09832537, 0.7451661, -1.48399746], [-1.14832532, 0.7774875, -1.47714746], [-1.09832537, 0.7469975, -1.42714751], [-1.03691912, 0.6951661, -1.52714753], [-1.05623484, 0.6951661, -1.47714746], [-1.06759751, 0.6951661, -1.42714751], [-1.03403234, 0.645166159, -1.52714753], [-1.06271636, 0.645166159, -1.47714746], [-1.07073736, 0.645166159, -1.42714751], [-1.07355237, 0.645166159, -1.37714744], [-1.0483253, 0.5951661, -1.4851687], [-1.08751917, 0.5951661, -1.42714751], [-1.09981763, 0.5951661, -1.37714744], [-1.0483253, 0.5594433, -1.47714746], [-1.10520637, 0.545166135, -1.42714751], [-1.0483253, 0.445166141, -1.51117122], [-1.0483253, 0.395166129, -1.559637], [-1.09832537, 0.395166129, -1.50122058], [-1.14301407, 0.395166129, -1.42714751], [-1.14832532, 0.7986419, -1.32714748], [-1.09832537, 0.7488911, -1.27714741], [-1.09677815, 0.7451661, -1.22714746], [-1.14832532, 0.7723681, -1.22714746], [-1.1983254, 0.7761947, -1.17714751], [-1.06775117, 0.6951661, -1.32714748], [-1.0640198, 0.6951661, -1.17714751], [-1.098431, 0.645166159, -1.22714746], [-1.07994366, 0.645166159, -1.17714751], [-1.09597933, 0.5951661, -1.27714741], [-1.11147463, 0.545166135, -1.32714748], [-1.10559916, 0.495166123, -1.22714746], [-1.15391839, 0.395166129, -1.32714748], [-1.149475, 0.395166129, -1.22714746], [-1.14832532, 0.401672482, -1.17714751], [-1.00274611, 1.79588175, -0.97714746], [-1.03548014, 1.69607425, -1.07714748], [-1.13416982, 1.3951664, -1.12714744], [-1.19096935, 0.8451661, -1.02714753], [-1.19161367, 0.795166135, -1.12714744], [-1.14832532, 0.770040035, -1.12714744], [-1.13308978, 0.7451661, -1.07714748], [-1.14832532, 0.768553, -1.02714753], [-1.13181949, 0.7451661, -0.97714746], [-1.06186008, 0.6951661, -1.12714744], [-1.06642735, 0.6951661, -1.07714748], [-1.09657526, 0.6951661, -0.97714746], [-1.0625236, 0.645166159, -1.12714744], [-1.04106212, 0.645166159, -1.07714748], [-1.0483253, 0.645166159, -1.01357055], [-1.09832537, 0.645166159, -0.9780116], [-1.07631159, 0.5951661, -1.07714748], [-1.08145881, 0.5951661, -1.02714753], [-1.108216, 0.5951661, -0.97714746], [-1.10784113, 0.495166123, -1.12714744], [-1.09832537, 0.4589195, -0.97714746], [-1.13528407, 0.395166129, -1.07714748], [-1.12894654, 0.395166129, -0.97714746], [-1.0353334, 1.69652224, -0.9271475], [-1.174197, 0.8451661, -0.9271475], [-1.13504279, 0.795166135, -0.8771475], [-1.12918115, 0.795166135, -0.8271475], [-1.09019423, 0.7451661, -0.777147532], [-1.09956229, 0.6951661, -0.9271475], [-1.07000554, 0.6951661, -0.777147532], [-1.12831748, 0.645166159, -0.9271475], [-1.117225, 0.645166159, -0.8771475], [-1.09632111, 0.645166159, -0.8271475], [-1.12885737, 0.5951661, -0.9271475], [-1.12847364, 0.5951661, -0.8771475], [-1.11790586, 0.5951661, -0.8271475], [-1.09832537, 0.616038, -0.777147532], [-1.132064, 0.5951661, -0.777147532], [-1.10147262, 0.545166135, -0.9271475], [-1.1260674, 0.545166135, -0.8271475], [-1.12063849, 0.495166123, -0.8771475], [-1.16688955, 0.495166123, -0.777147532], [-1.10768414, 0.445166141, -0.9271475], [-1.14832532, 0.445166141, -0.8068446], [-1.1384604, 0.395166129, -0.9271475], [-1.14832532, 0.428515583, -0.8771475], [-1.15222514, 0.395166129, -0.8271475], [-1.1983254, 0.368224651, -0.8271475], [-1.1983254, 0.369333029, -0.777147532], [-1.16885006, 0.8451661, -0.72714746], [-1.1983254, 0.8899395, -0.6271475], [-1.09170628, 0.7451661, -0.6771475], [-1.09330261, 0.7451661, -0.5771475], [-1.0657109, 0.6951661, -0.72714746], [-1.07800686, 0.6951661, -0.6271475], [-1.0730139, 0.6951661, -0.5771475], [-1.06565952, 0.645166159, -0.72714746], [-1.09832537, 0.6539047, -0.6271475], [-1.14832532, 0.6187627, -0.72714746], [-1.09832537, 0.6432742, -0.6771475], [-1.14832532, 0.636361063, -0.6771475], [-1.14832532, 0.6379301, -0.6271475], [-1.1983254, 0.622862339, -0.6271475], [-1.1983254, 0.545166135, -0.753029168], [-1.18355858, 0.545166135, -0.5771475], [-1.1983254, 0.495166123, -0.7382094], [-1.19180369, 0.495166123, -0.5771475], [-1.174021, 0.445166141, -0.72714746], [-1.16112256, 0.395166129, -0.6271475], [-1.16901767, 0.395166129, -0.5771475], [-1.1983254, 0.355030328, -0.6271475], [-1.03577781, 1.69516683, -0.527147532], [-1.1983254, 0.8673492, -0.47714752], [-1.11722457, 0.7451661, -0.47714752], [-1.11775184, 0.7451661, -0.4271475], [-1.0585618, 0.6951661, -0.527147532], [-1.07997549, 0.6951661, -0.47714752], [-1.10975492, 0.6951661, -0.4271475], [-1.0624789, 0.645166159, -0.527147532], [-1.09832537, 0.645166159, -0.5680939], [-1.07136166, 0.645166159, -0.47714752], [-1.10481441, 0.645166159, -0.4271475], [-1.09081984, 0.5951661, -0.527147532], [-1.14832532, 0.5951661, -0.5583459], [-1.10083413, 0.5951661, -0.4271475], [-1.13293743, 0.495166123, -0.4271475], [-1.1983254, 0.495166123, -0.40972054], [-1.15887749, 0.445166141, -0.527147532], [-1.1983254, 0.445166141, -0.5689676], [-1.15553129, 0.445166141, -0.47714752], [-1.19174218, 0.445166141, -0.4271475], [-1.13738155, 0.395166129, -0.47714752], [-1.153364, 0.395166129, -0.4271475], [-1.1983254, 0.346119, -0.527147532], [-1.14832532, 0.3636761, -0.47714752], [-1.1983254, 0.34770596, -0.47714752], [-1.19976437, 1.195166, -1.52714753], [-1.24832523, 1.04710186, -1.52714753], [-1.24832535, 0.9451661, -1.559637], [-1.28175712, 0.945166767, -1.52714753], [-1.28175712, 0.945166767, -1.42714751], [-1.28132975, 0.9464697, -1.37714744], [-1.24832535, 0.8951661, -1.559637], [-1.29722106, 0.898016751, -1.52714753], [-1.29678631, 0.8993423, -1.47714746], [-1.29681993, 0.899240136, -1.42714751], [-1.29739463, 0.8974876, -1.37714744], [-1.233532, 0.8451661, -1.52714753], [-1.25040221, 0.8451661, -1.37714744], [-1.24832523, 1.04710186, -1.27714741], [-1.265224, 0.995577455, -1.27714741], [-1.264924, 0.996491969, -1.17714751], [-1.28011811, 0.950164258, -1.32714748], [-1.26008964, 0.8951661, -1.27714741], [-1.25399816, 0.8951661, -1.22714746], [-1.27384734, 0.8451661, -1.32714748], [-1.25739765, 0.8451661, -1.17714751], [-1.24832535, 0.8326026, -1.32714748], [-1.29832542, 0.795166135, -1.28263545], [-1.27004385, 0.795166135, -1.22714746], [-1.24896014, 1.045166, -1.07714748], [-1.26513124, 0.9958597, -1.07714748], [-1.23404157, 0.9451661, -1.12714744], [-1.22021377, 0.8951661, -1.07714748], [-1.22338033, 0.8451661, -1.12714744], [-1.24832523, 1.04710186, -0.8271475], [-1.26480615, 0.996851265, -0.9271475], [-1.23727584, 0.9451661, -0.9271475], [-1.20300126, 0.8951661, -0.777147532], [-1.26535952, 0.995163739, -0.6771475], [-1.26535952, 0.995163739, -0.5771475], [-1.23412037, 0.9451661, -0.6771475], [-1.27988911, 0.950862646, -0.6271475], [-1.24899137, 0.9451661, -0.5771475], [-1.20717168, 0.8951661, -0.72714746], [-1.21498621, 0.5951661, -0.72714746], [-1.24832535, 0.595511556, -0.6771475], [-1.23561871, 0.5951661, -0.6271475], [-1.22865975, 0.545166135, -0.6771475], [-1.21437311, 0.495166123, -0.6271475], [-1.21028578, 0.445166141, -0.6271475], [-1.19976437, 1.195166, -0.527147532], [-1.26535952, 0.995163739, -0.4271475], [-1.28033745, 0.9494956, -0.4271475], [-1.24832535, 0.8451661, -0.44176358], [-1.24832535, 0.545166135, -0.394482434], [-1.29832542, 0.495166123, -0.38890487], [-1.25239837, 0.433711022, -0.386665076]], "Indices": [0, 1, 856, 856, 1, 4, 1, 2, 4, 3, 4, 15, 3, 15, 5, 6, 7, 0, 7, 2578, 30, 0, 7, 8, 8, 1, 0, 8, 11, 1, 7, 32, 8, 8, 32, 13, 9, 11, 8, 9, 8, 13, 2, 1, 11, 16, 2, 9, 11, 9, 2, 12, 16, 9, 9, 13, 12, 12, 18, 16, 13, 14, 18, 10, 14, 13, 2, 16, 4, 4, 16, 15, 16, 17, 15, 16, 18, 17, 13, 18, 12, 18, 20, 19, 20, 21, 19, 18, 14, 20, 14, 37, 20, 20, 37, 21, 5, 15, 24, 5, 24, 22, 22, 24, 23, 24, 15, 17, 17, 25, 24, 23, 24, 26, 24, 25, 26, 23, 26, 28, 17, 18, 25, 18, 27, 25, 25, 27, 26, 26, 27, 28, 18, 19, 27, 19, 28, 27, 28, 19, 29, 29, 19, 21, 2578, 31, 30, 30, 32, 7, 30, 33, 32, 30, 31, 33, 31, 62, 33, 13, 32, 10, 33, 34, 32, 34, 33, 35, 33, 52, 35, 10, 32, 34, 34, 36, 10, 34, 35, 36, 40, 10, 36, 10, 40, 14, 14, 38, 37, 38, 39, 37, 40, 38, 14, 38, 46, 39, 40, 41, 38, 38, 41, 46, 36, 35, 40, 40, 50, 41, 40, 42, 50, 42, 40, 53, 35, 53, 40, 22, 23, 56, 23, 28, 56, 28, 43, 56, 28, 29, 43, 29, 21, 43, 37, 43, 21, 37, 39, 44, 43, 37, 44, 43, 44, 45, 39, 47, 44, 44, 47, 45, 39, 46, 47, 41, 48, 46, 46, 48, 47, 48, 41, 50, 47, 48, 49, 48, 50, 49, 49, 50, 51, 50, 42, 51, 51, 42, 53, 31, 3031, 62, 61, 62, 3031, 52, 33, 62, 52, 62, 80, 63, 52, 80, 52, 63, 35, 63, 54, 35, 66, 54, 63, 53, 35, 54, 55, 53, 54, 55, 54, 64, 64, 54, 66, 22, 56, 911, 56, 43, 57, 43, 45, 57, 57, 45, 49, 45, 47, 49, 49, 58, 57, 49, 51, 58, 51, 53, 58, 57, 58, 59, 58, 53, 59, 53, 55, 59, 64, 60, 55, 55, 60, 59, 59, 60, 101, 911, 56, 107, 107, 56, 57, 105, 59, 106, 59, 101, 106, 61, 76, 62, 76, 80, 62, 80, 88, 63, 63, 88, 66, 65, 64, 66, 66, 68, 65, 68, 67, 65, 68, 71, 67, 60, 64, 69, 101, 60, 69, 64, 65, 69, 65, 70, 69, 65, 67, 70, 70, 67, 71, 3157, 76, 61, 3157, 72, 76, 72, 77, 76, 73, 75, 72, 72, 75, 77, 3167, 73, 72, 3167, 74, 73, 74, 75, 73, 77, 80, 76, 75, 78, 77, 74, 79, 75, 79, 78, 75, 79, 83, 78, 77, 82, 80, 82, 81, 80, 78, 82, 77, 83, 82, 78, 83, 84, 82, 176, 179, 83, 80, 81, 88, 81, 93, 88, 86, 93, 81, 84, 86, 81, 82, 84, 81, 83, 85, 84, 85, 86, 84, 83, 179, 85, 85, 97, 86, 91, 66, 88, 68, 66, 91, 184, 90, 87, 89, 185, 90, 94, 91, 88, 89, 91, 94, 89, 92, 91, 90, 92, 89, 90, 184, 92, 93, 94, 88, 94, 93, 89, 93, 95, 89, 93, 96, 95, 185, 89, 95, 86, 96, 93, 97, 96, 86, 97, 95, 96, 184, 98, 92, 91, 99, 68, 92, 98, 91, 98, 99, 91, 99, 71, 68, 98, 100, 99, 69, 70, 101, 106, 101, 103, 101, 102, 103, 101, 70, 102, 70, 71, 102, 71, 104, 102, 102, 104, 100, 100, 103, 102, 71, 99, 104, 100, 104, 99, 107, 57, 105, 57, 59, 105, 228, 105, 106, 107, 967, 911, 107, 105, 112, 105, 228, 109, 112, 105, 108, 105, 109, 108, 128, 111, 110, 968, 107, 150, 150, 107, 144, 107, 112, 144, 144, 112, 129, 112, 108, 113, 132, 112, 113, 108, 109, 114, 127, 113, 114, 114, 113, 108, 3498, 116, 115, 134, 116, 3498, 117, 116, 134, 116, 119, 118, 117, 119, 116, 119, 121, 118, 121, 120, 118, 117, 135, 119, 135, 121, 119, 121, 315, 120, 331, 124, 298, 298, 122, 110, 123, 122, 298, 124, 123, 298, 143, 122, 123, 141, 143, 123, 126, 128, 122, 143, 126, 122, 133, 127, 128, 126, 133, 128, 143, 125, 126, 125, 133, 126, 122, 128, 110, 128, 127, 111, 1087, 968, 150, 144, 129, 145, 129, 112, 130, 112, 132, 130, 129, 130, 131, 130, 132, 133, 125, 131, 133, 133, 131, 130, 127, 133, 132, 127, 132, 113, 137, 143, 139, 139, 136, 137, 138, 141, 123, 139, 143, 141, 139, 141, 138, 124, 140, 138, 139, 138, 140, 138, 123, 124, 148, 142, 143, 137, 148, 143, 136, 148, 137, 147, 125, 143, 143, 142, 147, 149, 1087, 150, 402, 150, 151, 151, 150, 144, 145, 129, 146, 129, 131, 147, 142, 146, 147, 147, 146, 129, 148, 146, 142, 147, 131, 125, 139, 140, 155, 156, 136, 139, 155, 156, 139, 156, 148, 136, 156, 154, 148, 149, 150, 160, 152, 151, 144, 152, 144, 161, 161, 144, 153, 153, 144, 145, 145, 146, 148, 148, 153, 145, 154, 153, 148, 375, 155, 157, 155, 375, 156, 375, 158, 156, 140, 352, 155, 392, 156, 158, 159, 156, 392, 159, 154, 156, 160, 150, 402, 402, 151, 403, 403, 151, 152, 405, 152, 161, 407, 161, 159, 154, 161, 153, 159, 161, 154, 3169, 162, 74, 163, 162, 4235, 74, 162, 164, 165, 164, 162, 163, 165, 162, 167, 166, 168, 164, 79, 74, 165, 169, 164, 169, 79, 164, 165, 170, 169, 169, 174, 79, 172, 174, 169, 170, 171, 169, 171, 172, 169, 79, 174, 83, 175, 174, 172, 173, 175, 172, 175, 176, 174, 172, 171, 173, 175, 173, 177, 174, 176, 83, 176, 175, 177, 177, 178, 176, 178, 179, 176, 178, 189, 179, 179, 97, 85, 180, 183, 221, 221, 181, 180, 87, 185, 182, 90, 185, 87, 182, 185, 180, 185, 183, 180, 182, 184, 87, 180, 181, 182, 181, 184, 182, 185, 186, 223, 185, 95, 186, 186, 95, 188, 95, 97, 188, 223, 186, 187, 186, 188, 187, 179, 188, 97, 189, 188, 179, 189, 187, 188, 190, 192, 191, 220, 191, 192, 181, 192, 184, 192, 98, 184, 192, 190, 98, 190, 100, 98, 106, 103, 193, 193, 103, 194, 193, 194, 229, 190, 191, 194, 229, 194, 191, 194, 103, 100, 190, 194, 100, 4275, 231, 195, 202, 195, 244, 202, 4274, 195, 197, 198, 200, 4274, 202, 196, 200, 198, 204, 196, 202, 199, 208, 204, 198, 208, 198, 4284, 196, 199, 163, 163, 199, 165, 196, 163, 4286, 4284, 4289, 208, 4289, 201, 208, 4325, 201, 4289, 201, 210, 208, 202, 203, 205, 199, 202, 205, 204, 208, 206, 203, 207, 205, 199, 205, 165, 165, 205, 170, 205, 207, 170, 253, 254, 203, 203, 254, 207, 206, 208, 210, 206, 210, 209, 207, 211, 170, 170, 211, 171, 211, 207, 212, 207, 254, 212, 171, 213, 216, 171, 211, 213, 212, 214, 211, 214, 213, 211, 212, 254, 214, 216, 213, 215, 213, 214, 215, 214, 254, 215, 216, 215, 217, 257, 216, 217, 218, 177, 216, 257, 218, 216, 171, 216, 173, 216, 177, 173, 218, 178, 177, 189, 178, 218, 219, 220, 221, 220, 181, 221, 221, 183, 219, 183, 185, 222, 219, 183, 222, 219, 222, 264, 185, 223, 222, 264, 222, 224, 222, 223, 224, 258, 224, 218, 224, 225, 218, 225, 223, 187, 224, 223, 225, 189, 218, 225, 225, 187, 189, 220, 227, 191, 220, 192, 181, 106, 193, 229, 106, 229, 228, 228, 229, 230, 226, 109, 230, 230, 109, 228, 191, 227, 229, 230, 229, 227, 226, 230, 227, 232, 233, 235, 195, 231, 244, 233, 239, 234, 234, 235, 233, 236, 235, 234, 238, 237, 197, 200, 238, 197, 4324, 237, 239, 239, 237, 238, 4324, 239, 233, 239, 238, 240, 240, 238, 241, 239, 240, 234, 240, 241, 242, 234, 242, 236, 240, 242, 234, 238, 200, 241, 294, 4325, 314, 4325, 294, 201, 210, 201, 294, 244, 203, 202, 244, 245, 203, 244, 243, 245, 241, 248, 246, 241, 246, 242, 236, 247, 291, 236, 242, 247, 242, 246, 247, 249, 246, 248, 247, 246, 249, 245, 253, 203, 241, 200, 250, 200, 204, 250, 241, 250, 248, 250, 204, 206, 251, 250, 206, 248, 250, 251, 248, 251, 252, 249, 248, 252, 243, 253, 245, 251, 206, 209, 252, 251, 209, 210, 294, 252, 252, 209, 210, 256, 215, 254, 253, 256, 254, 256, 255, 215, 255, 217, 215, 253, 295, 256, 217, 255, 257, 256, 257, 255, 256, 295, 257, 295, 258, 257, 257, 258, 218, 220, 260, 259, 261, 260, 220, 261, 262, 260, 219, 262, 261, 262, 264, 260, 261, 220, 219, 262, 219, 264, 300, 264, 265, 263, 258, 295, 224, 258, 266, 267, 266, 258, 263, 267, 258, 224, 266, 264, 265, 264, 266, 267, 265, 266, 263, 265, 267, 268, 114, 270, 259, 268, 220, 269, 220, 268, 269, 226, 227, 270, 226, 269, 268, 270, 269, 269, 227, 220, 114, 109, 270, 270, 109, 226, 274, 4344, 271, 231, 4344, 274, 273, 272, 276, 232, 281, 275, 232, 275, 4348, 4348, 275, 276, 276, 275, 277, 276, 277, 273, 231, 274, 244, 278, 275, 281, 277, 275, 280, 280, 275, 278, 273, 277, 279, 277, 280, 279, 278, 283, 280, 279, 280, 286, 281, 232, 235, 281, 235, 282, 281, 282, 278, 282, 235, 289, 289, 235, 236, 278, 282, 283, 282, 289, 283, 284, 271, 120, 120, 315, 284, 244, 274, 243, 271, 284, 274, 284, 243, 274, 285, 280, 283, 290, 285, 283, 286, 280, 285, 285, 290, 287, 286, 285, 287, 288, 286, 287, 291, 289, 236, 283, 289, 290, 290, 289, 291, 290, 291, 287, 287, 291, 292, 287, 292, 288, 243, 293, 253, 284, 293, 243, 284, 325, 293, 291, 247, 249, 291, 249, 294, 314, 329, 294, 249, 252, 294, 294, 292, 291, 329, 292, 294, 293, 295, 253, 328, 295, 293, 260, 296, 259, 296, 110, 259, 297, 110, 296, 298, 110, 297, 260, 264, 296, 264, 300, 296, 297, 296, 299, 296, 300, 299, 298, 297, 331, 297, 299, 331, 299, 300, 301, 335, 302, 295, 300, 265, 303, 300, 303, 301, 302, 263, 295, 303, 265, 263, 302, 303, 263, 301, 303, 302, 111, 259, 110, 259, 111, 268, 111, 127, 268, 268, 127, 114, 304, 306, 307, 305, 304, 307, 115, 271, 4344, 115, 116, 271, 116, 118, 271, 306, 272, 307, 307, 308, 305, 305, 308, 4374, 307, 272, 273, 279, 309, 273, 307, 273, 310, 310, 273, 309, 310, 308, 307, 316, 309, 279, 286, 316, 279, 310, 309, 317, 317, 309, 316, 312, 4374, 308, 322, 312, 313, 312, 308, 313, 4379, 312, 314, 311, 327, 323, 312, 322, 314, 118, 120, 271, 121, 135, 315, 318, 308, 310, 317, 318, 310, 316, 286, 319, 317, 316, 319, 320, 317, 319, 318, 317, 320, 135, 326, 315, 321, 313, 318, 313, 308, 318, 286, 288, 319, 319, 288, 324, 320, 319, 321, 319, 324, 321, 321, 318, 320, 321, 322, 313, 339, 337, 323, 322, 321, 324, 339, 323, 327, 315, 325, 284, 315, 326, 325, 324, 288, 329, 288, 292, 329, 329, 322, 324, 325, 328, 293, 326, 328, 325, 314, 322, 329, 330, 295, 328, 330, 335, 295, 124, 331, 332, 331, 299, 333, 332, 331, 333, 334, 335, 330, 333, 299, 302, 299, 301, 302, 333, 302, 335, 334, 333, 335, 134, 336, 117, 134, 4429, 336, 4429, 337, 336, 311, 323, 337, 117, 336, 135, 336, 338, 135, 326, 135, 338, 337, 339, 336, 336, 339, 338, 327, 340, 339, 340, 338, 339, 340, 326, 338, 327, 348, 340, 326, 340, 328, 328, 340, 341, 340, 348, 341, 341, 330, 328, 345, 330, 341, 140, 124, 342, 342, 124, 332, 342, 332, 343, 342, 343, 344, 344, 347, 345, 350, 344, 345, 332, 333, 346, 347, 343, 346, 346, 343, 332, 344, 343, 347, 345, 347, 330, 347, 334, 330, 346, 333, 334, 347, 346, 334, 4380, 348, 327, 341, 350, 345, 352, 140, 349, 349, 140, 342, 349, 342, 344, 350, 349, 344, 353, 348, 351, 353, 157, 352, 352, 157, 155, 353, 350, 341, 348, 353, 341, 352, 349, 350, 353, 352, 350, 354, 356, 355, 4687, 354, 355, 355, 356, 357, 358, 359, 357, 359, 355, 357, 351, 4695, 360, 358, 361, 359, 360, 362, 351, 362, 379, 351, 364, 363, 365, 363, 470, 365, 364, 366, 363, 366, 367, 363, 366, 368, 367, 363, 369, 371, 363, 367, 369, 363, 370, 470, 363, 371, 370, 371, 369, 489, 371, 489, 486, 489, 369, 490, 374, 373, 372, 372, 508, 374, 508, 507, 374, 375, 376, 372, 376, 509, 372, 375, 372, 158, 372, 373, 158, 157, 376, 375, 376, 157, 377, 378, 379, 377, 378, 377, 157, 368, 380, 367, 380, 368, 381, 380, 381, 384, 381, 382, 384, 382, 381, 394, 383, 369, 367, 367, 380, 383, 385, 384, 382, 387, 380, 384, 385, 399, 514, 384, 385, 514, 384, 514, 387, 383, 386, 369, 383, 380, 387, 387, 386, 383, 369, 386, 490, 386, 387, 388, 388, 387, 389, 514, 389, 387, 490, 386, 516, 516, 386, 388, 388, 389, 516, 390, 373, 374, 392, 390, 391, 373, 390, 158, 390, 392, 158, 391, 159, 392, 394, 1668, 393, 394, 393, 395, 395, 382, 394, 1668, 160, 396, 1668, 396, 393, 395, 393, 397, 385, 382, 395, 397, 385, 395, 393, 396, 398, 397, 393, 398, 399, 385, 397, 399, 397, 398, 399, 398, 401, 398, 400, 401, 396, 160, 402, 398, 396, 400, 396, 402, 400, 400, 402, 534, 402, 403, 534, 403, 152, 404, 404, 406, 540, 404, 152, 405, 404, 405, 406, 406, 405, 407, 405, 161, 407, 406, 407, 391, 540, 406, 408, 406, 391, 408, 391, 407, 159, 409, 4687, 355, 409, 4997, 4687, 409, 355, 359, 4997, 432, 410, 412, 541, 411, 412, 411, 417, 417, 416, 412, 415, 412, 414, 416, 414, 412, 416, 417, 542, 417, 411, 413, 418, 417, 413, 420, 421, 559, 420, 419, 421, 419, 422, 421, 556, 422, 419, 423, 421, 422, 427, 426, 424, 425, 427, 424, 610, 427, 425, 360, 424, 426, 426, 428, 360, 426, 427, 428, 428, 427, 610, 429, 360, 428, 409, 359, 430, 430, 359, 361, 430, 4997, 409, 431, 430, 361, 432, 4997, 430, 456, 432, 430, 432, 433, 410, 411, 434, 413, 611, 434, 411, 434, 435, 413, 436, 435, 434, 611, 436, 434, 437, 436, 611, 418, 413, 438, 438, 413, 435, 438, 439, 418, 436, 438, 435, 439, 441, 440, 436, 437, 439, 436, 439, 438, 437, 442, 439, 442, 441, 439, 444, 440, 441, 443, 440, 444, 442, 445, 441, 445, 444, 441, 444, 446, 443, 445, 446, 444, 418, 439, 447, 439, 440, 448, 439, 448, 447, 448, 451, 447, 448, 443, 449, 440, 443, 448, 450, 451, 449, 449, 451, 448, 449, 443, 446, 446, 450, 449, 446, 453, 450, 453, 452, 450, 450, 452, 454, 362, 429, 683, 428, 683, 429, 429, 362, 360, 431, 455, 430, 1837, 467, 456, 430, 455, 456, 455, 1837, 456, 456, 457, 471, 471, 458, 456, 432, 456, 458, 432, 458, 433, 458, 460, 433, 477, 460, 458, 433, 460, 459, 433, 459, 613, 613, 459, 661, 661, 459, 460, 454, 452, 461, 464, 463, 462, 465, 463, 464, 456, 467, 468, 467, 466, 468, 466, 467, 470, 467, 469, 470, 456, 468, 457, 468, 466, 472, 457, 468, 471, 468, 472, 471, 470, 472, 466, 472, 470, 473, 473, 474, 472, 458, 471, 477, 471, 472, 475, 471, 475, 477, 472, 474, 475, 478, 477, 475, 475, 474, 476, 475, 476, 478, 476, 479, 478, 478, 479, 480, 478, 480, 684, 480, 479, 481, 684, 480, 482, 482, 480, 483, 480, 481, 483, 483, 481, 499, 484, 482, 483, 483, 502, 686, 483, 499, 502, 686, 502, 505, 379, 362, 683, 470, 469, 365, 485, 470, 370, 473, 470, 485, 485, 370, 371, 473, 485, 487, 473, 487, 474, 485, 371, 486, 485, 486, 487, 474, 487, 488, 487, 486, 491, 488, 487, 491, 474, 488, 476, 488, 491, 492, 486, 489, 491, 491, 489, 493, 489, 490, 493, 492, 476, 488, 495, 492, 491, 493, 495, 491, 493, 521, 498, 493, 490, 521, 492, 479, 476, 494, 479, 492, 496, 492, 495, 496, 494, 492, 495, 493, 498, 498, 496, 495, 479, 494, 497, 497, 494, 496, 497, 496, 695, 496, 498, 528, 696, 481, 497, 481, 479, 497, 499, 481, 696, 500, 499, 501, 499, 500, 502, 502, 500, 503, 500, 504, 503, 501, 504, 500, 505, 503, 504, 502, 503, 505, 507, 508, 506, 372, 509, 508, 509, 376, 510, 376, 377, 510, 510, 377, 513, 510, 513, 512, 511, 379, 683, 513, 379, 511, 512, 513, 511, 513, 377, 379, 515, 389, 514, 516, 517, 490, 516, 518, 517, 519, 518, 516, 389, 515, 520, 389, 520, 519, 389, 519, 516, 521, 490, 517, 522, 521, 517, 517, 518, 522, 518, 519, 522, 522, 519, 523, 520, 523, 519, 524, 523, 520, 521, 522, 498, 498, 522, 525, 526, 525, 522, 527, 526, 523, 523, 526, 522, 523, 524, 527, 527, 524, 529, 525, 528, 498, 525, 526, 528, 526, 717, 528, 526, 527, 717, 527, 530, 717, 530, 527, 529, 506, 531, 507, 390, 507, 531, 532, 390, 531, 374, 507, 390, 390, 532, 391, 401, 514, 399, 515, 514, 401, 400, 515, 401, 515, 400, 533, 533, 400, 534, 520, 515, 533, 520, 533, 524, 524, 533, 534, 534, 529, 524, 529, 534, 535, 535, 534, 403, 530, 529, 535, 536, 530, 535, 536, 535, 537, 535, 403, 537, 537, 403, 538, 538, 403, 539, 403, 404, 539, 538, 539, 728, 728, 539, 729, 539, 404, 731, 729, 539, 731, 731, 404, 540, 532, 731, 540, 532, 540, 408, 532, 408, 391, 417, 543, 542, 417, 418, 543, 418, 544, 543, 545, 542, 546, 547, 542, 543, 547, 546, 542, 545, 546, 548, 546, 549, 548, 546, 547, 553, 549, 546, 553, 550, 553, 547, 551, 550, 547, 543, 544, 551, 547, 543, 551, 548, 549, 552, 549, 553, 554, 549, 554, 555, 549, 555, 552, 553, 420, 554, 553, 550, 420, 550, 419, 420, 419, 550, 551, 419, 551, 556, 557, 552, 558, 559, 555, 554, 559, 564, 555, 558, 552, 555, 564, 558, 555, 554, 420, 559, 551, 560, 556, 556, 560, 422, 560, 551, 561, 557, 562, 563, 557, 558, 562, 570, 557, 563, 559, 421, 564, 421, 423, 564, 562, 558, 564, 423, 560, 565, 565, 560, 566, 560, 423, 422, 561, 566, 560, 561, 624, 566, 562, 567, 568, 568, 569, 562, 562, 569, 563, 569, 577, 563, 577, 570, 563, 562, 571, 567, 572, 564, 423, 571, 562, 572, 564, 572, 562, 572, 423, 565, 572, 565, 573, 565, 566, 573, 574, 573, 566, 566, 624, 574, 568, 583, 575, 567, 583, 568, 576, 569, 575, 575, 569, 568, 577, 569, 576, 571, 578, 567, 567, 578, 583, 572, 573, 579, 572, 579, 571, 571, 579, 578, 579, 573, 580, 573, 574, 580, 580, 574, 581, 582, 576, 584, 575, 583, 576, 583, 584, 576, 583, 578, 585, 583, 585, 584, 586, 585, 578, 586, 578, 579, 580, 595, 579, 579, 595, 586, 589, 588, 587, 589, 587, 590, 582, 584, 591, 591, 584, 592, 592, 585, 586, 592, 584, 585, 594, 593, 586, 593, 592, 586, 594, 586, 595, 590, 587, 588, 593, 594, 602, 594, 596, 602, 594, 597, 596, 595, 597, 594, 597, 652, 596, 652, 603, 596, 591, 592, 598, 599, 598, 600, 600, 598, 592, 599, 600, 605, 593, 601, 592, 601, 600, 592, 606, 605, 600, 601, 606, 600, 602, 601, 593, 596, 603, 602, 601, 602, 604, 604, 602, 603, 604, 606, 601, 605, 5328, 599, 606, 604, 607, 425, 608, 610, 609, 610, 608, 609, 739, 610, 433, 611, 410, 410, 611, 541, 433, 613, 612, 411, 541, 611, 437, 611, 612, 612, 611, 433, 437, 612, 442, 612, 613, 445, 442, 612, 445, 445, 613, 614, 445, 614, 446, 447, 544, 418, 615, 544, 447, 451, 615, 447, 446, 614, 453, 544, 615, 616, 451, 616, 615, 616, 451, 450, 551, 544, 616, 450, 617, 616, 454, 617, 450, 620, 561, 551, 620, 551, 618, 618, 551, 616, 618, 617, 619, 616, 617, 618, 561, 620, 621, 622, 621, 620, 618, 622, 620, 622, 618, 623, 618, 619, 623, 623, 619, 625, 621, 624, 561, 622, 624, 621, 623, 627, 622, 625, 627, 623, 624, 622, 626, 622, 627, 626, 628, 629, 630, 630, 627, 625, 629, 627, 630, 574, 624, 581, 581, 624, 626, 633, 632, 642, 642, 634, 633, 626, 627, 631, 631, 627, 635, 633, 636, 632, 633, 628, 636, 633, 629, 628, 633, 634, 637, 633, 637, 629, 629, 637, 627, 637, 635, 627, 638, 588, 589, 590, 639, 589, 639, 638, 589, 581, 626, 640, 641, 638, 642, 638, 639, 642, 643, 639, 590, 643, 644, 639, 644, 642, 639, 640, 643, 645, 640, 631, 643, 640, 626, 631, 631, 644, 643, 642, 632, 641, 632, 655, 641, 634, 642, 646, 646, 642, 644, 631, 635, 646, 644, 631, 646, 655, 636, 647, 655, 632, 636, 648, 655, 634, 648, 634, 646, 655, 647, 634, 637, 634, 649, 647, 649, 634, 648, 646, 635, 648, 635, 650, 637, 649, 635, 649, 650, 635, 588, 638, 590, 652, 651, 603, 597, 645, 652, 638, 641, 590, 641, 653, 590, 590, 653, 643, 653, 654, 643, 652, 643, 651, 654, 651, 643, 645, 643, 652, 641, 655, 653, 648, 654, 653, 648, 653, 655, 648, 650, 656, 648, 656, 660, 651, 657, 603, 657, 604, 603, 658, 604, 657, 657, 654, 659, 651, 654, 657, 658, 657, 659, 654, 648, 659, 660, 659, 648, 604, 658, 607, 683, 428, 610, 614, 613, 663, 613, 661, 664, 664, 661, 662, 663, 452, 453, 663, 453, 614, 613, 664, 663, 664, 662, 685, 663, 461, 452, 665, 461, 663, 663, 664, 665, 665, 664, 666, 664, 685, 666, 666, 685, 687, 665, 462, 667, 617, 454, 461, 665, 667, 617, 461, 665, 617, 665, 668, 462, 668, 665, 666, 668, 666, 671, 666, 687, 671, 667, 462, 669, 462, 463, 669, 619, 617, 667, 619, 667, 669, 668, 670, 462, 668, 671, 670, 619, 669, 625, 462, 670, 464, 670, 671, 694, 669, 463, 672, 463, 465, 672, 625, 669, 673, 669, 672, 673, 464, 675, 465, 670, 674, 464, 464, 674, 675, 670, 694, 678, 675, 672, 465, 630, 676, 628, 630, 673, 676, 675, 676, 672, 673, 672, 676, 625, 673, 630, 675, 677, 676, 675, 674, 677, 670, 678, 674, 674, 678, 677, 628, 676, 679, 628, 679, 636, 679, 678, 680, 680, 678, 682, 676, 677, 679, 678, 679, 677, 636, 679, 647, 680, 647, 679, 649, 647, 680, 680, 682, 681, 680, 681, 650, 680, 650, 649, 682, 678, 745, 678, 694, 745, 650, 681, 656, 610, 748, 683, 477, 661, 460, 661, 477, 684, 684, 477, 478, 661, 684, 662, 662, 684, 685, 482, 685, 684, 685, 482, 484, 484, 483, 686, 688, 685, 484, 688, 687, 685, 484, 686, 689, 686, 505, 689, 688, 484, 689, 688, 689, 690, 689, 505, 706, 690, 689, 691, 691, 689, 706, 687, 688, 692, 688, 690, 692, 693, 692, 690, 690, 691, 693, 671, 687, 694, 692, 694, 687, 694, 692, 693, 694, 693, 750, 748, 714, 683, 528, 695, 496, 497, 695, 696, 696, 695, 697, 697, 695, 698, 696, 697, 501, 499, 696, 501, 501, 697, 699, 699, 697, 698, 701, 699, 698, 504, 501, 699, 700, 504, 699, 699, 701, 700, 702, 701, 698, 504, 703, 505, 504, 700, 703, 700, 704, 703, 701, 704, 700, 704, 701, 702, 705, 704, 702, 505, 703, 706, 708, 691, 706, 706, 703, 707, 708, 706, 707, 703, 704, 707, 705, 707, 704, 705, 708, 707, 691, 708, 693, 750, 693, 749, 710, 711, 749, 749, 709, 710, 508, 509, 710, 509, 711, 710, 709, 506, 710, 710, 506, 508, 711, 509, 712, 712, 509, 713, 509, 510, 713, 712, 713, 716, 712, 716, 756, 714, 716, 715, 714, 756, 716, 715, 713, 512, 512, 713, 510, 716, 713, 715, 714, 715, 512, 683, 714, 511, 714, 512, 511, 717, 695, 528, 718, 717, 530, 695, 717, 698, 717, 718, 719, 698, 717, 702, 702, 717, 720, 717, 719, 720, 702, 720, 705, 719, 727, 720, 720, 727, 721, 705, 720, 708, 721, 708, 720, 693, 708, 722, 722, 723, 693, 708, 721, 722, 723, 722, 724, 693, 723, 749, 749, 723, 709, 709, 723, 725, 723, 724, 725, 709, 725, 531, 725, 726, 531, 506, 709, 531, 726, 532, 531, 718, 530, 536, 719, 718, 536, 537, 719, 536, 719, 537, 727, 727, 537, 538, 727, 538, 721, 721, 538, 728, 722, 721, 728, 729, 722, 728, 729, 731, 730, 722, 729, 730, 730, 724, 722, 725, 724, 730, 730, 731, 726, 725, 730, 726, 726, 731, 532, 732, 595, 580, 581, 732, 580, 595, 732, 597, 733, 5328, 605, 605, 606, 743, 743, 606, 607, 743, 733, 605, 5389, 733, 734, 733, 743, 736, 734, 733, 736, 5390, 734, 735, 735, 734, 738, 734, 736, 738, 737, 735, 738, 737, 738, 5396, 5396, 739, 609, 738, 739, 5396, 645, 732, 581, 640, 645, 581, 597, 732, 645, 741, 658, 659, 741, 659, 742, 742, 659, 660, 740, 660, 656, 740, 742, 660, 741, 607, 658, 741, 743, 607, 743, 741, 742, 743, 742, 736, 742, 746, 736, 738, 736, 744, 739, 748, 610, 738, 744, 739, 739, 744, 748, 681, 682, 745, 656, 681, 745, 656, 745, 740, 742, 740, 746, 746, 740, 745, 744, 736, 747, 744, 747, 748, 745, 694, 746, 694, 750, 746, 752, 736, 746, 746, 750, 752, 736, 752, 747, 747, 752, 755, 747, 755, 748, 749, 711, 750, 750, 711, 751, 711, 712, 751, 751, 712, 754, 750, 751, 753, 753, 751, 754, 752, 750, 753, 752, 753, 755, 754, 712, 756, 753, 754, 755, 754, 756, 755, 748, 757, 714, 748, 755, 757, 757, 756, 714, 757, 755, 756, 378, 353, 351, 351, 379, 378, 378, 157, 353, 807, 5454, 760, 758, 807, 760, 811, 807, 758, 759, 811, 758, 761, 760, 5453, 762, 760, 761, 763, 760, 762, 758, 760, 763, 758, 763, 764, 758, 764, 759, 764, 763, 765, 5453, 766, 761, 761, 767, 762, 767, 765, 762, 764, 765, 769, 768, 761, 766, 767, 761, 768, 765, 767, 769, 768, 769, 767, 770, 771, 766, 771, 772, 766, 772, 773, 768, 766, 772, 768, 768, 773, 769, 763, 762, 776, 765, 763, 776, 775, 777, 779, 777, 778, 779, 787, 779, 780, 779, 778, 780, 776, 762, 765, 777, 782, 781, 777, 781, 778, 781, 783, 778, 778, 783, 780, 784, 781, 785, 785, 781, 782, 783, 781, 784, 784, 785, 786, 784, 786, 783, 786, 856, 783, 785, 774, 786, 842, 855, 774, 774, 855, 786, 786, 855, 856, 788, 787, 780, 787, 788, 858, 780, 783, 788, 783, 790, 788, 790, 783, 856, 858, 788, 791, 788, 789, 791, 788, 790, 789, 790, 794, 789, 796, 860, 791, 792, 789, 793, 791, 792, 796, 789, 792, 791, 792, 793, 796, 789, 794, 793, 794, 795, 793, 794, 862, 795, 862, 861, 795, 793, 797, 796, 793, 795, 798, 797, 793, 798, 795, 799, 798, 798, 799, 797, 795, 861, 799, 802, 801, 800, 803, 802, 800, 802, 803, 805, 805, 803, 804, 805, 804, 844, 806, 5454, 807, 808, 810, 809, 808, 809, 807, 807, 809, 806, 800, 878, 803, 811, 808, 807, 803, 878, 812, 812, 813, 803, 803, 813, 804, 810, 808, 814, 810, 814, 815, 759, 764, 811, 764, 808, 811, 812, 817, 813, 812, 884, 817, 815, 814, 816, 814, 808, 820, 814, 820, 816, 808, 764, 820, 820, 764, 769, 813, 817, 849, 815, 818, 5608, 818, 815, 816, 816, 820, 819, 816, 819, 818, 771, 826, 821, 818, 821, 5608, 826, 5608, 821, 822, 772, 771, 818, 822, 821, 822, 771, 821, 818, 819, 822, 822, 773, 772, 819, 820, 822, 820, 769, 773, 822, 820, 773, 825, 771, 770, 826, 771, 825, 824, 839, 841, 827, 774, 828, 840, 827, 828, 828, 830, 841, 840, 828, 841, 841, 830, 824, 828, 774, 829, 830, 828, 829, 824, 830, 823, 834, 833, 831, 832, 834, 831, 835, 833, 836, 836, 833, 834, 837, 835, 836, 838, 837, 839, 839, 837, 836, 827, 834, 832, 827, 840, 834, 840, 836, 834, 841, 836, 840, 839, 836, 841, 774, 827, 842, 827, 832, 842, 831, 842, 832, 0, 842, 5617, 843, 844, 804, 844, 843, 889, 889, 843, 847, 889, 847, 845, 813, 846, 804, 804, 846, 843, 846, 775, 847, 843, 846, 847, 775, 779, 845, 775, 845, 847, 779, 787, 845, 846, 850, 848, 849, 846, 813, 849, 850, 846, 848, 775, 846, 777, 775, 848, 850, 849, 851, 817, 851, 849, 850, 777, 848, 850, 782, 777, 851, 782, 850, 817, 852, 851, 817, 887, 852, 782, 853, 785, 851, 853, 782, 852, 853, 851, 785, 853, 829, 829, 853, 854, 823, 854, 852, 823, 852, 887, 854, 853, 852, 829, 774, 785, 854, 830, 829, 823, 830, 854, 0, 855, 842, 856, 855, 0, 890, 845, 857, 845, 787, 857, 787, 858, 857, 891, 857, 858, 859, 856, 4, 856, 859, 790, 860, 894, 858, 892, 858, 894, 858, 791, 860, 790, 862, 794, 790, 859, 862, 863, 864, 862, 864, 861, 862, 859, 863, 862, 859, 4, 863, 865, 864, 863, 863, 4, 865, 4, 3, 865, 894, 860, 866, 860, 796, 867, 868, 860, 867, 860, 868, 866, 796, 797, 867, 868, 867, 869, 867, 797, 869, 869, 797, 870, 797, 799, 870, 799, 861, 871, 861, 864, 871, 799, 871, 870, 871, 864, 865, 870, 871, 872, 871, 865, 872, 870, 872, 22, 872, 865, 3, 22, 872, 5, 872, 3, 5, 873, 874, 5719, 875, 5719, 874, 874, 876, 875, 875, 876, 897, 874, 877, 876, 876, 877, 898, 801, 802, 873, 873, 802, 805, 873, 877, 874, 873, 805, 877, 877, 805, 844, 879, 880, 878, 800, 879, 878, 878, 884, 812, 879, 881, 880, 881, 882, 880, 878, 880, 883, 880, 882, 883, 878, 883, 884, 881, 888, 882, 888, 885, 882, 882, 885, 883, 883, 885, 886, 884, 883, 886, 817, 884, 887, 884, 886, 887, 885, 888, 824, 885, 824, 886, 823, 887, 824, 887, 886, 824, 839, 888, 838, 888, 839, 824, 889, 877, 844, 898, 877, 889, 890, 889, 845, 889, 890, 898, 899, 890, 857, 899, 857, 891, 858, 902, 891, 858, 892, 902, 893, 892, 894, 892, 893, 902, 893, 894, 895, 893, 895, 903, 894, 866, 896, 895, 894, 896, 866, 868, 896, 895, 896, 906, 896, 869, 906, 896, 868, 869, 906, 869, 22, 22, 869, 870, 5963, 875, 897, 876, 898, 897, 897, 899, 915, 898, 899, 897, 898, 890, 899, 901, 900, 891, 900, 899, 891, 902, 901, 891, 903, 902, 893, 901, 902, 903, 901, 903, 904, 903, 895, 904, 904, 895, 905, 905, 895, 906, 906, 22, 911, 915, 899, 907, 900, 901, 908, 899, 900, 907, 907, 900, 908, 908, 901, 909, 909, 910, 917, 901, 904, 909, 910, 909, 904, 904, 911, 910, 911, 904, 905, 905, 906, 911, 912, 6003, 5963, 5963, 897, 912, 913, 930, 912, 912, 897, 915, 912, 915, 913, 914, 913, 915, 918, 913, 914, 914, 907, 920, 914, 915, 907, 923, 916, 917, 907, 908, 916, 916, 908, 917, 917, 908, 909, 923, 917, 926, 917, 910, 926, 929, 910, 911, 930, 6003, 912, 918, 931, 930, 930, 913, 918, 918, 919, 936, 914, 919, 918, 921, 941, 920, 939, 919, 920, 921, 920, 916, 922, 921, 923, 919, 914, 920, 920, 907, 916, 924, 922, 923, 921, 916, 923, 924, 923, 925, 923, 926, 925, 925, 926, 927, 926, 910, 928, 926, 928, 929, 927, 926, 929, 910, 929, 928, 929, 911, 946, 946, 911, 967, 933, 947, 931, 948, 947, 933, 931, 918, 932, 932, 918, 936, 931, 932, 933, 936, 934, 932, 933, 932, 934, 935, 933, 934, 948, 933, 935, 934, 939, 937, 934, 936, 939, 934, 938, 935, 937, 938, 934, 919, 939, 936, 938, 940, 935, 942, 940, 941, 922, 942, 941, 920, 941, 939, 939, 941, 937, 940, 938, 941, 938, 937, 941, 941, 921, 922, 944, 942, 922, 943, 942, 944, 952, 942, 943, 924, 945, 944, 943, 944, 945, 922, 924, 944, 955, 943, 945, 952, 943, 955, 924, 925, 945, 945, 925, 955, 955, 925, 927, 927, 929, 946, 930, 931, 1042, 957, 948, 949, 957, 931, 947, 947, 948, 957, 948, 935, 949, 950, 935, 940, 951, 950, 940, 949, 935, 950, 940, 942, 951, 953, 964, 962, 953, 951, 952, 951, 953, 962, 951, 942, 952, 953, 952, 954, 953, 954, 966, 954, 952, 955, 954, 955, 1086, 955, 927, 1086, 927, 946, 1086, 967, 107, 968, 956, 976, 6016, 6016, 6003, 956, 995, 976, 956, 6003, 930, 956, 956, 930, 1019, 1019, 930, 1042, 931, 957, 1042, 949, 958, 957, 958, 959, 1056, 951, 961, 959, 1056, 959, 960, 960, 959, 961, 950, 959, 958, 959, 950, 951, 958, 949, 950, 962, 961, 951, 961, 962, 963, 963, 962, 964, 964, 953, 966, 965, 964, 966, 954, 1086, 966, 970, 969, 971, 969, 970, 972, 972, 973, 969, 969, 973, 971, 972, 974, 973, 973, 974, 975, 977, 972, 970, 974, 972, 977, 6152, 976, 6157, 980, 979, 978, 982, 980, 978, 981, 979, 980, 983, 980, 982, 981, 980, 984, 984, 980, 983, 985, 983, 982, 986, 985, 982, 984, 983, 985, 1004, 985, 986, 984, 985, 1004, 978, 987, 982, 987, 1008, 982, 990, 6158, 988, 989, 6158, 990, 982, 1008, 986, 6158, 991, 987, 987, 991, 1008, 6158, 989, 991, 6157, 976, 993, 991, 1010, 1008, 989, 990, 1010, 991, 989, 1010, 1143, 992, 993, 996, 974, 977, 974, 996, 994, 1019, 995, 956, 1007, 976, 995, 1019, 1007, 995, 999, 998, 997, 999, 1000, 998, 1002, 999, 997, 1001, 1002, 997, 1000, 999, 1003, 1003, 999, 1002, 984, 1004, 1135, 1135, 1004, 1006, 1006, 1004, 1005, 998, 1005, 997, 1006, 1005, 998, 997, 1005, 1001, 986, 1008, 1004, 1004, 1008, 1005, 1008, 1009, 1005, 990, 1029, 1010, 1008, 1010, 1009, 1010, 1011, 1009, 1010, 1029, 1011, 976, 1007, 1012, 1007, 1031, 1012, 993, 976, 1012, 993, 1012, 1143, 1017, 1014, 1016, 1013, 1015, 1014, 1015, 1016, 1014, 1018, 1003, 1002, 1015, 1013, 1016, 1017, 1016, 1013, 1027, 1020, 1019, 1027, 1019, 1042, 1022, 1002, 1001, 1022, 1001, 1021, 1021, 1001, 1005, 1002, 1022, 1018, 1019, 1020, 1007, 1007, 1020, 1031, 1024, 1022, 1021, 1023, 1024, 1021, 1025, 1022, 1024, 1027, 1026, 1020, 1026, 1031, 1020, 1005, 1009, 1021, 1009, 1030, 1021, 1021, 1030, 1023, 1025, 1024, 1023, 1025, 1023, 1030, 1032, 1025, 1030, 1028, 1025, 1032, 1034, 1033, 1035, 1009, 1011, 1030, 1029, 1033, 1011, 1011, 1034, 1030, 1011, 1033, 1034, 1034, 1035, 1032, 1030, 1034, 1032, 1012, 1031, 1036, 1143, 1012, 1036, 1039, 1043, 1037, 1038, 1039, 1037, 1039, 1038, 1040, 1040, 1038, 1050, 1050, 1051, 1041, 1050, 1041, 1040, 1044, 1027, 1042, 1044, 1042, 1045, 1045, 1042, 1048, 1038, 1037, 1044, 1038, 1044, 1045, 1037, 1043, 1044, 1046, 1044, 1047, 1043, 1047, 1044, 1043, 1039, 1047, 1048, 1042, 957, 1045, 1048, 1049, 1038, 1045, 1050, 1050, 1045, 1049, 1046, 1047, 1050, 1046, 1050, 1057, 1039, 1051, 1047, 1047, 1051, 1050, 1048, 957, 958, 1056, 1048, 958, 1049, 1048, 1056, 1057, 1049, 1056, 1049, 1057, 1050, 1044, 1026, 1027, 1044, 1052, 1026, 1054, 1053, 1028, 1052, 1044, 1046, 1046, 1057, 1058, 1055, 1053, 1054, 1059, 1055, 1054, 1026, 1052, 1031, 1033, 1064, 1035, 1028, 1032, 1060, 1028, 1060, 1054, 1046, 1061, 1052, 1061, 1031, 1052, 1060, 1062, 1054, 1058, 1061, 1046, 1063, 1061, 1058, 1054, 1062, 1059, 1062, 1065, 1059, 1065, 1079, 1059, 1060, 1032, 1035, 1060, 1035, 1064, 1060, 1064, 1062, 1061, 1063, 1067, 1062, 1064, 1065, 1061, 1067, 1031, 1067, 1233, 1036, 1233, 1066, 1234, 1031, 1067, 1036, 1233, 1067, 1066, 1069, 1041, 1051, 1068, 1069, 1051, 1071, 1069, 1068, 1070, 1071, 1068, 1070, 1072, 1071, 1071, 1072, 1254, 1072, 1077, 1254, 1051, 1073, 1068, 1068, 1073, 1070, 1074, 960, 1075, 1072, 1080, 1074, 1072, 1074, 1075, 1070, 1076, 1072, 1076, 1080, 1072, 1072, 1075, 1077, 1057, 1056, 1074, 1078, 1058, 1057, 1074, 1078, 1057, 1074, 1056, 960, 1074, 1080, 1078, 1080, 1076, 1258, 960, 961, 1075, 1058, 1081, 1063, 1081, 1058, 1078, 1078, 1082, 1081, 1080, 1082, 1078, 1258, 1082, 1080, 1083, 1063, 1081, 1083, 1067, 1063, 1081, 1082, 1083, 1082, 1084, 1083, 1066, 1067, 1083, 1066, 1083, 1084, 1234, 1066, 1084, 963, 1264, 1263, 1085, 1264, 963, 1077, 1075, 963, 963, 1075, 961, 1077, 963, 1263, 1085, 963, 964, 1085, 964, 965, 1085, 965, 1267, 1086, 965, 966, 965, 1086, 1267, 1086, 946, 1268, 946, 967, 1269, 1269, 967, 968, 1269, 968, 1087, 1270, 1351, 6226, 1088, 1270, 6226, 1089, 1270, 1088, 1119, 1270, 1089, 1093, 1092, 1090, 1091, 1093, 1090, 1095, 1094, 1092, 1093, 1095, 1092, 1096, 1097, 6226, 1098, 1093, 1091, 1108, 1095, 1093, 1098, 1108, 1093, 6226, 1097, 1088, 1097, 1099, 1088, 1100, 1108, 1098, 1100, 1098, 1101, 1101, 1098, 1102, 1101, 1099, 1100, 1101, 1089, 1099, 1089, 1088, 1099, 1103, 1104, 1101, 1103, 1101, 1102, 1104, 1089, 1101, 1104, 1105, 1089, 1106, 1095, 1091, 1090, 1106, 1091, 1106, 1094, 1095, 1096, 1107, 1097, 1107, 1111, 1097, 1098, 1091, 970, 1098, 970, 971, 1091, 1095, 970, 1095, 1108, 970, 1097, 1111, 1099, 1098, 971, 1102, 1102, 971, 973, 1108, 1100, 1110, 1108, 1110, 1109, 1100, 1111, 1110, 1100, 1099, 1111, 1102, 973, 975, 1109, 1103, 977, 1104, 1103, 1110, 1110, 1103, 1109, 1110, 1105, 1104, 1110, 1111, 1105, 1112, 1114, 1113, 1107, 1114, 1111, 1113, 1114, 1107, 1115, 1111, 1114, 1108, 1109, 977, 970, 1108, 977, 1112, 981, 1115, 1115, 1114, 1112, 6236, 1117, 1116, 1115, 981, 984, 1117, 1138, 1116, 1115, 984, 1135, 1111, 1115, 1135, 1118, 1116, 1137, 1138, 1137, 1116, 6237, 1118, 988, 6237, 1116, 1118, 1118, 990, 988, 1278, 6238, 992, 992, 1143, 1278, 1120, 1121, 1102, 1122, 1103, 1102, 1121, 1122, 1102, 1105, 1124, 1089, 1123, 1121, 1120, 1089, 1124, 1119, 1126, 1121, 1123, 1125, 1126, 1123, 1127, 1122, 1121, 1126, 1127, 1121, 1124, 1128, 1119, 1102, 975, 1120, 1120, 975, 974, 977, 1103, 1122, 1105, 1111, 1124, 1122, 1123, 994, 974, 994, 1120, 1123, 1120, 994, 1111, 1130, 1124, 1122, 1129, 1123, 1123, 1129, 1125, 1122, 1127, 1129, 1124, 1130, 1128, 977, 1122, 996, 1122, 994, 996, 1131, 1111, 1135, 1111, 1131, 1130, 998, 1000, 1132, 1131, 1132, 1173, 1131, 1173, 1130, 1132, 1000, 1134, 1134, 1000, 1003, 1173, 1132, 1134, 1118, 1137, 1141, 1136, 1135, 1006, 1131, 1135, 1136, 1141, 1137, 1139, 1132, 1006, 998, 1136, 1006, 1132, 1131, 1136, 1132, 1140, 1139, 1137, 1133, 1140, 1138, 1138, 1140, 1137, 1140, 1179, 1139, 1179, 1180, 1139, 1133, 1175, 1179, 1140, 1133, 1179, 1118, 1141, 990, 1141, 1142, 990, 1142, 1029, 990, 1139, 1180, 1142, 1141, 1139, 1142, 1119, 1144, 1145, 1119, 1145, 1270, 1144, 1146, 1145, 1145, 1146, 1149, 1149, 1146, 1147, 1149, 1147, 1148, 1151, 1126, 1125, 1150, 1151, 1125, 1152, 1127, 1126, 1151, 1152, 1126, 1128, 1144, 1119, 1168, 1144, 1128, 1153, 1151, 1150, 1154, 1152, 1151, 1155, 1169, 1146, 1169, 1154, 1146, 1146, 1156, 1155, 1146, 1144, 1156, 1144, 1168, 1156, 1158, 1151, 1153, 1157, 1158, 1153, 1159, 1160, 1154, 1159, 1154, 1158, 1158, 1154, 1151, 1155, 1161, 1160, 1155, 1160, 1169, 1161, 1147, 1160, 1154, 1160, 1146, 1147, 1146, 1160, 1162, 1155, 1156, 1161, 1155, 1162, 1197, 1158, 1157, 1163, 1197, 1157, 1164, 1159, 1158, 1164, 1158, 1165, 1165, 1158, 1197, 1148, 1147, 1164, 1148, 1164, 1165, 1147, 1161, 1164, 1198, 1164, 1188, 1161, 1188, 1164, 1286, 1161, 1162, 1286, 1166, 1161, 1166, 1188, 1161, 1129, 1167, 1150, 1125, 1129, 1150, 1127, 1152, 1167, 1129, 1127, 1167, 1128, 1130, 1168, 1153, 1150, 1013, 1153, 1013, 1014, 1150, 1167, 1013, 1167, 1170, 1013, 1152, 1154, 1169, 1152, 1169, 1167, 1167, 1169, 1170, 1170, 1017, 1013, 1170, 1157, 1017, 1014, 1017, 1153, 1157, 1153, 1017, 1160, 1159, 1170, 1160, 1170, 1169, 1156, 1168, 1162, 1168, 1172, 1162, 1170, 1171, 1163, 1157, 1170, 1163, 1159, 1164, 1198, 1159, 1198, 1170, 1170, 1198, 1171, 1172, 1286, 1162, 1134, 1003, 1018, 1018, 1173, 1134, 1130, 1174, 1168, 1130, 1173, 1174, 1176, 1173, 1178, 1174, 1173, 1176, 1168, 1174, 1172, 1174, 1176, 1220, 1174, 1220, 1172, 1022, 1178, 1018, 1178, 1173, 1018, 1178, 1022, 1025, 1175, 1288, 1181, 1175, 1181, 1179, 1178, 1025, 1182, 1178, 1182, 1176, 1180, 1183, 1185, 1180, 1179, 1183, 1181, 1183, 1179, 1177, 1182, 1028, 1182, 1025, 1028, 1176, 1182, 1177, 1186, 1185, 1183, 1181, 1222, 1183, 1142, 1184, 1029, 1184, 1033, 1029, 1142, 1180, 1184, 1180, 1185, 1184, 1184, 1185, 1033, 1185, 1186, 1033, 1186, 1228, 1033, 1143, 1036, 1233, 1143, 1233, 1187, 1149, 1148, 1189, 1189, 1148, 1188, 1190, 1191, 1188, 1191, 1199, 1189, 1191, 1189, 1188, 1191, 1190, 1192, 1192, 1193, 1195, 1192, 1195, 1191, 1194, 1191, 1195, 1193, 1196, 1195, 1197, 1198, 1165, 1165, 1198, 1148, 1188, 1148, 1198, 1166, 1199, 1188, 1200, 1197, 1163, 1201, 1197, 1202, 1202, 1197, 1200, 1190, 1188, 1201, 1190, 1201, 1202, 1192, 1201, 1203, 1188, 1203, 1201, 1199, 1191, 1203, 1199, 1203, 1188, 1204, 1205, 1200, 1206, 1202, 1205, 1205, 1202, 1200, 1190, 1202, 1192, 1202, 1206, 1192, 1194, 1207, 1203, 1194, 1203, 1191, 1192, 1203, 1193, 1207, 1193, 1203, 1205, 1204, 1208, 1208, 1209, 1205, 1206, 1205, 1209, 1209, 1196, 1206, 1217, 1206, 1196, 1196, 1193, 1210, 1193, 1207, 1210, 1171, 1211, 1163, 1198, 1197, 1211, 1198, 1211, 1171, 1211, 1212, 1200, 1163, 1211, 1200, 1197, 1201, 1211, 1211, 1201, 1212, 1213, 1295, 1172, 1204, 1200, 1039, 1204, 1039, 1040, 1200, 1212, 1039, 1216, 1039, 1214, 1212, 1214, 1039, 1206, 1214, 1201, 1201, 1214, 1212, 1192, 1206, 1201, 1207, 1295, 1213, 1207, 1213, 1215, 1040, 1041, 1208, 1040, 1208, 1204, 1041, 1216, 1208, 1214, 1208, 1216, 1208, 1214, 1217, 1206, 1217, 1214, 1207, 1215, 1210, 1215, 1251, 1210, 1177, 1053, 1218, 1177, 1218, 1176, 1220, 1176, 1218, 1172, 1220, 1213, 1039, 1216, 1051, 1220, 1218, 1219, 1213, 1220, 1215, 1220, 1219, 1221, 1221, 1219, 1256, 1221, 1215, 1220, 1221, 1251, 1215, 1028, 1053, 1177, 1053, 1223, 1218, 1224, 1186, 1183, 1222, 1224, 1183, 1223, 1053, 1055, 1218, 1223, 1219, 1222, 1225, 1224, 1223, 1055, 1226, 1226, 1055, 1059, 1226, 1219, 1223, 1224, 1256, 1231, 1224, 1227, 1256, 1257, 1227, 1225, 1225, 1227, 1224, 1064, 1033, 1228, 1233, 1305, 1187, 1229, 1064, 1228, 1230, 1064, 1229, 1186, 1231, 1228, 1231, 1229, 1228, 1186, 1224, 1231, 1230, 1079, 1065, 1230, 1229, 1079, 1229, 1232, 1079, 1231, 1256, 1232, 1231, 1232, 1229, 1064, 1230, 1065, 1305, 1233, 1235, 1234, 1235, 1233, 1195, 1196, 1236, 1237, 1238, 1236, 1208, 1239, 1209, 1209, 1239, 1236, 1196, 1209, 1236, 1217, 1196, 1244, 1217, 1244, 1243, 1240, 1196, 1210, 1240, 1241, 1196, 1241, 1244, 1196, 1236, 1239, 1242, 1243, 1236, 1242, 1244, 1237, 1243, 1237, 1236, 1243, 1241, 1245, 1244, 1246, 1237, 1244, 1246, 1253, 1237, 1238, 1237, 1247, 1253, 1247, 1237, 1245, 1248, 1244, 1244, 1248, 1246, 1247, 1323, 1238, 1247, 1253, 1255, 1335, 1247, 1255, 1249, 1216, 1239, 1249, 1239, 1250, 1216, 1041, 1239, 1041, 1069, 1239, 1239, 1208, 1217, 1239, 1217, 1250, 1250, 1217, 1243, 1240, 1210, 1251, 1069, 1071, 1242, 1069, 1242, 1239, 1250, 1242, 1249, 1071, 1249, 1242, 1243, 1242, 1250, 1253, 1071, 1254, 1253, 1246, 1258, 1252, 1258, 1246, 1248, 1252, 1246, 1253, 1254, 1255, 1216, 1249, 1073, 1051, 1216, 1073, 1257, 1221, 1256, 1221, 1257, 1251, 1070, 1073, 1258, 1073, 1249, 1258, 1249, 1071, 1258, 1258, 1252, 1259, 1252, 1260, 1259, 1071, 1253, 1258, 1070, 1258, 1076, 1226, 1059, 1079, 1219, 1226, 1256, 1226, 1079, 1256, 1227, 1257, 1256, 1256, 1079, 1232, 1235, 1262, 1260, 1259, 1261, 1258, 1258, 1261, 1082, 1259, 1262, 1261, 1260, 1262, 1259, 1235, 1234, 1262, 1084, 1261, 1234, 1084, 1082, 1261, 1262, 1234, 1261, 1263, 1335, 1255, 1254, 1077, 1263, 1255, 1254, 1263, 1264, 1085, 1265, 1266, 1264, 1265, 1264, 1266, 1346, 1265, 1085, 1267, 1266, 1265, 1267, 1346, 1266, 1348, 1266, 1267, 1348, 1348, 1267, 1268, 1349, 1268, 946, 1350, 1269, 149, 149, 1269, 1087, 6279, 1273, 1271, 1273, 1272, 1271, 1117, 1274, 1275, 1271, 1272, 1276, 1277, 1117, 1275, 1276, 1277, 1275, 1138, 1117, 1277, 1272, 1284, 1276, 6236, 1274, 1117, 6342, 1278, 1363, 1363, 1278, 1282, 6342, 6238, 1278, 1279, 1284, 1272, 1273, 1279, 1272, 1273, 1280, 1279, 1281, 1284, 1279, 1280, 1281, 1279, 1283, 1138, 1277, 1276, 1284, 1277, 1284, 1283, 1277, 1138, 1283, 1133, 1283, 1175, 1133, 1284, 1175, 1283, 1284, 1287, 1175, 1284, 1281, 1287, 1282, 1278, 1143, 1282, 1143, 1374, 1374, 1143, 1187, 1270, 1145, 1285, 1285, 1145, 1149, 1296, 1289, 1281, 1280, 1296, 1281, 1280, 1297, 1296, 1287, 1288, 1175, 1281, 1289, 1287, 1287, 1289, 1288, 1301, 1181, 1288, 1289, 1301, 1288, 1374, 1187, 1383, 1187, 1304, 1383, 1294, 1291, 1285, 1285, 1291, 1290, 1291, 1292, 1290, 1292, 1291, 1310, 1285, 1149, 1294, 1294, 1149, 1189, 1199, 1293, 1294, 1199, 1294, 1189, 1293, 1194, 1195, 1293, 1195, 1294, 1294, 1195, 1291, 1295, 1166, 1286, 1199, 1166, 1295, 1199, 1295, 1293, 1295, 1194, 1293, 1207, 1194, 1295, 1286, 1172, 1295, 1302, 1296, 1298, 1297, 1299, 1296, 1299, 1298, 1296, 1300, 1336, 1302, 1302, 1298, 1300, 1298, 1328, 1300, 1299, 1328, 1298, 1222, 1181, 1301, 1225, 1222, 1301, 1289, 1302, 1301, 1289, 1296, 1302, 1225, 1251, 1257, 1225, 1301, 1251, 1301, 1303, 1251, 1302, 1336, 1303, 1302, 1303, 1301, 1305, 1304, 1187, 1342, 1306, 1305, 1305, 1306, 1304, 1406, 1304, 1306, 1305, 1235, 1307, 1305, 1307, 1342, 1308, 1422, 1290, 1318, 1310, 1291, 1311, 1312, 1292, 1311, 1292, 1310, 1292, 1308, 1290, 1292, 1433, 1308, 1292, 1312, 1433, 1314, 1310, 1318, 1313, 1314, 1318, 1315, 1311, 1310, 1314, 1315, 1310, 1314, 1313, 1325, 1325, 1316, 1314, 1315, 1314, 1316, 1317, 1316, 1309, 1316, 1425, 1309, 1316, 1343, 1425, 1291, 1195, 1318, 1318, 1195, 1236, 1311, 1319, 1326, 1311, 1326, 1312, 1433, 1312, 1320, 1238, 1321, 1318, 1238, 1318, 1236, 1311, 1315, 1321, 1311, 1321, 1319, 1315, 1313, 1321, 1313, 1318, 1321, 1317, 1322, 1334, 1324, 1323, 1313, 1324, 1313, 1315, 1323, 1325, 1313, 1316, 1324, 1315, 1316, 1334, 1324, 1334, 1316, 1317, 1323, 1335, 1325, 1316, 1325, 1343, 1241, 1326, 1327, 1240, 1326, 1241, 1327, 1326, 1319, 1312, 1326, 1328, 1328, 1299, 1320, 1328, 1320, 1312, 1321, 1238, 1330, 1327, 1329, 1245, 1327, 1245, 1241, 1330, 1332, 1319, 1330, 1319, 1321, 1332, 1331, 1319, 1327, 1319, 1329, 1331, 1329, 1319, 1332, 1334, 1333, 1331, 1332, 1333, 1334, 1322, 1333, 1333, 1322, 1445, 1330, 1238, 1323, 1245, 1329, 1248, 1324, 1330, 1323, 1332, 1330, 1324, 1334, 1332, 1324, 1323, 1247, 1335, 1326, 1240, 1336, 1240, 1251, 1336, 1336, 1300, 1326, 1328, 1326, 1300, 1252, 1329, 1340, 1329, 1331, 1340, 1333, 1337, 1331, 1445, 1338, 1333, 1333, 1338, 1337, 1329, 1252, 1248, 1303, 1336, 1251, 1337, 1338, 1406, 1339, 1337, 1406, 1252, 1340, 1260, 1331, 1341, 1340, 1339, 1341, 1337, 1341, 1331, 1337, 1306, 1342, 1341, 1306, 1341, 1339, 1406, 1306, 1339, 1260, 1340, 1342, 1341, 1342, 1340, 1342, 1307, 1260, 1307, 1235, 1260, 1343, 1325, 1335, 1343, 1335, 1344, 1344, 1335, 1264, 1335, 1263, 1264, 1344, 1264, 1345, 1347, 1344, 1345, 1264, 1346, 1345, 1345, 1346, 1347, 1347, 1346, 1348, 1267, 1086, 1268, 1268, 1349, 1661, 946, 1269, 1349, 1349, 1269, 1350, 1352, 6338, 1351, 1353, 1352, 1351, 1270, 1353, 1351, 6338, 1352, 1354, 1354, 1352, 1356, 1356, 1352, 1366, 6339, 1354, 1355, 1356, 1355, 1354, 1356, 1366, 1368, 1356, 1357, 1355, 1356, 1368, 1357, 1355, 1357, 6340, 1273, 1358, 1371, 6340, 1361, 1358, 1361, 1371, 1358, 6340, 1357, 1361, 6279, 1358, 1273, 1359, 1363, 1360, 1375, 1353, 1270, 1364, 1352, 1353, 1375, 1364, 1353, 1352, 1364, 1366, 1270, 1285, 1375, 1366, 1364, 1365, 1364, 1375, 1365, 1367, 1361, 1357, 1368, 1367, 1357, 1368, 1369, 1367, 1369, 1370, 1367, 1379, 1367, 1370, 1369, 1504, 1370, 1372, 1362, 1373, 1361, 1379, 1371, 1361, 1367, 1379, 1373, 1360, 1363, 1360, 1373, 1362, 1373, 1374, 1372, 1372, 1374, 1380, 1373, 1363, 1282, 1373, 1282, 1374, 1377, 1375, 1285, 1378, 1376, 1375, 1377, 1378, 1375, 1375, 1376, 1365, 1376, 1378, 1365, 1365, 1378, 1537, 1378, 1397, 1537, 1504, 1537, 1370, 1398, 1379, 1370, 1537, 1397, 1370, 1397, 1398, 1370, 1381, 1273, 1371, 1379, 1381, 1371, 1379, 1401, 1381, 1382, 1380, 1383, 1273, 1381, 1280, 1380, 1374, 1383, 1280, 1381, 1297, 1384, 1386, 1385, 1387, 1386, 1384, 1388, 1387, 1389, 1385, 1386, 1390, 1390, 1386, 1577, 1386, 1387, 1577, 1387, 1391, 1577, 1387, 1388, 1391, 1393, 1378, 1377, 1392, 1393, 1377, 1394, 1393, 1392, 1418, 1394, 1392, 1377, 1290, 1392, 1285, 1290, 1377, 1378, 1393, 1397, 1395, 1290, 1422, 1392, 1395, 1418, 1290, 1395, 1392, 1397, 1393, 1396, 1402, 1398, 1399, 1398, 1400, 1399, 1397, 1396, 1398, 1396, 1400, 1398, 1401, 1402, 1297, 1401, 1379, 1402, 1379, 1398, 1402, 1402, 1399, 1404, 1381, 1401, 1297, 1383, 1403, 1382, 1297, 1402, 1404, 1297, 1404, 1299, 1405, 1403, 1407, 1304, 1407, 1383, 1383, 1407, 1403, 1406, 1407, 1304, 1409, 1408, 1411, 1408, 1409, 1413, 1409, 1602, 1413, 1602, 1609, 1413, 1411, 1410, 1389, 1411, 1408, 1410, 1412, 1410, 1408, 1412, 1408, 1414, 1413, 1414, 1408, 1413, 1609, 1414, 1389, 1415, 1388, 1389, 1410, 1415, 1415, 1410, 1416, 1412, 1414, 1417, 1412, 1417, 1416, 1412, 1416, 1410, 1622, 1391, 1388, 1622, 1388, 1415, 1416, 1622, 1415, 1421, 1394, 1418, 1419, 1393, 1394, 1421, 1419, 1394, 1420, 1421, 1418, 1434, 1423, 1395, 1434, 1395, 1422, 1395, 1424, 1418, 1395, 1423, 1424, 1419, 1418, 1424, 1419, 1424, 1396, 1419, 1396, 1393, 1422, 1308, 1425, 1425, 1308, 1309, 1426, 1434, 1422, 1425, 1426, 1422, 1418, 1419, 1420, 1427, 1426, 1428, 1426, 1425, 1428, 1429, 1427, 1420, 1429, 1420, 1419, 1427, 1428, 1420, 1421, 1420, 1430, 1428, 1430, 1420, 1429, 1419, 1421, 1429, 1421, 1431, 1430, 1431, 1421, 1432, 1428, 1425, 1440, 1430, 1432, 1432, 1430, 1428, 1431, 1430, 1440, 1308, 1433, 1434, 1434, 1433, 1423, 1424, 1423, 1435, 1423, 1433, 1435, 1396, 1424, 1435, 1436, 1309, 1434, 1309, 1308, 1434, 1434, 1426, 1437, 1434, 1437, 1436, 1438, 1437, 1426, 1437, 1438, 1439, 1317, 1309, 1436, 1427, 1438, 1426, 1429, 1438, 1427, 1439, 1438, 1429, 1439, 1429, 1431, 1439, 1431, 1637, 1637, 1431, 1441, 1425, 1343, 1432, 1440, 1441, 1431, 1433, 1320, 1442, 1399, 1433, 1442, 1435, 1433, 1399, 1400, 1435, 1399, 1396, 1435, 1400, 1317, 1436, 1322, 1322, 1436, 1437, 1439, 1443, 1437, 1439, 1637, 1443, 1442, 1320, 1299, 1404, 1442, 1299, 1399, 1442, 1404, 1444, 1638, 1405, 1437, 1445, 1322, 1437, 1446, 1445, 1444, 1446, 1443, 1446, 1437, 1443, 1637, 1444, 1443, 1638, 1444, 1637, 1405, 1407, 1446, 1405, 1446, 1444, 1407, 1338, 1445, 1446, 1407, 1445, 1407, 1406, 1338, 1440, 1432, 1448, 1452, 1449, 1448, 1450, 1449, 1452, 1440, 1448, 1441, 1441, 1448, 1451, 1432, 1343, 1452, 1432, 1452, 1448, 1448, 1449, 1451, 1452, 1343, 1344, 1451, 1449, 1454, 1453, 1452, 1455, 1452, 1453, 1450, 1454, 1449, 1450, 1451, 1454, 1653, 1452, 1344, 1455, 1456, 1453, 1455, 1450, 1453, 1456, 1450, 1456, 1457, 1454, 1450, 1457, 1455, 1344, 1347, 1456, 1455, 1458, 1455, 1347, 1458, 1456, 1458, 1457, 1457, 1458, 1459, 1454, 1457, 1459, 1347, 1348, 1458, 1459, 1458, 1460, 1458, 1348, 1460, 1348, 1268, 1460, 1460, 1268, 1661, 1661, 1349, 1665, 1665, 1349, 1666, 1349, 1350, 1666, 1666, 1350, 1461, 1350, 149, 1461, 1461, 149, 160, 1462, 6470, 6341, 1359, 1462, 6341, 1360, 1462, 1359, 1463, 1462, 1360, 1362, 1463, 1360, 6470, 1462, 1464, 1464, 1462, 1465, 1465, 1463, 1506, 1462, 1463, 1465, 1506, 1737, 1465, 1464, 1465, 1669, 1465, 1737, 1669, 1672, 1468, 1466, 1475, 1468, 1672, 1468, 1467, 1469, 1468, 1471, 1472, 1468, 1472, 1467, 1468, 1473, 1471, 1473, 1468, 1474, 1475, 1474, 1468, 1471, 1476, 1472, 1694, 1485, 1478, 1471, 1477, 1476, 1473, 1477, 1471, 1474, 1479, 1478, 1474, 1478, 1473, 1473, 1478, 1477, 1478, 1479, 1694, 1694, 1479, 1696, 1479, 1480, 1481, 1479, 1474, 1480, 1474, 1475, 1480, 1696, 1479, 1481, 1470, 1482, 1475, 1482, 1480, 1475, 1483, 1476, 1484, 1483, 1472, 1476, 1476, 1477, 1484, 1478, 1484, 1477, 1480, 1486, 1481, 1510, 1486, 1482, 1486, 1480, 1482, 1483, 1484, 1487, 1478, 1492, 1484, 1492, 1478, 1485, 1486, 1489, 1481, 1481, 1489, 1490, 1486, 1491, 1489, 1510, 1491, 1486, 1484, 1492, 1487, 1485, 1488, 1492, 1489, 1493, 1490, 1490, 1493, 1495, 1494, 1489, 1491, 1494, 1493, 1489, 1493, 1500, 1495, 1498, 1496, 1497, 1502, 1496, 1498, 1499, 1502, 1498, 1499, 1495, 1500, 1493, 1501, 1500, 1494, 1501, 1493, 1502, 354, 4686, 356, 354, 1502, 4686, 1496, 1502, 1503, 356, 1502, 1502, 1499, 1503, 1500, 356, 1503, 1500, 357, 356, 1499, 1500, 1503, 1500, 358, 357, 1501, 358, 1500, 1369, 1368, 1366, 1365, 1369, 1366, 1504, 1369, 1365, 1505, 1463, 1362, 1372, 1505, 1362, 1463, 1505, 1506, 1745, 1748, 1470, 1748, 1507, 1482, 1748, 1482, 1470, 1509, 1510, 1507, 1482, 1507, 1510, 1507, 1511, 1509, 1511, 1751, 1512, 1511, 1750, 1751, 1513, 1517, 1512, 1751, 1508, 1513, 1512, 1751, 1513, 1510, 1509, 1514, 1510, 1514, 1491, 1511, 1520, 1514, 1511, 1514, 1509, 1517, 1515, 1512, 1520, 1511, 1512, 1520, 1512, 1515, 1517, 1523, 1516, 1517, 1516, 1515, 1491, 1514, 1518, 1520, 1519, 1514, 1519, 1518, 1514, 1520, 1521, 1519, 1520, 1515, 1521, 1523, 1385, 1522, 1523, 1522, 1516, 1521, 1515, 1516, 1521, 1516, 1524, 1491, 1518, 1494, 1518, 1519, 1525, 1494, 1518, 1525, 1526, 1525, 1521, 1525, 1519, 1521, 1522, 1527, 1531, 1385, 1527, 1522, 1516, 1522, 1528, 1522, 1531, 1528, 1526, 1521, 1524, 1526, 1524, 1528, 1528, 1524, 1516, 1494, 1525, 1529, 1530, 1529, 1526, 1529, 1525, 1526, 1532, 1528, 1531, 1530, 1526, 1528, 1530, 1528, 1532, 1494, 1529, 1533, 1494, 1533, 1501, 1529, 1530, 1533, 1532, 1531, 1587, 1532, 1534, 1530, 1587, 1534, 1532, 1535, 1530, 1534, 1535, 1534, 1536, 1501, 1533, 358, 358, 1533, 361, 361, 1530, 1535, 361, 1533, 1530, 1537, 1504, 1365, 1538, 1505, 1372, 1380, 1538, 1372, 1539, 1538, 1380, 1382, 1539, 1380, 1505, 1538, 1540, 1540, 1506, 1505, 1540, 1538, 1539, 1540, 1539, 1593, 1540, 1541, 1506, 1541, 1737, 1506, 1737, 1541, 1783, 1542, 1545, 1544, 1544, 1545, 1543, 1546, 1551, 1549, 1547, 1548, 1543, 1544, 1543, 1551, 1543, 1548, 1551, 1543, 1545, 1547, 1508, 1549, 1513, 1508, 1546, 1549, 1513, 1549, 1550, 1549, 1551, 1550, 1547, 1557, 1550, 1548, 1547, 1550, 1551, 1548, 1550, 1547, 1552, 1557, 1559, 1553, 1561, 1513, 1554, 1517, 1550, 1555, 1554, 1513, 1550, 1554, 1557, 1555, 1550, 1559, 1562, 1558, 1562, 1559, 1556, 1556, 1559, 1561, 1552, 1560, 1557, 1559, 1558, 1553, 1553, 1558, 1600, 1523, 1554, 1385, 1517, 1554, 1523, 1554, 1384, 1385, 1554, 1555, 1384, 1384, 1555, 1563, 1563, 1555, 1557, 1557, 1560, 1563, 1563, 1560, 1565, 1566, 1567, 1564, 1558, 1567, 1600, 1564, 1567, 1558, 1568, 1562, 1572, 1572, 1562, 1573, 1564, 1568, 1569, 1568, 1564, 1558, 1562, 1568, 1558, 1384, 1563, 1387, 1564, 1569, 1570, 1563, 1565, 1389, 1387, 1563, 1389, 1564, 1570, 1566, 1570, 1571, 1566, 1385, 1390, 1527, 1574, 1527, 1390, 1577, 1574, 1390, 1568, 1575, 1569, 1568, 1576, 1575, 1568, 1572, 1576, 1575, 1578, 1570, 1569, 1575, 1570, 1579, 1575, 1576, 1579, 1580, 1575, 1580, 1578, 1575, 1578, 1623, 1571, 1570, 1578, 1571, 1580, 1624, 1578, 1624, 1623, 1578, 1581, 1624, 1580, 1587, 1531, 1527, 1588, 1587, 1527, 1572, 1573, 1582, 1527, 1574, 1588, 1572, 1583, 1576, 1579, 1583, 1582, 1582, 1583, 1572, 1588, 1574, 1584, 1574, 1577, 1584, 1576, 1583, 1579, 1584, 1577, 1391, 1584, 1391, 1627, 1584, 1627, 1628, 1536, 1534, 1835, 1534, 1587, 1589, 1589, 1587, 1588, 1835, 1534, 1590, 1590, 1534, 1589, 1584, 1591, 1588, 1589, 1588, 1591, 1590, 1589, 1592, 1592, 1589, 1591, 1628, 1591, 1584, 1585, 1591, 1628, 1586, 1591, 1585, 1592, 1591, 1586, 1403, 1539, 1382, 1539, 1403, 1593, 1593, 1403, 1405, 1540, 1593, 1541, 1643, 1594, 1595, 1597, 1545, 1594, 1594, 1643, 1596, 1597, 1594, 1596, 1598, 1447, 1602, 1602, 1597, 1598, 1597, 1596, 1598, 1547, 1545, 1552, 1597, 1599, 1552, 1409, 1599, 1597, 1552, 1545, 1597, 1601, 1553, 1600, 1607, 1601, 1600, 1601, 1561, 1553, 1602, 1409, 1597, 1601, 1607, 1603, 1603, 1605, 1604, 1603, 1604, 1601, 1552, 1599, 1560, 1560, 1599, 1411, 1409, 1411, 1599, 1606, 1607, 1600, 1561, 1601, 1608, 1603, 1607, 1613, 1608, 1601, 1604, 1614, 1869, 1603, 1614, 1603, 1613, 1603, 1869, 1605, 1447, 1610, 1602, 1610, 1609, 1602, 1609, 1610, 1611, 1411, 1389, 1565, 1411, 1565, 1560, 1606, 1567, 1566, 1606, 1566, 1612, 1600, 1567, 1606, 1606, 1612, 1607, 1612, 1613, 1607, 1611, 1648, 1615, 1566, 1571, 1612, 1571, 1616, 1612, 1616, 1613, 1612, 1616, 1617, 1613, 1417, 1414, 1619, 1614, 1613, 1618, 1613, 1617, 1618, 1414, 1609, 1619, 1611, 1620, 1609, 1609, 1620, 1619, 1615, 1621, 1611, 1611, 1621, 1620, 1571, 1623, 1616, 1624, 1617, 1623, 1416, 1631, 1622, 1617, 1616, 1623, 1417, 1619, 1631, 1417, 1631, 1416, 1631, 1619, 1625, 1624, 1618, 1617, 1619, 1620, 1625, 1621, 1626, 1620, 1620, 1626, 1625, 1622, 1627, 1391, 1585, 1628, 1629, 1586, 1585, 1630, 1630, 1585, 1629, 1622, 1631, 1627, 1628, 1627, 1632, 1632, 1627, 1631, 1629, 1628, 1633, 1633, 1628, 1632, 1630, 1629, 1904, 1904, 1629, 1633, 1634, 1632, 1631, 1634, 1631, 1625, 1633, 1632, 1634, 1636, 1904, 1633, 1626, 1634, 1625, 1626, 1635, 1636, 1626, 1636, 1634, 1634, 1636, 1633, 1638, 1637, 1640, 1637, 1441, 1640, 1638, 1639, 1405, 1639, 1593, 1405, 1640, 1653, 1641, 1641, 1638, 1640, 1639, 1638, 1641, 1593, 1639, 1541, 1639, 1642, 1541, 1639, 1641, 1642, 1642, 1656, 1944, 1642, 1944, 1912, 1643, 1644, 1596, 1645, 1598, 1596, 1596, 1644, 1645, 1644, 1646, 1645, 1643, 1646, 1644, 1645, 1647, 1598, 1598, 1647, 1447, 1645, 1646, 1647, 1647, 1610, 1447, 1610, 1648, 1611, 1610, 1647, 1648, 1647, 1646, 1648, 1646, 1649, 1648, 1648, 1649, 1615, 1615, 1650, 1621, 1649, 1650, 1615, 1621, 1651, 1626, 1650, 1651, 1621, 1626, 1651, 1635, 1652, 364, 365, 364, 1652, 366, 1652, 1941, 366, 1658, 368, 366, 1941, 1658, 366, 1653, 1640, 1441, 1451, 1653, 1441, 1454, 1660, 1654, 1653, 1454, 1654, 1656, 1641, 1653, 1653, 1654, 1656, 1656, 1654, 1655, 1642, 1641, 1656, 1656, 1655, 1657, 1944, 1656, 1657, 381, 368, 1658, 1658, 1659, 381, 381, 1659, 394, 1660, 1454, 1459, 1459, 1460, 1660, 1660, 1460, 1661, 1654, 1660, 1655, 1655, 1660, 1661, 1655, 1661, 1662, 1657, 1655, 1662, 1657, 1662, 1663, 1663, 1662, 1664, 1662, 1661, 1665, 1664, 1662, 1665, 1666, 1461, 1668, 1666, 1668, 1667, 1668, 1963, 1667, 1461, 160, 1668, 1963, 1668, 394, 394, 1659, 1963, 6541, 1669, 6542, 6542, 1669, 1670, 1670, 1669, 1740, 1672, 1671, 6542, 6542, 1670, 1672, 1672, 1670, 1673, 1670, 1740, 1673, 1671, 1672, 1466, 1676, 1675, 1674, 1672, 1673, 1475, 1677, 1679, 1676, 1678, 1675, 1676, 1679, 1678, 1676, 1680, 1674, 1681, 1681, 1674, 1682, 1682, 1674, 1675, 1683, 1682, 1675, 1674, 1684, 1676, 1680, 1684, 1674, 1683, 1675, 1685, 1684, 1686, 1676, 1686, 1677, 1676, 1675, 1678, 1685, 1475, 1673, 1470, 1746, 1679, 1677, 1746, 1677, 1687, 1687, 1677, 1686, 1679, 1746, 1688, 1679, 1688, 1678, 1681, 1689, 1680, 1681, 1682, 1689, 1682, 1683, 1689, 1689, 1690, 1684, 1680, 1689, 1684, 1683, 1685, 1690, 1689, 1683, 1690, 1692, 1686, 1690, 1692, 1691, 1686, 1684, 1690, 1686, 1692, 1690, 1685, 1692, 1685, 1698, 1698, 1685, 1678, 1686, 1691, 1687, 1691, 1693, 1687, 1678, 1688, 1698, 1702, 1694, 1695, 1696, 1704, 1694, 1695, 1694, 1704, 1692, 1700, 1691, 1700, 1692, 1697, 1698, 1697, 1692, 1697, 1698, 1706, 1700, 1699, 1691, 1691, 1699, 1693, 1698, 1688, 1701, 1701, 1706, 1698, 1694, 1702, 1485, 1695, 1703, 1702, 1703, 1695, 1704, 1703, 1704, 1705, 1699, 1700, 1707, 1702, 1703, 1708, 1709, 1708, 1703, 1704, 1696, 1715, 1705, 1704, 1710, 1704, 1715, 1710, 1709, 1703, 1705, 1710, 1709, 1705, 1696, 1481, 1717, 1481, 1490, 1717, 1707, 1720, 1711, 1700, 1720, 1707, 1697, 1712, 1700, 1712, 1720, 1700, 1712, 1697, 1706, 1712, 1706, 1713, 1714, 1708, 1709, 1717, 1716, 1715, 1716, 1710, 1715, 1714, 1709, 1710, 1716, 1714, 1710, 1715, 1696, 1717, 1711, 1720, 1719, 1712, 1721, 1720, 1713, 1718, 1721, 1713, 1721, 1712, 1721, 1718, 1727, 1722, 1708, 1714, 1716, 1723, 1730, 1717, 1723, 1716, 1722, 1714, 1725, 1722, 1725, 1731, 1714, 1716, 1725, 1724, 1725, 1730, 1716, 1730, 1725, 1717, 1490, 1726, 1490, 1495, 1726, 1726, 1723, 1717, 1718, 1725, 1724, 1718, 1724, 1727, 1720, 1728, 1719, 1721, 1728, 1720, 1721, 1735, 1728, 1735, 1721, 1727, 1722, 1730, 1729, 1730, 1723, 1729, 1724, 1730, 1731, 1722, 1731, 1730, 1726, 1495, 1732, 1723, 1726, 1732, 1731, 1733, 1727, 1724, 1731, 1727, 1719, 1728, 1736, 1734, 1728, 1735, 1735, 1727, 1733, 1735, 1733, 1778, 1497, 1729, 1498, 1732, 1499, 1498, 1729, 1723, 1732, 1498, 1729, 1732, 1732, 1495, 1499, 1728, 1734, 1736, 1669, 1737, 1738, 1737, 1739, 1738, 1669, 1738, 1740, 1740, 1738, 1741, 1738, 1739, 1741, 1741, 1739, 1742, 1743, 1745, 1673, 1673, 1740, 1743, 1743, 1740, 1741, 1743, 1741, 1742, 1745, 1470, 1673, 1743, 1742, 1747, 1742, 1744, 1747, 1687, 1693, 1749, 1746, 1687, 1749, 1749, 1688, 1746, 1750, 1748, 1745, 1745, 1743, 1750, 1751, 1750, 1747, 1750, 1743, 1747, 1752, 1751, 1747, 1747, 1744, 1752, 1753, 1749, 1693, 1753, 1693, 1699, 1749, 1701, 1688, 1749, 1753, 1701, 1507, 1748, 1750, 1755, 1754, 1753, 1701, 1753, 1754, 1757, 1754, 1755, 1701, 1754, 1756, 1756, 1754, 1757, 1968, 1706, 1701, 1756, 1968, 1701, 1751, 1752, 1508, 1756, 1757, 1806, 1807, 1968, 1756, 1806, 1807, 1756, 1699, 1707, 1758, 1758, 1753, 1699, 1507, 1750, 1511, 1753, 1758, 1755, 1758, 1763, 1755, 1763, 1759, 1755, 1761, 1759, 1760, 1761, 1757, 1759, 1757, 1755, 1759, 1758, 1707, 1711, 1762, 1758, 1711, 1713, 1706, 1968, 1758, 1762, 1763, 1763, 1764, 1759, 1760, 1764, 1765, 1761, 1760, 1765, 1764, 1760, 1759, 1711, 1719, 1766, 1766, 1762, 1711, 1767, 1762, 1766, 1762, 1767, 1763, 1763, 1767, 1764, 1767, 1768, 1764, 1765, 1764, 1769, 1769, 1764, 1768, 1770, 1766, 1719, 1766, 1770, 1767, 1771, 1767, 1770, 1767, 1771, 1768, 1779, 1772, 1769, 1779, 1769, 1771, 1772, 1773, 1775, 1772, 1779, 1773, 1771, 1769, 1768, 1773, 1774, 1775, 1774, 1967, 1775, 1719, 1736, 1770, 1736, 1781, 1770, 1735, 1777, 1734, 1778, 1777, 1735, 1781, 1776, 1770, 1778, 1780, 1777, 1776, 1779, 1771, 1776, 1771, 1770, 1777, 1779, 1776, 1777, 1773, 1779, 1780, 1773, 1777, 1780, 1774, 1773, 1967, 1774, 1780, 1967, 1780, 1778, 1734, 1777, 1781, 1736, 1734, 1781, 1781, 1777, 1776, 1535, 1536, 1782, 361, 1535, 431, 1535, 1782, 431, 1783, 1739, 1737, 1739, 1783, 1791, 1791, 1783, 1784, 1784, 1783, 1785, 1742, 1739, 1786, 1739, 1791, 1786, 1787, 1791, 1784, 1788, 1787, 1789, 1784, 1785, 1789, 1787, 1784, 1789, 1742, 1786, 1744, 1744, 1786, 1790, 1790, 1786, 1791, 1790, 1791, 1792, 1787, 1793, 1791, 1793, 1795, 1791, 1795, 1792, 1791, 1793, 1542, 1794, 1793, 1788, 1542, 1793, 1787, 1788, 1788, 1545, 1542, 1794, 1795, 1793, 1796, 1792, 1795, 1744, 1790, 1800, 1792, 1797, 1790, 1797, 1800, 1790, 1792, 1798, 1797, 1544, 1794, 1542, 1544, 1799, 1794, 1544, 1802, 1799, 1792, 1796, 1798, 1798, 1796, 1805, 1752, 1800, 1546, 1752, 1744, 1800, 1800, 1551, 1546, 1801, 1551, 1800, 1801, 1800, 1797, 1551, 1802, 1544, 1797, 1803, 1801, 1798, 1803, 1797, 1802, 1804, 1799, 1798, 1805, 1803, 1752, 1546, 1508, 1757, 1808, 1806, 1807, 1806, 1808, 1802, 1551, 1809, 1801, 1809, 1551, 1801, 1810, 1809, 1801, 1803, 1810, 1802, 1809, 1804, 1811, 1809, 1810, 1804, 1809, 1811, 1810, 1805, 1812, 1810, 1803, 1805, 1757, 1761, 1814, 1761, 1813, 1814, 1757, 1814, 1808, 1556, 1818, 1813, 1814, 1813, 1818, 1819, 1814, 1818, 1808, 1819, 1820, 1808, 1814, 1819, 1808, 1820, 1807, 1810, 1815, 1811, 1810, 1816, 1815, 1816, 1810, 1812, 1817, 1816, 1812, 1562, 1813, 1765, 1813, 1761, 1765, 1813, 1562, 1556, 1968, 1807, 1969, 1556, 1561, 1818, 1820, 1821, 1807, 1807, 1821, 1969, 1819, 1818, 1822, 1816, 1822, 1815, 1816, 1820, 1822, 1820, 1819, 1822, 1820, 1816, 1817, 1820, 1817, 1821, 1821, 1817, 1823, 1769, 1562, 1765, 1573, 1562, 1769, 1821, 1825, 1832, 1821, 1823, 1825, 1769, 1826, 1573, 1769, 1772, 1826, 1582, 1573, 1826, 1775, 1827, 1772, 1827, 1826, 1772, 1967, 1968, 1775, 1968, 1827, 1775, 1582, 1580, 1579, 1582, 1826, 1580, 1826, 1828, 1580, 1827, 1828, 1826, 1829, 1828, 1827, 1968, 1829, 1827, 1828, 1830, 1580, 1831, 1830, 1828, 1829, 1831, 1828, 1832, 1831, 1829, 1830, 1824, 1581, 1830, 1581, 1580, 1831, 1824, 1830, 1825, 1824, 1831, 1832, 1825, 1831, 1833, 1586, 1834, 1835, 1782, 1536, 1590, 1836, 1835, 1837, 1836, 1833, 1836, 1590, 1833, 1586, 1833, 1592, 1590, 1592, 1833, 1833, 1834, 1837, 1837, 1834, 1838, 1782, 1835, 455, 455, 431, 1782, 1835, 1836, 455, 1836, 1837, 455, 1912, 1783, 1541, 1642, 1912, 1541, 1912, 1839, 1783, 1783, 1839, 1785, 1785, 1839, 1840, 1841, 1788, 1789, 1789, 1785, 1841, 1595, 1841, 1840, 1841, 1785, 1840, 1840, 1846, 1595, 1788, 1841, 1594, 1545, 1788, 1594, 1842, 1795, 1794, 1848, 1842, 1794, 1842, 1796, 1795, 1594, 1841, 1595, 1843, 1842, 1848, 1844, 1796, 1842, 1843, 1844, 1842, 1845, 1844, 1843, 1595, 1846, 1643, 1848, 1794, 1799, 1847, 1848, 1799, 1850, 1848, 1847, 1849, 1850, 1847, 1848, 1850, 1843, 1796, 1844, 1851, 1850, 1849, 1852, 1850, 1845, 1843, 1850, 1852, 1845, 1845, 1852, 1851, 1845, 1851, 1844, 1799, 1804, 1847, 1804, 1854, 1847, 1805, 1796, 1853, 1854, 1855, 1849, 1854, 1849, 1847, 1855, 1856, 1851, 1854, 1856, 1855, 1851, 1856, 1853, 1851, 1853, 1796, 1849, 1855, 1852, 1851, 1852, 1855, 1811, 1854, 1804, 1853, 1812, 1805, 1853, 1857, 1812, 1811, 1856, 1854, 1811, 1857, 1856, 1853, 1856, 1857, 1858, 1604, 1605, 1861, 1858, 1605, 1861, 1862, 1863, 1861, 1863, 1858, 1858, 1863, 1859, 1864, 1860, 1859, 1863, 1864, 1859, 1874, 1818, 1561, 1608, 1874, 1561, 1815, 1857, 1811, 1815, 1867, 1857, 1812, 1857, 1817, 1817, 1857, 1865, 1866, 1874, 1608, 1857, 1867, 1868, 1857, 1868, 1865, 1608, 1858, 1866, 1608, 1604, 1858, 1866, 1858, 1870, 1867, 1870, 1859, 1870, 1858, 1859, 1867, 1859, 1860, 1867, 1860, 1868, 1868, 1860, 1873, 1869, 1871, 1605, 1605, 1871, 1861, 1872, 1861, 1871, 1861, 1872, 1862, 1860, 1864, 1873, 1822, 1818, 1874, 1815, 1822, 1867, 1817, 1865, 1823, 1823, 1865, 1876, 1866, 1875, 1874, 1875, 1822, 1874, 1822, 1875, 1867, 1865, 1868, 1876, 1877, 1869, 1614, 1866, 1870, 1875, 1867, 1875, 1870, 1868, 1873, 1876, 1876, 1873, 1878, 1869, 1877, 1879, 1871, 1869, 1879, 1872, 1871, 1884, 1871, 1879, 1884, 1884, 1880, 1872, 1825, 1881, 1824, 1823, 1876, 1881, 1825, 1823, 1881, 1881, 1884, 1824, 1884, 1882, 1824, 1877, 1882, 1883, 1877, 1618, 1882, 1877, 1614, 1618, 1884, 1883, 1882, 1881, 1885, 1884, 1876, 1878, 1885, 1881, 1876, 1885, 1877, 1883, 1879, 1884, 1879, 1883, 1880, 1884, 1886, 1886, 1884, 1885, 1886, 1885, 1887, 1886, 1887, 1936, 1885, 1878, 1887, 1581, 1824, 1624, 1891, 1889, 1888, 1889, 1891, 1890, 1890, 1891, 1893, 1824, 1882, 1624, 1892, 1891, 1888, 1895, 1892, 1888, 1893, 1891, 1894, 1894, 1891, 1892, 1882, 1618, 1624, 1895, 1896, 1892, 1894, 1892, 1897, 1897, 1892, 1896, 1898, 1899, 1896, 1887, 1899, 1936, 1887, 1897, 1899, 1897, 1896, 1899, 1834, 1586, 1900, 1900, 1586, 1630, 1901, 1834, 1889, 1888, 1889, 1900, 1834, 1900, 1889, 1889, 1890, 1902, 1901, 1889, 1902, 1890, 1893, 1902, 1900, 1630, 1904, 1888, 1900, 1895, 1902, 1906, 1901, 1906, 1903, 1901, 1900, 1904, 1895, 1904, 1905, 1895, 1905, 1903, 1895, 1896, 1895, 1903, 1896, 1903, 1906, 1902, 1907, 1906, 1904, 1636, 1635, 1904, 1635, 1908, 1938, 1905, 1904, 1908, 1938, 1904, 1906, 1909, 1898, 1896, 1906, 1898, 1907, 1909, 1906, 1910, 1837, 1838, 1834, 1901, 1838, 1911, 1910, 1838, 1940, 1910, 1911, 1901, 1903, 1911, 1838, 1901, 1911, 1903, 1905, 1911, 1940, 1911, 1938, 1911, 1905, 1938, 1910, 467, 1837, 1910, 1940, 467, 467, 1940, 469, 1944, 1913, 1912, 1839, 1912, 1914, 1912, 1913, 1914, 1839, 1914, 1840, 1840, 1914, 1846, 1914, 1913, 1947, 1915, 1643, 1846, 1846, 1914, 1916, 1846, 1916, 1915, 1916, 1914, 1918, 1947, 1918, 1914, 1915, 1917, 1643, 1915, 1916, 1917, 1918, 1947, 1948, 1917, 1919, 1643, 1643, 1919, 1646, 1916, 1920, 1917, 1917, 1920, 1919, 1920, 1916, 1918, 1921, 1920, 1918, 1921, 1918, 1922, 1922, 1918, 1948, 1646, 1919, 1649, 1920, 1923, 1919, 1923, 1920, 1863, 1923, 1863, 1862, 1920, 1921, 1863, 1864, 1863, 1924, 1921, 1924, 1863, 1924, 1921, 1922, 1924, 1922, 1925, 1922, 1948, 1926, 1926, 1925, 1922, 1919, 1927, 1649, 1872, 1927, 1923, 1927, 1919, 1923, 1864, 1928, 1929, 1864, 1924, 1928, 1923, 1862, 1872, 1929, 1873, 1864, 1928, 1924, 1925, 1930, 1928, 1925, 1925, 1926, 1930, 1930, 1926, 1973, 1872, 1931, 1927, 1929, 1928, 1932, 1872, 1880, 1931, 1929, 1932, 1933, 1929, 1933, 1873, 1873, 1933, 1878, 1932, 1928, 1930, 1932, 1930, 1933, 1927, 1934, 1649, 1649, 1934, 1650, 1931, 1935, 1927, 1927, 1935, 1934, 1886, 1931, 1880, 1886, 1936, 1931, 1936, 1935, 1931, 1887, 1933, 1930, 1887, 1878, 1933, 1934, 1651, 1650, 1935, 1937, 1934, 1934, 1937, 1651, 1898, 1936, 1899, 1898, 1937, 1936, 1937, 1935, 1936, 1908, 1635, 1651, 1908, 1651, 1937, 1908, 1937, 1938, 1937, 1898, 1909, 1937, 1909, 1938, 1938, 1909, 1939, 1907, 1939, 1909, 1939, 1943, 1938, 1942, 1940, 1938, 1941, 1940, 1942, 1938, 1943, 1942, 1943, 1941, 1942, 1658, 1941, 1943, 1652, 365, 469, 1652, 469, 1940, 1652, 1940, 1941, 1944, 1657, 1945, 1945, 1947, 1944, 1947, 1945, 1946, 1913, 1944, 1947, 1947, 1946, 1948, 1948, 1946, 1949, 1949, 1926, 1948, 1926, 1949, 1952, 1949, 1950, 1952, 1973, 1926, 1952, 1952, 1950, 1951, 1952, 1951, 1975, 1953, 1943, 1939, 1939, 1976, 1953, 1954, 1943, 1953, 1961, 1954, 1953, 1962, 1961, 1953, 1659, 1658, 1943, 1955, 1659, 1943, 1943, 1954, 1955, 1663, 1945, 1657, 1664, 1945, 1663, 1664, 1946, 1945, 1664, 1665, 1956, 1946, 1664, 1956, 1956, 1665, 1666, 1956, 1666, 1957, 1949, 1946, 1956, 1957, 1949, 1956, 1957, 1666, 1958, 1949, 1957, 1958, 1666, 1960, 1958, 1958, 1950, 1949, 1958, 1960, 1959, 1951, 1950, 1958, 1959, 1951, 1958, 1960, 1666, 1667, 1975, 1951, 1959, 1959, 1960, 1961, 1975, 1959, 1962, 1959, 1961, 1962, 1960, 1667, 1963, 1954, 1960, 1963, 1961, 1960, 1954, 1659, 1955, 1963, 1954, 1963, 1955, 1713, 1964, 1718, 1731, 1725, 1965, 1725, 1718, 1964, 1725, 1964, 1965, 1965, 1964, 1966, 1965, 1966, 1733, 1731, 1965, 1733, 1733, 1966, 1778, 1968, 1964, 1713, 1964, 1968, 1966, 1966, 1968, 1967, 1966, 1967, 1778, 1969, 1821, 1832, 1968, 1969, 1829, 1829, 1969, 1832, 1893, 1894, 1970, 1971, 1970, 1894, 1897, 1971, 1894, 1897, 1887, 1971, 1893, 1970, 1902, 1970, 1971, 1907, 1902, 1970, 1907, 1930, 1972, 1887, 1973, 1972, 1930, 1887, 1972, 1971, 1971, 1972, 1974, 1972, 1973, 1974, 1974, 1973, 1976, 1971, 1974, 1939, 1907, 1971, 1939, 1974, 1976, 1939, 1976, 1973, 1977, 1973, 1952, 1977, 1952, 1975, 1977, 1977, 1975, 1962, 1976, 1977, 1953, 1953, 1977, 1962, 1978, 2008, 1981, 1981, 5473, 1978, 1981, 1979, 5473, 1979, 1981, 1980, 1982, 1984, 1980, 1982, 1980, 1981, 5473, 1979, 1983, 1984, 1979, 1980, 1984, 1985, 1979, 1985, 1983, 1979, 1988, 1983, 1998, 1985, 1986, 1983, 1986, 1998, 1983, 1987, 1988, 1989, 1991, 1992, 1990, 1991, 1993, 1995, 1992, 1991, 1995, 1992, 1994, 1990, 1990, 1994, 2001, 1994, 1996, 2001, 1998, 2060, 1988, 2060, 1989, 1988, 1994, 1992, 1996, 1995, 1997, 1992, 1992, 1997, 1996, 2060, 1998, 1986, 1999, 1996, 2120, 2120, 1996, 1997, 2000, 1996, 1999, 1999, 2125, 2000, 1996, 2000, 2001, 2000, 2219, 2001, 2000, 2183, 2219, 2219, 6, 2001, 2001, 6, 0, 2002, 2003, 2008, 1984, 2004, 2006, 1982, 2004, 1984, 2004, 2005, 2006, 2006, 2007, 2063, 2005, 2007, 2006, 2007, 2068, 2063, 2013, 2014, 1982, 1982, 2014, 2004, 2005, 2004, 2015, 2004, 2014, 2015, 2007, 2005, 2009, 2005, 2015, 2009, 2009, 2068, 2007, 2010, 2068, 2009, 2008, 2012, 2011, 1981, 2008, 2011, 2008, 2003, 2012, 1981, 2011, 1982, 1982, 2011, 2013, 2009, 2015, 2016, 2016, 2010, 2009, 2017, 2010, 2016, 2011, 2012, 2023, 2003, 2021, 2012, 2012, 2021, 2023, 2003, 2029, 2021, 2023, 2013, 2011, 2018, 2014, 2013, 2014, 2019, 2015, 2018, 2019, 2014, 2015, 2020, 2016, 2019, 2020, 2015, 2016, 2020, 2017, 2013, 2022, 2018, 2022, 2013, 2030, 2021, 2024, 2023, 2021, 2029, 2024, 2023, 2030, 2013, 2027, 2025, 2026, 2024, 2028, 2023, 2028, 2024, 2029, 2023, 2028, 2030, 2030, 2028, 2031, 2028, 2029, 2031, 2029, 2034, 2031, 2026, 2032, 2033, 2025, 2032, 2026, 2033, 2032, 2113, 2034, 2036, 2035, 2031, 2034, 2035, 2035, 2361, 2031, 2039, 2037, 2038, 2039, 2040, 2041, 2038, 2040, 2039, 2041, 2042, 2045, 2040, 2042, 2041, 2044, 2037, 2039, 2043, 2037, 2044, 5664, 2044, 2041, 2041, 2044, 2039, 2045, 5664, 2041, 2045, 2042, 2056, 2046, 2043, 2044, 2046, 2057, 2043, 5664, 2046, 2044, 2046, 2049, 2057, 2057, 2049, 2060, 2047, 2050, 2051, 2047, 2051, 2048, 2048, 2051, 5698, 2284, 2048, 2282, 2282, 2048, 5698, 2038, 2052, 2053, 2037, 2052, 2038, 2038, 2053, 2040, 2042, 2040, 2054, 2056, 2042, 2054, 2037, 2043, 2052, 2052, 2043, 2057, 2047, 2048, 2058, 2047, 2058, 2055, 2047, 2055, 2050, 2284, 2059, 2048, 2048, 2059, 2058, 1985, 2053, 2052, 1985, 1984, 2053, 1984, 2061, 2053, 2040, 2053, 2062, 2053, 2061, 2062, 2054, 2040, 2063, 2040, 2062, 2063, 2056, 2054, 2064, 2054, 2063, 2064, 2052, 2057, 1985, 2055, 2066, 2065, 2055, 2067, 2066, 2058, 2067, 2055, 2058, 2059, 2067, 2057, 2060, 1986, 1985, 2057, 1986, 2006, 2061, 1984, 2062, 2061, 2006, 2063, 2062, 2006, 2063, 2068, 2064, 2065, 2070, 2069, 2065, 2066, 2070, 2070, 2066, 2073, 2066, 2071, 2073, 2066, 2067, 2071, 2067, 2059, 2071, 2072, 2070, 2073, 2070, 2072, 2069, 2069, 2072, 2076, 2074, 2073, 2071, 2075, 2074, 2071, 2076, 2077, 2069, 2072, 2073, 2078, 2079, 2010, 2017, 2072, 2080, 2076, 2081, 2080, 2078, 2080, 2072, 2078, 2073, 2081, 2078, 2073, 2074, 2081, 2074, 2082, 2081, 2075, 2079, 2082, 2075, 2082, 2074, 2080, 2083, 2076, 2084, 2080, 2081, 2083, 2080, 2084, 2086, 2304, 2085, 2083, 2087, 2077, 2076, 2083, 2077, 2088, 2083, 2084, 2088, 2087, 2083, 2090, 2085, 2304, 2089, 2085, 2090, 2091, 2019, 2018, 2091, 2092, 2019, 2019, 2092, 2020, 2017, 2092, 2079, 2020, 2092, 2017, 2092, 2091, 2093, 2092, 2093, 2081, 2092, 2081, 2082, 2079, 2092, 2082, 2081, 2093, 2094, 2081, 2094, 2084, 2086, 2095, 2096, 2085, 2095, 2086, 2097, 2098, 2094, 2084, 2099, 2088, 2084, 2094, 2099, 2094, 2098, 2099, 2085, 2089, 2095, 2095, 2089, 2100, 2022, 2091, 2018, 2022, 2101, 2091, 2022, 2030, 2101, 2093, 2091, 2101, 2102, 2093, 2101, 2093, 2102, 2094, 2096, 2095, 2103, 2097, 2094, 2102, 2103, 2100, 2104, 2095, 2100, 2103, 2027, 2105, 2025, 2101, 2030, 2102, 2030, 2106, 2102, 2107, 2025, 2105, 2107, 2108, 2025, 2109, 2108, 2107, 2025, 2108, 2032, 2032, 2108, 2110, 2111, 2108, 2109, 2111, 2110, 2108, 2113, 2032, 2112, 2032, 2110, 2112, 2112, 2110, 2114, 2030, 2175, 2106, 2106, 2175, 2169, 2110, 2115, 2114, 2111, 2115, 2110, 2112, 2116, 2113, 2112, 2114, 2116, 2030, 2031, 2175, 2031, 2177, 2175, 2031, 2361, 2177, 2060, 2049, 1989, 2051, 2050, 2117, 2117, 2050, 2118, 2282, 5698, 5699, 2118, 1995, 1993, 2118, 1993, 2117, 2050, 2055, 2118, 2055, 2119, 2118, 2118, 2120, 1997, 2120, 2118, 2119, 1997, 1995, 2118, 2055, 2065, 2119, 2065, 2121, 2119, 2121, 2120, 2119, 2069, 2121, 2065, 2120, 2121, 2122, 2123, 1999, 2120, 2122, 2123, 2120, 2124, 2134, 2131, 2069, 2077, 2121, 2077, 2135, 2121, 2121, 2135, 2141, 2141, 2122, 2121, 2142, 2122, 2141, 2125, 1999, 2126, 2126, 1999, 2123, 2142, 2123, 2122, 2142, 2126, 2123, 2128, 2143, 2129, 2129, 2130, 2131, 2143, 2130, 2129, 2133, 2127, 2128, 2132, 2127, 2133, 2129, 2134, 2128, 2134, 2133, 2128, 2131, 2134, 2129, 2087, 2136, 2135, 2087, 2135, 2077, 2136, 2137, 2141, 2136, 2087, 2137, 2087, 2088, 2137, 2138, 2088, 2099, 2137, 2088, 2138, 2140, 2089, 2090, 2139, 2089, 2140, 2142, 2141, 2137, 2144, 2137, 2138, 2144, 2143, 2137, 2143, 2142, 2137, 2139, 2140, 2400, 2126, 2146, 2145, 2125, 2126, 2145, 2127, 2147, 2142, 2127, 2142, 2128, 2147, 2146, 2142, 2146, 2126, 2142, 2143, 2128, 2142, 2143, 2148, 2130, 2143, 2144, 2148, 2149, 2125, 2145, 2149, 2147, 2132, 2132, 2147, 2127, 2136, 2098, 2097, 2136, 2097, 2135, 2141, 2098, 2136, 2141, 2151, 2098, 2151, 2099, 2098, 2099, 2151, 2138, 2104, 2139, 2152, 2104, 2100, 2139, 2100, 2089, 2139, 2153, 2141, 2135, 2141, 2153, 2151, 2153, 2155, 2151, 2138, 2154, 2144, 2138, 2151, 2154, 2151, 2155, 2154, 2400, 2152, 2139, 2146, 2153, 2145, 2147, 2153, 2146, 2147, 2155, 2153, 2154, 2155, 2156, 2156, 2144, 2154, 2156, 2148, 2144, 2158, 2162, 2157, 2160, 2149, 2145, 2160, 2145, 2159, 2159, 2145, 2153, 2149, 2155, 2147, 2149, 2160, 2155, 2161, 2162, 2156, 2161, 2156, 2155, 2162, 2158, 2150, 2148, 2156, 2150, 2156, 2162, 2150, 2097, 2106, 2135, 2106, 2097, 2102, 2135, 2106, 2153, 2157, 2163, 2164, 2162, 2163, 2157, 2159, 2153, 2166, 2162, 2161, 2165, 2162, 2165, 2163, 2106, 2166, 2153, 2164, 2163, 2167, 2167, 2165, 2205, 2163, 2165, 2167, 2106, 2169, 2166, 2166, 2169, 2168, 2166, 2168, 2192, 2169, 2170, 2168, 2170, 2435, 2436, 2169, 2435, 2170, 2170, 2171, 2168, 2436, 2437, 2170, 2437, 2171, 2170, 2435, 2169, 2172, 2169, 2175, 2172, 2175, 2178, 2172, 2175, 2177, 2178, 2179, 2178, 2177, 2177, 2453, 2179, 2000, 2125, 2180, 2133, 2188, 2132, 2188, 2133, 2181, 2133, 2134, 2181, 2134, 2124, 2181, 2181, 2474, 2182, 2124, 2474, 2181, 2183, 2000, 2180, 2185, 2189, 2182, 2190, 2189, 2185, 2474, 2184, 2182, 2184, 2185, 2182, 2185, 2184, 2186, 2474, 2187, 2184, 2187, 2186, 2184, 2125, 2149, 2180, 2132, 2188, 2149, 2149, 2189, 2180, 2182, 2189, 2188, 2188, 2189, 2149, 2181, 2182, 2188, 2189, 2190, 2183, 2180, 2189, 2183, 2190, 2222, 2183, 2185, 2222, 2190, 2192, 2160, 2159, 2155, 2160, 2193, 2193, 2160, 2192, 2193, 2161, 2155, 2193, 2192, 2195, 2195, 2192, 2198, 2196, 2161, 2193, 2195, 2196, 2193, 2197, 2195, 2198, 2196, 2195, 2197, 2191, 2203, 2221, 2192, 2159, 2166, 2165, 2196, 2199, 2161, 2196, 2165, 2198, 2200, 2197, 2200, 2202, 2197, 2197, 2202, 2201, 2196, 2197, 2201, 2196, 2201, 2199, 2205, 2165, 2204, 2201, 2206, 2204, 2201, 2204, 2199, 2199, 2204, 2165, 2198, 2207, 2200, 2202, 2200, 2207, 2207, 2201, 2202, 2203, 2208, 2226, 2171, 2209, 2168, 2209, 2205, 2204, 2209, 2171, 2205, 2168, 2210, 2198, 2192, 2168, 2198, 2206, 2210, 2209, 2209, 2210, 2168, 2204, 2206, 2209, 2198, 2210, 2207, 2206, 2201, 2207, 2210, 2206, 2207, 2208, 2211, 2226, 2226, 2211, 2212, 2213, 2226, 2212, 2214, 2174, 2173, 2215, 2216, 2214, 2212, 2216, 2215, 2215, 2213, 2212, 2217, 2213, 2215, 2176, 2214, 2173, 2214, 2521, 2215, 2215, 2521, 2217, 2521, 2218, 2217, 2186, 2187, 2220, 2185, 2186, 2222, 2220, 2191, 2221, 2220, 2221, 2186, 2183, 2222, 2219, 2186, 2223, 2222, 2186, 2221, 2223, 2225, 2222, 2223, 2221, 2224, 2223, 2224, 2221, 2203, 2227, 2222, 2225, 2219, 2222, 2227, 6, 2219, 2227, 2226, 2224, 2203, 2225, 2223, 2226, 2226, 2223, 2224, 2226, 2213, 2225, 2225, 2213, 2217, 2217, 2227, 2225, 2230, 2233, 2227, 2218, 2228, 2217, 2228, 2557, 2227, 2228, 2227, 2217, 2557, 2229, 2227, 2229, 2230, 2227, 2218, 2559, 2228, 2230, 2229, 2231, 7, 6, 2227, 2227, 2233, 7, 2230, 2231, 2233, 2231, 2234, 2233, 2234, 2578, 2233, 2233, 2578, 7, 2231, 2232, 2234, 2235, 2236, 2240, 2238, 2241, 2237, 2238, 2239, 2286, 2238, 2286, 2241, 2235, 2240, 2002, 2002, 2240, 2003, 2241, 2286, 2293, 2240, 2242, 2003, 2591, 2241, 2594, 2594, 2293, 2730, 2241, 2293, 2594, 2242, 2249, 2003, 2003, 2249, 2029, 2244, 2316, 2243, 2243, 2245, 2244, 2027, 2026, 2248, 2026, 2247, 2248, 2248, 2243, 2316, 2248, 2247, 2243, 2243, 2246, 2245, 2246, 2252, 2253, 2243, 2252, 2246, 2247, 2026, 2251, 2243, 2247, 2252, 2247, 2251, 2252, 2249, 2034, 2029, 2251, 2026, 2254, 2026, 2033, 2254, 2251, 2254, 2255, 2252, 2251, 2255, 2255, 2253, 2252, 2250, 2256, 2249, 2628, 2253, 2635, 2033, 2113, 2254, 2254, 2257, 2255, 2113, 2257, 2254, 2255, 2635, 2253, 2255, 2257, 2635, 2257, 2343, 2635, 2256, 2641, 2249, 2249, 2258, 2034, 2641, 2258, 2249, 2034, 2260, 2036, 2258, 2260, 2034, 2259, 2035, 2036, 2259, 2036, 2260, 2361, 2035, 2259, 2261, 2259, 2260, 2261, 2260, 2262, 2258, 2262, 2260, 2261, 2364, 2259, 2661, 2264, 2262, 2271, 2261, 2268, 2268, 2261, 2262, 2262, 2264, 2268, 2271, 2265, 2261, 2261, 2265, 2364, 2266, 2364, 2265, 2266, 2263, 2355, 2266, 2267, 2263, 2266, 2265, 2269, 2267, 2266, 2270, 2266, 2269, 2270, 2268, 2272, 2271, 2264, 2274, 2268, 2268, 2274, 2272, 2265, 2275, 2269, 2273, 2271, 2272, 2273, 2272, 2274, 2271, 2275, 2265, 2275, 2271, 2273, 2275, 2276, 2269, 2269, 2276, 2270, 2275, 2277, 2276, 2274, 2702, 2273, 2273, 2702, 2275, 2239, 2280, 2286, 2239, 2278, 2280, 2279, 2045, 2280, 2045, 2056, 2280, 2280, 2278, 2279, 2281, 2288, 2285, 2282, 2283, 2284, 2284, 2283, 2285, 2286, 2280, 2287, 2284, 2289, 2059, 2288, 2284, 2285, 2288, 2290, 2284, 2290, 2289, 2284, 2297, 2290, 2288, 2064, 2291, 2056, 2056, 2291, 2280, 2280, 2291, 2292, 2287, 2292, 2295, 2287, 2280, 2292, 2287, 2293, 2286, 2059, 2289, 2294, 2293, 2287, 2295, 2289, 2296, 2294, 2380, 2302, 2296, 2380, 2296, 2290, 2290, 2296, 2289, 2380, 2290, 2297, 2303, 2064, 2068, 2291, 2064, 2303, 2292, 2291, 2298, 2291, 2303, 2298, 2298, 2295, 2292, 2300, 2295, 2298, 2296, 2299, 2071, 2296, 2071, 2294, 2294, 2071, 2059, 2293, 2295, 2300, 2293, 2300, 2301, 2293, 2301, 2730, 2296, 2381, 2299, 2296, 2302, 2381, 2010, 2303, 2068, 2307, 2298, 2303, 2298, 2307, 2300, 2075, 2071, 2299, 2304, 2075, 2299, 2300, 2307, 2309, 2305, 2299, 2381, 2305, 2304, 2299, 2301, 2300, 2309, 2010, 2079, 2303, 2303, 2079, 2307, 2086, 2308, 2304, 2308, 2307, 2304, 2075, 2304, 2079, 2307, 2079, 2304, 2309, 2308, 2312, 2307, 2308, 2309, 2304, 2305, 2090, 2309, 2312, 2310, 2301, 2310, 2306, 2301, 2309, 2310, 2311, 2317, 2245, 2313, 2317, 2311, 2312, 2096, 2103, 2312, 2308, 2096, 2308, 2086, 2096, 2314, 2312, 2103, 2104, 2314, 2103, 2312, 2315, 2310, 2312, 2314, 2315, 2316, 2244, 2318, 2244, 2317, 2318, 2245, 2317, 2244, 2320, 2319, 2318, 2317, 2320, 2318, 2105, 2322, 2321, 2105, 2027, 2322, 2027, 2248, 2322, 2316, 2322, 2248, 2324, 2321, 2322, 2323, 2321, 2324, 2316, 2318, 2324, 2322, 2316, 2324, 2324, 2326, 2323, 2326, 2324, 2318, 2326, 2318, 2319, 2746, 2325, 2327, 2327, 2420, 2842, 2327, 2842, 2746, 2328, 2107, 2105, 2328, 2105, 2329, 2321, 2329, 2105, 2329, 2325, 2330, 2328, 2109, 2107, 2328, 2332, 2109, 2321, 2323, 2329, 2323, 2333, 2329, 2325, 2329, 2333, 2330, 2325, 2334, 2334, 2325, 2331, 2326, 2333, 2323, 2335, 2333, 2326, 2335, 2325, 2333, 2325, 2335, 2327, 2335, 2326, 2336, 2327, 2336, 2431, 2327, 2335, 2336, 2329, 2339, 2328, 2329, 2330, 2337, 2339, 2329, 2337, 2339, 2338, 2328, 2328, 2338, 2332, 2334, 2341, 2337, 2334, 2337, 2330, 2334, 2331, 2341, 2341, 2331, 2348, 2109, 2332, 2111, 2332, 2340, 2111, 2332, 2338, 2340, 2257, 2113, 2349, 2342, 2257, 2349, 2342, 2343, 2257, 2339, 2344, 2338, 2339, 2337, 2344, 2337, 2341, 2344, 2343, 2342, 2351, 2338, 2344, 2340, 2345, 2340, 2344, 2344, 2341, 2345, 2340, 2115, 2111, 2345, 2346, 2340, 2346, 2115, 2340, 2341, 2347, 2345, 2347, 2346, 2345, 2341, 2348, 2347, 2116, 2349, 2113, 2349, 2350, 2342, 2349, 2116, 2352, 2352, 2116, 2114, 2342, 2350, 2351, 2350, 2349, 2351, 2115, 2352, 2114, 2354, 2363, 2353, 2259, 2364, 2361, 2358, 2355, 2356, 2357, 2355, 2358, 2357, 2360, 2359, 2357, 2358, 2360, 2358, 2353, 2360, 2363, 2359, 2360, 2362, 2359, 2363, 2353, 2363, 2360, 2366, 2357, 2367, 2366, 2365, 2357, 2365, 2355, 2357, 2359, 2367, 2357, 2359, 2368, 2367, 2369, 2361, 2364, 2362, 2368, 2359, 2266, 2355, 2365, 2266, 2365, 2781, 2365, 2366, 2781, 2364, 2370, 2369, 2370, 2373, 2369, 2266, 2372, 2371, 2364, 2266, 2371, 2781, 2372, 2266, 2364, 2371, 2370, 2371, 2373, 2370, 2371, 2372, 2374, 2372, 2786, 2788, 2372, 2788, 2374, 2374, 2377, 2371, 2371, 2377, 2373, 2375, 2374, 2788, 2376, 2374, 2375, 2377, 2374, 2376, 2375, 2378, 2376, 2376, 2378, 2377, 2379, 2800, 2377, 2378, 2379, 2377, 2384, 2380, 2297, 2382, 2389, 2381, 2380, 2382, 2381, 2380, 2381, 2302, 2382, 2380, 2384, 2383, 2389, 2382, 2384, 2383, 2382, 2385, 2383, 2384, 2131, 2394, 2124, 2124, 2394, 2386, 2385, 2386, 2394, 2387, 2385, 2384, 2386, 2385, 2387, 2389, 2388, 2305, 2381, 2389, 2305, 2390, 2391, 2306, 2393, 2388, 2389, 2392, 2388, 2393, 2389, 2383, 2393, 2131, 2130, 2394, 2392, 2394, 2130, 2392, 2393, 2394, 2393, 2385, 2394, 2393, 2383, 2385, 2384, 2832, 2387, 2305, 2395, 2090, 2090, 2395, 2140, 2305, 2388, 2395, 2390, 2306, 2310, 2397, 2390, 2310, 2400, 2140, 2396, 2140, 2395, 2396, 2388, 2392, 2396, 2395, 2388, 2396, 2390, 2397, 2399, 2390, 2399, 2391, 2399, 2397, 2398, 2401, 2130, 2148, 2400, 2396, 2392, 2400, 2392, 2402, 2130, 2401, 2392, 2401, 2402, 2392, 2403, 2398, 2397, 2480, 2403, 2397, 2401, 2148, 2150, 2404, 2401, 2150, 2402, 2401, 2404, 2405, 2403, 2480, 2104, 2152, 2314, 2314, 2152, 2406, 2315, 2314, 2406, 2310, 2406, 2397, 2315, 2406, 2310, 2400, 2408, 2152, 2408, 2406, 2152, 2399, 2398, 2407, 2398, 2415, 2407, 2158, 2157, 2409, 2402, 2408, 2400, 2402, 2410, 2408, 2410, 2406, 2408, 2397, 2410, 2480, 2406, 2410, 2397, 2415, 2403, 2839, 2415, 2398, 2403, 2158, 2404, 2150, 2158, 2411, 2404, 2158, 2409, 2411, 2404, 2410, 2402, 2404, 2411, 2410, 2411, 2487, 2410, 2410, 2487, 2480, 2403, 2405, 2839, 2320, 2413, 2412, 2320, 2317, 2413, 2313, 2407, 2413, 2317, 2313, 2413, 2415, 2412, 2413, 2414, 2412, 2415, 2413, 2407, 2415, 2164, 2409, 2157, 2416, 2409, 2164, 2415, 2421, 2414, 2416, 2494, 2411, 2416, 2411, 2409, 2320, 2417, 2319, 2417, 2326, 2319, 2320, 2418, 2417, 2418, 2320, 2412, 2418, 2412, 2419, 2419, 2412, 2414, 2164, 2167, 2416, 2421, 2428, 2414, 2428, 2419, 2414, 2416, 2167, 2499, 2167, 2205, 2499, 2416, 2499, 2494, 2421, 2429, 2428, 2421, 2905, 2429, 2326, 2417, 2336, 2336, 2417, 2422, 2423, 2327, 2431, 2327, 2423, 2420, 2417, 2424, 2422, 2418, 2424, 2417, 2424, 2418, 2423, 2418, 2419, 2423, 2426, 2423, 2425, 2419, 2425, 2423, 2420, 2426, 2427, 2420, 2423, 2426, 2419, 2428, 2425, 2431, 2336, 2430, 2336, 2422, 2430, 2433, 2422, 2424, 2433, 2432, 2422, 2432, 2430, 2422, 2423, 2431, 2433, 2423, 2433, 2424, 2431, 2434, 2433, 2425, 2433, 2426, 2434, 2426, 2433, 2434, 2427, 2426, 2432, 2436, 2435, 2432, 2433, 2436, 2425, 2428, 2433, 2445, 2427, 2434, 2433, 2438, 2436, 2438, 2437, 2436, 2433, 2428, 2438, 2430, 2439, 2115, 2430, 2115, 2346, 2431, 2430, 2346, 2431, 2346, 2440, 2440, 2346, 2347, 2348, 2441, 2347, 2441, 2440, 2347, 2430, 2432, 2439, 2439, 2432, 2442, 2431, 2440, 2434, 2434, 2440, 2443, 2443, 2440, 2441, 2435, 2172, 2444, 2442, 2432, 2435, 2444, 2442, 2435, 2434, 2443, 2445, 2174, 2446, 2173, 2115, 2439, 2352, 2439, 2447, 2352, 2447, 2349, 2352, 2751, 2349, 2447, 2447, 2442, 2448, 2439, 2442, 2447, 2444, 2172, 2178, 2448, 2442, 2444, 2448, 2444, 2178, 2448, 2449, 2447, 2751, 2447, 2449, 2448, 2451, 2450, 2449, 2450, 2524, 2449, 2448, 2450, 2178, 2451, 2448, 2179, 2451, 2178, 2450, 2451, 2452, 2452, 2524, 2450, 2529, 2524, 2452, 2453, 2451, 2179, 2529, 2452, 2454, 2361, 2453, 2177, 2453, 2460, 2451, 2452, 2451, 2455, 2454, 2452, 2456, 2452, 2455, 2456, 2458, 2363, 2354, 2457, 2363, 2458, 2361, 2459, 2453, 2459, 2361, 2464, 2462, 2461, 2457, 2457, 2458, 2462, 2458, 2463, 2462, 2453, 2459, 2460, 2461, 2459, 2464, 2461, 2462, 2459, 2462, 2460, 2459, 2455, 2451, 2460, 2462, 2455, 2460, 2456, 2455, 2462, 2463, 2456, 2462, 2456, 2463, 2539, 2465, 2362, 2363, 2363, 2457, 2465, 2457, 2461, 2467, 2467, 2466, 2457, 2466, 2465, 2457, 2461, 2464, 2467, 2467, 2468, 2466, 2362, 2465, 2368, 2470, 2368, 2465, 2369, 2471, 2464, 2361, 2369, 2464, 2470, 2465, 2466, 2464, 2471, 2467, 2467, 2471, 2468, 2550, 2472, 2471, 2468, 2472, 2469, 2471, 2472, 2468, 2471, 2473, 2550, 2473, 2549, 2550, 2373, 2473, 2369, 2369, 2473, 2471, 2473, 2551, 2549, 2373, 2377, 2473, 2473, 2377, 2551, 2377, 2800, 2551, 2475, 2386, 2387, 2386, 2477, 2124, 2477, 2386, 2475, 2476, 2474, 2124, 2478, 2474, 2476, 2477, 2476, 2124, 2552, 2478, 2476, 2552, 2476, 2477, 2475, 2896, 2477, 2474, 2478, 2187, 2477, 2553, 2552, 2477, 2896, 2553, 2553, 2896, 2897, 2481, 2194, 2482, 2484, 2482, 2194, 2483, 2484, 2194, 2479, 2486, 2482, 2479, 2482, 2484, 2481, 2482, 2486, 2483, 2191, 2484, 2479, 2484, 2485, 2484, 2191, 2485, 2486, 2479, 2485, 2486, 2485, 2553, 2480, 2487, 2488, 2405, 2480, 2488, 2491, 2405, 2488, 2481, 2489, 2194, 2489, 2492, 2194, 2481, 2490, 2489, 2194, 2492, 2483, 2481, 2486, 2490, 2486, 2493, 2490, 2492, 2191, 2483, 2203, 2191, 2492, 2411, 2494, 2487, 2487, 2494, 2488, 2503, 2491, 2488, 2489, 2495, 2492, 2495, 2489, 2496, 2496, 2489, 2490, 2502, 2496, 2490, 2504, 2492, 2495, 2497, 2490, 2493, 2497, 2502, 2490, 2492, 2498, 2203, 2504, 2498, 2492, 2493, 2505, 2497, 2488, 2494, 2500, 2500, 2503, 2488, 2501, 2495, 2496, 2504, 2501, 2208, 2495, 2501, 2504, 2505, 2502, 2497, 2208, 2498, 2504, 2208, 2203, 2498, 2499, 2205, 2506, 2205, 2171, 2506, 2494, 2499, 2506, 2428, 2429, 2500, 2428, 2500, 2506, 2506, 2500, 2494, 2503, 2500, 2429, 2905, 2503, 2429, 2208, 2501, 2211, 2496, 2507, 2501, 2501, 2507, 2211, 2507, 2496, 2502, 2509, 2507, 2502, 2508, 2509, 2502, 2502, 2505, 2508, 2933, 2556, 2508, 2505, 2933, 2508, 2506, 2171, 2437, 2438, 2506, 2437, 2428, 2506, 2438, 2211, 2507, 2509, 2510, 2211, 2509, 2510, 2212, 2211, 2510, 2509, 2511, 2509, 2508, 2511, 2512, 2511, 2508, 2556, 2512, 2508, 2446, 2174, 2513, 2513, 2174, 2214, 2514, 2446, 2513, 2513, 2214, 2216, 2514, 2513, 2516, 2515, 2446, 2514, 2515, 2514, 2517, 2517, 2514, 2516, 2510, 2216, 2212, 2513, 2216, 2510, 2510, 2516, 2513, 2510, 2511, 2516, 2511, 2518, 2516, 2518, 2517, 2516, 2512, 2517, 2518, 2176, 2521, 2214, 2446, 2519, 2176, 2446, 2176, 2173, 2449, 2524, 2520, 2446, 2515, 2519, 2515, 2526, 2519, 2532, 2526, 2515, 2523, 2522, 2176, 2522, 2521, 2176, 2523, 2176, 2519, 2530, 2523, 2519, 2520, 2524, 2525, 2522, 2527, 2521, 2519, 2526, 2530, 2521, 2527, 2218, 2527, 2536, 2218, 2522, 2523, 2528, 2525, 2524, 2534, 2524, 2529, 2534, 2522, 2528, 2527, 2528, 2535, 2527, 2526, 2531, 2530, 2535, 2536, 2527, 2526, 2532, 2531, 2528, 2523, 2533, 2533, 2523, 2530, 2529, 2454, 2534, 2528, 2533, 2535, 2530, 2538, 2533, 2531, 2540, 2530, 2530, 2540, 2538, 2541, 2540, 2531, 2532, 2541, 2531, 2533, 2538, 2542, 2533, 2542, 2537, 2454, 2456, 2539, 2534, 2454, 2539, 2533, 2537, 2535, 2535, 2537, 2536, 2537, 2566, 2536, 2538, 2546, 2542, 2540, 2546, 2538, 2540, 2541, 2547, 2540, 2547, 2546, 2542, 2544, 2543, 2542, 2546, 2545, 2542, 2545, 2544, 2469, 2545, 2546, 2469, 2546, 2547, 2543, 2548, 2542, 2544, 2550, 2549, 2543, 2544, 2549, 2550, 2545, 2472, 2550, 2544, 2545, 2469, 2472, 2545, 2543, 2549, 2548, 2549, 2551, 2548, 2800, 2871, 2551, 2551, 2871, 2574, 2478, 2220, 2187, 2552, 2485, 2478, 2478, 2485, 2220, 2553, 2485, 2552, 2553, 2897, 2554, 2485, 2191, 2220, 2553, 2554, 2555, 2553, 2555, 2486, 2486, 2555, 2493, 2511, 2512, 2518, 2558, 2229, 2557, 2559, 2218, 2536, 2559, 2557, 2228, 2559, 2560, 2557, 2558, 2557, 2560, 2558, 2231, 2229, 2231, 2558, 2560, 2536, 2562, 2559, 2559, 2562, 2560, 2562, 2563, 2560, 2560, 2563, 2232, 2560, 2232, 2231, 2563, 2564, 2232, 2564, 2561, 2232, 2565, 2561, 2564, 2536, 2566, 2562, 2567, 2563, 2566, 2566, 2563, 2562, 2568, 2564, 2563, 2567, 2568, 2563, 2564, 2568, 2565, 2569, 2537, 2542, 2566, 2537, 2569, 2566, 2569, 2567, 2569, 2570, 2567, 2568, 2567, 2571, 2567, 2570, 2571, 2542, 2548, 2569, 2548, 2572, 2569, 2569, 2572, 2570, 2570, 2572, 2571, 2548, 2551, 2572, 2551, 2573, 2572, 2574, 2573, 2551, 2574, 2935, 2573, 2561, 2575, 2232, 2232, 2577, 2234, 2232, 2575, 2577, 2577, 2576, 2234, 2565, 2575, 2561, 2577, 2575, 2565, 2578, 2234, 2576, 2577, 2568, 2576, 2565, 2568, 2577, 2568, 2579, 2576, 2579, 2568, 2571, 2579, 31, 2576, 2576, 31, 2578, 2571, 2572, 2579, 2572, 2573, 2579, 2579, 2573, 31, 2573, 2935, 31, 2580, 2236, 2581, 2581, 2236, 2235, 2581, 2582, 2580, 2580, 2583, 2237, 2580, 2582, 2583, 2584, 2586, 2585, 2583, 2238, 2237, 2587, 2584, 2589, 5903, 2587, 2589, 5905, 2589, 2590, 5904, 5905, 2590, 2589, 5905, 5903, 2240, 2236, 2580, 2591, 2240, 2580, 2591, 2580, 2237, 2241, 2591, 2237, 2584, 2585, 2588, 2588, 2593, 2589, 2584, 2588, 2589, 2585, 2595, 2588, 2586, 2592, 2585, 2585, 2592, 2595, 2589, 2593, 2590, 2242, 2240, 2591, 2596, 2242, 2591, 2597, 2592, 2598, 2591, 2594, 2596, 2592, 2597, 2595, 2588, 2595, 2593, 2595, 2600, 2593, 2595, 2602, 2600, 2595, 2597, 2602, 2596, 2594, 2599, 2594, 2730, 2599, 2599, 2730, 2732, 2596, 2603, 2601, 2596, 2601, 2242, 2242, 2601, 2249, 2598, 2605, 2597, 2597, 2605, 2602, 2603, 2599, 2604, 2596, 2599, 2603, 2599, 2732, 2604, 2602, 2737, 2600, 2601, 2603, 2609, 2609, 2603, 2604, 2605, 2742, 2602, 2602, 2742, 2737, 2601, 2606, 2249, 2606, 2601, 2609, 2607, 2605, 2608, 2607, 2610, 2605, 2605, 2610, 2742, 2604, 2245, 2609, 2611, 2250, 2606, 2606, 2250, 2249, 2606, 2609, 2611, 2608, 2613, 2612, 2607, 2608, 2612, 2614, 2611, 2609, 2607, 2612, 2615, 2609, 2246, 2614, 2607, 2615, 2610, 2610, 2615, 2616, 2609, 2245, 2246, 2617, 2742, 2616, 2742, 2610, 2616, 2618, 2250, 2611, 2619, 2621, 2620, 2621, 2612, 2613, 2621, 2613, 2620, 2620, 2613, 2944, 2611, 2614, 2618, 2618, 2614, 2622, 2615, 2621, 2623, 2621, 2619, 2623, 2612, 2621, 2615, 2622, 2614, 2246, 2253, 2622, 2246, 2624, 2616, 2623, 2623, 2616, 2615, 2616, 2624, 2331, 2616, 2331, 2617, 2625, 2250, 2618, 2623, 2619, 2626, 2620, 2627, 2619, 2619, 2627, 2626, 2944, 2627, 2620, 2618, 2622, 2625, 2625, 2622, 2628, 2626, 2629, 2623, 2628, 2622, 2253, 2623, 2629, 2624, 2624, 2629, 2630, 2256, 2250, 2625, 2631, 2256, 2625, 2632, 2626, 2627, 2627, 2633, 2632, 2625, 2628, 2631, 2626, 2634, 2629, 2634, 2626, 2632, 2628, 2635, 2631, 2629, 2634, 2630, 2630, 2634, 2748, 2631, 2636, 2256, 2256, 2636, 2641, 2637, 2632, 2638, 2638, 2632, 2633, 2636, 2631, 2635, 2632, 2639, 2634, 2637, 2639, 2632, 2640, 2636, 2635, 2634, 2639, 2748, 2642, 2641, 2636, 2639, 2637, 2649, 2642, 2636, 2643, 2642, 2646, 2644, 2642, 2644, 2641, 2641, 2644, 2258, 2638, 2647, 2637, 2646, 2642, 2645, 2647, 2649, 2637, 2645, 2643, 2755, 2642, 2643, 2645, 2755, 2646, 2645, 2648, 2644, 2646, 2648, 2646, 2656, 2647, 2653, 2649, 2656, 2646, 2654, 2649, 2650, 2639, 2654, 2646, 2754, 2644, 2651, 2258, 2651, 2644, 2648, 2655, 2651, 2648, 2648, 2656, 2655, 2649, 2653, 2652, 2649, 2652, 2650, 2652, 2660, 2650, 2258, 2651, 2262, 2653, 2658, 2657, 2652, 2653, 2657, 2656, 2654, 2659, 2652, 2657, 2660, 2651, 2661, 2262, 2661, 2651, 2655, 2662, 2661, 2655, 2655, 2656, 2662, 2658, 2664, 2657, 2656, 2659, 2663, 2657, 2664, 2660, 2666, 2355, 2665, 2666, 2659, 2654, 2666, 2665, 2659, 2665, 2667, 2659, 2667, 2663, 2659, 2664, 2668, 2660, 2661, 2674, 2264, 2662, 2674, 2661, 2674, 2662, 2669, 2662, 2670, 2669, 2662, 2656, 2670, 2656, 2663, 2670, 2670, 2663, 2671, 2263, 2665, 2355, 2263, 2672, 2665, 2667, 2665, 2673, 2665, 2672, 2673, 2671, 2663, 2667, 2673, 2671, 2667, 2664, 2775, 2668, 2674, 2682, 2264, 2669, 2675, 2674, 2669, 2670, 2676, 2669, 2676, 2675, 2676, 2677, 2675, 2676, 2671, 2678, 2670, 2671, 2676, 2679, 2955, 2677, 2679, 2677, 2678, 2678, 2677, 2676, 2263, 2267, 2672, 2673, 2672, 2680, 2678, 2671, 2673, 2678, 2673, 2681, 2681, 2673, 2680, 2679, 2678, 2681, 2955, 2683, 2684, 2955, 2679, 2683, 2679, 2685, 2683, 2672, 2267, 2686, 2267, 2270, 2686, 2680, 2686, 2784, 2680, 2672, 2686, 2687, 2685, 2680, 2685, 2681, 2680, 2685, 2679, 2681, 2264, 2682, 2274, 2683, 2688, 2684, 2685, 2689, 2688, 2685, 2688, 2683, 2685, 2687, 2689, 2687, 2690, 2689, 2684, 2688, 2693, 2689, 2691, 2688, 2686, 2270, 2692, 2270, 2276, 2692, 2686, 2692, 2789, 2789, 2692, 2691, 2789, 2691, 2690, 2690, 2691, 2689, 2785, 2970, 2792, 2688, 2691, 2693, 2692, 2276, 2694, 2692, 2694, 2701, 2701, 2691, 2692, 2792, 2970, 2695, 2702, 2274, 2696, 2682, 2697, 2274, 2274, 2697, 2696, 2275, 2702, 2277, 2693, 2698, 2706, 2691, 2698, 2693, 2701, 2699, 2691, 2699, 2698, 2691, 2277, 2694, 2276, 2277, 2700, 2694, 2700, 2797, 2694, 2701, 2694, 2797, 2696, 2703, 2702, 2697, 2704, 2696, 2696, 2704, 2703, 2706, 2705, 2704, 2706, 2704, 2697, 2706, 2698, 2705, 2698, 2708, 2705, 2708, 2698, 2699, 2277, 2702, 2700, 2701, 2801, 2699, 2703, 2707, 2702, 2704, 2711, 2703, 2703, 2711, 2707, 2711, 2704, 2705, 2705, 2708, 2711, 2709, 2708, 2699, 2709, 2699, 2807, 2707, 2714, 2702, 2714, 2707, 2710, 2710, 2707, 2711, 2711, 2708, 2712, 2712, 2708, 2713, 2709, 2713, 2708, 2713, 2709, 2818, 2709, 2807, 2818, 2710, 2715, 2714, 2711, 2716, 2710, 2710, 2716, 2715, 2716, 2711, 2712, 2717, 2716, 2712, 2714, 2723, 2702, 2712, 2713, 2717, 2713, 2818, 2819, 2713, 2819, 2719, 2715, 3002, 2714, 3002, 2715, 2720, 2720, 2715, 2716, 2717, 2721, 2716, 2716, 2721, 2720, 3002, 2724, 2723, 2714, 3002, 2723, 2717, 2722, 2995, 2717, 2713, 2722, 2722, 2713, 2719, 2723, 2823, 2718, 2725, 2723, 2724, 2725, 2823, 2723, 5904, 2590, 2726, 2281, 2726, 2727, 2727, 2288, 2281, 2590, 2727, 2726, 2288, 2727, 2729, 2727, 2728, 2729, 2729, 2297, 2288, 2590, 2728, 2727, 2728, 2590, 2593, 2297, 2729, 2828, 2593, 2600, 2728, 2600, 2733, 2728, 2728, 2733, 2729, 2301, 2731, 2730, 2600, 2737, 2733, 2730, 2731, 2732, 2732, 2731, 2734, 2735, 2734, 2731, 2735, 2731, 2306, 2306, 2731, 2301, 2733, 2741, 2729, 2738, 2604, 2732, 2739, 2738, 2732, 2737, 2741, 2733, 2732, 2734, 2739, 2734, 2735, 2739, 2739, 2735, 2736, 2739, 2736, 2738, 2738, 2736, 2740, 2604, 2311, 2245, 2604, 2743, 2311, 2743, 2313, 2311, 2604, 2738, 2743, 2737, 2742, 2741, 2742, 2744, 2741, 2743, 2740, 2313, 2743, 2738, 2740, 2745, 2617, 2325, 2745, 2742, 2617, 2742, 2745, 2744, 2745, 2325, 2746, 2745, 2746, 2744, 2744, 2746, 2842, 2617, 2331, 2325, 2331, 2624, 2630, 2630, 2747, 2331, 2747, 2348, 2331, 2640, 2635, 2343, 2640, 2343, 2351, 2747, 2630, 2748, 2847, 2348, 2747, 2749, 2636, 2640, 2351, 2751, 2640, 2751, 2749, 2640, 2639, 2750, 2748, 2751, 2351, 2349, 2748, 2750, 2747, 2752, 2749, 2751, 2750, 2848, 2747, 2636, 2749, 2643, 2639, 2753, 2750, 2750, 2753, 2848, 2643, 2749, 2755, 2754, 2646, 2755, 2639, 2650, 2753, 2650, 2758, 2753, 2755, 2749, 2756, 2749, 2752, 2756, 2756, 2752, 2760, 2755, 2756, 2761, 2755, 2761, 2759, 2755, 2759, 2754, 2754, 2759, 2757, 2761, 2756, 2760, 2753, 2758, 2856, 2758, 2861, 2856, 2654, 2754, 2762, 2660, 2763, 2650, 2764, 2762, 2757, 2757, 2762, 2754, 2650, 2763, 2758, 2765, 2764, 2766, 2757, 2759, 2766, 2764, 2757, 2766, 2761, 2765, 2766, 2761, 2760, 2765, 2760, 2767, 2765, 2759, 2761, 2766, 2758, 2763, 2861, 2763, 2771, 2861, 2772, 2654, 2762, 2660, 2768, 2763, 2764, 2772, 2762, 2764, 2769, 2772, 2770, 2353, 2773, 2770, 2773, 2769, 2770, 2769, 2765, 2765, 2769, 2764, 2771, 2763, 2768, 2353, 2770, 2354, 2765, 2767, 2770, 2355, 2666, 2356, 2666, 2772, 2356, 2666, 2654, 2772, 2774, 2768, 2668, 2768, 2660, 2668, 2772, 2773, 2356, 2773, 2358, 2356, 2772, 2769, 2773, 2773, 2353, 2358, 2771, 2774, 2778, 2768, 2774, 2771, 2774, 2668, 2780, 2775, 2780, 2668, 2367, 2777, 2776, 2366, 2367, 2776, 2776, 2777, 2774, 2777, 2367, 2368, 2774, 2777, 2778, 2778, 2777, 2368, 2778, 2368, 2470, 2774, 2779, 2776, 2779, 2774, 2780, 2366, 2776, 2781, 2779, 2782, 2776, 2782, 2781, 2776, 2780, 2782, 2779, 2783, 2782, 2780, 2775, 2783, 2780, 2999, 2783, 2775, 2687, 2680, 2784, 2781, 2786, 2372, 2782, 2786, 2781, 2782, 2783, 2787, 2785, 2787, 2783, 2785, 2783, 2999, 2782, 2790, 2786, 2787, 2790, 2782, 2784, 2686, 2789, 2690, 2687, 2784, 2789, 2690, 2784, 2787, 2785, 2792, 2786, 2793, 2788, 2792, 2794, 2787, 2786, 2790, 2793, 2787, 2791, 2790, 2791, 2787, 2794, 2793, 2375, 2788, 2791, 2793, 2790, 2794, 2793, 2791, 2794, 2795, 2796, 2794, 2695, 2795, 2794, 2792, 2695, 2375, 2796, 2378, 2793, 2796, 2375, 2796, 2793, 2794, 2795, 2695, 2798, 2378, 2796, 2799, 2379, 2378, 2799, 2796, 2803, 2799, 2796, 2795, 2803, 2798, 2803, 2795, 2800, 2379, 2799, 2802, 2797, 2700, 2802, 2700, 2808, 2808, 2700, 2702, 2797, 2802, 2801, 2701, 2797, 2801, 2803, 2798, 2809, 2799, 2803, 2805, 2804, 2803, 2809, 2805, 2803, 2804, 2799, 2872, 2800, 2805, 2872, 2799, 2873, 2805, 2804, 2801, 2806, 2807, 2801, 2807, 2699, 2802, 2806, 2801, 2802, 2808, 2806, 2798, 2984, 2809, 2810, 2807, 2806, 2809, 2811, 2804, 2806, 2808, 2881, 2881, 2810, 2806, 2804, 2811, 2873, 2808, 2702, 2723, 2818, 2807, 2813, 2984, 3000, 2814, 2984, 2814, 2809, 2816, 2808, 2812, 2815, 2808, 2816, 2807, 2810, 2813, 2813, 2810, 2822, 2809, 2814, 2811, 2723, 2817, 2808, 2818, 2820, 2812, 2818, 2812, 2817, 2817, 2812, 2808, 2820, 2818, 2813, 2814, 3000, 2821, 2820, 2822, 2816, 2812, 2820, 2816, 2820, 2813, 2822, 2814, 2821, 2811, 2811, 2821, 2892, 2823, 2818, 2817, 2823, 2817, 2718, 2718, 2817, 2723, 2818, 2823, 2819, 2823, 2824, 2819, 2824, 2719, 2819, 2827, 2821, 2825, 2891, 2821, 2827, 2725, 2824, 2823, 2719, 2824, 2725, 2826, 2825, 3001, 2827, 2825, 2826, 2827, 3019, 2891, 2384, 2297, 2828, 2729, 2830, 2828, 2384, 2828, 2829, 2828, 2830, 2829, 2391, 2736, 2735, 2391, 2735, 2306, 2729, 2741, 2830, 2829, 2830, 2831, 2832, 2384, 2833, 2833, 2384, 2829, 2833, 2829, 2831, 2387, 2832, 2894, 2736, 2399, 2740, 2391, 2399, 2736, 2741, 2834, 2830, 2831, 2830, 2835, 2830, 2834, 2835, 2833, 2836, 2832, 2831, 2836, 2833, 2835, 2836, 2831, 2837, 2832, 2836, 2894, 2832, 2837, 2740, 2399, 2407, 2740, 2407, 2313, 2741, 2744, 2834, 2834, 2744, 2835, 2744, 2838, 2835, 2836, 2841, 2840, 2838, 2836, 2835, 2841, 2836, 2838, 2836, 2840, 2837, 2840, 2902, 2837, 2843, 2838, 2842, 2842, 2838, 2744, 2415, 2839, 2421, 2841, 2838, 2843, 2841, 2843, 2840, 2840, 2843, 2844, 2421, 2839, 2905, 2840, 2844, 2902, 2843, 2842, 2420, 2843, 2420, 2844, 2844, 2845, 2902, 2844, 2420, 2427, 2844, 2427, 2845, 2845, 2445, 2846, 2845, 2427, 2445, 2348, 2847, 2441, 2847, 2747, 2848, 2847, 2849, 2441, 2849, 2443, 2441, 2848, 2849, 2847, 2849, 2850, 2443, 2850, 2445, 2443, 2848, 2851, 2849, 2851, 2850, 2849, 2846, 2445, 2852, 2852, 2445, 2850, 2851, 2853, 2850, 2853, 2852, 2850, 2854, 2752, 2751, 2848, 2856, 2851, 2855, 2752, 2854, 2520, 2855, 2854, 2520, 2854, 2449, 2449, 2854, 2751, 2848, 2753, 2856, 2760, 2752, 2857, 2752, 2855, 2857, 2520, 2525, 2855, 2525, 2857, 2855, 2851, 2856, 2853, 2859, 2858, 2856, 2856, 2858, 2853, 2859, 2856, 2861, 2859, 2860, 2858, 2767, 2760, 2863, 2760, 2857, 2863, 2861, 2862, 2859, 2859, 2862, 2860, 2862, 2922, 2860, 2919, 2539, 2866, 2919, 2866, 2863, 2919, 2863, 2857, 2770, 2458, 2354, 2767, 2863, 2770, 2864, 2861, 2771, 2770, 2866, 2458, 2866, 2463, 2458, 2770, 2863, 2866, 2862, 2861, 2864, 2865, 2862, 2864, 2869, 2862, 2865, 2862, 2869, 2922, 2866, 2539, 2463, 2922, 2869, 2870, 2778, 2867, 2864, 2778, 2864, 2771, 2867, 2466, 2865, 2864, 2867, 2865, 2865, 2466, 2868, 2868, 2466, 2468, 2869, 2865, 2868, 2868, 2468, 2469, 2869, 2868, 2870, 2870, 2868, 2469, 2778, 2470, 2867, 2867, 2470, 2466, 2805, 2876, 2872, 2876, 2805, 2873, 2872, 2874, 2800, 2875, 2874, 2872, 2876, 2875, 2872, 2877, 2876, 2873, 2874, 2927, 2871, 2800, 2874, 2871, 2878, 2927, 2875, 2927, 2874, 2875, 2876, 2878, 2875, 2879, 2878, 2876, 2877, 2879, 2876, 2880, 2881, 2808, 2882, 2810, 2881, 2811, 2887, 2873, 2883, 2882, 2881, 2883, 2881, 2880, 2887, 2884, 2873, 2873, 2884, 2877, 2879, 2886, 2878, 2878, 2886, 2885, 2884, 2879, 2877, 2886, 2879, 2884, 2808, 2815, 2880, 2822, 2815, 2816, 2822, 2882, 2815, 2882, 2880, 2815, 2882, 2822, 2810, 2811, 2892, 2887, 2882, 2883, 2880, 2887, 2888, 2884, 2886, 2888, 2889, 2886, 2884, 2888, 2889, 2885, 2886, 2888, 2892, 2890, 2888, 2887, 2892, 2888, 2890, 2889, 2889, 2890, 2893, 2889, 2893, 2928, 2892, 2821, 2891, 2892, 2891, 2890, 2890, 2891, 2893, 2891, 3024, 2893, 2475, 2387, 2894, 2475, 2894, 2895, 2896, 2475, 2895, 2837, 2898, 2894, 2895, 2898, 2899, 2895, 2894, 2898, 2895, 2899, 2896, 2896, 2900, 2897, 2900, 2896, 2899, 2491, 2839, 2405, 2837, 2902, 2898, 2902, 2903, 2898, 2898, 2903, 2899, 2839, 2491, 2905, 2503, 2905, 2491, 2505, 2493, 2931, 2932, 2505, 2931, 2899, 2903, 2901, 2903, 2904, 2901, 2902, 2908, 2903, 2906, 2903, 2908, 2933, 2505, 2932, 2903, 2906, 2904, 2902, 2845, 2907, 2908, 2902, 2907, 2909, 2908, 2907, 2906, 2908, 2909, 2910, 2907, 2846, 2846, 2907, 2845, 2911, 2907, 2910, 2907, 2911, 2909, 2911, 2512, 2909, 2852, 2912, 2910, 2846, 2852, 2910, 2913, 2912, 2853, 2912, 2852, 2853, 2912, 2914, 2515, 2912, 2515, 2911, 2912, 2911, 2910, 2911, 2515, 2517, 2913, 2914, 2912, 2911, 2517, 2512, 2915, 2532, 2515, 2915, 2515, 2914, 2915, 2914, 2913, 2913, 2917, 2915, 2913, 2853, 2917, 2916, 2525, 2534, 2857, 2525, 2916, 2858, 2917, 2853, 2915, 2918, 2532, 2918, 2915, 2917, 2916, 2534, 2919, 2919, 2857, 2916, 2858, 2920, 2917, 2860, 2920, 2858, 2917, 2920, 2921, 2918, 2923, 2532, 2532, 2923, 2541, 2921, 2918, 2917, 2921, 2923, 2918, 2539, 2919, 2534, 2860, 2922, 2920, 2920, 2922, 2921, 2921, 2924, 2923, 2924, 2921, 2922, 2541, 2926, 2925, 2547, 2541, 2925, 2870, 2925, 2926, 2870, 2926, 2922, 2926, 2541, 2923, 2924, 2926, 2923, 2922, 2926, 2924, 2870, 2469, 2547, 2870, 2547, 2925, 2871, 2927, 2928, 2885, 2927, 2878, 2928, 2927, 2885, 2871, 2928, 2574, 2928, 2929, 2574, 2885, 2889, 2928, 2929, 2935, 2574, 2928, 2893, 2929, 2929, 2893, 3024, 2935, 2929, 3024, 2554, 2897, 2555, 2897, 2900, 2555, 2900, 2930, 2555, 2930, 2900, 2899, 2555, 2930, 2931, 2555, 2931, 2493, 2930, 2901, 2931, 2930, 2899, 2901, 2904, 2931, 2901, 2932, 2931, 2904, 2906, 2932, 2904, 2933, 2932, 2906, 2933, 2934, 2556, 2909, 2933, 2906, 2934, 2933, 2909, 2934, 2512, 2556, 2909, 2512, 2934, 2935, 3031, 31, 5977, 2937, 2936, 5977, 3185, 2937, 2586, 2936, 2938, 2936, 2937, 2938, 2938, 2598, 2592, 2598, 2938, 2941, 2937, 2940, 2938, 2938, 2940, 2941, 3185, 2939, 2937, 2937, 2939, 2940, 2592, 2586, 2938, 2939, 2942, 2940, 2941, 2605, 2598, 2940, 2942, 2941, 2605, 2941, 2608, 2942, 2943, 2941, 2941, 2943, 2608, 2608, 2944, 2613, 2608, 2943, 2944, 3193, 2944, 2943, 2944, 2633, 2627, 2633, 2944, 2638, 2647, 2638, 2945, 2945, 2638, 2944, 3193, 3197, 2944, 2944, 3197, 2945, 2945, 2946, 2653, 2647, 2945, 2653, 2945, 3197, 2946, 2946, 2947, 2658, 2658, 2653, 2946, 2946, 2949, 2947, 2946, 3197, 2949, 2658, 2947, 2664, 2947, 2949, 2948, 3197, 2954, 2949, 2664, 2947, 2950, 2947, 2948, 2950, 2775, 2664, 2950, 2674, 2675, 2951, 2952, 2948, 2953, 2953, 2948, 2949, 2949, 2954, 2953, 2955, 2951, 2675, 2677, 2955, 2675, 2948, 2952, 2956, 2948, 2956, 2950, 2957, 2950, 2956, 2682, 2674, 2951, 2958, 2682, 2951, 2953, 2960, 2959, 2952, 2953, 2959, 2953, 2954, 2960, 2955, 2684, 2958, 2955, 2958, 2951, 2785, 2957, 2956, 2958, 2964, 2961, 2958, 2961, 2682, 2960, 2962, 2959, 2962, 2960, 2963, 2963, 2960, 2954, 2964, 2958, 2684, 2962, 2965, 2959, 2959, 2965, 2952, 2966, 2785, 2956, 2966, 2956, 2965, 2965, 2956, 2952, 2962, 2963, 2969, 2964, 2684, 2974, 2684, 2693, 2974, 2967, 2961, 2964, 2974, 2967, 2964, 2965, 2962, 2968, 2968, 2962, 2969, 2785, 2966, 2970, 2965, 2971, 2966, 2966, 2971, 2970, 2971, 2965, 2968, 2961, 2972, 2682, 2682, 2972, 2697, 2972, 2961, 2967, 2973, 2972, 2967, 2973, 2967, 2974, 2969, 2975, 2968, 2969, 2976, 2975, 2693, 2706, 2974, 2970, 2971, 2977, 2968, 2975, 2971, 2695, 2970, 2977, 2697, 2972, 2978, 2706, 2697, 2978, 2972, 2973, 2978, 2978, 2974, 2706, 2973, 2974, 2978, 2979, 2975, 2976, 2979, 2976, 2980, 2977, 2982, 2981, 2971, 2979, 2977, 2977, 2979, 2982, 2979, 2971, 2975, 2981, 2695, 2977, 2798, 2695, 2981, 2983, 2979, 2988, 2979, 2980, 2988, 2982, 2983, 2981, 2979, 2983, 2982, 2798, 2981, 2984, 2983, 2986, 2985, 2981, 2983, 2985, 2988, 2987, 2983, 2983, 2987, 2986, 2980, 2991, 2988, 2981, 2985, 2984, 2985, 2994, 2984, 2985, 2986, 2989, 2987, 2990, 2986, 2986, 2990, 2989, 2988, 2991, 2987, 2987, 2991, 2990, 2985, 2989, 2994, 2992, 2989, 2993, 2993, 2989, 2990, 2989, 2992, 2994, 2721, 2717, 2995, 2990, 2996, 2993, 2991, 2997, 2990, 2990, 2997, 2996, 2992, 2998, 2994, 2998, 2992, 3038, 2775, 2950, 2999, 2950, 2957, 2999, 2999, 2957, 2785, 2984, 2994, 3000, 2825, 2821, 3000, 2998, 3001, 2994, 2994, 3001, 3000, 3001, 2825, 3000, 2720, 3003, 3002, 2721, 3006, 2720, 2720, 3006, 3003, 2995, 3004, 3007, 2722, 3004, 2995, 3004, 2725, 2724, 2725, 3004, 2719, 2719, 3004, 2722, 3003, 3005, 3002, 3010, 3005, 3003, 3010, 3003, 3006, 3005, 3004, 2724, 3002, 3005, 2724, 3004, 3005, 3007, 3010, 3007, 3005, 3006, 3011, 3010, 3007, 3010, 3047, 3010, 3011, 3013, 3014, 3047, 3010, 3014, 3010, 3013, 3065, 3066, 3014, 3013, 3065, 3014, 2826, 3009, 3015, 3001, 3009, 2826, 2826, 3015, 2827, 3015, 3016, 2827, 2827, 3016, 3019, 3009, 3008, 3017, 3015, 3009, 3017, 3018, 3015, 3017, 3015, 3018, 3016, 3016, 3018, 3019, 3008, 3012, 3017, 3012, 3020, 3017, 3020, 3021, 3018, 3020, 3018, 3017, 3021, 3023, 3018, 3018, 3023, 3019, 3023, 3022, 3019, 3023, 3026, 3022, 3019, 3024, 2891, 3019, 3025, 3024, 3019, 3022, 3025, 3026, 3027, 3025, 3022, 3026, 3025, 3027, 3028, 3025, 3025, 3029, 3024, 3031, 3024, 3029, 3025, 3028, 3029, 3029, 3032, 3031, 3028, 3030, 3029, 3029, 3030, 3032, 3030, 3111, 3032, 3024, 3031, 2935, 3032, 3033, 3031, 3033, 3034, 3031, 3031, 3034, 61, 3006, 2721, 2995, 2993, 3035, 3040, 2992, 2993, 3040, 2996, 3036, 2993, 2993, 3036, 3035, 2997, 3037, 2996, 2996, 3037, 3036, 2992, 3040, 3038, 2998, 3038, 3039, 3047, 3006, 2995, 3047, 2995, 3007, 3041, 3040, 3036, 3036, 3040, 3035, 3041, 3036, 3037, 3042, 3040, 3043, 3042, 3038, 3040, 3041, 3044, 3040, 3044, 3045, 3046, 3044, 3041, 3045, 3041, 3037, 3045, 3042, 3008, 3009, 3042, 3009, 3039, 3042, 3039, 3038, 3011, 3006, 3047, 3043, 3049, 3042, 3043, 3040, 3049, 3040, 3044, 3050, 3040, 3050, 3049, 3051, 3050, 3044, 3051, 3044, 3052, 3046, 3053, 3044, 3053, 3052, 3044, 3042, 3012, 3008, 3042, 3054, 3012, 3042, 3049, 3054, 3079, 3012, 3054, 3055, 3054, 3049, 3050, 3055, 3049, 3046, 3080, 3053, 3047, 3057, 3056, 3047, 3056, 3011, 3011, 3056, 3013, 3056, 3057, 3058, 3047, 3014, 3057, 3014, 3059, 3057, 3058, 3057, 3060, 3060, 3057, 3059, 3048, 3058, 3061, 3058, 3060, 3061, 3062, 3059, 3014, 3062, 3067, 3063, 3062, 3063, 3059, 3059, 3063, 3060, 3064, 3060, 3063, 3052, 3064, 3051, 3052, 3061, 3064, 3061, 3060, 3064, 3065, 3013, 3056, 3066, 3062, 3014, 3066, 3067, 3062, 3056, 3068, 3065, 3056, 3058, 3068, 3065, 3068, 3070, 3058, 3069, 3068, 3068, 3069, 3070, 3058, 4176, 3069, 3065, 3070, 3066, 3071, 3070, 3069, 4176, 4178, 3069, 3069, 4178, 3071, 3103, 3066, 3073, 3070, 3071, 3073, 4178, 3072, 3071, 3071, 3072, 3073, 3070, 3073, 3066, 3074, 3073, 3072, 3072, 4180, 3074, 3074, 3075, 3073, 3074, 4180, 3075, 3039, 3009, 3001, 2998, 3039, 3001, 3077, 3046, 3045, 3076, 3077, 3045, 3078, 3077, 3076, 3055, 3079, 3054, 3051, 3082, 3050, 3046, 3077, 3080, 3020, 3079, 3021, 3079, 3089, 3021, 3055, 3083, 3079, 3083, 3089, 3079, 3050, 3081, 3055, 3081, 3083, 3055, 3082, 3081, 3050, 3077, 3084, 3080, 3089, 3023, 3021, 3077, 3083, 3084, 3082, 3084, 3081, 3083, 3081, 3084, 3083, 3098, 3089, 3083, 3077, 3098, 3012, 3079, 3020, 3085, 3063, 3067, 3091, 3085, 3067, 3064, 3063, 3085, 3051, 3064, 3082, 3085, 3086, 3064, 3064, 3086, 3082, 3082, 3088, 3084, 3082, 3086, 3088, 3086, 3087, 3088, 3089, 3026, 3023, 3087, 3090, 3088, 3091, 3067, 3066, 3091, 3092, 3085, 3085, 3092, 3086, 3101, 3091, 3066, 3092, 3091, 3093, 3086, 3093, 3087, 3092, 3093, 3086, 3094, 3091, 3101, 3093, 3091, 3094, 3087, 3093, 3095, 3093, 3094, 3095, 3087, 3095, 3090, 3066, 3103, 3101, 3075, 3105, 3073, 3078, 3096, 3077, 3077, 3096, 3097, 4186, 3097, 3096, 3077, 3097, 3098, 3100, 3089, 3098, 3099, 3100, 3098, 3030, 3100, 3099, 3098, 3097, 3099, 3089, 3100, 3026, 3100, 3027, 3026, 3100, 3028, 3027, 3028, 3100, 3030, 3090, 3095, 3113, 3102, 3094, 3101, 3095, 3094, 3102, 3102, 3101, 3104, 3104, 3101, 3103, 3073, 3117, 3103, 3073, 3105, 3117, 4186, 3099, 3097, 4189, 3106, 3099, 3109, 3106, 4189, 3118, 3109, 4189, 3106, 3030, 3099, 3030, 3106, 3107, 3106, 3109, 3107, 3109, 3108, 3107, 3111, 3030, 3107, 3095, 3102, 3113, 3115, 3102, 3104, 3113, 3102, 3115, 3113, 3115, 3114, 3113, 3114, 3110, 3112, 3114, 3115, 3110, 3114, 3112, 3116, 3104, 3103, 3139, 3104, 3116, 3104, 3139, 3115, 3138, 3139, 3116, 3103, 3117, 3116, 3244, 3118, 4189, 3119, 3118, 3244, 3120, 3118, 3119, 3119, 3121, 3120, 3121, 3122, 3120, 3123, 3122, 3121, 3109, 3118, 3108, 3124, 3108, 3118, 3118, 3120, 3124, 3125, 4199, 3129, 3130, 3124, 3122, 3124, 3120, 3122, 3125, 3129, 3126, 3122, 3133, 3130, 3107, 3108, 3111, 3110, 3112, 3127, 3128, 3032, 3111, 3108, 3124, 3128, 3111, 3108, 3128, 3129, 3112, 3115, 3134, 3129, 3115, 4199, 3127, 3129, 3127, 3112, 3129, 3128, 3124, 3130, 3126, 3129, 3131, 3130, 3133, 3132, 3139, 3134, 3115, 3128, 3130, 3033, 3032, 3128, 3033, 3135, 3134, 3139, 3134, 3136, 3129, 3134, 3135, 3136, 3129, 3137, 3131, 3136, 3137, 3129, 3130, 3132, 3033, 3136, 3135, 3141, 3140, 3139, 3138, 3135, 3139, 3141, 3139, 3140, 3141, 3142, 3137, 3136, 3138, 3143, 3140, 3142, 3160, 3144, 3116, 3117, 3138, 3138, 3145, 3143, 3144, 3160, 3165, 3138, 3117, 3145, 3105, 3178, 3117, 3117, 3178, 3145, 3122, 3146, 3133, 3123, 3146, 3122, 3147, 3146, 3123, 3146, 3132, 3133, 3146, 3147, 3151, 3150, 3146, 3151, 3132, 3148, 3033, 3132, 3146, 3148, 3146, 3150, 3148, 3149, 3034, 3148, 3034, 3033, 3148, 3150, 3154, 3148, 61, 3034, 3149, 3152, 3141, 3140, 3136, 3141, 3152, 3153, 3136, 3152, 3142, 3136, 3153, 3149, 3148, 3154, 3149, 3154, 61, 3154, 3157, 61, 3154, 3150, 3155, 3150, 3158, 3155, 3151, 4222, 3150, 4222, 3158, 3150, 3152, 3140, 3159, 3152, 3159, 3153, 3154, 3155, 3157, 3155, 3163, 3157, 3155, 3158, 3163, 3156, 3158, 4222, 3143, 3159, 3140, 3153, 3159, 3160, 3160, 3142, 3153, 3161, 3156, 3162, 3161, 3163, 3158, 3156, 3161, 3158, 3143, 3145, 3159, 3160, 3159, 3164, 3165, 3160, 3164, 3161, 3162, 3166, 3163, 3167, 3157, 3163, 3161, 3167, 3167, 3161, 3168, 3166, 3169, 3161, 3169, 3168, 3161, 3157, 3167, 72, 3168, 3169, 74, 3145, 3164, 3159, 3145, 3170, 3164, 3170, 3171, 3164, 3164, 3172, 3165, 3171, 3172, 3164, 3174, 3171, 3170, 3173, 3174, 3170, 3172, 3171, 3175, 3175, 3171, 3174, 3174, 3173, 3176, 3175, 3174, 3176, 3177, 3175, 3176, 3168, 74, 3167, 3145, 3178, 3170, 3178, 3179, 3173, 3170, 3178, 3173, 3180, 3179, 3178, 3173, 3179, 3176, 3177, 3176, 3179, 3180, 3177, 3179, 3181, 3184, 3185, 3183, 3182, 3181, 3181, 3182, 3184, 3184, 3186, 3185, 3182, 3187, 3184, 3184, 3187, 3186, 3183, 3188, 3182, 3182, 3188, 3187, 3185, 3191, 2939, 2939, 3191, 2942, 3186, 3189, 3185, 3185, 3189, 3191, 3187, 3189, 3186, 3188, 3190, 3187, 3187, 3190, 3189, 3190, 3192, 3189, 3189, 3192, 3191, 3191, 2943, 2942, 3191, 3192, 2943, 3192, 3193, 2943, 3192, 3194, 3193, 3194, 3195, 3193, 3193, 3196, 3197, 3193, 3195, 3196, 3196, 3198, 3197, 3196, 3195, 3198, 3197, 3198, 2954, 3198, 2963, 2954, 2963, 3199, 2969, 3198, 3199, 2963, 3200, 2976, 2969, 3200, 2969, 3199, 3199, 3202, 3200, 3199, 3254, 3202, 2980, 2976, 3200, 2980, 3200, 3201, 3200, 3202, 3201, 3254, 3257, 3202, 3201, 3206, 2991, 3202, 3203, 3201, 3201, 3203, 3206, 3202, 3205, 3203, 3202, 3257, 3205, 2991, 2980, 3201, 3206, 3203, 3204, 3204, 3203, 3205, 3206, 3207, 2991, 3204, 3208, 3206, 3206, 3208, 3207, 3204, 3209, 3208, 3204, 3205, 3209, 3214, 3208, 3209, 3210, 3209, 3205, 3210, 3211, 3209, 3209, 3211, 3214, 3207, 3214, 2997, 3207, 2997, 2991, 3208, 3214, 3207, 3215, 3213, 4169, 3211, 3218, 3214, 3211, 3268, 3218, 3225, 3214, 3218, 3217, 3218, 3268, 3219, 3220, 3215, 3215, 3220, 3221, 3222, 3220, 3219, 3221, 3220, 3223, 3224, 3221, 3223, 3214, 3225, 3216, 3216, 3225, 3228, 3223, 3220, 3222, 3229, 3223, 3222, 3218, 3226, 3225, 3218, 3217, 3227, 3227, 3226, 3218, 3225, 3226, 3228, 3228, 3226, 3234, 3230, 3223, 3229, 3224, 3223, 3230, 3231, 3230, 3229, 3224, 3230, 3236, 3236, 3230, 3235, 3232, 3226, 3227, 3227, 3286, 3233, 3240, 3232, 3227, 3233, 3240, 3227, 3226, 3232, 3234, 3230, 3231, 3235, 3237, 3234, 3232, 3236, 3235, 3239, 3245, 3237, 3232, 3244, 3237, 3245, 3232, 3240, 3245, 3238, 3239, 3235, 4195, 3239, 3238, 3286, 3241, 3233, 3241, 3240, 3233, 3241, 3242, 3240, 3242, 3241, 3243, 3246, 3239, 4195, 3119, 3244, 3245, 3240, 3242, 3245, 3245, 3247, 3119, 3242, 3249, 3247, 3242, 3247, 3245, 3242, 3243, 3249, 3183, 3251, 3188, 3183, 3250, 3251, 3188, 3251, 3190, 3190, 3251, 3192, 3250, 3304, 3251, 3251, 3252, 3192, 3251, 3253, 3252, 3251, 3304, 3253, 3305, 3253, 3304, 3192, 3252, 3194, 3194, 3252, 3195, 3306, 3252, 3253, 3252, 3306, 3195, 3195, 3307, 3198, 3195, 3349, 3307, 3198, 3254, 3199, 3307, 3254, 3198, 3307, 3255, 3254, 3255, 3310, 3254, 3254, 3310, 3256, 3254, 3261, 3257, 3256, 3261, 3254, 3256, 3310, 3258, 3261, 3256, 3258, 3258, 3259, 3261, 3259, 3265, 3261, 3259, 3268, 3265, 3260, 3205, 3257, 3260, 3257, 3261, 3261, 3265, 3260, 3265, 3264, 3260, 3266, 3263, 3262, 3267, 3266, 3262, 3265, 3268, 3264, 3268, 3211, 3264, 3270, 3269, 3276, 3270, 3272, 3271, 3269, 3270, 3271, 3272, 3262, 3263, 3272, 3263, 3271, 3260, 3264, 3210, 3205, 3260, 3210, 3273, 3270, 3276, 3272, 3270, 3273, 3262, 3272, 3267, 3210, 3264, 3211, 3274, 3273, 3277, 3272, 3273, 3280, 3280, 3273, 3274, 3272, 3280, 3267, 3212, 3213, 3275, 3275, 3276, 3269, 3271, 3275, 3269, 3275, 3213, 3276, 3276, 3213, 3215, 3273, 3276, 3277, 3276, 3215, 3277, 3278, 3267, 3281, 3277, 3279, 3274, 3274, 3279, 3280, 3267, 3280, 3281, 3281, 3280, 3279, 3281, 3282, 3278, 3288, 3279, 3224, 3281, 3279, 3285, 3282, 3281, 3285, 3277, 3221, 3279, 3277, 3215, 3221, 3279, 3221, 3224, 3285, 3287, 3283, 3285, 3279, 3288, 3282, 3285, 3284, 3284, 3285, 3283, 3288, 3224, 3236, 3227, 3217, 3328, 3286, 3227, 3328, 3332, 3286, 3328, 3332, 3293, 3286, 3297, 3294, 3287, 3288, 3236, 3289, 3285, 3288, 3289, 3285, 3289, 3290, 3285, 3290, 3287, 3287, 3290, 3297, 3236, 3291, 3289, 3236, 3239, 3291, 3291, 3239, 3301, 3291, 3301, 3289, 3293, 3292, 3286, 3286, 3292, 3241, 3292, 3293, 3298, 3294, 3297, 3295, 3293, 3300, 3298, 3290, 3289, 3296, 3296, 3302, 3297, 3296, 3297, 3290, 3292, 3298, 3241, 3241, 3298, 3243, 3297, 3302, 3299, 3297, 3299, 3295, 3298, 3300, 3243, 3301, 3239, 3246, 3289, 3301, 3296, 3296, 3301, 3302, 3301, 3246, 3302, 3302, 3246, 3248, 3299, 3302, 3248, 3250, 6002, 3305, 3305, 3304, 3250, 3253, 3305, 3306, 3306, 3349, 3195, 3350, 3307, 3349, 3350, 3309, 3307, 3307, 3308, 3255, 3307, 3309, 3308, 3308, 3310, 3255, 3311, 3310, 3308, 3308, 3309, 3312, 3311, 3308, 3312, 3312, 3309, 3361, 3312, 3313, 3311, 3313, 3316, 3311, 3312, 3359, 3313, 3359, 3322, 3313, 3315, 3367, 3314, 3310, 3316, 3258, 3316, 3259, 3258, 3310, 3311, 3316, 3320, 3315, 3314, 3367, 3315, 3317, 3316, 3318, 3259, 3318, 3268, 3259, 3316, 3313, 3318, 3317, 3315, 3320, 3321, 3317, 3320, 3313, 3322, 3318, 3319, 3263, 3320, 3320, 3263, 3266, 3319, 3320, 3314, 3321, 3266, 3267, 3320, 3266, 3321, 3319, 3271, 3263, 3319, 4243, 3271, 3323, 3268, 3322, 3318, 3322, 3268, 3323, 3217, 3268, 3321, 3267, 3278, 3278, 3282, 3324, 3278, 3324, 3321, 3326, 3324, 3282, 3381, 3323, 3371, 3328, 3217, 3323, 3323, 3381, 3328, 3374, 3317, 3329, 3321, 3324, 3327, 3327, 3324, 3326, 3321, 3327, 3317, 3284, 3283, 3326, 3327, 3326, 3283, 3331, 3283, 3287, 3327, 3283, 3331, 3317, 3327, 3329, 3327, 3331, 3329, 3282, 3284, 3326, 3332, 3381, 3450, 3328, 3381, 3332, 3325, 3329, 3330, 3334, 3335, 3329, 3330, 3329, 3335, 3331, 3287, 3294, 3329, 3331, 3334, 3293, 3332, 3333, 3333, 3339, 3293, 3340, 3339, 3333, 3337, 3335, 3334, 3330, 3335, 3336, 3336, 3335, 3337, 3338, 3336, 3337, 3339, 3340, 3342, 3333, 3400, 3340, 3331, 3341, 3334, 3331, 3294, 3341, 3334, 3341, 3337, 3341, 3294, 3295, 3337, 3341, 3295, 3338, 3337, 4332, 3293, 3339, 3300, 4332, 3337, 3343, 3340, 3344, 3342, 3344, 3340, 3345, 3295, 3299, 3343, 3337, 3295, 3343, 3342, 4295, 4310, 3342, 3344, 4295, 4310, 3300, 3339, 3342, 4310, 3339, 3300, 4310, 4264, 3345, 3402, 3344, 3346, 3347, 6002, 3346, 6013, 3347, 6002, 3347, 3305, 3347, 3348, 3305, 3305, 3404, 3306, 3348, 3404, 3305, 3404, 3349, 3306, 3349, 3410, 3350, 3410, 3352, 3350, 3352, 3351, 3350, 3350, 3351, 3309, 3354, 3358, 3353, 3358, 3354, 3355, 3354, 3356, 3355, 3351, 3361, 3309, 3351, 3352, 3357, 3361, 3351, 3357, 3357, 3352, 3353, 3359, 3312, 3361, 3357, 3358, 3361, 3358, 3359, 3361, 3353, 3358, 3357, 3358, 3355, 3359, 3355, 3356, 3359, 3362, 3363, 3424, 3364, 3363, 3362, 3424, 3363, 3425, 3425, 3363, 3364, 3364, 3370, 3365, 3425, 3364, 3365, 3360, 3425, 3366, 3425, 3365, 3366, 3314, 3367, 3368, 3362, 3424, 3368, 3370, 3367, 3317, 3368, 3367, 3369, 3369, 3367, 3370, 3362, 3369, 3364, 3368, 3369, 3362, 3364, 3369, 3370, 3370, 3317, 3374, 3368, 3319, 3314, 3368, 4350, 3319, 3371, 3356, 3430, 3427, 3430, 3356, 3359, 3356, 3322, 3372, 3360, 3366, 3322, 3356, 3371, 3377, 3376, 3372, 3373, 3365, 3370, 3366, 3365, 3373, 3323, 3322, 3371, 3372, 3366, 3377, 3370, 3374, 3373, 3373, 3374, 3378, 3430, 3375, 3371, 3371, 3375, 3381, 3382, 3376, 3384, 3379, 3373, 3378, 3366, 3373, 3377, 3377, 3373, 3379, 3376, 3377, 3384, 3377, 3379, 3384, 3378, 3380, 3379, 3379, 3380, 3384, 3374, 3329, 3325, 3374, 3325, 3378, 3378, 3325, 3380, 3375, 3450, 3381, 3385, 3332, 3450, 3382, 3384, 3383, 3336, 3390, 3380, 3384, 3380, 3390, 3325, 3330, 3380, 3380, 3330, 3336, 3385, 3386, 3332, 3386, 3387, 3332, 3478, 3387, 3386, 3387, 3333, 3332, 3388, 3391, 3389, 3478, 3395, 3387, 3383, 3384, 3388, 3384, 3390, 3391, 3384, 3391, 3388, 3390, 3336, 3392, 3393, 3390, 3392, 3391, 3390, 3393, 3391, 3393, 3389, 3389, 3393, 3476, 3387, 3394, 3333, 3387, 3395, 3394, 3336, 3338, 3392, 3392, 3338, 3393, 3476, 3393, 3484, 3400, 3333, 3394, 3395, 3396, 3394, 3393, 3338, 3397, 3484, 3393, 3398, 3393, 3397, 3398, 3338, 4332, 3397, 3396, 3399, 3394, 3401, 3400, 3394, 3400, 3345, 3340, 3400, 3401, 3345, 3399, 3401, 3394, 3401, 3402, 3345, 3399, 3402, 3401, 6013, 3403, 3347, 3947, 3403, 6013, 3500, 3403, 3947, 3403, 3348, 3347, 3403, 3747, 3348, 3501, 3502, 3404, 3404, 3502, 3349, 3405, 3410, 3349, 3405, 3407, 3410, 3412, 3406, 3408, 3408, 3407, 3409, 3408, 3406, 3407, 3408, 3414, 3412, 3406, 3411, 3410, 3406, 3410, 3407, 3412, 3411, 3406, 3416, 3411, 3412, 3409, 3511, 3408, 3416, 3412, 3413, 3414, 3408, 3511, 3427, 3413, 3412, 3414, 3427, 3412, 3511, 3556, 3414, 3411, 3354, 3353, 3411, 3353, 3410, 3410, 3353, 3352, 3411, 3416, 3354, 3417, 3415, 3420, 3417, 3418, 3415, 3413, 3354, 3416, 3354, 3413, 3356, 3419, 3417, 3422, 3420, 3415, 3424, 3420, 3421, 3417, 3417, 3421, 3422, 3421, 3360, 3422, 3420, 3425, 3421, 3424, 3425, 3420, 3421, 3425, 3360, 3524, 3423, 3539, 3418, 3423, 3524, 3415, 3426, 3424, 3424, 3426, 3368, 3368, 3426, 4350, 3427, 3356, 3413, 3556, 3427, 3414, 3428, 3429, 3427, 3556, 3428, 3427, 3430, 3427, 3429, 3439, 3430, 3429, 3428, 3437, 3429, 3437, 3439, 3429, 3556, 3431, 3428, 3431, 3437, 3428, 3432, 3419, 3422, 3417, 3419, 3433, 3433, 3419, 3432, 3433, 3418, 3417, 3422, 3435, 3432, 3433, 3432, 3435, 3433, 3435, 3434, 3434, 3418, 3433, 3436, 3434, 3435, 3422, 3360, 3372, 3418, 3434, 3423, 3422, 3372, 3376, 3434, 3436, 3423, 3423, 3436, 3566, 3438, 3437, 3431, 3439, 3375, 3430, 3438, 3441, 3437, 3437, 3440, 3439, 3441, 3440, 3437, 3443, 3436, 3435, 3442, 3443, 3435, 3448, 3442, 3435, 3443, 3442, 3444, 3443, 3444, 3445, 3448, 3446, 3442, 3444, 3442, 3446, 3422, 3376, 3448, 3422, 3448, 3435, 3436, 3443, 3447, 3447, 3443, 3445, 3448, 3376, 3382, 3447, 3445, 3449, 3566, 3436, 3633, 3436, 3447, 3633, 3633, 3449, 3637, 3447, 3449, 3633, 3440, 3451, 3439, 3441, 3451, 3440, 3451, 3450, 3439, 3441, 3669, 3451, 3451, 3452, 3450, 3669, 3672, 3451, 3452, 3385, 3450, 3451, 3464, 3452, 3672, 3464, 3451, 3375, 3439, 3450, 3446, 3453, 3444, 3454, 3445, 3444, 3453, 3454, 3444, 3460, 3455, 3453, 3455, 3454, 3453, 3460, 3456, 3455, 3455, 3456, 3470, 3470, 3468, 3455, 3448, 3382, 3457, 3448, 3457, 3446, 3446, 3457, 3453, 3458, 3454, 3459, 3458, 3449, 3454, 3449, 3445, 3454, 3453, 3457, 3460, 3454, 3455, 3459, 3457, 3382, 3383, 3460, 3457, 3383, 3459, 3455, 3461, 3456, 3460, 3462, 3460, 3383, 3462, 3461, 3455, 3463, 3455, 3468, 3463, 3449, 3458, 3637, 3464, 3466, 3452, 3465, 3385, 3452, 3466, 3465, 3452, 3464, 3467, 3466, 3466, 3473, 3465, 3467, 3473, 3466, 3474, 3469, 3456, 3470, 3456, 3469, 3385, 3465, 3386, 3469, 3474, 3471, 3470, 3469, 3471, 3472, 3470, 3471, 3468, 3470, 3472, 3465, 3478, 3386, 3465, 3473, 3478, 3462, 3383, 3388, 3463, 3468, 3475, 3462, 3388, 3474, 3462, 3474, 3456, 3474, 3388, 3389, 3476, 3474, 3389, 3474, 3476, 3471, 3472, 3471, 3477, 3471, 3476, 3477, 3475, 3468, 3472, 3477, 3475, 3472, 3473, 3480, 3478, 3467, 3719, 3473, 3473, 3719, 3480, 3463, 3475, 3720, 3475, 3477, 3479, 3479, 3477, 3476, 3478, 3481, 3395, 3480, 3482, 3478, 3478, 3482, 3481, 3476, 3484, 3479, 3481, 3485, 3395, 3395, 3485, 3396, 3480, 3483, 3482, 3481, 3486, 3485, 3487, 3486, 3482, 3482, 3486, 3481, 3487, 3482, 3488, 3488, 3482, 3483, 3484, 3398, 3491, 3485, 3489, 3396, 3396, 3489, 3399, 3486, 3490, 3485, 3485, 3490, 3489, 3493, 3490, 3486, 3491, 3398, 4354, 3489, 4335, 3399, 3490, 3492, 3489, 3489, 3492, 4335, 3492, 3490, 3493, 3494, 3486, 3487, 3494, 3493, 3486, 3487, 3495, 3494, 3487, 3496, 3495, 3487, 3488, 3496, 3495, 3497, 3494, 3493, 3494, 4364, 3497, 4365, 3494, 4365, 4364, 3494, 3497, 3495, 3499, 4365, 3497, 115, 3497, 3498, 115, 3499, 3498, 3497, 3499, 134, 3498, 3403, 3500, 3747, 3747, 3404, 3348, 3404, 3748, 3501, 3501, 3504, 3502, 3349, 3502, 3405, 3501, 3505, 3504, 3503, 3407, 3502, 3502, 3407, 3405, 3503, 3502, 3504, 3407, 3503, 3409, 3503, 3508, 3409, 3504, 3506, 3503, 3506, 3508, 3503, 3510, 3506, 3504, 3510, 3504, 3507, 3507, 3504, 3505, 3508, 3511, 3409, 3506, 3509, 3508, 3510, 3509, 3506, 3512, 3508, 3509, 3513, 3512, 3510, 3510, 3512, 3509, 3510, 3514, 3513, 3510, 3507, 3514, 3511, 3508, 3520, 3511, 3520, 3556, 3418, 3516, 3415, 3513, 3514, 3527, 3512, 3518, 3517, 3512, 3517, 3508, 3513, 3518, 3512, 3513, 3515, 3518, 3515, 3519, 3518, 3527, 3515, 3513, 3519, 3515, 3527, 3508, 3517, 3520, 3516, 3522, 3521, 3516, 3521, 3415, 3522, 3523, 3521, 3522, 3524, 3525, 3522, 3516, 3524, 3523, 3522, 3525, 3526, 3523, 3525, 3418, 3524, 3516, 3518, 3528, 3517, 3529, 3528, 3518, 3529, 3518, 3530, 3530, 3518, 3519, 3527, 3538, 3519, 3538, 3530, 3519, 3528, 3531, 3517, 3529, 3531, 3528, 3415, 3521, 3426, 3525, 3524, 3533, 3533, 3534, 3525, 3534, 3526, 3525, 3523, 3526, 3535, 3535, 3526, 3534, 3533, 3524, 3536, 3529, 3530, 3537, 3538, 3537, 3530, 3524, 3539, 3536, 3569, 3536, 3531, 3569, 3531, 3570, 3536, 3539, 3531, 3531, 3540, 3570, 3531, 3529, 3540, 3529, 3537, 3540, 3538, 3532, 3537, 3532, 3545, 3537, 3521, 3541, 3426, 3523, 3776, 3541, 3523, 3541, 3521, 3534, 3542, 3543, 3533, 3542, 3534, 3535, 3534, 3543, 3776, 3523, 3535, 3776, 3535, 3547, 3547, 3535, 3543, 3533, 3536, 3542, 3536, 3569, 3542, 3542, 3569, 3579, 3540, 3537, 3544, 3537, 3545, 3544, 3543, 3542, 3546, 3546, 3547, 3543, 3546, 3542, 3548, 3548, 3549, 3546, 3547, 3546, 3549, 3550, 3547, 3549, 3542, 3579, 3548, 3549, 3548, 4382, 4382, 3551, 3549, 3551, 3550, 3549, 4382, 3548, 3552, 3553, 3554, 3555, 3553, 3559, 3557, 3554, 3553, 3557, 3558, 3554, 3557, 3558, 3557, 3561, 3557, 3560, 3561, 3557, 3559, 3560, 3560, 3559, 3613, 3565, 3520, 3517, 3562, 3520, 3565, 3556, 3520, 3562, 3561, 3560, 3563, 3556, 3564, 3431, 3564, 3556, 3562, 3560, 3613, 3568, 3563, 3560, 3568, 3517, 3531, 3565, 3561, 3574, 3572, 3565, 3567, 3562, 3561, 3563, 3574, 3574, 3563, 3578, 3566, 3539, 3423, 3562, 3567, 3564, 3568, 3578, 3563, 3531, 3539, 3565, 3572, 3571, 3532, 3571, 3545, 3532, 3565, 3539, 3575, 3569, 3570, 3573, 3573, 3570, 3540, 3572, 3828, 3571, 3565, 3575, 3567, 3569, 3573, 3577, 3577, 3573, 3576, 3539, 3566, 3575, 3632, 3577, 3576, 3540, 3580, 3573, 3540, 3544, 3580, 3579, 3569, 3581, 3582, 3576, 3580, 3580, 3576, 3573, 3569, 3577, 3581, 3576, 3582, 3632, 3548, 3579, 3583, 3583, 3584, 3548, 3583, 3579, 3586, 3587, 3584, 3580, 3587, 3580, 3588, 3584, 3583, 3580, 3583, 3586, 3580, 3580, 3544, 3585, 3580, 3585, 3588, 3588, 3585, 3589, 3586, 3581, 3591, 3579, 3581, 3586, 3587, 3588, 3590, 3580, 3586, 3582, 3586, 3591, 3582, 3590, 3588, 3589, 3548, 3584, 3552, 3584, 3592, 3552, 3584, 3587, 4387, 3587, 3593, 4387, 3590, 3593, 3587, 3590, 3594, 3593, 3596, 3800, 3595, 3596, 3595, 3597, 3595, 3599, 3597, 3599, 3598, 3597, 3800, 3600, 3555, 3555, 3600, 3553, 3601, 3600, 3596, 3600, 3800, 3596, 3596, 3597, 3601, 3598, 3601, 3597, 3553, 3600, 3559, 3559, 3600, 3604, 3600, 3601, 3604, 3615, 3438, 3610, 3441, 3438, 3615, 3606, 3872, 3605, 3601, 3607, 3608, 3602, 3872, 3606, 3602, 3606, 3609, 3438, 3431, 3610, 3559, 3604, 3613, 3603, 3612, 3611, 3603, 3611, 3605, 3611, 3614, 3605, 3604, 3601, 3613, 3613, 3601, 3608, 3605, 3614, 3606, 3614, 3616, 3606, 3617, 3441, 3615, 3617, 3608, 3607, 3617, 3622, 3608, 3606, 3616, 3609, 3431, 3564, 3610, 3610, 3564, 3619, 3612, 3618, 3611, 3568, 3613, 3627, 3618, 3620, 3614, 3611, 3618, 3614, 3619, 3621, 3615, 3610, 3619, 3615, 3613, 3608, 3627, 3614, 3620, 3616, 3615, 3621, 3617, 3621, 3622, 3617, 3627, 3608, 3622, 3616, 3631, 3623, 3616, 3623, 3609, 3564, 3567, 3619, 3567, 3624, 3619, 3578, 3568, 3625, 3624, 3626, 3619, 3568, 3627, 3625, 3628, 3625, 3627, 3629, 3622, 3621, 3629, 3621, 3626, 3626, 3621, 3619, 3627, 3629, 3630, 3627, 3622, 3629, 3628, 3627, 3630, 3567, 3575, 3624, 3566, 3633, 3575, 3624, 3633, 3626, 3575, 3633, 3624, 3626, 3633, 3635, 3626, 3635, 3629, 3635, 3633, 3634, 3630, 3629, 3636, 3629, 3635, 3636, 3639, 3628, 3636, 3628, 3630, 3636, 3634, 3633, 3638, 3638, 3633, 3637, 3634, 3638, 3697, 3632, 3640, 3577, 3640, 3581, 3577, 3632, 3582, 3640, 3635, 3642, 3641, 3634, 3642, 3635, 3641, 3636, 3635, 3642, 3634, 3697, 3636, 3643, 3639, 3581, 3640, 3591, 3640, 3582, 3591, 3646, 3644, 3645, 3646, 3647, 3648, 3645, 3647, 3646, 3636, 3641, 3646, 3641, 3644, 3646, 3641, 3649, 3644, 3643, 3636, 3646, 3643, 3646, 3648, 3641, 3642, 3650, 3641, 3650, 3649, 3642, 3704, 3650, 3645, 3651, 3647, 3645, 3644, 3651, 3644, 3649, 3652, 3651, 3644, 3652, 3652, 3650, 3653, 3649, 3650, 3652, 3651, 3652, 3654, 3654, 3652, 3653, 3650, 3704, 3653, 3654, 3653, 3704, 3655, 3656, 3657, 3598, 3655, 3658, 3655, 3659, 3658, 3598, 3599, 3655, 3655, 3657, 3659, 3657, 3660, 3659, 3656, 3919, 3657, 3658, 3607, 3601, 3598, 3658, 3601, 3607, 3658, 3662, 3658, 3659, 3662, 3662, 3659, 3671, 3664, 3663, 3665, 3666, 3664, 3665, 3659, 3660, 3671, 3657, 3667, 3660, 3919, 3667, 3657, 3668, 3661, 3609, 3661, 3602, 3609, 3607, 3662, 3674, 3674, 3662, 3670, 3664, 3668, 3663, 3663, 3668, 3678, 3661, 3668, 3664, 3672, 3669, 3679, 3662, 3671, 3670, 3663, 3678, 3665, 3666, 3665, 3691, 3660, 3673, 3671, 3667, 3682, 3660, 3660, 3682, 3673, 3617, 3674, 3441, 3617, 3607, 3674, 3668, 3676, 3675, 3676, 3668, 3609, 3674, 3677, 3669, 3441, 3674, 3669, 3674, 3670, 3677, 3668, 3675, 3678, 3669, 3677, 3679, 3670, 3671, 3679, 3677, 3670, 3679, 3665, 3678, 3691, 3679, 3680, 3672, 3671, 3680, 3679, 3671, 3681, 3680, 3671, 3673, 3681, 3673, 3682, 3681, 3459, 3684, 3683, 3458, 3459, 3683, 3683, 3684, 3685, 3685, 3675, 3676, 3685, 3676, 3686, 3623, 3676, 3609, 3686, 3676, 3623, 3684, 3459, 3687, 3685, 3684, 3688, 3688, 3684, 3687, 3675, 3688, 3678, 3685, 3688, 3675, 3689, 3690, 3687, 3689, 3687, 3461, 3461, 3687, 3459, 3688, 3687, 3690, 3691, 3688, 3690, 3678, 3688, 3691, 3689, 3461, 3463, 3681, 3702, 3696, 3680, 3681, 3696, 3681, 3682, 3702, 3685, 3692, 3683, 3683, 3692, 3458, 3686, 3693, 3685, 3685, 3693, 3692, 3631, 3686, 3623, 3631, 3694, 3686, 3694, 3693, 3686, 3689, 3706, 3690, 3691, 3690, 3700, 3691, 3700, 3927, 3463, 3695, 3689, 3689, 3695, 3706, 3692, 3637, 3458, 3638, 3637, 3692, 3638, 3692, 3697, 3692, 3693, 3697, 3694, 3698, 3693, 3693, 3698, 3697, 3690, 3706, 3699, 3690, 3699, 3700, 3927, 3700, 3707, 3696, 3702, 3701, 3682, 3703, 3702, 3697, 3704, 3642, 3698, 3705, 3697, 3697, 3705, 3704, 3700, 3699, 3708, 3699, 3706, 3708, 3700, 3708, 3707, 3710, 3701, 3702, 3710, 3702, 3711, 3711, 3702, 3703, 3654, 3704, 3705, 3712, 3708, 3706, 3712, 3713, 3708, 3713, 3707, 3708, 3714, 3713, 4396, 3714, 3709, 3713, 3709, 3707, 3713, 3715, 3714, 4396, 4397, 3715, 4396, 3680, 3716, 3672, 3672, 3716, 3464, 3717, 3464, 3716, 3680, 3718, 3716, 3680, 3696, 3718, 3716, 3718, 3717, 3464, 3717, 3719, 3717, 3718, 3719, 3719, 3467, 3464, 3720, 3695, 3463, 3696, 3721, 3718, 3475, 3479, 3720, 3721, 3722, 3718, 3718, 3722, 3719, 3719, 3725, 3480, 3725, 3719, 3722, 3695, 3720, 3706, 3701, 3721, 3696, 3701, 3723, 3721, 3720, 3479, 3727, 3724, 3721, 3723, 3722, 3721, 3724, 3722, 3724, 3725, 3480, 3725, 3483, 3701, 3726, 3723, 3701, 3710, 3726, 3720, 3727, 3706, 3726, 3728, 3723, 3723, 3728, 3724, 3484, 3727, 3479, 3724, 3728, 3725, 3725, 3729, 3483, 3483, 3729, 3488, 3728, 3729, 3725, 3730, 3712, 3706, 3731, 3726, 3710, 3731, 3710, 3711, 3730, 3727, 3732, 3706, 3727, 3730, 3726, 3733, 3728, 3731, 3733, 3726, 3732, 3727, 3484, 3732, 3484, 3734, 3734, 3484, 3491, 3739, 3488, 3729, 3729, 3728, 3745, 3728, 3740, 3745, 3712, 3730, 3735, 3735, 4397, 3712, 3735, 3736, 4401, 3735, 3730, 3736, 3730, 3732, 3736, 3734, 3491, 3737, 3736, 3732, 3734, 3737, 3736, 3734, 3739, 3496, 3488, 3729, 3745, 3739, 3496, 3742, 3495, 3739, 3742, 3496, 3739, 3743, 3742, 3745, 3744, 3743, 3745, 3743, 3739, 3745, 3740, 3746, 3499, 3495, 3742, 3745, 3741, 3744, 3741, 4422, 3744, 3745, 3746, 3741, 3746, 4424, 3741, 4422, 3741, 4424, 3404, 3747, 3748, 3749, 3750, 3748, 3750, 3751, 3748, 3748, 3752, 3501, 3751, 3752, 3748, 3754, 3752, 3751, 3751, 3753, 3754, 3754, 3753, 3757, 3755, 3757, 3753, 3501, 3756, 3505, 3752, 3756, 3501, 3756, 3752, 3754, 3757, 3756, 3754, 3756, 3759, 3505, 3756, 3994, 3759, 3757, 3988, 3756, 3756, 3988, 3994, 3505, 3758, 3507, 3505, 3759, 3758, 3759, 3760, 3758, 3759, 3761, 3760, 3994, 3761, 3759, 3758, 3514, 3507, 3760, 3762, 3758, 3761, 3763, 3760, 3760, 3763, 3762, 3762, 3514, 3758, 3527, 3514, 3762, 3767, 3762, 3764, 3762, 3763, 3764, 3762, 3767, 3527, 3765, 3764, 3763, 3763, 3766, 3765, 3767, 3764, 3768, 3538, 3527, 3767, 3538, 3767, 3769, 3769, 3767, 3768, 3532, 3538, 3770, 3770, 3538, 3769, 3771, 3773, 3765, 3771, 3765, 3766, 3764, 3772, 3768, 3765, 3772, 3764, 3765, 3773, 3772, 3774, 3768, 3772, 3769, 3768, 3774, 3769, 3775, 3770, 3775, 3769, 3774, 3776, 3784, 3541, 3547, 3778, 3776, 3773, 3779, 3772, 3773, 3771, 3777, 3773, 3777, 3779, 3779, 3777, 3780, 3778, 3547, 3550, 3781, 3778, 3550, 3772, 3779, 3774, 3782, 3774, 3780, 3774, 3779, 3780, 3783, 3774, 3782, 3775, 3774, 3783, 3776, 3785, 3784, 3784, 3785, 4039, 3776, 3778, 3785, 3787, 3785, 3786, 3789, 3551, 3788, 3789, 3781, 3551, 3781, 3550, 3551, 3781, 3789, 3790, 3778, 3781, 3790, 3778, 3790, 3785, 3786, 3785, 3790, 3791, 3786, 3790, 3782, 3786, 3791, 3782, 3787, 3786, 3788, 3792, 3793, 3788, 3793, 3789, 3790, 3789, 3793, 3794, 3795, 3783, 3794, 3783, 3791, 3791, 3783, 3782, 3797, 3796, 3798, 3796, 3802, 3799, 3800, 3801, 3595, 3801, 3802, 3797, 3797, 3802, 3796, 3802, 3803, 3799, 3555, 3554, 3800, 3554, 3802, 3801, 3554, 3801, 3800, 3554, 3558, 3802, 3558, 3809, 3802, 3802, 3809, 3803, 3804, 4073, 3803, 3807, 3808, 3804, 3558, 3561, 3809, 3803, 3809, 3807, 3804, 3803, 3807, 3805, 3806, 3876, 3875, 3805, 3876, 3561, 3812, 3809, 3809, 3812, 3807, 3812, 3813, 3807, 3808, 3807, 3814, 3807, 3813, 3814, 3808, 3814, 3815, 3810, 3818, 3612, 3810, 3811, 3818, 3816, 3819, 3817, 3572, 3820, 3812, 3572, 3812, 3561, 3813, 3812, 3820, 3821, 3813, 3820, 3821, 3822, 3813, 3813, 3822, 3814, 3823, 3815, 3822, 3815, 3814, 3822, 3823, 3822, 3824, 3823, 3824, 3816, 3823, 3816, 3817, 3826, 3816, 3825, 3824, 3825, 3816, 3816, 3826, 3819, 3572, 3532, 3770, 3820, 3572, 3770, 3770, 3827, 3820, 3820, 3827, 3821, 3574, 3828, 3572, 3821, 3827, 3829, 3822, 3821, 3829, 3830, 3828, 3574, 3578, 3830, 3574, 3831, 3824, 3829, 3824, 3822, 3829, 3832, 3830, 3578, 3832, 3578, 3833, 3833, 3825, 3834, 3832, 3833, 3834, 3831, 3834, 3825, 3831, 3825, 3824, 3835, 3544, 3545, 3571, 3835, 3545, 3770, 3775, 3827, 3836, 3827, 3775, 3828, 3830, 3835, 3828, 3835, 3571, 3835, 3830, 3838, 3829, 3836, 3837, 3836, 3829, 3827, 3831, 3837, 3838, 3837, 3831, 3829, 3830, 3832, 3838, 3832, 3834, 3838, 3838, 3834, 3831, 3585, 3544, 3840, 3585, 3840, 3839, 3841, 3840, 3835, 3544, 3835, 3840, 3841, 3843, 3842, 3840, 3841, 3842, 3844, 3842, 3775, 3842, 3843, 3775, 3775, 3783, 3844, 3585, 3845, 3589, 3585, 3839, 3845, 3838, 3841, 3835, 3838, 3843, 3841, 3775, 3843, 3846, 3846, 3843, 3838, 3836, 3846, 3837, 3775, 3846, 3836, 3845, 3590, 3589, 3837, 3846, 3838, 3840, 3848, 3847, 3839, 3840, 3847, 3792, 3842, 3793, 3792, 3848, 3842, 3848, 3840, 3842, 3793, 3842, 3849, 3842, 3844, 3849, 3795, 3844, 3783, 3795, 3850, 3844, 3850, 3849, 3844, 4456, 3851, 3845, 4456, 3845, 3847, 3847, 3845, 3839, 3845, 3594, 3590, 3845, 3853, 3594, 3845, 3851, 3853, 3852, 3594, 3853, 3855, 3852, 3853, 3854, 3852, 3855, 3855, 3853, 3856, 3860, 4118, 3902, 3858, 3801, 3857, 3797, 3857, 3801, 3797, 3798, 3857, 3798, 3859, 3857, 3857, 3860, 3858, 4118, 3860, 3859, 3859, 3860, 3857, 3858, 3599, 3595, 3860, 3599, 3858, 3801, 3858, 3595, 3867, 3861, 3862, 3862, 3861, 3863, 3863, 3861, 3916, 3865, 3867, 3866, 3864, 3865, 3866, 3866, 3867, 3868, 3865, 3869, 3870, 3865, 3870, 3871, 3867, 3871, 3861, 3867, 3865, 3871, 3868, 3867, 3862, 3872, 3870, 3869, 3871, 3870, 3873, 3873, 3870, 3872, 3861, 3873, 3916, 3871, 3873, 3861, 3872, 3602, 3920, 3873, 3872, 3920, 3874, 3865, 3864, 3874, 3864, 3875, 3805, 3875, 3866, 3875, 3864, 3866, 3868, 3805, 3866, 3806, 3805, 3868, 3869, 3874, 3605, 3874, 3603, 3605, 3865, 3874, 3869, 3605, 3872, 3869, 3612, 3874, 3810, 3612, 3603, 3874, 3810, 3875, 3811, 3810, 3874, 3875, 3612, 3818, 3618, 3618, 3818, 3877, 3811, 3877, 3818, 3811, 3878, 3877, 3811, 3875, 3878, 3878, 3885, 3877, 3875, 3879, 3878, 3819, 3879, 3876, 3879, 3875, 3876, 3877, 3880, 3618, 3880, 3620, 3618, 3881, 3877, 3885, 3880, 3877, 3881, 3880, 3882, 3620, 3882, 3616, 3620, 3883, 3880, 3881, 3882, 3880, 3883, 3882, 3631, 3616, 3884, 3631, 3882, 3883, 3884, 3882, 3578, 3625, 3887, 3826, 3825, 3886, 3826, 3886, 3878, 3826, 3878, 3879, 3885, 3878, 3886, 3819, 3826, 3879, 3887, 3625, 3888, 3888, 3625, 3628, 3885, 3889, 3887, 3885, 3887, 3881, 3881, 3887, 3888, 3886, 3889, 3885, 3628, 3890, 3888, 3881, 3890, 3883, 3888, 3890, 3881, 3891, 3631, 3890, 3891, 3890, 3892, 3631, 3884, 3890, 3884, 3883, 3890, 3887, 3833, 3578, 3825, 3833, 3893, 3893, 3833, 3887, 3825, 3893, 3886, 3893, 3887, 3889, 3886, 3893, 3889, 3628, 3639, 3890, 3892, 3639, 3894, 3892, 3890, 3639, 3643, 3894, 3639, 3643, 3895, 3894, 3895, 3896, 3894, 3647, 3897, 3648, 3897, 3899, 3648, 3899, 3643, 3648, 3899, 3895, 3643, 3899, 3898, 3895, 3654, 3896, 3898, 3896, 3895, 3898, 3855, 3900, 3854, 3897, 3647, 3651, 3856, 3900, 3855, 3899, 3654, 3898, 3899, 3897, 3654, 3897, 3651, 3654, 3860, 3901, 3599, 3860, 3902, 3901, 3908, 3901, 3903, 3902, 3903, 3901, 3904, 3908, 3903, 3901, 3908, 3599, 3908, 3655, 3599, 3911, 3910, 3909, 3906, 3910, 3911, 3906, 3911, 3907, 3907, 3911, 4141, 3912, 3656, 3908, 3656, 3655, 3908, 3912, 3908, 3904, 3911, 3909, 3913, 4141, 3911, 3913, 4143, 4141, 3913, 3903, 3914, 3904, 3904, 3914, 3912, 3905, 3914, 3903, 3916, 3873, 3915, 3906, 3863, 3916, 3910, 3917, 3909, 3910, 3915, 3917, 3910, 3916, 3915, 3915, 3921, 3917, 3906, 3916, 3910, 3909, 3917, 3918, 3913, 3909, 3918, 4143, 3913, 3918, 3912, 3919, 3656, 3912, 3914, 3919, 3920, 3602, 3661, 3873, 3920, 3915, 3920, 3661, 3921, 3921, 3661, 3664, 3915, 3920, 3921, 3921, 3664, 3666, 3922, 3921, 3666, 3917, 3921, 3922, 3917, 3922, 3918, 3914, 3923, 3919, 3919, 3923, 3667, 3914, 4149, 3923, 3666, 3691, 3922, 3918, 3922, 4150, 3667, 3923, 3682, 3923, 3926, 3682, 3891, 3694, 3631, 3925, 3694, 3891, 3892, 3925, 3891, 3691, 3927, 3924, 3922, 3691, 3924, 3894, 3925, 3892, 3894, 3698, 3925, 3698, 3694, 3925, 3682, 3926, 3703, 3923, 3928, 3926, 3926, 3928, 3936, 3896, 3698, 3894, 3705, 3698, 3896, 3707, 3709, 3927, 3924, 3927, 3929, 4155, 3924, 3929, 3926, 3930, 3703, 3703, 3930, 3711, 3926, 3936, 3930, 3896, 3654, 3705, 3932, 3934, 3931, 3932, 3931, 3933, 3927, 3709, 3934, 3934, 3932, 3929, 3934, 3929, 3927, 3933, 3929, 3932, 3711, 3930, 3935, 3940, 3935, 3930, 3940, 3930, 3936, 3928, 3941, 3936, 3931, 3938, 4472, 3934, 3938, 3931, 3931, 4472, 4508, 3714, 3937, 3709, 3934, 3709, 3938, 3709, 3937, 3938, 3715, 3939, 3714, 3940, 3936, 3941, 3935, 3942, 3711, 3942, 3731, 3711, 3942, 3935, 3940, 3942, 3733, 3731, 3943, 3733, 3942, 3940, 4485, 3942, 3943, 3942, 4486, 3942, 4485, 4486, 3943, 3738, 3733, 4489, 3738, 3943, 4486, 4489, 3943, 6306, 3944, 3947, 3946, 3944, 3945, 3946, 3948, 3944, 3944, 3948, 3947, 3948, 4521, 3947, 3949, 3500, 3947, 4521, 3949, 3947, 3949, 4542, 3500, 4542, 3950, 3500, 3500, 3950, 3747, 3950, 3953, 3747, 4550, 3951, 3950, 3747, 3953, 3952, 3950, 3951, 3953, 3951, 3954, 3953, 3747, 3952, 3748, 3952, 3953, 3956, 3955, 3952, 3956, 3953, 3954, 3956, 3956, 3958, 3955, 3958, 3957, 3955, 3956, 3954, 3958, 3958, 3963, 3957, 3959, 3958, 3954, 3955, 3965, 3749, 3955, 3749, 3952, 3952, 3749, 3748, 3960, 3967, 3968, 3960, 4560, 3961, 3967, 3960, 3961, 3962, 3965, 3955, 3955, 3957, 3962, 3971, 4560, 3960, 3957, 3963, 3962, 3962, 3963, 3964, 3963, 3958, 3959, 3959, 3964, 3963, 3750, 3749, 3965, 3968, 3967, 3966, 3970, 3968, 3966, 3969, 3970, 3966, 3968, 3971, 3960, 3968, 3970, 3971, 3962, 3964, 3965, 3965, 3964, 3975, 3970, 3969, 3977, 3971, 3970, 3977, 4568, 3971, 3977, 3972, 3975, 3964, 3964, 3959, 3973, 3751, 3750, 3965, 3975, 3751, 3965, 3966, 3967, 3974, 3966, 3974, 3969, 3977, 3969, 3976, 3976, 3969, 3974, 3976, 3978, 3977, 3979, 3977, 3978, 3751, 3975, 3753, 3755, 3753, 3975, 3974, 3980, 3976, 3975, 3972, 3755, 3967, 3982, 3974, 3967, 3984, 3982, 3976, 3980, 4055, 4055, 4047, 3976, 3974, 3982, 3981, 3974, 3981, 3980, 3983, 4055, 3980, 3983, 3980, 3981, 3984, 3981, 3982, 3986, 3981, 3984, 3757, 3755, 3985, 3985, 3755, 3993, 3986, 3987, 3983, 3981, 3986, 3983, 3757, 3989, 3988, 3984, 3991, 3990, 3757, 3985, 3989, 3984, 3990, 3986, 3990, 3992, 3986, 3985, 3993, 3989, 3986, 3992, 3987, 3992, 4072, 3987, 3994, 3988, 3989, 3991, 3995, 3990, 3995, 3991, 3996, 3996, 3991, 3984, 3990, 3995, 3992, 3992, 3995, 3998, 3989, 3993, 3997, 3997, 3993, 4083, 3999, 3992, 3998, 4072, 3992, 3999, 3994, 4000, 3761, 3989, 4001, 3994, 3994, 4001, 4000, 3996, 4003, 4002, 3995, 3996, 4002, 3989, 3997, 4001, 4004, 4011, 4001, 3997, 4004, 4001, 3995, 4002, 3998, 4002, 4005, 3998, 4006, 4004, 3997, 4007, 3998, 4005, 3997, 4083, 4006, 3998, 4009, 4008, 3998, 4008, 3999, 4000, 4020, 3761, 3761, 4020, 3763, 4001, 4010, 4000, 4000, 4010, 4020, 4010, 4001, 4011, 4003, 4021, 4012, 4002, 4003, 4012, 4013, 4011, 4004, 4013, 4005, 4012, 4005, 4002, 4012, 4013, 4014, 4015, 4013, 4004, 4014, 4004, 4006, 4014, 4005, 4013, 4015, 4005, 4015, 4007, 4007, 4015, 4016, 4018, 4014, 4006, 4018, 4006, 4017, 4018, 4017, 4019, 4019, 4008, 4009, 4019, 4009, 4018, 4094, 4018, 4009, 4029, 4010, 4011, 4011, 4022, 4029, 4013, 4022, 4011, 4012, 4022, 4013, 4012, 4023, 4022, 4012, 4021, 4023, 4015, 4014, 4024, 4026, 4015, 4024, 4026, 4016, 4015, 4023, 4025, 4022, 4014, 4018, 4024, 4018, 4027, 4024, 4027, 4026, 4024, 4020, 3766, 3763, 4010, 4028, 4020, 4020, 4028, 3766, 4028, 4010, 4029, 4022, 4025, 4030, 4022, 4030, 4029, 3766, 4033, 3771, 3766, 4028, 4033, 4034, 4028, 4029, 4032, 4034, 4029, 4032, 4030, 4031, 4032, 4029, 4030, 3777, 3771, 4033, 4033, 4028, 4034, 4032, 4031, 4035, 3782, 4033, 4036, 3782, 3780, 4033, 3780, 3777, 4033, 4034, 4036, 4033, 4034, 4037, 4036, 4032, 4037, 4034, 4032, 4038, 4037, 4038, 4032, 4035, 3785, 4040, 4039, 4040, 4037, 4039, 4039, 4037, 4041, 4041, 4035, 4042, 4036, 3787, 3782, 4036, 4040, 3787, 4040, 3785, 3787, 4037, 4040, 4036, 4041, 4037, 4038, 4035, 4041, 4038, 3794, 4043, 3795, 3972, 3964, 4044, 3973, 4045, 3964, 4045, 4044, 3964, 4615, 4045, 3973, 4046, 3972, 4044, 3978, 3976, 4047, 3979, 3978, 4047, 4048, 3979, 4047, 4044, 4049, 4046, 4045, 4049, 4044, 4049, 4045, 4618, 4046, 4054, 3972, 4048, 4047, 4050, 4046, 4051, 4054, 4049, 4051, 4046, 4052, 4051, 4049, 4054, 4053, 3755, 4054, 3755, 3972, 4056, 4050, 4055, 4050, 4047, 4055, 4054, 4060, 4053, 4051, 4060, 4054, 4051, 4057, 4060, 4058, 4057, 4052, 4052, 4057, 4051, 4055, 3983, 4061, 4060, 4059, 4053, 4059, 3755, 4053, 4055, 4061, 4056, 4056, 4061, 4062, 4059, 4060, 3796, 4060, 4063, 3796, 4060, 4057, 4063, 4063, 3798, 3796, 4057, 4119, 4063, 3993, 3755, 4059, 3987, 4061, 3983, 3987, 4064, 4061, 4061, 4066, 4067, 4061, 4064, 4066, 4065, 4066, 4064, 4061, 4067, 4062, 3799, 4059, 3796, 4069, 4066, 4065, 4068, 4069, 4065, 4067, 4066, 4069, 4068, 4070, 4071, 4068, 4071, 4069, 4069, 4071, 4122, 4067, 4069, 4122, 3993, 4059, 4073, 3987, 4072, 4064, 4065, 4072, 4074, 4072, 4065, 4064, 4073, 4059, 3799, 3803, 4073, 3799, 4065, 4074, 4068, 4070, 4076, 4075, 4076, 4070, 4068, 4073, 4077, 3993, 4077, 4083, 3993, 3999, 4078, 4072, 4073, 3804, 4077, 4072, 4086, 4074, 4072, 4078, 4086, 4074, 4079, 4068, 4086, 4079, 4074, 3806, 4075, 4076, 3806, 4076, 4080, 4079, 4076, 4068, 4079, 4080, 4076, 4125, 4081, 4082, 4078, 3999, 4084, 3999, 4008, 4084, 4077, 3804, 4085, 4085, 3804, 3808, 4083, 4077, 4085, 4086, 4078, 4084, 4079, 4086, 4087, 4087, 4086, 4088, 4089, 4086, 4084, 4088, 4086, 4089, 3806, 4080, 4090, 3876, 3806, 4090, 4080, 4079, 4087, 4080, 4087, 4090, 4125, 4090, 4087, 4081, 4125, 4087, 4088, 4081, 4087, 4088, 4082, 4081, 4088, 4089, 4082, 4017, 4091, 4092, 4017, 4083, 4091, 4017, 4006, 4083, 4092, 4105, 4093, 4092, 4093, 4017, 4017, 4093, 4019, 4008, 4093, 4084, 4008, 4019, 4093, 4091, 4085, 3815, 3815, 4085, 3808, 4083, 4085, 4091, 4093, 4105, 4108, 4108, 4084, 4093, 4108, 4099, 4084, 3817, 4096, 4095, 4096, 4101, 4097, 4095, 4096, 4097, 4097, 4101, 4098, 4084, 4099, 4089, 4096, 3817, 4100, 4100, 3817, 3819, 4101, 4096, 4100, 4102, 4098, 4101, 4089, 4099, 4082, 4099, 4103, 4082, 4105, 4092, 4104, 4092, 4091, 4104, 4018, 4094, 4027, 4106, 4104, 4091, 4106, 4091, 3823, 4106, 3823, 4107, 3823, 4091, 3815, 4107, 4114, 4105, 4104, 4106, 4105, 4106, 4107, 4105, 4108, 4105, 4114, 4095, 3823, 3817, 4107, 3823, 4095, 4107, 4095, 4109, 4095, 4097, 4109, 4098, 4110, 4108, 4098, 4108, 4109, 4098, 4109, 4097, 4108, 4110, 4099, 4102, 4111, 4112, 4110, 4112, 4116, 4110, 4102, 4112, 4110, 4098, 4102, 4116, 4099, 4110, 4116, 4113, 4099, 4113, 4103, 4099, 4107, 4109, 4114, 4108, 4114, 4109, 4112, 4111, 4115, 4115, 4116, 4112, 4043, 3850, 3795, 4117, 4118, 4120, 4063, 4120, 3798, 4119, 4120, 4063, 4058, 4117, 4119, 4057, 4058, 4119, 4120, 3859, 3798, 4119, 4117, 4120, 4118, 3859, 4120, 4121, 4071, 4070, 4121, 3862, 4071, 4071, 3862, 4122, 4122, 3862, 3863, 4123, 4122, 4124, 4122, 3863, 4124, 4633, 4123, 4139, 3868, 4121, 4075, 4121, 4070, 4075, 4121, 3868, 3862, 4075, 3806, 3868, 4126, 3876, 4090, 4125, 4126, 4090, 4125, 4127, 4126, 4082, 4128, 4125, 4125, 4128, 4127, 4126, 3819, 3876, 4100, 3819, 4126, 4127, 4100, 4126, 4127, 4101, 4100, 4101, 4127, 4102, 4127, 4129, 4102, 4127, 4128, 4129, 4103, 4128, 4082, 4129, 4128, 4103, 4102, 4130, 4111, 4129, 4130, 4102, 4129, 4131, 4130, 4113, 4129, 4103, 4131, 4129, 4113, 4111, 4130, 4115, 4131, 4115, 4130, 4116, 4115, 4131, 4113, 4116, 4131, 4117, 4635, 4118, 4635, 4133, 4118, 4118, 4132, 3902, 4132, 3903, 3902, 4133, 4132, 4118, 4132, 4134, 3903, 4137, 4134, 4132, 4133, 4135, 4132, 4135, 4137, 4132, 4134, 3905, 3903, 4136, 3905, 4134, 4137, 4136, 4134, 3906, 3907, 4138, 4138, 4140, 4139, 4139, 4140, 4633, 4633, 4140, 4142, 4138, 3907, 4141, 4140, 4138, 4141, 4142, 4140, 4141, 4144, 4141, 4143, 4142, 4141, 4144, 4647, 4142, 4144, 4136, 4145, 3905, 4137, 4146, 4136, 4136, 4146, 4145, 4135, 4146, 4137, 4138, 3863, 3906, 4124, 3863, 4138, 4139, 4124, 4138, 4123, 4124, 4139, 4143, 3918, 4150, 4144, 4143, 4147, 4143, 4150, 4147, 4647, 4144, 4147, 3905, 4149, 3914, 4145, 4149, 3905, 4146, 4148, 4145, 4145, 4148, 4149, 4147, 4150, 4651, 4149, 4151, 3923, 4149, 4657, 4151, 4150, 3922, 3924, 4651, 4150, 4154, 4150, 3924, 4154, 4651, 4154, 4655, 4151, 4158, 3923, 4657, 4661, 4151, 3924, 4155, 4154, 4153, 4655, 4154, 3923, 4158, 3928, 4154, 4155, 4156, 4156, 4152, 4154, 4153, 4154, 4152, 4661, 4158, 4151, 4157, 4152, 4156, 4157, 4161, 4152, 4664, 4152, 4161, 4157, 4156, 4155, 3933, 4157, 4155, 3933, 3931, 4157, 4157, 4508, 4161, 4155, 3929, 3933, 4158, 4159, 3928, 3928, 4159, 3941, 4160, 4159, 4158, 4157, 3931, 4508, 4160, 4474, 4159, 4474, 3941, 4159, 4474, 4160, 4162, 4163, 4162, 4160, 4509, 4163, 4682, 4164, 4497, 4509, 4682, 4164, 4509, 4164, 4165, 4510, 4172, 4166, 4167, 4167, 3213, 3212, 3037, 2997, 3214, 4167, 4166, 4169, 4167, 4169, 3213, 3214, 3216, 3037, 3037, 3216, 3045, 4171, 4166, 4168, 4170, 4171, 4168, 3215, 4171, 3219, 3215, 4169, 4171, 4169, 4166, 4171, 4166, 4172, 4173, 4166, 4173, 4168, 4168, 4173, 4170, 4173, 4182, 4170, 4174, 4173, 4172, 4174, 4172, 4176, 3048, 4175, 4174, 4174, 4175, 4173, 4175, 3052, 3053, 4175, 3053, 4183, 4183, 3053, 3080, 4173, 4183, 4182, 4175, 4183, 4173, 4174, 3058, 3048, 4174, 4176, 3058, 4244, 4176, 4172, 3048, 3061, 4175, 4175, 3061, 3052, 4177, 4179, 4176, 4179, 4178, 4176, 4178, 4180, 3072, 4181, 3075, 4180, 4181, 4180, 4179, 4179, 4180, 4178, 4246, 3075, 4181, 3219, 4171, 4170, 3045, 3216, 3076, 3219, 4170, 3222, 3216, 3228, 3076, 3076, 3228, 3078, 3222, 4185, 3229, 4170, 4182, 3222, 4182, 4185, 3222, 4183, 3080, 3084, 3084, 4184, 4183, 4182, 4184, 4185, 4183, 4184, 4182, 4184, 3090, 4185, 3084, 3088, 4184, 3088, 3090, 4184, 3228, 3234, 3096, 3078, 3228, 3096, 4186, 3096, 3234, 4185, 4192, 3229, 3113, 4185, 3090, 4185, 3113, 4192, 4187, 3235, 3231, 4187, 3231, 3229, 3234, 3237, 4186, 4186, 3237, 4189, 4187, 4188, 3235, 3237, 3244, 4189, 3235, 4188, 3238, 3238, 4188, 4190, 4191, 3238, 4190, 3229, 4192, 4187, 4186, 4189, 3099, 4187, 4192, 4188, 4192, 4193, 4188, 4188, 4193, 4190, 4190, 4193, 4191, 4192, 3110, 4193, 3113, 3110, 4192, 4193, 3110, 4194, 4218, 4217, 3105, 4195, 3238, 4191, 4195, 4200, 3246, 3247, 3121, 3119, 3247, 3249, 3121, 3246, 4200, 4196, 3246, 4196, 3248, 3248, 4196, 4247, 3249, 3123, 3121, 4195, 4193, 4197, 4195, 4191, 4193, 4195, 4197, 4198, 4200, 4195, 4198, 4196, 4200, 4247, 3123, 3249, 4201, 3243, 4202, 3249, 4250, 4202, 3243, 4197, 4193, 4194, 4197, 4194, 4199, 4198, 3125, 4200, 4198, 4199, 3125, 4198, 4197, 4199, 4200, 3125, 3126, 4247, 4200, 4206, 3249, 4202, 4201, 4194, 3127, 4199, 3110, 3127, 4194, 3126, 3131, 4200, 4201, 4203, 3123, 4203, 4201, 4204, 4201, 4202, 4204, 4219, 4207, 4202, 4205, 3131, 3137, 4205, 4200, 3131, 4206, 4200, 4209, 4200, 4205, 4209, 4203, 4204, 4221, 4202, 4207, 4204, 4204, 4207, 4221, 4209, 4205, 4208, 4206, 4209, 4210, 4208, 4205, 3137, 3142, 4208, 3137, 4208, 4212, 4209, 4211, 4212, 4208, 4209, 4213, 4210, 4212, 4213, 4209, 4211, 4208, 3142, 4211, 3142, 4214, 3142, 3144, 4214, 4212, 4211, 4215, 4211, 4214, 4215, 4216, 4213, 4215, 4213, 4212, 4215, 3165, 4214, 3144, 4214, 4233, 4215, 4215, 4233, 4216, 4217, 3178, 3105, 3123, 4203, 3147, 4203, 4220, 3151, 3147, 4203, 3151, 4220, 4203, 4221, 4219, 4221, 4207, 3151, 4220, 4222, 4226, 4225, 4223, 4220, 4230, 3156, 4221, 4224, 4220, 4220, 4224, 4230, 4225, 4228, 4224, 4225, 4224, 4219, 4219, 4224, 4221, 4222, 4220, 3156, 4226, 4228, 4225, 4227, 4228, 4226, 4226, 4223, 4227, 4230, 4224, 4228, 4230, 3162, 3156, 4227, 4235, 4228, 4223, 4286, 4227, 4227, 4286, 4235, 4232, 4214, 3165, 4233, 4214, 4232, 4233, 4238, 4216, 4238, 4229, 4216, 3162, 4230, 4234, 3166, 3162, 4234, 4231, 4229, 4238, 4234, 3169, 3166, 4228, 4235, 4230, 4230, 162, 4234, 162, 3169, 4234, 4235, 162, 4230, 4286, 163, 4235, 3165, 4236, 4232, 3172, 4236, 3165, 4217, 4237, 3178, 4217, 4232, 4237, 4236, 4237, 4232, 4233, 4232, 4217, 4218, 4233, 4217, 4238, 4233, 4218, 4236, 3172, 4239, 4239, 3172, 3175, 4240, 4237, 4236, 4239, 4240, 4236, 3177, 4242, 4241, 3177, 4241, 3175, 3175, 4241, 4239, 4241, 4240, 4239, 166, 4241, 4242, 168, 166, 4242, 167, 4240, 4241, 166, 167, 4241, 4237, 4240, 3180, 3178, 4237, 3180, 4242, 3177, 3180, 3180, 4240, 4242, 4240, 167, 168, 4242, 4240, 168, 3275, 4167, 3212, 3271, 4243, 3275, 4172, 4167, 4244, 3275, 4243, 4167, 4243, 4244, 4167, 4243, 4291, 4244, 4244, 4177, 4176, 4291, 4245, 4244, 4177, 4245, 4179, 4244, 4245, 4177, 4179, 4245, 4181, 4181, 4245, 4246, 3299, 3248, 4247, 3299, 4247, 4248, 3299, 4248, 4294, 3303, 4250, 3243, 4248, 4249, 4251, 4248, 4247, 4249, 4294, 4248, 4251, 4252, 4294, 4251, 4247, 4206, 4249, 4255, 4251, 4249, 4250, 4266, 4202, 4202, 4266, 4219, 4267, 4266, 4250, 4249, 4206, 4253, 4249, 4253, 4254, 4254, 4255, 4249, 4253, 4206, 4256, 4253, 4257, 4254, 4254, 4257, 4255, 4257, 4253, 4256, 4255, 4257, 4258, 4255, 4258, 4259, 4210, 4256, 4206, 4260, 4256, 4210, 4261, 4257, 4260, 4257, 4256, 4260, 4257, 4262, 4258, 4261, 4262, 4257, 4258, 4263, 4259, 4262, 4263, 4258, 4210, 4213, 4260, 4261, 4213, 4216, 4260, 4213, 4261, 4218, 4287, 4238, 3303, 4264, 4250, 3300, 3303, 3243, 3300, 4264, 3303, 4250, 4264, 4267, 4264, 4268, 4267, 4269, 4268, 4265, 4265, 4268, 4264, 4269, 4316, 4273, 4275, 4273, 4316, 231, 4275, 4316, 4270, 4219, 4266, 4266, 4267, 4270, 4267, 4268, 4271, 4270, 4267, 4271, 4268, 4269, 4271, 4273, 4272, 4270, 4271, 4273, 4270, 4269, 4273, 4271, 4274, 4272, 4275, 4272, 4273, 4275, 4276, 4322, 4281, 195, 4274, 4275, 4276, 4281, 237, 4219, 4270, 4225, 4282, 4277, 4261, 4262, 4261, 4278, 4278, 4261, 4277, 4263, 4262, 4278, 4270, 4272, 4223, 4225, 4270, 4223, 4277, 4282, 4279, 4277, 4279, 4278, 4263, 4278, 4280, 4280, 4278, 4279, 4274, 4286, 4223, 4274, 4223, 4272, 4284, 4281, 4279, 4322, 4280, 4281, 4280, 4279, 4281, 196, 4286, 4274, 198, 4281, 4284, 4281, 197, 237, 4281, 198, 197, 4282, 4261, 4216, 4279, 4283, 4284, 4279, 4282, 4283, 4216, 4229, 4282, 4282, 4229, 4231, 4282, 4231, 4283, 4285, 4284, 4283, 4218, 4325, 4287, 4283, 4231, 4288, 4231, 4238, 4288, 4285, 4287, 4289, 4285, 4288, 4287, 4285, 4283, 4288, 4285, 4289, 4284, 4325, 4289, 4287, 4287, 4288, 4238, 4243, 4290, 4291, 4290, 4292, 4291, 4329, 4292, 4290, 4293, 4245, 4291, 4291, 4292, 4293, 4292, 4329, 4293, 4246, 4245, 4293, 4329, 4246, 4293, 4330, 3105, 4246, 3105, 3075, 4246, 4331, 4218, 3105, 3343, 3299, 4294, 3343, 4294, 4297, 4296, 4295, 3344, 3343, 4297, 4298, 3343, 4298, 4332, 4299, 4294, 4252, 4297, 4294, 4299, 4296, 4301, 4295, 4295, 4301, 4300, 4298, 4297, 4302, 4332, 4298, 4336, 4298, 4302, 4336, 4299, 4252, 4303, 4252, 4251, 4303, 4302, 4297, 4299, 4303, 4302, 4299, 4306, 4304, 4302, 4302, 4304, 4336, 4303, 4251, 4305, 4251, 4255, 4305, 4306, 4302, 4303, 4306, 4303, 4305, 4308, 4259, 4263, 4308, 4305, 4259, 4305, 4255, 4259, 4305, 4309, 4306, 4308, 4309, 4305, 4306, 4309, 4304, 4304, 4309, 4307, 4325, 4331, 4341, 4325, 4218, 4331, 3402, 4296, 3344, 4310, 4311, 4264, 4295, 4300, 4310, 4310, 4300, 4311, 231, 4312, 4344, 4264, 4311, 4265, 4300, 4313, 4311, 4300, 4314, 4313, 4316, 4313, 4314, 4301, 4315, 4300, 4315, 4314, 4300, 4316, 4314, 4312, 4315, 4312, 4314, 4312, 231, 4316, 4265, 4311, 4313, 4269, 4265, 4313, 4313, 4316, 4269, 4317, 4307, 4323, 4318, 4323, 4324, 4317, 4323, 4318, 4317, 4318, 4319, 233, 4318, 4324, 4319, 4318, 232, 232, 4318, 233, 4320, 4308, 4263, 4322, 4320, 4263, 4309, 4308, 4321, 4321, 4308, 4320, 4307, 4309, 4323, 4321, 4320, 4322, 4309, 4321, 4323, 4322, 4276, 4324, 4321, 4322, 4324, 4323, 4321, 4324, 4276, 237, 4324, 4263, 4280, 4322, 3319, 4350, 4243, 4243, 4350, 4290, 4350, 4326, 4290, 4290, 4326, 4327, 4350, 4352, 4326, 4328, 4327, 4326, 4326, 4352, 4328, 4290, 4327, 4329, 4327, 4328, 4329, 4329, 4328, 4246, 4246, 4328, 4330, 3105, 4330, 4395, 4331, 3105, 4395, 3399, 4335, 4334, 3397, 4332, 4333, 3398, 3397, 4356, 3397, 4333, 4356, 4337, 4336, 4339, 4337, 4333, 4336, 4333, 4332, 4336, 4333, 4337, 4356, 4338, 4337, 4339, 4361, 4337, 4338, 4304, 4339, 4336, 4339, 4304, 4340, 4340, 4304, 4307, 4339, 4340, 4338, 314, 4325, 4341, 3402, 4342, 4296, 4334, 3402, 3399, 4342, 3402, 4334, 4301, 4296, 4342, 4301, 4342, 4343, 4342, 4334, 4343, 4343, 4344, 4312, 4334, 4344, 4343, 4335, 4344, 4334, 272, 4372, 276, 4301, 4343, 4315, 4343, 4312, 4315, 4346, 4338, 4345, 4361, 4338, 4347, 4347, 4338, 4346, 4345, 4348, 4346, 4346, 4348, 276, 4372, 4347, 276, 4347, 4346, 276, 4338, 4340, 4345, 4340, 4307, 4349, 4349, 4307, 4317, 4340, 4349, 4345, 4345, 4319, 4348, 4345, 4349, 4319, 4349, 4317, 4319, 4319, 232, 4348, 4341, 4381, 314, 3426, 4384, 4350, 4384, 4351, 4350, 4351, 4352, 4350, 4328, 4352, 4351, 4395, 4330, 4328, 4354, 3398, 4353, 3491, 4354, 4360, 4335, 3492, 4355, 3492, 3493, 4355, 4357, 4353, 4356, 4353, 3398, 4356, 4353, 4358, 4354, 4357, 4358, 4353, 4354, 4358, 4360, 4375, 4359, 4357, 4358, 4357, 4359, 4337, 4361, 4356, 4356, 4361, 4357, 4357, 4361, 4375, 4395, 4417, 4331, 4362, 4417, 4418, 4417, 4362, 4331, 4362, 4341, 4331, 4355, 4363, 4335, 4364, 4363, 4355, 4355, 3493, 4364, 4364, 4365, 4363, 4363, 4366, 4335, 4366, 4344, 4335, 4365, 115, 4366, 4365, 4366, 4363, 4366, 115, 4344, 4367, 4359, 4375, 4358, 4359, 4368, 4368, 4359, 4367, 4369, 4370, 4360, 4369, 4360, 4368, 4368, 4360, 4358, 4371, 4375, 4347, 4375, 4371, 4367, 4367, 4371, 4373, 4368, 4367, 4373, 4374, 4369, 4368, 4373, 4374, 4368, 4372, 4371, 4347, 4373, 4371, 4372, 4372, 272, 306, 304, 4373, 306, 4373, 4372, 306, 4373, 304, 305, 4373, 305, 4374, 4369, 4377, 4370, 4377, 4430, 4370, 4375, 4361, 4347, 312, 4369, 4374, 4369, 4376, 4377, 4430, 4377, 4418, 4379, 4376, 4369, 312, 4379, 4369, 311, 337, 4378, 4377, 4381, 4362, 4376, 4381, 4377, 4418, 4377, 4362, 4376, 4379, 4381, 4381, 4379, 314, 4380, 327, 311, 4378, 4380, 311, 4362, 4381, 4341, 4382, 3552, 4383, 3541, 4384, 3426, 4384, 4385, 4351, 4384, 4451, 4385, 4351, 4385, 4328, 4383, 4386, 4438, 4383, 3552, 4386, 3552, 3592, 4386, 3584, 4387, 4388, 3584, 4388, 3592, 3592, 4388, 4386, 4387, 3593, 3594, 4387, 3594, 4389, 4387, 4389, 4388, 4438, 4386, 4390, 4390, 4388, 4391, 4386, 4388, 4390, 4391, 4388, 4392, 4388, 4389, 4392, 4389, 4393, 4392, 3852, 4393, 4389, 4391, 4392, 4394, 4392, 4393, 4394, 4395, 4385, 4468, 4385, 4395, 4328, 3713, 3712, 4396, 3712, 4397, 4396, 4505, 4398, 4395, 4417, 4395, 4398, 4397, 3735, 4399, 4397, 4400, 3715, 4397, 4399, 4400, 3735, 4401, 4402, 3735, 4402, 4399, 4400, 4399, 4402, 4403, 4400, 4402, 3736, 3737, 4401, 3738, 4409, 3733, 3737, 3491, 4360, 3728, 4404, 3740, 3728, 3733, 4404, 4402, 4401, 4405, 4403, 4402, 4406, 4402, 4405, 4406, 4401, 3737, 4407, 4401, 4407, 4408, 4360, 4425, 3737, 3737, 4425, 4407, 4425, 4410, 4407, 4410, 4408, 4407, 4409, 4411, 4404, 3733, 4409, 4404, 4405, 4412, 4413, 4401, 4412, 4405, 4406, 4405, 4414, 4405, 4413, 4414, 4412, 4408, 4416, 4412, 4401, 4408, 4416, 4408, 4415, 4408, 4410, 4415, 4398, 4413, 4417, 4414, 4413, 4398, 4413, 4412, 4417, 4412, 4416, 4417, 4418, 4417, 4416, 4416, 4415, 4418, 3743, 3744, 3742, 4419, 3740, 4404, 3746, 3740, 4419, 3744, 4421, 4420, 3744, 4420, 3742, 3742, 4420, 3499, 4421, 3744, 4422, 3746, 4423, 4424, 4419, 4423, 3746, 3499, 4429, 134, 4425, 4360, 4370, 4426, 4410, 4425, 4411, 4432, 4404, 4404, 4432, 4419, 4427, 4420, 4421, 4427, 4421, 4428, 4422, 4428, 4421, 4428, 4424, 4423, 4428, 4423, 4432, 4432, 4423, 4419, 4420, 4427, 4429, 4420, 4429, 3499, 4422, 4424, 4428, 4425, 4370, 4430, 4426, 4425, 4431, 4425, 4430, 4431, 4415, 4410, 4426, 4431, 4415, 4426, 4427, 4428, 4433, 4427, 4433, 4429, 4429, 4433, 4434, 4434, 337, 4429, 4418, 4431, 4430, 4415, 4431, 4418, 4378, 4433, 4428, 4378, 4428, 4435, 4436, 4435, 4428, 4428, 4432, 4436, 4433, 4378, 4434, 4380, 4435, 4436, 4378, 337, 4434, 348, 4380, 4436, 4435, 4380, 4378, 3784, 4442, 3541, 4039, 4437, 3784, 3784, 4437, 4442, 3551, 4382, 3788, 3788, 4382, 4383, 3788, 4383, 4439, 4439, 4383, 4438, 3792, 4439, 4450, 3792, 3788, 4439, 3793, 4440, 3790, 3791, 4441, 3794, 3791, 3790, 4441, 3790, 4440, 4441, 3541, 4442, 4384, 4444, 4443, 4437, 4437, 4443, 4442, 4445, 4443, 4444, 4446, 4445, 4447, 4439, 4448, 4449, 4438, 4448, 4439, 4449, 4450, 4439, 4455, 4446, 4447, 4442, 4451, 4384, 4451, 4442, 4443, 4445, 4451, 4443, 4451, 4453, 4452, 4445, 4446, 4451, 4446, 4453, 4451, 4449, 4452, 4453, 4449, 4448, 4452, 4448, 4468, 4452, 4450, 4449, 4453, 4450, 4453, 4454, 4446, 4455, 4453, 4455, 4454, 4453, 4385, 4451, 4452, 4385, 4452, 4468, 3848, 4456, 3847, 3848, 4457, 4456, 3848, 3792, 4457, 3792, 4450, 4457, 3793, 3849, 4440, 3850, 4440, 3849, 4441, 4440, 3850, 4456, 4457, 3851, 4457, 4458, 3851, 3594, 3852, 4389, 3851, 4458, 3853, 4458, 4459, 3853, 4460, 3852, 3854, 3853, 4459, 3856, 4459, 4461, 3856, 4462, 4448, 4438, 4462, 4438, 4390, 4462, 4390, 4463, 4463, 4450, 4462, 4463, 4457, 4450, 4463, 4390, 4465, 4465, 4390, 4391, 4457, 4465, 4458, 4463, 4465, 4457, 4470, 4455, 4464, 4391, 4394, 4466, 4391, 4466, 4465, 4458, 4466, 4459, 4465, 4466, 4458, 4460, 4467, 4466, 4460, 4466, 4393, 4460, 4393, 3852, 4393, 4466, 4394, 4459, 4467, 4461, 4466, 4467, 4459, 4505, 4395, 4470, 4469, 4448, 4462, 4469, 4468, 4448, 4462, 4450, 4454, 4462, 4454, 4469, 4470, 4468, 4469, 4454, 4470, 4469, 4455, 4470, 4454, 4470, 4395, 4468, 3854, 4471, 4460, 3900, 4471, 3854, 4461, 3900, 3856, 4471, 3900, 4461, 4460, 4471, 4467, 4461, 4467, 4471, 4480, 4473, 3937, 4473, 3938, 3937, 4473, 4472, 3938, 3937, 3714, 4475, 3714, 3939, 4475, 3941, 4476, 3940, 3941, 4474, 4476, 4476, 4474, 4477, 4478, 4472, 4481, 4478, 4508, 4472, 4479, 4481, 4472, 4473, 4479, 4472, 4473, 4480, 4479, 4480, 4481, 4479, 3937, 4475, 4482, 3937, 4482, 4480, 4482, 4491, 4480, 4481, 4480, 4483, 4483, 4480, 4491, 4483, 4492, 4481, 4478, 4398, 4505, 4478, 4498, 4398, 4478, 4481, 4498, 4498, 4481, 4492, 4398, 4498, 4414, 3715, 4400, 4484, 3715, 4484, 3939, 3939, 4484, 4475, 3940, 4476, 4485, 4477, 4488, 4476, 4484, 4400, 4403, 4476, 4487, 4485, 4487, 4486, 4485, 4488, 4487, 4476, 4489, 4495, 3738, 3738, 4495, 4409, 4489, 4486, 4490, 4486, 4487, 4490, 4488, 4490, 4487, 4482, 4484, 4493, 4475, 4484, 4482, 4491, 4482, 4493, 4491, 4492, 4483, 4493, 4484, 4403, 4493, 4403, 4406, 4494, 4491, 4493, 4492, 4491, 4494, 4495, 4489, 4496, 4489, 4490, 4496, 4411, 4495, 4499, 4411, 4409, 4495, 4495, 4496, 4499, 4490, 4497, 4496, 4496, 4497, 4499, 4414, 4498, 4493, 4414, 4493, 4406, 4494, 4493, 4498, 4492, 4494, 4498, 4497, 4501, 4499, 4500, 4501, 4497, 348, 4501, 4513, 4432, 4411, 4499, 4436, 4432, 4501, 4432, 4499, 4501, 348, 4436, 4501, 4039, 4502, 4437, 4503, 4502, 4041, 4041, 4502, 4039, 4041, 4042, 4503, 3794, 4441, 4043, 4437, 4502, 4444, 4502, 4503, 4444, 4444, 4503, 4504, 4444, 4504, 4445, 4504, 4447, 4445, 4504, 4455, 4447, 4043, 4441, 3850, 4504, 4464, 4455, 4504, 4506, 4464, 4506, 4470, 4464, 4505, 4506, 4504, 4506, 4505, 4470, 4507, 4505, 5398, 4507, 4161, 4508, 4162, 4477, 4474, 4505, 4507, 4478, 4507, 4508, 4478, 4162, 4509, 4477, 4509, 4488, 4477, 4162, 4163, 4509, 4509, 4497, 4488, 4488, 4497, 4490, 4512, 4500, 4497, 4497, 4164, 4512, 4164, 4510, 4512, 4165, 4511, 4510, 351, 348, 4511, 4512, 4513, 4500, 4513, 4501, 4500, 4511, 4513, 4512, 4512, 4510, 4511, 4511, 348, 4513, 3945, 4514, 3946, 4515, 4516, 4514, 4516, 4515, 4517, 4515, 4518, 4517, 4517, 4518, 4594, 3948, 3946, 4519, 4514, 4519, 3946, 4519, 4514, 4516, 4516, 4517, 4519, 4517, 4520, 4519, 4520, 4517, 4594, 4519, 4521, 3948, 4700, 4522, 4524, 4521, 4519, 4523, 4519, 4520, 4523, 4523, 4520, 4525, 4526, 4527, 4524, 4597, 4525, 4520, 4521, 4528, 3949, 4521, 4530, 4529, 4521, 4529, 4528, 4524, 4522, 4531, 4521, 4523, 4530, 4525, 4530, 4523, 4524, 4531, 4526, 4531, 4532, 4526, 4525, 4597, 4603, 4532, 4533, 4526, 4528, 4542, 3949, 4529, 4534, 4528, 4528, 4534, 4542, 4534, 4529, 4530, 4535, 4534, 4530, 4535, 4530, 4536, 4530, 4525, 4536, 4525, 4603, 4536, 4536, 4603, 4537, 4708, 4538, 4533, 4532, 4708, 4533, 4534, 4540, 4542, 4535, 4539, 4534, 4534, 4539, 4540, 4539, 4535, 4541, 4535, 4536, 4541, 4541, 4536, 4537, 4543, 4540, 4539, 4539, 4541, 4543, 4541, 4537, 4544, 4543, 4541, 4544, 4540, 4545, 4542, 4545, 4540, 4543, 4546, 4545, 4543, 4543, 4544, 4546, 4546, 4544, 4547, 4547, 4544, 4548, 4542, 4550, 3950, 4545, 4549, 4542, 4542, 4549, 4550, 4546, 4725, 4545, 4545, 4725, 4549, 4546, 4547, 4725, 4549, 4551, 4550, 4550, 4552, 3951, 4550, 4551, 4553, 4553, 4552, 4550, 4553, 4744, 4555, 4555, 4552, 4553, 3951, 4552, 4554, 3954, 3951, 4554, 4552, 4555, 4554, 4558, 4554, 4556, 4556, 4554, 4555, 4744, 4557, 4555, 4555, 4557, 4556, 3959, 4554, 4558, 3959, 3954, 4554, 3961, 4560, 4559, 4568, 4560, 3971, 4556, 4562, 4558, 4556, 4557, 4561, 4562, 4556, 4561, 4753, 4562, 4561, 4558, 3973, 3959, 4558, 4615, 3973, 4562, 4615, 4558, 4560, 4564, 4563, 4560, 4563, 4559, 4564, 4566, 4565, 4563, 4564, 4565, 4566, 4567, 4565, 4568, 4564, 4560, 4568, 4569, 4564, 4569, 4566, 4564, 4570, 4569, 4568, 4566, 4569, 4570, 4575, 4615, 4571, 4571, 4615, 4562, 4753, 4571, 4562, 4559, 4563, 3961, 4565, 4577, 4563, 4566, 4574, 4567, 3977, 4572, 4568, 4572, 4573, 4568, 4573, 4570, 4568, 4566, 4570, 4574, 4574, 4570, 4573, 4048, 4572, 3979, 3979, 4572, 3977, 4571, 4753, 4576, 4575, 4571, 4576, 4563, 3984, 3961, 3961, 3984, 3967, 4577, 3984, 4563, 4573, 4572, 4579, 4579, 4574, 4573, 4572, 4048, 4579, 4579, 4580, 4574, 4574, 4580, 4578, 4577, 4581, 3984, 4581, 3996, 3984, 3996, 4581, 4003, 4007, 4582, 3998, 4582, 4009, 3998, 4003, 4585, 4021, 4016, 4582, 4007, 4583, 4582, 4016, 4009, 4584, 4094, 4009, 4582, 4584, 4582, 4583, 4584, 4586, 4021, 4585, 4023, 4021, 4586, 4023, 4586, 4025, 4016, 4026, 4583, 4583, 4027, 4584, 4026, 4027, 4583, 4581, 4585, 4003, 4581, 4588, 4585, 4025, 4587, 4030, 4025, 4586, 4587, 4586, 4585, 4587, 4031, 4587, 4585, 4031, 4585, 4588, 4587, 4031, 4030, 4588, 4589, 4031, 4031, 4589, 4035, 4588, 4590, 4589, 4588, 4776, 4590, 4589, 4591, 4035, 4035, 4591, 4042, 4589, 4592, 4591, 4590, 4592, 4589, 4590, 4776, 4592, 4518, 4777, 4594, 4595, 4520, 4594, 4594, 4777, 4595, 4595, 4777, 4596, 4781, 4596, 4777, 4597, 4520, 4595, 4599, 4527, 4526, 4598, 4599, 4526, 4600, 4597, 4596, 4596, 4597, 4595, 4599, 4598, 4601, 4596, 4602, 4600, 4781, 4602, 4596, 4526, 4533, 4598, 4533, 4604, 4598, 4600, 4605, 4597, 4597, 4605, 4603, 4598, 4604, 4606, 4601, 4598, 4606, 4600, 4607, 4605, 4600, 4602, 4607, 4538, 4608, 4604, 4533, 4538, 4604, 4603, 4605, 4609, 4604, 4608, 4606, 4605, 4610, 4609, 4607, 4610, 4605, 4602, 4610, 4607, 4537, 4603, 4609, 4537, 4609, 4544, 4544, 4609, 4612, 4609, 4611, 4612, 4610, 4611, 4609, 4544, 4612, 4548, 4548, 4612, 4613, 4612, 4614, 4613, 4611, 4614, 4612, 4548, 4613, 4818, 4818, 4613, 4614, 4575, 4576, 4616, 4615, 4618, 4045, 4575, 4618, 4615, 4620, 4048, 4050, 4618, 4575, 4616, 4618, 4052, 4049, 4624, 4052, 4618, 4624, 4618, 4619, 4618, 4616, 4619, 4616, 4617, 4619, 4629, 4624, 4619, 4617, 4629, 4619, 4056, 4620, 4050, 4620, 4579, 4048, 4620, 4621, 4579, 4580, 4579, 4621, 4622, 4620, 4056, 4621, 4620, 4622, 4868, 4621, 4623, 4058, 4052, 4624, 4627, 4625, 4623, 4868, 4623, 4625, 4624, 4629, 4058, 4868, 4625, 4903, 4626, 4062, 4067, 4626, 4622, 4062, 4622, 4056, 4062, 4622, 4627, 4621, 4626, 4627, 4622, 4621, 4627, 4623, 4628, 4626, 4067, 4627, 4626, 4628, 4627, 4628, 4634, 4067, 4122, 4628, 4584, 4027, 4094, 4855, 4891, 4617, 4617, 4891, 4629, 4631, 4629, 4891, 4891, 4630, 4631, 4893, 4630, 4891, 4893, 4636, 4630, 4058, 4629, 4117, 4629, 4631, 4117, 4903, 4625, 4632, 4631, 4635, 4117, 4634, 4632, 4625, 4903, 4632, 4908, 4627, 4634, 4625, 4122, 4633, 4628, 4634, 4628, 4633, 4122, 4123, 4633, 5240, 4678, 5397, 4630, 4636, 4631, 4636, 4635, 4631, 4133, 4635, 4638, 4636, 4637, 4635, 4637, 4638, 4635, 4639, 4638, 4637, 4640, 4632, 4634, 4645, 4640, 4634, 4908, 4632, 4641, 4641, 4632, 4640, 4640, 4645, 4642, 4641, 4640, 4642, 4643, 4135, 4133, 4133, 4638, 4643, 4644, 4135, 4643, 4638, 4644, 4643, 4638, 4639, 4644, 4646, 4633, 4142, 4634, 4633, 4645, 4645, 4633, 4646, 4645, 4646, 4642, 4642, 4649, 4641, 4646, 4142, 4647, 4648, 4646, 4647, 4642, 4646, 4648, 4649, 4642, 4648, 4135, 4650, 4146, 4644, 4650, 4135, 4639, 4942, 4644, 4644, 4942, 4650, 4647, 4147, 4651, 4647, 4651, 4648, 4146, 4650, 4148, 4649, 4648, 4659, 4648, 4651, 4659, 4942, 4652, 4650, 4650, 4652, 4654, 4650, 4654, 4148, 4652, 4653, 4654, 4659, 4651, 4656, 4148, 4654, 4149, 4149, 4654, 4657, 4656, 4651, 4655, 4152, 4658, 4153, 4153, 4658, 4656, 4658, 4659, 4656, 4656, 4655, 4153, 4956, 4660, 4654, 4654, 4660, 4657, 4658, 4152, 4664, 4666, 4659, 4658, 4661, 4667, 4158, 4661, 4657, 4662, 4667, 4661, 4662, 4663, 4662, 4660, 4660, 4662, 4657, 4658, 4664, 4665, 4665, 4666, 4658, 4662, 4668, 4667, 4659, 4666, 4669, 4670, 4668, 4662, 4662, 4663, 4670, 4665, 4664, 4671, 4665, 4671, 4672, 4672, 4666, 4665, 4678, 4666, 4672, 4667, 4160, 4158, 4667, 4668, 4160, 4673, 4669, 4666, 4673, 4666, 4678, 4668, 4670, 4163, 4160, 4668, 4163, 4664, 4161, 4676, 4677, 4671, 4676, 4671, 4664, 4676, 4675, 4671, 4677, 4675, 4672, 4671, 4678, 4672, 4675, 4679, 4677, 4676, 5399, 4679, 4676, 4678, 4685, 4674, 4673, 4678, 4674, 4653, 4956, 4654, 4663, 4983, 4670, 4983, 4683, 4670, 4688, 4683, 4680, 4163, 4670, 4682, 4681, 4673, 4674, 4685, 4681, 4674, 4670, 4683, 4682, 4682, 4683, 4684, 4684, 4164, 4682, 4683, 4688, 4684, 4686, 4687, 6494, 4687, 4686, 354, 4692, 4164, 4684, 4692, 4684, 4689, 4688, 4689, 4684, 4688, 4691, 4690, 4688, 4690, 4689, 4691, 360, 4690, 4165, 4164, 4692, 4693, 4165, 4692, 4690, 4694, 4693, 4689, 4690, 4693, 4689, 4693, 4692, 4695, 4694, 4690, 5400, 4694, 4695, 4690, 360, 4695, 4696, 4701, 4700, 4696, 4700, 4697, 4700, 4698, 4697, 4696, 4697, 4699, 4697, 4698, 4699, 4700, 4702, 4698, 4702, 4699, 4698, 4699, 4702, 4704, 4699, 4704, 4696, 4700, 4701, 4522, 4696, 4710, 4703, 4696, 4703, 4701, 4710, 4696, 4704, 4700, 4527, 4702, 4524, 4527, 4700, 4701, 4706, 4522, 4701, 4703, 4706, 4703, 4710, 4707, 4705, 4531, 4522, 4705, 4522, 4706, 4706, 4703, 4707, 4532, 4531, 4705, 4708, 4532, 4705, 4538, 4708, 4709, 4705, 4712, 4711, 4706, 4712, 4705, 4707, 4716, 4713, 4707, 4713, 4706, 4706, 4713, 4712, 4707, 4714, 4716, 4705, 4711, 4708, 4707, 4710, 4714, 4708, 4711, 4709, 4710, 4796, 4714, 4711, 4712, 4715, 4712, 4713, 4726, 4714, 4796, 4718, 4712, 4719, 4715, 4712, 4726, 4719, 4726, 4713, 4716, 4716, 4714, 5025, 4711, 4715, 4709, 4715, 4717, 4709, 4720, 4721, 4719, 4719, 4722, 4715, 4721, 4724, 4719, 4724, 4722, 4719, 4722, 4723, 4717, 4715, 4722, 4717, 4724, 4723, 4722, 4730, 4724, 4721, 4714, 4718, 5025, 4732, 4719, 4726, 4716, 5025, 4726, 4547, 4727, 4725, 4721, 4732, 4728, 4720, 4732, 4721, 4720, 4719, 4732, 4727, 4547, 4548, 4721, 4728, 4729, 4730, 4721, 4729, 4731, 4549, 4725, 4732, 4726, 4733, 4733, 4726, 5025, 4731, 4725, 4727, 4732, 4734, 4728, 4728, 4734, 4729, 4735, 4548, 4818, 4727, 4548, 4735, 4551, 4549, 4731, 4737, 4551, 4731, 4732, 4733, 4736, 4727, 4737, 4731, 4737, 4727, 4738, 5027, 4734, 4736, 4736, 4734, 4732, 4727, 4735, 4738, 4738, 4735, 4833, 4551, 4737, 4553, 4733, 4740, 4739, 4736, 4733, 4739, 4741, 4737, 4738, 5027, 4736, 4739, 4743, 4738, 4833, 4743, 4741, 4738, 4740, 5029, 4739, 4553, 4737, 4744, 4737, 4742, 4744, 4742, 4737, 4741, 4744, 4742, 4747, 4741, 4745, 4742, 4741, 4743, 4745, 4745, 4743, 4746, 4744, 4750, 4557, 4747, 4750, 4744, 4742, 4748, 4747, 4748, 4742, 4745, 4745, 4746, 4748, 4748, 4746, 4749, 4557, 4750, 4561, 4561, 4750, 4751, 4747, 4752, 4750, 4750, 4752, 4751, 4752, 4747, 4748, 4753, 4561, 4754, 4561, 4751, 4754, 4752, 4755, 4751, 4755, 4754, 4751, 4748, 4755, 4752, 4844, 4753, 4754, 4843, 4753, 4844, 4755, 4844, 4754, 4749, 4844, 4755, 4755, 4748, 4749, 4567, 4756, 4757, 4565, 4567, 4757, 4757, 4756, 4758, 4759, 4756, 4567, 4759, 4760, 4761, 4759, 4761, 4756, 4758, 4756, 4762, 4762, 4756, 4761, 4763, 4768, 4758, 4763, 4758, 4764, 4764, 4758, 4762, 4764, 4762, 5043, 4765, 4753, 4843, 4766, 4761, 4760, 4852, 4766, 4760, 4762, 4761, 4853, 4853, 4761, 4766, 5043, 4762, 4853, 4757, 4577, 4565, 4758, 4767, 4757, 4757, 4767, 4577, 4768, 4767, 4758, 4574, 4759, 4567, 4759, 4769, 4760, 4753, 4765, 4576, 4760, 4769, 4852, 4769, 4770, 4852, 4574, 4771, 4759, 4578, 4771, 4574, 4759, 4771, 4769, 4578, 4770, 4771, 4771, 4770, 4769, 4767, 4772, 4577, 4772, 4773, 4577, 4577, 4773, 4581, 4772, 5057, 4773, 4773, 4774, 4581, 4581, 4774, 4588, 4773, 4775, 4774, 5057, 4775, 4773, 4774, 4776, 4588, 4775, 4593, 4776, 4775, 4776, 4774, 5356, 4593, 4775, 5240, 4593, 5356, 4776, 4593, 4592, 4777, 4518, 4778, 4777, 4778, 4779, 4780, 4779, 4869, 4779, 4778, 4869, 4781, 4777, 4779, 4780, 4781, 4779, 4782, 4781, 4780, 4702, 4527, 4783, 4527, 4599, 4783, 4702, 4783, 4704, 4783, 4784, 4704, 4784, 4787, 4704, 4783, 4599, 4601, 4785, 4783, 4601, 4784, 4783, 4785, 4787, 4784, 4785, 4786, 4602, 4781, 4786, 4781, 4782, 4704, 4787, 4710, 4785, 4601, 4788, 4601, 4606, 4788, 4787, 4785, 4788, 4786, 4789, 4602, 4782, 4791, 4790, 4786, 4782, 4790, 4786, 4790, 4789, 4792, 4538, 4709, 4792, 4793, 4538, 4793, 4608, 4538, 4793, 4710, 4787, 4792, 4710, 4793, 4788, 4606, 4608, 4793, 4788, 4608, 4787, 4788, 4793, 4789, 4798, 4602, 4602, 4798, 4610, 4790, 4794, 4789, 4789, 4794, 4798, 4794, 4790, 4795, 4790, 4791, 4795, 4795, 4791, 4804, 4796, 4710, 4792, 4794, 4797, 4798, 4797, 4794, 4801, 4794, 4795, 4801, 4801, 4795, 4803, 4804, 4803, 4795, 4792, 4709, 4805, 4792, 4805, 4806, 4796, 4792, 4799, 4792, 4806, 4799, 4799, 4718, 4796, 4798, 4797, 4800, 4797, 4801, 4800, 4798, 4802, 4610, 4610, 4802, 4611, 4802, 4798, 4800, 4709, 4717, 4805, 4801, 4808, 4800, 4800, 4808, 4807, 4807, 4802, 4800, 4801, 4803, 4808, 4808, 4803, 4809, 4803, 4804, 4809, 4809, 4804, 4810, 4717, 4723, 4805, 4806, 4805, 4723, 4724, 4806, 4723, 4724, 4730, 4811, 4724, 4811, 4806, 4806, 4811, 4799, 4718, 4799, 4812, 4812, 4799, 4811, 4807, 4813, 4815, 4808, 4814, 4813, 4808, 4813, 4807, 4802, 4815, 4611, 4611, 4815, 4614, 4815, 4802, 4807, 4814, 4808, 4816, 4816, 4808, 4809, 4809, 4810, 4816, 4816, 4810, 4817, 4730, 4812, 4811, 4730, 4819, 4812, 4730, 4729, 4819, 4819, 4826, 4812, 4820, 4818, 4813, 4820, 4813, 4814, 4815, 4813, 4818, 4818, 4614, 4815, 4820, 4821, 4822, 4820, 4814, 4821, 4814, 4816, 4821, 4822, 4821, 4823, 4816, 4817, 4823, 4821, 4816, 4823, 4824, 4735, 4818, 4729, 4734, 4819, 4818, 4820, 4824, 4826, 4819, 4825, 4825, 4819, 4734, 4824, 4822, 4827, 4820, 4822, 4824, 4827, 4834, 4824, 4828, 4826, 4825, 4827, 4822, 4823, 4827, 4823, 4829, 4735, 4824, 4833, 4833, 4824, 4834, 5027, 5063, 4825, 4734, 5027, 4825, 4834, 4827, 4831, 4825, 5063, 4828, 4827, 4829, 4831, 4831, 4829, 4832, 4831, 4832, 4836, 4836, 4834, 4831, 4833, 4835, 4743, 4743, 4835, 4746, 4835, 4833, 4834, 4746, 4835, 4837, 4835, 4834, 4837, 4837, 4834, 4838, 4838, 4834, 4839, 4834, 4836, 4839, 4839, 4836, 4840, 4837, 4841, 4749, 4837, 4749, 4746, 4837, 4838, 4841, 4842, 4841, 4838, 4842, 4838, 4839, 4842, 4840, 4851, 4842, 4839, 4840, 4749, 4841, 4844, 4846, 4843, 4844, 4845, 4843, 4846, 4849, 4846, 4844, 4844, 4841, 4849, 4848, 4845, 4846, 4847, 4845, 4848, 4849, 4848, 4846, 4841, 4842, 4849, 4850, 4848, 4849, 4842, 4851, 4849, 4849, 4851, 4850, 4853, 4766, 4854, 4843, 4845, 4765, 4765, 4845, 4855, 4856, 4766, 4852, 4854, 4766, 4857, 4845, 4847, 4855, 4857, 4766, 4856, 4854, 4857, 4858, 4857, 4856, 4859, 4860, 4857, 4859, 4858, 4857, 4860, 4861, 4858, 4860, 4576, 4765, 4616, 4770, 4862, 4852, 4616, 4765, 4855, 4852, 4862, 4856, 4617, 4616, 4855, 4859, 4856, 4863, 4863, 4856, 4862, 4896, 4864, 4859, 4864, 4860, 4859, 4861, 4860, 4865, 4860, 4864, 4865, 4866, 4862, 4770, 4866, 4867, 4862, 4862, 4867, 4863, 4864, 4896, 4901, 4901, 4865, 4864, 4578, 4868, 4770, 4578, 4580, 4868, 4580, 4621, 4868, 4770, 4868, 4866, 4866, 4868, 4867, 4870, 4780, 4869, 4782, 4780, 4871, 4780, 4870, 4871, 4782, 4871, 4791, 4872, 4791, 4871, 4791, 4872, 4804, 4872, 4873, 4804, 4872, 4920, 4873, 4804, 4873, 4810, 4810, 4873, 4874, 4810, 4874, 4875, 4875, 4874, 5092, 4823, 4817, 4876, 4876, 4817, 4810, 5091, 4876, 4875, 4876, 4810, 4875, 4878, 4877, 4823, 4877, 4829, 4823, 4823, 4876, 4878, 4876, 5091, 4878, 5091, 4879, 4878, 4829, 4877, 4832, 4880, 4877, 4878, 4880, 4832, 4877, 4880, 4878, 4879, 4881, 4880, 4879, 4832, 4882, 4836, 4832, 4880, 4882, 4882, 4880, 4883, 4880, 4881, 4883, 4836, 4882, 4884, 4883, 4885, 4882, 4885, 4884, 4882, 4883, 5096, 4885, 4851, 4840, 4884, 4840, 4836, 4884, 4884, 4886, 5099, 4884, 4885, 4886, 4886, 4885, 5096, 5099, 4886, 4887, 4888, 4847, 4848, 4850, 4888, 4848, 4847, 4888, 4891, 4892, 4850, 4889, 4889, 4850, 4851, 4888, 4850, 4890, 4892, 4890, 4850, 4888, 4894, 4891, 4890, 4894, 4888, 4891, 4855, 4847, 4859, 4863, 4896, 4894, 4893, 4891, 4894, 4890, 4935, 4895, 4890, 4892, 4935, 4890, 4895, 4896, 4863, 4867, 4902, 4865, 4901, 4903, 4897, 4896, 4897, 4901, 4896, 4897, 4903, 4905, 4892, 5128, 4895, 4893, 4894, 4898, 4893, 4898, 4636, 4895, 4899, 4935, 4895, 4900, 4899, 4895, 5128, 4900, 4867, 4903, 4896, 4868, 4903, 4867, 4901, 4897, 4907, 4907, 4904, 4902, 4907, 4902, 4901, 4897, 4905, 4906, 4897, 4906, 4907, 4908, 4906, 4905, 4908, 4905, 4903, 4899, 4900, 4909, 4904, 4910, 4911, 4907, 4910, 4904, 4907, 4912, 4910, 4906, 4912, 4907, 4906, 4908, 4913, 4906, 4913, 4912, 4911, 4910, 4915, 4910, 4912, 4914, 4910, 4914, 4915, 4914, 4913, 4916, 4912, 4913, 4914, 4915, 4914, 4918, 4918, 4914, 4917, 4914, 4916, 4917, 4918, 4917, 5182, 5240, 4994, 4678, 4870, 4919, 4872, 4871, 4870, 4872, 4920, 4872, 4921, 4873, 4920, 4874, 4874, 4920, 4922, 4920, 4921, 4922, 4874, 4922, 5092, 4925, 4923, 4924, 4925, 4924, 4926, 4924, 4927, 4926, 4928, 4925, 4926, 4928, 4927, 4929, 4928, 4926, 4927, 4886, 4923, 4887, 4930, 4887, 4925, 4925, 4887, 4923, 4925, 4931, 4930, 4932, 4931, 4928, 4931, 4925, 4928, 4928, 4929, 4932, 4932, 4929, 4933, 4930, 5204, 4887, 4930, 4931, 4933, 4931, 4932, 4933, 4898, 4934, 4636, 4894, 4935, 4898, 4898, 4935, 4934, 4934, 4637, 4636, 4935, 4936, 4934, 4639, 4934, 4936, 4639, 4637, 4934, 4937, 4908, 4641, 4935, 4899, 4938, 4899, 4909, 4938, 4935, 4938, 4939, 4935, 4939, 4936, 4939, 4639, 4936, 4913, 4908, 4940, 4908, 4937, 4940, 4909, 5222, 4938, 4649, 4940, 4641, 4940, 4937, 4641, 4938, 5222, 4947, 4939, 4941, 4639, 4939, 4943, 4941, 4939, 4938, 4943, 4947, 4943, 4938, 4941, 4942, 4639, 4941, 4944, 4942, 4943, 4945, 4941, 4945, 4944, 4941, 4913, 4940, 4946, 4913, 4946, 4916, 4916, 4946, 4917, 4940, 4649, 4946, 4942, 4948, 4652, 4944, 4948, 4942, 4945, 4949, 4944, 4944, 4949, 4948, 4943, 4949, 4945, 4943, 4951, 4949, 4943, 4947, 4951, 4950, 4949, 4951, 4917, 4946, 5182, 4649, 4659, 4955, 4946, 4649, 4952, 4952, 4649, 4955, 4953, 4946, 4952, 4652, 4948, 4653, 4971, 4948, 4949, 4949, 4950, 4971, 4954, 4953, 4955, 4953, 4952, 4955, 4948, 4971, 4956, 4954, 4955, 5233, 4955, 4659, 4957, 4663, 4660, 4956, 4958, 4955, 4957, 4955, 4958, 5233, 4957, 4959, 4958, 4960, 4957, 4669, 4957, 4659, 4669, 4957, 4960, 4959, 4961, 4959, 4960, 4961, 4984, 4959, 4669, 4673, 4960, 4960, 4962, 4961, 4960, 4673, 4962, 4872, 4919, 4963, 4872, 4963, 4921, 5290, 4921, 4963, 4921, 5290, 5190, 4965, 4950, 4951, 4967, 4966, 4968, 4969, 4974, 4967, 4967, 4968, 4969, 4968, 4970, 4969, 4971, 4950, 4972, 4972, 4950, 4965, 4965, 4966, 4972, 4972, 4966, 4973, 4966, 4967, 4973, 4973, 4967, 4974, 4948, 4956, 4653, 4971, 4977, 4956, 4971, 4972, 4977, 4977, 4972, 4973, 4974, 4975, 4973, 4974, 4976, 4975, 4979, 4680, 4977, 4973, 4975, 4979, 4977, 4973, 4979, 4980, 5282, 4978, 4981, 4979, 4975, 4981, 4975, 4976, 5282, 4980, 5288, 4663, 4977, 4983, 4663, 4956, 4977, 4680, 4983, 4977, 4978, 4958, 4986, 4958, 4978, 5233, 4978, 4986, 4982, 4982, 4980, 4978, 5014, 4987, 4981, 4981, 4680, 4979, 5288, 4980, 4982, 4958, 4959, 4986, 4983, 4680, 4683, 4986, 4959, 4984, 4985, 4986, 4984, 4981, 4987, 4680, 4961, 4988, 4984, 4984, 4988, 4985, 4990, 4986, 4985, 4990, 4985, 4989, 4989, 4985, 4988, 4991, 4990, 4989, 4986, 4992, 4982, 4986, 4990, 4992, 4990, 4991, 4992, 4982, 4992, 5018, 4962, 4673, 4993, 4993, 4673, 4681, 4961, 4962, 4988, 4988, 4962, 4993, 4994, 4685, 4678, 4685, 4995, 4681, 4681, 4995, 4993, 4988, 4993, 4995, 4685, 4994, 4995, 4988, 4995, 4989, 4989, 4995, 5023, 4995, 4994, 5023, 4989, 5023, 4991, 4994, 5289, 5023, 4964, 6492, 4996, 4964, 4996, 4997, 4964, 4997, 4963, 6492, 6494, 4996, 6494, 4687, 4996, 4687, 4997, 4996, 4963, 4997, 5290, 5290, 4997, 410, 4998, 415, 414, 4999, 415, 4998, 4999, 412, 415, 541, 412, 4999, 4999, 4998, 5003, 5001, 5004, 5002, 5294, 5000, 4999, 5294, 4999, 5003, 414, 5003, 4998, 414, 416, 5003, 5294, 5003, 416, 5005, 5002, 5006, 5005, 5001, 5002, 5006, 5002, 5007, 5002, 5004, 5007, 5008, 5006, 5007, 5008, 5005, 5006, 5009, 4974, 4969, 4970, 5010, 4969, 5010, 5009, 4969, 4974, 5009, 5011, 5010, 5013, 5009, 5009, 5013, 5011, 4976, 5011, 5012, 4976, 4974, 5011, 5012, 5011, 5013, 4976, 5014, 4981, 5014, 4976, 5012, 5015, 5014, 5012, 5013, 5015, 5012, 5014, 5017, 4987, 5014, 5015, 5017, 5015, 5016, 5017, 5016, 5019, 5017, 5338, 5019, 5016, 4987, 5017, 4688, 4680, 4987, 4688, 4992, 4991, 5020, 5021, 4992, 5020, 5018, 4992, 5021, 5018, 5021, 5289, 5017, 5024, 4688, 5019, 5024, 5017, 424, 5024, 5019, 5019, 5338, 5022, 5019, 5022, 424, 5022, 425, 424, 4991, 5023, 5020, 5021, 5020, 5023, 5289, 5021, 5023, 4691, 4688, 5024, 5024, 360, 4691, 5024, 424, 360, 4718, 4812, 5025, 5025, 5026, 4733, 4733, 5026, 4740, 5028, 5026, 5025, 5031, 5027, 4739, 5031, 5032, 5027, 5025, 5062, 5028, 5032, 5063, 5027, 5028, 5062, 5068, 5029, 4740, 5030, 5030, 4740, 5026, 5030, 5026, 5028, 5029, 5031, 4739, 5030, 5035, 5029, 5033, 5035, 5030, 5028, 5033, 5030, 5033, 5028, 5040, 5035, 5034, 5029, 5029, 5034, 5031, 5031, 5034, 5032, 5034, 5036, 5032, 5028, 5068, 5075, 5033, 5039, 5037, 5039, 5033, 5040, 5035, 5037, 5034, 5037, 5035, 5033, 5040, 5028, 5075, 5037, 5039, 5038, 5037, 5042, 5041, 5034, 5037, 5041, 5042, 5037, 5038, 5034, 5041, 5036, 5041, 5043, 5036, 5036, 5043, 5078, 5039, 5045, 5044, 5038, 5039, 5044, 5039, 5040, 5045, 4764, 5041, 5042, 4764, 5042, 4763, 5047, 5046, 5042, 5046, 4763, 5042, 5042, 5038, 5047, 5038, 5044, 5047, 5041, 4764, 5043, 5046, 5047, 5048, 5048, 5047, 5049, 5078, 5043, 4853, 5046, 5048, 5083, 5085, 5048, 5049, 5040, 5075, 5132, 4768, 5051, 5050, 4767, 4768, 5050, 5051, 5044, 5045, 5051, 5045, 5050, 5040, 5053, 5045, 5045, 5053, 5050, 5046, 4768, 4763, 5046, 5052, 4768, 5052, 5051, 4768, 5044, 5052, 5047, 5051, 5052, 5044, 5047, 5052, 5049, 5052, 5083, 5089, 5046, 5083, 5052, 5049, 5089, 5085, 5052, 5089, 5049, 5050, 4772, 4767, 5050, 5053, 4772, 5054, 5053, 5040, 5054, 5040, 5132, 5053, 5055, 4772, 5053, 5054, 5055, 5054, 5132, 5342, 5055, 5343, 4772, 4772, 5343, 5057, 5343, 5056, 5057, 5056, 5058, 5057, 5059, 5061, 5058, 5059, 5058, 5056, 5356, 5061, 5059, 5060, 4775, 5057, 5057, 5058, 5060, 5060, 5058, 5061, 5356, 4775, 5060, 5061, 5356, 5060, 4812, 4826, 5062, 4812, 5062, 5025, 5064, 5062, 4826, 4828, 5064, 4826, 5064, 4828, 4830, 5064, 5067, 5062, 5063, 5065, 4830, 5063, 4830, 4828, 5065, 5064, 4830, 5095, 5067, 5064, 5065, 5095, 5064, 5062, 5067, 5068, 5063, 5066, 5065, 5032, 5066, 5063, 5069, 5070, 5066, 5069, 5066, 5071, 5070, 5069, 5068, 5070, 5068, 5067, 5032, 5036, 5066, 5036, 5071, 5066, 5068, 5105, 5075, 5071, 5072, 5069, 5072, 5073, 5069, 5073, 5074, 5069, 5074, 5068, 5069, 5074, 5105, 5068, 5036, 5078, 5071, 5078, 5079, 5071, 5071, 5079, 5076, 5072, 5071, 5076, 5072, 5076, 5073, 5076, 5077, 5073, 5075, 5105, 5082, 5076, 5079, 5080, 5076, 5080, 5077, 5080, 5081, 5077, 5083, 5048, 5084, 5084, 5048, 5085, 5078, 4854, 5079, 5078, 4853, 4854, 5083, 5084, 5086, 5085, 5086, 5084, 5079, 5087, 5080, 5079, 4854, 5087, 4854, 4858, 5087, 5087, 4858, 4861, 5088, 5087, 4861, 5088, 5080, 5087, 5088, 5081, 5080, 5075, 5082, 5132, 5083, 5086, 5089, 5085, 5089, 5086, 5088, 4861, 5090, 4861, 4865, 5090, 5119, 5088, 5090, 5356, 5367, 5240, 4875, 5092, 5091, 4881, 4879, 5091, 5091, 5092, 4881, 4881, 5092, 5093, 5093, 5092, 5191, 5095, 5065, 5094, 5094, 5065, 5066, 4883, 4881, 5093, 5096, 4883, 5093, 5096, 5093, 5097, 5066, 5070, 5094, 5067, 5095, 5094, 5070, 5067, 5094, 5098, 5096, 5097, 5101, 5074, 5073, 5100, 5101, 5073, 5101, 5105, 5074, 5107, 5105, 5101, 5096, 5098, 4886, 5102, 5101, 5100, 5103, 5107, 5101, 5102, 5103, 5101, 5077, 5104, 5073, 4884, 5099, 5110, 5073, 5104, 5100, 5104, 5111, 5100, 5099, 4887, 5106, 5099, 5106, 5110, 5100, 5111, 5102, 5107, 5103, 5201, 5112, 4851, 4884, 5108, 5109, 5104, 5108, 5104, 5081, 5081, 5104, 5077, 5109, 5108, 5082, 5109, 5082, 5105, 4884, 5110, 5112, 5104, 5109, 5111, 5105, 5107, 5109, 5106, 4887, 5118, 5107, 5201, 5111, 5109, 5107, 5111, 4889, 4851, 5112, 5081, 5088, 5113, 5108, 5081, 5113, 5082, 5108, 5113, 5082, 5113, 5114, 5082, 5114, 5132, 5115, 5110, 5116, 5115, 5112, 5110, 5110, 5106, 5116, 5117, 5132, 5114, 5106, 5118, 5116, 5113, 5088, 5119, 5119, 5114, 5113, 5120, 5114, 5119, 5112, 4892, 4889, 5112, 5115, 4892, 5115, 5121, 4892, 5122, 5121, 5115, 5114, 5120, 5117, 5115, 5116, 5122, 5116, 5118, 5123, 5090, 4865, 4902, 5124, 5090, 4902, 5119, 5090, 5124, 5119, 5124, 5125, 5125, 5120, 5119, 5127, 5120, 5125, 4892, 5121, 5126, 5121, 5122, 5126, 5127, 5117, 5120, 5117, 5127, 5132, 5128, 5126, 5129, 5128, 4892, 5126, 5129, 5126, 5122, 5129, 5122, 5130, 5130, 5122, 5116, 5131, 5130, 5123, 5130, 5116, 5123, 5124, 5133, 5125, 4902, 5133, 5124, 5125, 5133, 5127, 5133, 4902, 4904, 5132, 5127, 5134, 5134, 5127, 5133, 5129, 5136, 5135, 5128, 5129, 5135, 5130, 5131, 5137, 5130, 5137, 5129, 5129, 5137, 5136, 5135, 4900, 5128, 4909, 4900, 5135, 5131, 5138, 5145, 5131, 5145, 5137, 5139, 5140, 5133, 5133, 5140, 5134, 5133, 4904, 5141, 4904, 4911, 5141, 5139, 5133, 5141, 5136, 5143, 5142, 5135, 5136, 5142, 5137, 5144, 5136, 5136, 5144, 5143, 5137, 5145, 5144, 4909, 5135, 5142, 5145, 5138, 5146, 5139, 5147, 5158, 5140, 5139, 5158, 4911, 5148, 5141, 4915, 5148, 4911, 5141, 5148, 5149, 5141, 5149, 5139, 5139, 5149, 5147, 5148, 4915, 5142, 5148, 5142, 5143, 4915, 5152, 5142, 5142, 5152, 5153, 5148, 5143, 5149, 5143, 5154, 5149, 5143, 5144, 5154, 5150, 5145, 5151, 5150, 5154, 5145, 5154, 5144, 5145, 5151, 5145, 5146, 5151, 5146, 5155, 5156, 5142, 5153, 5155, 5146, 5157, 5158, 5147, 5160, 5140, 5158, 5159, 5147, 5149, 5160, 5151, 5162, 5150, 5162, 5161, 5150, 4918, 5152, 4915, 5149, 5163, 5160, 5149, 5154, 5163, 5150, 5163, 5154, 5162, 5151, 5155, 5164, 5152, 4918, 5164, 5153, 5152, 5164, 5165, 5153, 5165, 5156, 5153, 5158, 5166, 5167, 5159, 5158, 5168, 5158, 5167, 5168, 5160, 5169, 5158, 5158, 5169, 5166, 5161, 5170, 5169, 5150, 5161, 5169, 5172, 5171, 5173, 5160, 5163, 5169, 5163, 5150, 5169, 5182, 5174, 4918, 5174, 5164, 4918, 5165, 5174, 5172, 5165, 5164, 5174, 5174, 5171, 5172, 5175, 5172, 5173, 5165, 5172, 5175, 5176, 5167, 5166, 5176, 5177, 5167, 5177, 5168, 5167, 5187, 5176, 5170, 5187, 5170, 5178, 5176, 5166, 5170, 5166, 5169, 5170, 5179, 5180, 5183, 5187, 5178, 5179, 5178, 5180, 5179, 5178, 5181, 5180, 5171, 5174, 5182, 5171, 5182, 5183, 5180, 5184, 5183, 5184, 5175, 5183, 5171, 5183, 5173, 5175, 5173, 5183, 5181, 5185, 5180, 5185, 5184, 5180, 5177, 5176, 5186, 5176, 5187, 5186, 5179, 5183, 5188, 5179, 5188, 5187, 5183, 5182, 5189, 5183, 5189, 5188, 5189, 5235, 5188, 5187, 5188, 5236, 5236, 5188, 5235, 4921, 5190, 4922, 5092, 4922, 5191, 5191, 4922, 5190, 5191, 5190, 5192, 5193, 5097, 5191, 5097, 5093, 5191, 5193, 5191, 5192, 5193, 5192, 5196, 5194, 5097, 5193, 5194, 5098, 5097, 5194, 5193, 5195, 5195, 5193, 5196, 5196, 5192, 5197, 5098, 5194, 4923, 4886, 5098, 4923, 5194, 5195, 4924, 4923, 5194, 4924, 5195, 5198, 4924, 4924, 5198, 4927, 5195, 5196, 5198, 5198, 4929, 4927, 5199, 4929, 5198, 5198, 5196, 5197, 5198, 5197, 5199, 5200, 5199, 5197, 5103, 5102, 5201, 5199, 5203, 4929, 4929, 5203, 4933, 5203, 5199, 5202, 5202, 5199, 5200, 5202, 5200, 5207, 5208, 5118, 4887, 5204, 5208, 4887, 5111, 5201, 5102, 5204, 4930, 4933, 5202, 5205, 5212, 5203, 5202, 5212, 5206, 5205, 5202, 5203, 5212, 4933, 5202, 5207, 5206, 5208, 5209, 5118, 5208, 5204, 5210, 5211, 5209, 5208, 5210, 5211, 5208, 4933, 5210, 5204, 4933, 5212, 5210, 5213, 5211, 5205, 5213, 5205, 5214, 5211, 5210, 5205, 5210, 5212, 5205, 5205, 5206, 5214, 5213, 5214, 5251, 5214, 5206, 5207, 5214, 5207, 5215, 5214, 5215, 5251, 5216, 5215, 5207, 5123, 5118, 5209, 5123, 5209, 5217, 5211, 5217, 5209, 5211, 5213, 5221, 5217, 5211, 5221, 5213, 5251, 5218, 5213, 5218, 5221, 5123, 5217, 5131, 5131, 5217, 5219, 5219, 5217, 5220, 5217, 5221, 5220, 5138, 5219, 5220, 5131, 5219, 5138, 5223, 5138, 5220, 5224, 5223, 5220, 5221, 5224, 5220, 5224, 5221, 5266, 5222, 4909, 5142, 5146, 5138, 5223, 5142, 5156, 5222, 5146, 5223, 5157, 5157, 5223, 5369, 5222, 5156, 4947, 5156, 5225, 4947, 5156, 5165, 5225, 4947, 5225, 5226, 5226, 5225, 5227, 5226, 4951, 4947, 5229, 5165, 5175, 5225, 5165, 5229, 5231, 5225, 5229, 5231, 5227, 5225, 5231, 5228, 5227, 5182, 4946, 5230, 5175, 5184, 5229, 5185, 5229, 5184, 5231, 5229, 5185, 4953, 5230, 4946, 4953, 4954, 5230, 4954, 5232, 5230, 4954, 5233, 5232, 5281, 5232, 5233, 5281, 5238, 5232, 5231, 5234, 5228, 5189, 5230, 5237, 5182, 5230, 5189, 5235, 5189, 5237, 5236, 5235, 5239, 5230, 5232, 5237, 5238, 5237, 5232, 5238, 5235, 5237, 5239, 5235, 5238, 5238, 5281, 5287, 5239, 5238, 5287, 5291, 5190, 5290, 5241, 5190, 5291, 5190, 5241, 5192, 5192, 5241, 5243, 5192, 5243, 5197, 5243, 5241, 5242, 5197, 5243, 5200, 5243, 5248, 5200, 5244, 5248, 5243, 5242, 5244, 5243, 5242, 5245, 5244, 5200, 5248, 5207, 5245, 5246, 5244, 5248, 5244, 5250, 5244, 5247, 5249, 5246, 5247, 5244, 5249, 5250, 5244, 5207, 5248, 5216, 5252, 5251, 5215, 5252, 5215, 5216, 5252, 5216, 5253, 5216, 5248, 5253, 5253, 5248, 5254, 5248, 5250, 5254, 5311, 5255, 5249, 5255, 5250, 5249, 5255, 5254, 5250, 5221, 5218, 5251, 5221, 5257, 5256, 5251, 5252, 5221, 5252, 5258, 5221, 5258, 5257, 5221, 5252, 5253, 5258, 5257, 5259, 5256, 5258, 5259, 5257, 5260, 5259, 5258, 5260, 5258, 5253, 5260, 5253, 5264, 5264, 5253, 5254, 5255, 5311, 5261, 5255, 5261, 5254, 5221, 5256, 5266, 5256, 5259, 5260, 5256, 5260, 5262, 5262, 5260, 5263, 5260, 5264, 5263, 5254, 5261, 5264, 5264, 5261, 5265, 5256, 5262, 5266, 5263, 5264, 5376, 5263, 5376, 5266, 5263, 5266, 5262, 5376, 5265, 5377, 5264, 5265, 5376, 5376, 5267, 5266, 5376, 5268, 5267, 5226, 4965, 4951, 5226, 5269, 4965, 5269, 4966, 4965, 5276, 5226, 5227, 5269, 5226, 5276, 5269, 5271, 4966, 5271, 4968, 4966, 5278, 5269, 5276, 5271, 5269, 5278, 5267, 5270, 5277, 5271, 4970, 4968, 5272, 5271, 5278, 5268, 5273, 5270, 5268, 5270, 5267, 5228, 5274, 5227, 5274, 5276, 5227, 5274, 5228, 5275, 5274, 5279, 5276, 5275, 5277, 5274, 5277, 5279, 5274, 5279, 5278, 5276, 5277, 5270, 5279, 5273, 5272, 5278, 5279, 5273, 5278, 5270, 5273, 5279, 5281, 5233, 5280, 5228, 5234, 5275, 5281, 5280, 5285, 5283, 5281, 5285, 5285, 5280, 5282, 5283, 5285, 5286, 5284, 5283, 5286, 5233, 4978, 5280, 4978, 5282, 5280, 5281, 5283, 5287, 5284, 5287, 5283, 5288, 5285, 5282, 5386, 5289, 4994, 410, 541, 5290, 5291, 5290, 541, 5292, 5241, 5291, 5291, 5000, 5292, 5291, 541, 5000, 541, 4999, 5000, 5001, 5293, 5292, 5292, 5293, 5241, 5001, 5294, 5004, 5001, 5292, 5294, 5292, 5000, 5294, 416, 542, 5294, 5005, 5295, 5293, 5293, 5295, 5242, 5293, 5242, 5241, 5293, 5001, 5005, 5294, 5296, 5297, 5294, 5297, 5004, 5004, 5297, 5007, 5296, 5294, 545, 5294, 542, 545, 5005, 5008, 5295, 5245, 5242, 5295, 5007, 5297, 5298, 5297, 5296, 5298, 5298, 5296, 548, 5296, 545, 548, 5301, 5295, 5008, 5246, 5245, 5295, 5298, 5300, 5299, 5298, 5299, 5007, 5007, 5299, 5008, 5298, 548, 5300, 5300, 548, 552, 5302, 5247, 5246, 5302, 5246, 5301, 5301, 5246, 5295, 5299, 5301, 5008, 5302, 5301, 5299, 5302, 5299, 5300, 5302, 5300, 5303, 5303, 5300, 557, 5300, 552, 557, 5305, 5247, 5302, 5305, 5304, 5247, 5304, 5249, 5247, 5305, 5302, 5303, 5305, 5303, 5306, 5305, 5306, 5304, 5306, 5303, 5310, 557, 5310, 5303, 5249, 5307, 5311, 5249, 5304, 5307, 5304, 5308, 5307, 5306, 5309, 5304, 5304, 5309, 5308, 5309, 5306, 5310, 557, 570, 5310, 5261, 5311, 5313, 5308, 5309, 5311, 5308, 5311, 5307, 5314, 5311, 5309, 5312, 5314, 5309, 5312, 5309, 5310, 577, 5312, 570, 570, 5312, 5310, 5261, 5313, 5265, 5311, 5314, 5315, 5311, 5315, 5313, 5314, 5312, 5316, 5314, 5316, 5315, 582, 5316, 576, 5316, 5312, 577, 5316, 577, 576, 5377, 5313, 5387, 5265, 5313, 5377, 5313, 5315, 5317, 5313, 5317, 5387, 5315, 5318, 5317, 5315, 5316, 5318, 591, 5316, 582, 591, 5318, 5316, 5387, 5317, 5320, 5318, 5319, 5317, 5319, 5320, 5317, 5319, 5318, 599, 591, 598, 5318, 598, 599, 5318, 5271, 5324, 4970, 5324, 5010, 4970, 5272, 5321, 5271, 5321, 5324, 5271, 5273, 5322, 5272, 5322, 5321, 5272, 5268, 5323, 5273, 5323, 5322, 5273, 5325, 5324, 5321, 5325, 5321, 5326, 5321, 5322, 5326, 5323, 5320, 5322, 5320, 5326, 5322, 5326, 5327, 5325, 5326, 5319, 5327, 5326, 5320, 5319, 5319, 599, 5327, 5327, 599, 5328, 5324, 5329, 5010, 5010, 5329, 5013, 5325, 5329, 5324, 5330, 5329, 5325, 5330, 5327, 5328, 5330, 5325, 5327, 5286, 5285, 5331, 5332, 5286, 5331, 5381, 5286, 5332, 5329, 5334, 5013, 5330, 5333, 5329, 5329, 5333, 5334, 5333, 5330, 5390, 5285, 5288, 5331, 5332, 5331, 5336, 5381, 5332, 5391, 5332, 5336, 5391, 5013, 5334, 5015, 5288, 4982, 5335, 5331, 5288, 5335, 5331, 5335, 5336, 5391, 5336, 5386, 5334, 5339, 5015, 5015, 5339, 5016, 5333, 5337, 5334, 5334, 5337, 5339, 4982, 5018, 5335, 5336, 5335, 5018, 5018, 5386, 5336, 5016, 5339, 5338, 5340, 5341, 5339, 5340, 5339, 5337, 5386, 5018, 5289, 5339, 5341, 5338, 5022, 5338, 608, 5338, 5341, 608, 5341, 5340, 609, 608, 425, 5022, 5341, 609, 608, 5055, 5054, 5342, 5342, 5343, 5055, 5343, 5342, 5345, 5344, 5352, 5347, 5342, 5352, 5344, 5345, 5056, 5343, 5346, 5345, 5342, 5342, 5344, 5346, 5346, 5344, 5347, 5345, 5348, 5056, 5348, 5345, 5346, 5348, 5346, 5349, 5346, 5347, 5349, 5349, 5347, 5355, 5059, 5056, 5350, 5056, 5348, 5350, 5350, 5349, 5059, 5349, 5356, 5059, 5350, 5348, 5349, 5342, 5132, 5352, 5352, 5132, 5351, 5352, 5351, 5353, 5347, 5352, 5354, 5354, 5353, 5362, 5352, 5353, 5354, 5347, 5354, 5355, 5355, 5354, 5366, 5349, 5355, 5356, 5355, 5366, 5367, 5356, 5355, 5367, 5134, 5140, 5351, 5134, 5351, 5132, 5351, 5140, 5358, 5140, 5159, 5358, 5161, 5162, 5357, 5360, 5357, 5162, 5155, 5360, 5162, 5157, 5360, 5155, 5159, 5168, 5358, 5353, 5351, 5358, 5170, 5161, 5357, 5359, 5170, 5357, 5357, 5360, 5359, 5358, 5168, 5361, 5168, 5177, 5361, 5353, 5358, 5361, 5353, 5361, 5362, 5170, 5359, 5178, 5365, 5354, 5362, 5359, 5360, 5181, 5178, 5359, 5181, 5363, 5365, 5362, 5181, 5360, 5185, 5186, 5361, 5177, 5361, 5186, 5364, 5364, 5186, 5187, 5362, 5364, 5363, 5361, 5364, 5362, 5364, 5187, 5373, 5363, 5373, 5372, 5364, 5373, 5363, 5373, 5187, 5236, 5354, 5365, 5366, 5366, 5365, 5374, 5375, 5240, 5367, 5366, 5374, 5375, 5367, 5366, 5375, 5223, 5224, 5368, 5224, 5266, 5267, 5224, 5267, 5368, 5223, 5368, 5369, 5157, 5369, 5360, 5360, 5369, 5370, 5370, 5369, 5371, 5369, 5368, 5371, 5371, 5368, 5378, 5360, 5370, 5185, 5372, 5365, 5363, 5370, 5371, 5231, 5185, 5370, 5231, 5371, 5378, 5234, 5231, 5371, 5234, 5379, 5382, 5372, 5373, 5236, 5239, 5373, 5239, 5383, 5372, 5383, 5379, 5373, 5383, 5372, 5365, 5372, 5374, 5372, 5382, 5374, 5374, 5382, 5385, 5374, 5385, 5375, 5268, 5376, 5377, 5368, 5267, 5277, 5368, 5277, 5378, 5277, 5275, 5378, 5234, 5378, 5275, 5284, 5380, 5383, 5379, 5383, 5380, 5379, 5380, 5381, 5380, 5284, 5286, 5381, 5380, 5286, 5383, 5239, 5287, 5383, 5287, 5284, 5379, 5381, 5382, 5382, 5381, 5384, 5382, 5384, 5385, 5385, 5384, 5386, 5240, 5375, 4994, 5375, 5386, 4994, 5375, 5385, 5386, 5377, 5323, 5268, 5377, 5388, 5323, 5377, 5387, 5388, 5320, 5388, 5387, 5320, 5323, 5388, 5330, 5328, 5389, 5328, 733, 5389, 5330, 5389, 5390, 5390, 5389, 734, 5391, 5384, 5381, 5390, 5392, 5333, 5386, 5384, 5391, 5333, 5393, 5337, 5333, 5392, 5393, 5393, 5392, 5394, 5394, 5392, 5390, 5390, 735, 5394, 5394, 735, 737, 5337, 5393, 5395, 5340, 5337, 5395, 5395, 5396, 5340, 5396, 609, 5340, 5393, 5394, 5395, 5395, 5394, 5396, 5394, 737, 5396, 4042, 4591, 4503, 4503, 4591, 4592, 4593, 4504, 4503, 4503, 4592, 4593, 4593, 4505, 4504, 5240, 4505, 4593, 5240, 5397, 4505, 5397, 5398, 4505, 4678, 4675, 5397, 5397, 4675, 5398, 4676, 4161, 4507, 5398, 4676, 4507, 4676, 5398, 5399, 5399, 5398, 4675, 4675, 4677, 5399, 4677, 4679, 5399, 4165, 4693, 5400, 4511, 4165, 5400, 4693, 4694, 5400, 5400, 351, 4511, 5400, 4695, 351, 5404, 5469, 5401, 5470, 5469, 5404, 5408, 5470, 5404, 5404, 5403, 5402, 5404, 5401, 5403, 5403, 5401, 5406, 5409, 5404, 5402, 5404, 5409, 5408, 5403, 5406, 5402, 5405, 5402, 5406, 5410, 5402, 5405, 5406, 5407, 5405, 5405, 5407, 5410, 5410, 5409, 5402, 5408, 5409, 5485, 5412, 5409, 5410, 5411, 5412, 5410, 5488, 5409, 5412, 5411, 5410, 5413, 5410, 5414, 5413, 5414, 5427, 5413, 5410, 5473, 5414, 5414, 5528, 5427, 5417, 5415, 5416, 5432, 5417, 5416, 5412, 5411, 5418, 5415, 5412, 5418, 5415, 5488, 5412, 5415, 5418, 5416, 5418, 5419, 5416, 5416, 5420, 5432, 5421, 5418, 5411, 5422, 5419, 5421, 5421, 5419, 5418, 5422, 5423, 5419, 5419, 5423, 5416, 5420, 5423, 5435, 5420, 5416, 5423, 5411, 5413, 5421, 5413, 5424, 5421, 5421, 5424, 5425, 5422, 5421, 5425, 5423, 5513, 5426, 5413, 5427, 5424, 5425, 5424, 5427, 5513, 5428, 5426, 5428, 5430, 5429, 5428, 5429, 5426, 5426, 5429, 5423, 5430, 5431, 5429, 5427, 5528, 5524, 5420, 5434, 5432, 5432, 5434, 5433, 5420, 5435, 5434, 5436, 5434, 5435, 5433, 5434, 5436, 5435, 5423, 5429, 5436, 5435, 5429, 5429, 5612, 5436, 5431, 5437, 5429, 5429, 5437, 5612, 5438, 5439, 5433, 5440, 5439, 5438, 5442, 5438, 5433, 5443, 5444, 5442, 5438, 5444, 5440, 5442, 5444, 5438, 5445, 5444, 5443, 5444, 5445, 5440, 5447, 5440, 5445, 5447, 5441, 5440, 5442, 5433, 5436, 5442, 5436, 5446, 5442, 5446, 5443, 5443, 5446, 5445, 5447, 5445, 5455, 5455, 5445, 5446, 5449, 5446, 5436, 5449, 5448, 5446, 5446, 5448, 5455, 5612, 5449, 5436, 5612, 5463, 5449, 5450, 5582, 5454, 5441, 5447, 5451, 5581, 5441, 5451, 5582, 5452, 5454, 760, 5454, 5453, 5447, 5455, 5451, 5451, 5455, 5456, 5454, 5457, 5453, 5452, 5457, 5454, 5452, 5587, 5457, 5587, 5458, 5457, 5448, 5456, 5455, 5448, 5459, 5456, 5448, 5449, 5459, 5459, 5460, 5456, 5461, 5453, 5457, 5461, 5457, 5462, 5453, 5461, 766, 5462, 5457, 5458, 5449, 5463, 5459, 5459, 5463, 5464, 5460, 5459, 5464, 5462, 5465, 770, 5462, 770, 5461, 5461, 770, 766, 5463, 5612, 5609, 5464, 5463, 5609, 5466, 5469, 5467, 5468, 5620, 5470, 5471, 5468, 5470, 5401, 5469, 5466, 5467, 5625, 5466, 5478, 5470, 5408, 5471, 5470, 5478, 5466, 5406, 5401, 5466, 5625, 5406, 5625, 5734, 5406, 5406, 5472, 5407, 5406, 5734, 5472, 5410, 5407, 5473, 5734, 2002, 5472, 5474, 5407, 5472, 5473, 5407, 5474, 5472, 1978, 5474, 5472, 2002, 1978, 1978, 2002, 2008, 5473, 5474, 1978, 5468, 5471, 5475, 5476, 5631, 5477, 5631, 5476, 5486, 5471, 5478, 5479, 5471, 5479, 5481, 5471, 5481, 5475, 5481, 5479, 5480, 5475, 5481, 5482, 5478, 5408, 5485, 5479, 5478, 5483, 5483, 5478, 5485, 5484, 5480, 5476, 5480, 5479, 5476, 5479, 5483, 5476, 5483, 5485, 5486, 5476, 5483, 5486, 5484, 5476, 5489, 5489, 5476, 5487, 5476, 5477, 5487, 5486, 5485, 5488, 5481, 5480, 5490, 5481, 5491, 5482, 5490, 5491, 5481, 5490, 5484, 5492, 5480, 5484, 5490, 5491, 5490, 5492, 5484, 5489, 5492, 5489, 5493, 5492, 5409, 5488, 5485, 5414, 5473, 5494, 5535, 5414, 5494, 5473, 1983, 5494, 5494, 1983, 1988, 5638, 5486, 5488, 5638, 5488, 5495, 5495, 5488, 5415, 5495, 5415, 5417, 5497, 5496, 5656, 5499, 5496, 5497, 5498, 5496, 5499, 5500, 5498, 5499, 5509, 5498, 5500, 5656, 5496, 5501, 5501, 5502, 5512, 5501, 5496, 5502, 5503, 5504, 5505, 5505, 5502, 5506, 5504, 5502, 5505, 5506, 5502, 5498, 5498, 5502, 5496, 5503, 5508, 5507, 5503, 5505, 5508, 5506, 5509, 5505, 5509, 5508, 5505, 5506, 5498, 5509, 5422, 5510, 5511, 5422, 5425, 5510, 5503, 5513, 5511, 5422, 5511, 5423, 5513, 5423, 5511, 5511, 5510, 5514, 5511, 5514, 5503, 5503, 5514, 5504, 5502, 5504, 5515, 5504, 5514, 5515, 5502, 5515, 5516, 5502, 5516, 5512, 5519, 5516, 5515, 5503, 5517, 5513, 5517, 5503, 5507, 5518, 5517, 5507, 5510, 5425, 5520, 5425, 5427, 5520, 5521, 5510, 5520, 5514, 5510, 5521, 5515, 5514, 5525, 5514, 5521, 5525, 5525, 5519, 5515, 5513, 5430, 5428, 5513, 5517, 5430, 5517, 5526, 5430, 5518, 5522, 5517, 5517, 5522, 5526, 5519, 5525, 5523, 5524, 5520, 5427, 5521, 5520, 5524, 5521, 5524, 5525, 5430, 5526, 5431, 5522, 5531, 5526, 5560, 5531, 5522, 5523, 5525, 5527, 5524, 5528, 5529, 5524, 5529, 5525, 5527, 5525, 5530, 5431, 5526, 5562, 5562, 5526, 5531, 5537, 5529, 5528, 5537, 5528, 5532, 5537, 5533, 5529, 5533, 5525, 5529, 5530, 5525, 5533, 5530, 5533, 5534, 5414, 5535, 5532, 5528, 5414, 5532, 5536, 5532, 5535, 5533, 5537, 5536, 5536, 5537, 5532, 5534, 5533, 5536, 5494, 5539, 5540, 5540, 5535, 5494, 5536, 5535, 5540, 5542, 5565, 5541, 5538, 5541, 5565, 5546, 5538, 5692, 5539, 5494, 1987, 5539, 1987, 5543, 5541, 5544, 5542, 5541, 5545, 5544, 5546, 5545, 5541, 5546, 5541, 5538, 5494, 1988, 1987, 5543, 1987, 1989, 1990, 5544, 1991, 1991, 5544, 5545, 5546, 1993, 5545, 5545, 1993, 1991, 5544, 1990, 5542, 5796, 5495, 5547, 5495, 5417, 5432, 5495, 5432, 5547, 5547, 5432, 5433, 5548, 5507, 5508, 5509, 5551, 5508, 5551, 5548, 5508, 5509, 5500, 5549, 5509, 5549, 5551, 5551, 5550, 5548, 5553, 5554, 5555, 5552, 5554, 5553, 5568, 5552, 5553, 5569, 5552, 5568, 5555, 5556, 5553, 5556, 5568, 5553, 5518, 5548, 5557, 5518, 5507, 5548, 5518, 5557, 5558, 5548, 5550, 5557, 5557, 5550, 5558, 5550, 5551, 5558, 5549, 5558, 5551, 5552, 5559, 5554, 5554, 5559, 5555, 5552, 5569, 5574, 5559, 5552, 5574, 5558, 5709, 5522, 5518, 5558, 5522, 5560, 5522, 5709, 5709, 5561, 5560, 5437, 5431, 5562, 5563, 5562, 5531, 5560, 5563, 5531, 5560, 5538, 5563, 5562, 5564, 5437, 5562, 5563, 5564, 5538, 5560, 5692, 5565, 5564, 5563, 5565, 5563, 5538, 5437, 5566, 5612, 5566, 5437, 5564, 5565, 5542, 5564, 5564, 5542, 5566, 5542, 1990, 5566, 5566, 1990, 2001, 5577, 5567, 5547, 5547, 5433, 5439, 5439, 5440, 5441, 5547, 5439, 5577, 5577, 5439, 5441, 5570, 5556, 5555, 5571, 5569, 5568, 5556, 5572, 5568, 5572, 5571, 5568, 5572, 5556, 5570, 5592, 5572, 5575, 5592, 5573, 5572, 5573, 5571, 5572, 5555, 5559, 5570, 5574, 5569, 5575, 5559, 5574, 5575, 5559, 5575, 5570, 5569, 5571, 5575, 5570, 5575, 5572, 5575, 5576, 5592, 5575, 5571, 5576, 5577, 5584, 5578, 5578, 5579, 5580, 5578, 5580, 5577, 801, 5580, 5579, 800, 801, 5579, 5581, 5584, 5441, 5577, 5441, 5584, 5583, 5584, 5581, 5582, 5583, 5581, 5450, 5585, 5583, 5582, 5450, 5583, 5584, 5583, 5585, 5584, 5585, 5578, 5578, 5585, 5588, 5588, 5579, 5578, 806, 5450, 5454, 806, 809, 810, 5590, 800, 5579, 5586, 5581, 5451, 5582, 5586, 5452, 5582, 5581, 5586, 5585, 5587, 5595, 5585, 5450, 5587, 5585, 5595, 5588, 5598, 5579, 5588, 5450, 5589, 5587, 806, 5589, 5450, 806, 810, 5589, 5579, 5598, 5590, 5586, 5573, 5591, 5586, 5456, 5573, 5586, 5451, 5456, 5456, 5571, 5573, 5592, 5593, 5573, 5593, 5591, 5573, 5592, 5594, 5593, 5452, 5586, 5591, 5452, 5591, 5595, 5593, 5596, 5591, 5596, 5595, 5591, 5597, 5593, 5594, 5597, 5596, 5593, 5595, 5587, 5452, 5588, 5595, 5596, 5588, 5597, 5598, 5588, 5596, 5597, 5587, 5589, 5458, 5589, 5599, 5458, 810, 5599, 5589, 5599, 810, 815, 5456, 5576, 5571, 5456, 5460, 5576, 5592, 5576, 5600, 5576, 5460, 5600, 5592, 5600, 5594, 5600, 5460, 5601, 5602, 5600, 5601, 5594, 5600, 5602, 5594, 5602, 5597, 5597, 5602, 5603, 5598, 5597, 5603, 5462, 5458, 5604, 5458, 5599, 5604, 5608, 5599, 815, 5601, 5464, 5605, 5601, 5460, 5464, 5602, 5601, 5605, 5606, 5602, 5605, 5603, 5602, 5606, 5607, 5603, 5606, 5462, 5604, 5465, 5604, 825, 5465, 5599, 5608, 5604, 5604, 5608, 825, 5609, 5605, 5464, 5605, 5609, 5610, 5606, 5605, 5610, 5607, 5606, 5611, 5606, 5610, 5611, 770, 5465, 825, 825, 5608, 826, 5607, 5611, 838, 5612, 5613, 5609, 5610, 5609, 5613, 5610, 5613, 5614, 5611, 5610, 5614, 833, 835, 5615, 831, 833, 5615, 5614, 5615, 835, 5614, 835, 5611, 5611, 835, 837, 5611, 837, 838, 5616, 5614, 5613, 831, 5616, 842, 5615, 5616, 831, 5614, 5616, 5615, 5612, 5617, 5613, 5617, 5612, 5566, 5613, 5617, 5616, 842, 5616, 5617, 5566, 2001, 5617, 2001, 0, 5617, 5620, 5618, 5621, 5621, 5618, 5622, 5618, 5620, 5729, 5729, 5620, 5623, 5729, 5623, 5619, 5619, 5623, 5629, 5469, 5620, 5467, 5621, 5624, 5620, 5620, 5624, 5467, 5622, 5624, 5621, 5622, 5734, 5624, 5469, 5470, 5620, 5620, 5468, 5623, 5467, 5624, 5625, 5624, 5734, 5625, 5628, 5629, 5623, 5630, 5619, 5629, 5630, 5634, 5619, 5634, 5744, 5619, 5632, 5628, 5631, 5632, 5631, 5635, 5635, 5631, 5638, 5636, 5633, 5632, 5636, 5632, 5637, 5637, 5632, 5635, 5636, 5637, 5639, 5626, 5627, 5639, 5626, 5639, 5637, 5626, 5637, 5635, 5468, 5628, 5623, 5468, 5640, 5628, 5468, 5475, 5640, 5641, 5628, 5640, 5629, 5644, 5630, 5628, 5644, 5629, 5630, 5644, 5634, 5628, 5642, 5477, 5628, 5477, 5631, 5643, 5633, 5648, 5628, 5641, 5642, 5643, 5644, 5633, 5632, 5633, 5628, 5644, 5628, 5633, 5634, 5644, 5645, 5631, 5486, 5638, 5633, 5636, 5648, 5641, 5482, 5646, 5641, 5640, 5482, 5640, 5475, 5482, 5648, 5477, 5646, 5648, 5646, 5647, 5641, 5646, 5642, 5477, 5642, 5646, 5643, 5648, 5647, 5643, 5647, 5649, 5644, 5643, 5649, 5645, 5644, 5649, 5648, 5650, 5477, 5650, 5487, 5477, 5648, 5636, 5651, 5648, 5651, 5650, 5645, 5649, 5652, 5650, 5489, 5487, 5653, 5489, 5650, 5651, 5653, 5650, 5654, 5653, 5651, 5639, 5669, 5654, 5639, 5654, 5651, 5639, 5651, 5636, 5482, 5491, 5646, 5646, 5655, 5647, 5646, 5491, 5655, 5491, 5492, 5655, 5655, 5649, 5647, 5658, 5655, 5493, 5493, 5655, 5492, 5649, 5655, 5658, 5652, 5649, 5659, 5493, 5489, 5653, 5493, 5653, 5656, 5493, 5656, 5658, 5656, 5653, 5657, 5657, 5653, 5654, 5658, 5659, 5649, 5658, 5656, 5501, 5661, 5663, 5662, 5664, 2045, 5665, 5664, 5666, 2046, 5664, 5667, 5666, 5665, 5667, 5664, 5660, 5661, 5667, 5660, 5667, 5665, 5694, 2046, 5666, 5694, 5543, 2046, 5543, 2049, 2046, 5662, 5694, 5666, 5667, 5662, 5666, 5661, 5662, 5667, 5495, 5797, 5626, 5626, 5638, 5495, 5635, 5638, 5626, 5495, 5796, 5797, 5656, 5657, 5497, 5668, 5497, 5654, 5654, 5497, 5657, 5669, 5668, 5654, 5670, 5499, 5497, 5497, 5668, 5670, 5669, 5804, 5668, 5804, 5671, 5668, 5668, 5672, 5670, 5668, 5671, 5672, 5672, 5671, 5804, 5670, 5672, 5499, 5672, 5500, 5499, 5658, 5501, 5673, 5659, 5658, 5673, 5512, 5675, 5501, 5673, 5501, 5675, 5673, 5675, 5674, 5676, 5674, 5675, 5659, 5673, 5677, 5512, 5516, 5675, 5674, 5680, 5673, 5675, 5519, 5678, 5675, 5516, 5519, 5679, 5676, 5675, 5674, 5676, 5680, 5680, 5676, 5679, 5673, 5680, 5681, 5523, 5678, 5519, 5708, 5678, 5523, 5679, 5708, 5682, 5679, 5682, 5680, 5681, 5685, 5683, 5680, 5685, 5681, 5708, 5523, 5684, 5523, 5527, 5684, 5708, 5684, 5682, 5680, 5682, 5685, 5682, 5684, 5685, 5686, 5683, 5685, 5530, 5684, 5527, 5686, 5684, 5530, 5685, 5684, 5686, 5530, 5534, 5687, 5686, 5530, 5687, 5683, 5686, 5687, 5690, 5691, 5687, 5690, 5534, 5536, 5687, 5534, 5690, 5688, 5692, 5715, 5688, 5693, 5692, 5689, 5693, 5688, 5539, 5694, 5540, 5540, 5662, 5690, 5694, 5662, 5540, 5690, 5663, 5691, 5662, 5663, 5690, 5690, 5536, 5540, 5692, 5696, 5546, 5696, 5693, 5695, 5696, 5692, 5693, 5543, 5694, 5539, 5697, 5695, 5699, 5698, 5697, 5699, 5696, 5697, 5546, 5695, 5697, 5696, 1989, 2049, 5543, 1993, 5546, 2117, 2117, 5697, 2051, 2117, 5546, 5697, 5698, 2051, 5697, 5547, 5963, 5796, 5700, 5500, 5672, 5549, 5500, 5700, 5701, 5700, 5672, 5811, 5702, 5672, 5702, 5701, 5672, 5549, 5700, 5704, 5703, 5704, 5700, 5703, 5700, 5701, 5675, 5678, 5705, 5679, 5675, 5705, 5702, 5706, 5701, 5549, 5704, 5558, 5701, 5706, 5703, 5706, 5707, 5703, 5703, 5707, 5704, 5705, 5678, 5708, 5708, 5679, 5705, 5712, 5706, 5838, 5706, 5712, 5711, 5706, 5711, 5707, 5709, 5558, 5710, 5558, 5704, 5710, 5707, 5711, 5704, 5704, 5711, 5710, 5717, 5561, 5709, 5711, 5712, 5713, 5710, 5717, 5709, 5711, 5714, 5710, 5710, 5714, 5717, 5714, 5711, 5713, 5561, 5716, 5715, 5560, 5561, 5715, 5712, 5689, 5718, 5717, 5716, 5561, 5714, 5716, 5717, 5713, 5712, 5718, 5713, 5718, 5716, 5713, 5716, 5714, 5715, 5692, 5560, 5688, 5715, 5716, 5718, 5688, 5716, 5689, 5688, 5718, 5567, 5963, 5547, 5719, 5567, 5577, 875, 5567, 5719, 5963, 5567, 875, 5577, 5580, 5719, 5719, 801, 873, 5580, 801, 5719, 800, 5590, 879, 5598, 5721, 5720, 5598, 5720, 5590, 5590, 5720, 879, 881, 879, 5720, 5603, 5721, 5598, 5721, 881, 5720, 888, 881, 5721, 5721, 5603, 5607, 5721, 5607, 888, 888, 5607, 838, 5723, 5722, 5725, 5725, 5722, 5729, 5725, 5724, 5723, 5726, 5724, 5725, 5618, 5722, 5727, 5727, 5722, 5728, 5723, 5728, 5722, 5722, 5618, 5729, 5723, 5724, 5728, 5729, 5619, 5740, 5724, 5726, 5730, 5730, 5726, 5731, 5727, 5732, 5618, 5618, 5732, 5622, 5728, 5733, 5727, 5727, 5733, 5732, 5728, 5724, 5733, 5733, 5724, 5730, 5732, 5734, 5622, 5733, 5872, 5732, 5732, 5872, 5734, 5733, 5859, 5872, 5733, 5730, 5853, 5730, 5849, 5853, 5872, 5875, 5734, 5883, 5899, 5734, 5734, 5899, 2002, 5736, 5735, 5893, 5899, 2235, 2002, 5736, 5738, 5737, 5736, 5737, 5735, 5738, 2239, 2238, 2238, 5737, 5738, 5739, 5729, 5740, 5739, 5726, 5725, 5739, 5725, 5729, 5741, 5739, 5740, 5795, 5906, 5742, 5726, 5743, 5731, 5739, 5743, 5726, 5744, 5740, 5619, 5739, 5741, 5743, 5743, 5741, 5746, 5740, 5744, 5745, 5740, 5745, 5746, 5746, 5741, 5740, 5626, 5748, 5747, 5627, 5626, 5747, 5747, 5748, 5749, 5731, 5743, 5752, 5754, 5744, 5634, 5743, 5746, 5750, 5753, 5752, 5743, 5750, 5753, 5743, 5744, 5754, 5751, 5745, 5744, 5751, 5746, 5745, 5751, 5750, 5746, 5751, 5747, 5800, 5627, 5627, 5800, 5639, 5749, 5800, 5747, 5645, 5754, 5634, 5751, 5754, 5758, 5757, 5750, 5758, 5750, 5751, 5758, 5750, 5757, 5753, 5757, 5755, 5753, 5800, 5756, 5639, 5639, 5756, 5669, 5921, 5755, 5757, 5754, 5645, 5652, 5754, 5652, 5758, 5757, 5758, 5759, 5757, 5759, 5921, 5659, 5760, 5652, 5760, 5758, 5652, 5758, 5760, 5759, 5759, 5760, 5761, 5760, 5808, 5763, 5762, 5761, 5763, 5763, 5761, 5760, 5763, 5765, 5764, 5808, 5765, 5763, 5764, 5762, 5763, 5766, 5764, 5765, 5766, 5768, 5767, 5765, 5768, 5766, 5768, 5765, 5769, 5768, 5771, 5767, 5771, 5776, 5767, 5768, 5772, 5771, 5774, 5772, 5768, 5773, 5772, 5774, 5768, 5769, 5774, 5770, 5779, 5775, 5771, 5778, 5776, 5771, 5777, 5778, 5772, 5777, 5771, 5772, 5773, 5777, 5777, 5773, 5780, 5779, 5781, 5775, 5950, 5738, 5736, 5782, 5738, 5950, 5776, 5782, 5950, 5776, 5778, 5782, 5778, 5787, 5782, 5783, 5778, 5777, 5783, 5660, 5778, 5660, 5789, 5778, 5789, 5787, 5778, 5781, 5779, 5784, 5663, 5783, 5780, 5783, 5777, 5780, 5781, 5784, 5785, 5775, 5781, 5785, 5782, 5787, 5738, 5661, 5660, 5783, 5784, 5786, 5788, 5784, 5788, 2285, 5661, 5783, 5663, 5790, 5785, 5784, 5790, 5784, 2285, 2239, 5738, 2278, 5665, 2279, 5791, 2045, 2279, 5665, 5791, 2279, 5792, 5792, 2279, 2278, 5792, 2278, 5787, 5787, 2278, 5738, 5786, 2281, 5788, 5791, 5660, 5665, 5789, 5660, 5791, 5792, 5789, 5791, 5787, 5789, 5792, 5788, 2281, 2285, 5790, 2285, 2283, 5793, 5795, 5794, 5742, 5794, 5795, 5795, 5793, 5796, 5796, 5793, 5797, 5748, 5626, 5797, 5793, 5749, 5797, 5749, 5748, 5797, 5749, 5793, 5798, 5793, 5794, 5798, 5742, 5799, 5794, 5799, 5798, 5794, 5749, 5798, 5800, 5798, 5801, 5800, 5799, 5801, 5798, 5801, 5802, 5800, 5800, 5802, 5756, 5803, 5669, 5756, 5802, 5805, 5803, 5802, 5803, 5756, 5804, 5669, 5803, 5805, 5804, 5803, 5807, 5760, 5659, 5808, 5760, 5807, 5804, 5805, 5812, 5809, 5812, 5805, 5809, 5805, 5810, 5810, 5805, 5806, 5804, 5811, 5672, 5812, 5811, 5804, 5813, 5808, 5807, 5807, 5659, 5677, 5815, 5807, 5677, 5813, 5807, 5815, 5810, 5814, 5809, 5809, 5814, 5816, 5817, 5811, 5812, 5812, 5818, 5817, 5816, 5812, 5809, 5818, 5812, 5816, 5817, 5702, 5811, 5765, 5808, 5813, 5814, 5820, 5819, 5681, 5677, 5673, 5681, 5821, 5677, 5821, 5815, 5677, 5821, 5813, 5815, 5822, 5816, 5819, 5816, 5814, 5819, 5822, 5817, 5818, 5816, 5822, 5818, 5702, 5817, 5838, 5813, 5823, 5765, 5681, 5826, 5821, 5823, 5813, 5821, 5826, 5823, 5821, 5822, 5819, 5828, 5819, 5820, 5828, 5683, 5826, 5681, 5828, 5817, 5822, 5828, 5824, 5817, 5817, 5824, 5838, 5824, 5825, 5838, 5823, 5826, 5827, 5769, 5765, 5823, 5827, 5769, 5823, 5770, 5775, 5820, 5820, 5775, 5828, 5830, 5825, 5828, 5825, 5824, 5828, 5827, 5773, 5774, 5827, 5826, 5773, 5826, 5829, 5773, 5769, 5827, 5774, 5826, 5687, 5829, 5687, 5826, 5683, 5691, 5780, 5829, 5829, 5780, 5773, 5775, 5831, 5828, 5829, 5687, 5691, 5828, 5831, 5832, 5830, 5832, 5833, 5832, 5830, 5828, 5830, 5833, 5689, 5689, 5833, 5693, 5780, 5691, 5663, 5775, 5785, 5831, 5785, 5834, 5831, 5832, 5831, 5834, 5834, 5695, 5833, 5834, 5833, 5832, 5833, 5695, 5693, 5785, 5835, 5834, 5790, 5835, 5785, 5699, 5695, 5836, 5835, 5836, 5834, 5834, 5836, 5695, 2282, 5699, 2283, 2283, 5699, 5837, 5790, 5837, 5835, 2283, 5837, 5790, 5837, 5699, 5836, 5835, 5837, 5836, 5838, 5706, 5702, 5838, 5825, 5712, 5689, 5825, 5830, 5689, 5712, 5825, 5844, 5840, 5841, 5840, 5839, 5841, 5841, 5839, 5842, 5845, 5843, 5840, 5840, 5843, 5839, 5840, 5844, 5845, 5842, 5852, 5841, 5841, 5852, 5844, 5845, 5844, 5846, 5844, 5852, 5846, 5847, 5845, 5855, 5847, 5848, 5845, 5848, 5847, 5855, 5851, 5846, 5852, 5846, 5855, 5845, 5846, 5851, 5855, 5731, 5849, 5730, 5859, 5733, 5853, 5854, 5851, 5852, 5855, 5851, 5854, 5856, 5855, 5854, 5853, 5849, 5857, 5855, 5850, 5848, 5855, 5856, 5850, 5856, 5858, 5850, 5859, 5853, 5860, 5854, 5852, 5861, 5852, 5862, 5861, 5861, 5866, 5854, 5863, 5860, 5853, 5854, 5866, 5856, 5866, 5867, 5856, 5853, 5857, 5863, 5856, 5867, 5858, 5867, 5864, 5858, 5866, 5861, 5865, 5865, 5861, 5862, 5860, 5863, 5870, 5867, 5871, 5864, 5865, 5868, 5869, 5866, 5865, 5869, 5866, 5869, 5867, 5869, 5874, 5867, 5867, 5874, 5871, 5872, 5859, 5860, 5873, 5872, 5860, 5873, 5860, 5877, 5869, 5868, 5878, 5860, 5870, 5877, 5869, 5878, 5874, 5877, 5870, 5933, 5873, 5876, 5875, 5873, 5875, 5872, 5876, 5873, 5877, 5868, 5881, 5878, 5877, 5933, 5879, 5878, 5941, 5874, 5875, 5876, 5880, 5877, 5884, 5880, 5876, 5877, 5880, 5877, 5879, 5884, 5884, 5879, 5885, 5881, 5889, 5878, 5885, 5879, 5882, 5889, 5941, 5878, 5875, 5883, 5734, 5883, 5875, 5880, 5884, 5883, 5880, 5881, 5887, 5886, 5889, 5881, 5886, 5885, 5882, 5888, 5888, 5882, 5890, 5889, 5886, 5891, 5896, 5883, 5884, 5887, 5971, 5897, 5884, 5885, 5896, 5896, 5885, 5888, 5887, 5897, 5892, 5886, 5887, 5892, 5892, 5894, 5886, 5888, 5890, 5895, 5893, 5888, 5895, 5886, 5894, 5891, 5894, 5951, 5891, 5899, 5883, 5896, 5971, 5974, 5897, 5893, 5735, 5896, 5893, 5896, 5888, 5892, 5897, 5900, 5892, 5900, 5894, 5893, 5895, 5736, 5898, 5951, 5894, 5974, 5902, 5897, 5735, 5737, 5896, 5900, 5903, 5901, 5900, 5901, 5898, 5894, 5900, 5898, 5899, 2581, 2235, 2581, 5899, 5896, 2582, 2581, 5896, 5896, 5737, 2583, 2583, 2582, 5896, 5897, 5902, 2587, 5900, 5897, 2587, 5903, 5900, 2587, 5901, 5905, 5904, 5905, 5901, 5903, 5902, 2584, 2587, 2583, 5737, 2238, 5956, 5843, 5906, 5966, 5843, 5956, 5845, 5907, 5843, 5906, 5907, 5742, 5906, 5843, 5907, 5906, 5795, 5956, 5848, 5908, 5907, 5845, 5848, 5907, 5849, 5731, 5752, 5909, 5848, 5850, 5908, 5848, 5909, 5907, 5908, 5910, 5910, 5908, 5909, 5907, 5911, 5742, 5911, 5907, 5910, 5912, 5857, 5849, 5912, 5849, 5752, 5912, 5752, 5913, 5912, 5913, 5914, 5850, 5858, 5909, 5858, 5915, 5909, 5753, 5913, 5752, 5909, 5915, 5910, 5915, 5916, 5910, 5910, 5916, 5917, 5910, 5958, 5911, 5958, 5910, 5917, 5857, 5912, 5863, 5863, 5912, 5914, 5863, 5914, 5922, 5918, 5915, 5864, 5864, 5915, 5858, 5913, 5753, 5919, 5913, 5919, 5914, 5914, 5919, 5922, 5915, 5918, 5916, 5916, 5918, 5924, 5919, 5755, 5921, 5753, 5755, 5919, 5916, 5920, 5917, 5920, 5916, 5924, 5920, 5926, 5960, 5920, 5960, 5917, 5917, 5960, 5958, 5863, 5922, 5870, 5864, 5871, 5927, 5918, 5864, 5927, 5922, 5919, 5923, 5918, 5927, 5924, 5923, 5919, 5925, 5919, 5921, 5925, 5921, 5759, 5761, 5761, 5925, 5921, 5924, 5926, 5920, 5928, 5870, 5922, 5922, 5923, 5928, 5927, 5929, 5924, 5928, 5925, 5930, 5928, 5923, 5925, 5929, 5931, 5924, 5925, 5761, 5930, 5960, 5926, 5932, 5931, 5926, 5924, 5932, 5926, 5931, 5927, 5871, 5934, 5934, 5871, 5874, 5870, 5928, 5933, 5933, 5928, 5935, 5936, 5927, 5934, 5927, 5936, 5929, 5762, 5930, 5761, 5762, 5937, 5930, 5935, 5928, 5930, 5937, 5935, 5930, 5929, 5938, 5931, 5936, 5938, 5929, 5938, 5939, 5932, 5931, 5938, 5932, 5940, 5879, 5933, 5874, 5941, 5934, 5942, 5940, 5935, 5940, 5933, 5935, 5934, 5941, 5936, 5941, 5943, 5936, 5942, 5935, 5937, 5942, 5937, 5764, 5764, 5937, 5762, 5936, 5943, 5938, 5962, 5944, 5820, 5962, 5939, 5944, 5943, 5939, 5938, 5944, 5939, 5943, 5879, 5945, 5882, 5940, 5945, 5879, 5766, 5945, 5942, 5942, 5945, 5940, 5946, 5943, 5941, 5942, 5764, 5766, 5949, 5943, 5946, 5820, 5944, 5947, 5949, 5944, 5943, 5949, 5947, 5944, 5882, 5948, 5895, 5945, 5948, 5882, 5895, 5890, 5882, 5889, 5891, 5941, 5948, 5945, 5766, 5767, 5948, 5766, 5946, 5951, 5949, 5891, 5946, 5941, 5951, 5946, 5891, 5949, 5770, 5947, 5948, 5950, 5895, 5950, 5948, 5767, 5776, 5950, 5767, 5949, 5951, 5952, 5949, 5952, 5779, 5779, 5770, 5949, 5736, 5895, 5950, 5951, 5898, 5953, 5951, 5953, 5952, 5952, 5953, 5954, 5779, 5952, 5954, 5898, 5955, 5953, 5901, 5955, 5898, 5786, 5954, 5955, 5955, 5954, 5953, 5786, 5784, 5954, 5784, 5779, 5954, 5786, 5955, 5904, 5955, 5901, 5904, 5786, 5904, 2726, 2726, 2281, 5786, 5796, 5956, 5795, 5963, 5956, 5796, 5911, 5799, 5742, 5799, 5957, 5801, 5801, 5957, 5802, 5958, 5799, 5911, 5958, 5957, 5799, 5802, 5957, 5959, 5805, 5802, 5959, 5959, 5957, 5960, 5960, 5957, 5958, 5961, 5959, 5960, 5961, 5960, 5932, 5961, 5806, 5959, 5806, 5805, 5959, 5810, 5806, 5961, 5814, 5961, 5962, 5962, 5932, 5939, 5962, 5961, 5932, 5961, 5814, 5810, 5820, 5814, 5962, 5770, 5820, 5947, 5839, 5966, 5964, 5839, 5964, 5965, 5965, 5964, 5982, 5965, 5842, 5839, 5966, 5839, 5843, 5842, 5862, 5852, 5842, 5983, 5862, 5862, 5967, 5865, 5865, 5967, 5868, 5862, 5968, 5967, 5862, 5969, 5968, 5967, 5970, 5868, 5868, 5970, 5881, 5967, 5968, 5970, 5970, 5971, 5881, 5970, 5968, 5971, 5968, 5972, 5971, 5968, 5985, 5972, 5881, 5971, 5887, 5985, 5973, 5972, 5972, 5973, 5971, 5973, 5974, 5971, 5973, 5975, 5974, 5985, 5976, 5973, 5973, 5976, 5975, 5978, 5902, 5974, 5975, 5978, 5974, 5976, 5977, 5975, 5975, 5977, 5978, 5978, 2586, 2584, 5902, 5978, 2584, 5978, 2936, 2586, 5978, 5977, 2936, 5964, 5980, 5981, 5980, 5979, 5981, 5979, 5992, 5981, 5980, 5964, 5966, 5981, 5982, 5964, 5992, 5982, 5981, 5982, 5842, 5965, 5982, 5983, 5842, 5983, 5984, 5969, 5983, 5969, 5862, 5969, 5984, 5968, 5968, 5984, 5985, 5984, 5986, 5985, 5986, 5976, 5985, 5976, 3181, 5977, 5976, 5986, 3181, 5977, 3181, 3185, 5986, 3183, 3181, 5956, 5963, 5966, 6015, 5997, 5990, 5988, 5997, 5989, 5987, 5988, 5989, 5989, 5997, 5996, 5990, 5988, 5980, 5997, 5988, 5990, 5966, 5990, 5980, 5990, 5966, 6015, 5980, 5988, 5979, 5987, 5991, 5988, 5988, 5991, 5979, 5987, 5989, 5991, 5979, 5991, 5992, 5989, 5992, 5991, 5982, 5992, 5983, 5983, 6001, 5984, 6001, 5993, 5984, 5984, 5993, 5986, 6001, 5994, 5993, 5993, 5994, 5986, 5986, 5994, 3183, 5994, 3250, 3183, 6015, 5963, 6003, 6015, 5966, 5963, 5995, 6006, 5998, 5996, 5997, 5998, 5997, 5995, 5998, 5995, 5997, 6014, 5997, 6015, 6014, 5998, 5999, 5996, 5996, 5999, 5989, 5999, 5992, 5989, 5999, 5983, 5992, 5999, 6000, 5983, 6000, 6001, 5983, 6010, 6011, 6001, 6001, 6002, 5994, 6001, 6011, 6002, 5994, 6002, 3250, 6007, 6005, 6008, 6006, 6007, 6008, 6005, 6004, 6008, 6008, 6004, 6025, 5995, 6007, 6006, 6007, 5995, 6014, 6008, 6009, 6006, 6006, 6009, 5998, 6025, 6009, 6008, 5998, 6009, 5999, 6009, 6000, 5999, 6000, 6182, 6010, 6000, 6010, 6001, 6010, 6013, 6011, 6011, 6012, 6002, 6013, 6012, 6011, 6013, 3346, 6012, 6012, 3346, 6002, 6014, 6056, 6007, 6093, 6014, 6015, 6015, 6003, 6016, 6163, 6017, 6023, 6018, 6163, 6023, 6050, 6018, 6024, 6051, 6167, 6018, 6005, 6019, 6004, 6019, 6021, 6020, 6004, 6019, 6020, 6021, 6023, 6022, 6020, 6021, 6022, 6023, 6017, 6022, 6019, 6005, 6024, 6024, 6005, 6007, 6021, 6019, 6024, 6024, 6018, 6021, 6018, 6023, 6021, 6024, 6007, 6056, 6020, 6025, 6004, 6026, 6022, 6017, 6020, 6027, 6025, 6027, 6020, 6028, 6020, 6022, 6028, 6028, 6022, 6026, 6027, 6028, 6029, 6031, 6029, 6068, 6028, 6069, 6029, 6069, 6068, 6029, 6030, 6069, 6028, 6025, 6032, 6009, 6027, 6033, 6025, 6033, 6032, 6025, 6026, 6034, 6028, 6027, 6029, 6033, 6029, 6031, 6033, 6028, 6034, 6030, 6030, 6036, 6035, 6040, 6036, 6037, 6030, 6037, 6036, 6032, 6038, 6009, 6032, 6033, 6038, 6038, 6033, 6039, 6033, 6031, 6039, 6031, 6081, 6039, 6036, 6040, 6035, 6042, 6044, 6043, 6041, 6039, 6047, 6038, 6039, 6041, 6042, 6041, 6045, 6045, 6041, 6047, 6044, 6042, 6046, 6046, 6042, 6045, 6039, 6081, 6047, 6045, 6047, 6084, 6046, 6045, 6084, 6041, 6048, 6038, 6038, 6048, 6009, 6042, 6048, 6041, 6042, 6043, 6048, 6048, 6049, 6009, 6043, 6049, 6048, 6009, 6049, 6000, 6049, 6260, 6000, 6355, 6182, 6000, 6010, 6182, 6013, 6182, 3947, 6013, 6050, 6051, 6018, 6184, 6167, 6051, 6052, 6184, 6051, 6054, 6052, 6051, 6053, 6054, 6051, 6055, 6054, 6053, 6024, 6056, 6050, 6050, 6056, 6057, 6050, 6057, 6058, 6051, 6050, 6058, 6051, 6058, 6059, 6059, 6060, 6051, 6056, 6014, 6061, 6062, 6057, 6061, 6061, 6057, 6056, 6058, 6057, 6062, 6063, 6064, 6053, 6063, 6053, 6060, 6060, 6053, 6051, 6055, 6053, 6065, 6053, 6064, 6065, 6064, 6066, 6065, 6067, 6031, 6068, 6069, 6030, 6070, 6072, 6067, 6068, 6071, 6067, 6072, 6059, 6058, 6072, 6059, 6072, 6069, 6069, 6072, 6068, 6069, 6060, 6059, 6069, 6070, 6060, 6070, 6073, 6060, 6062, 6071, 6072, 6062, 6074, 6071, 6058, 6062, 6072, 6063, 6060, 6073, 6065, 6066, 6095, 6031, 6067, 6081, 6075, 6070, 6035, 6030, 6035, 6070, 6067, 6071, 6081, 6073, 6070, 6075, 6040, 6037, 6076, 6040, 6076, 6082, 6071, 6074, 6081, 6081, 6074, 6129, 6078, 6075, 6077, 6075, 6078, 6073, 6078, 6079, 6073, 6076, 6073, 6082, 6079, 6082, 6073, 6080, 6078, 6077, 6110, 6080, 6077, 6080, 6079, 6078, 6040, 6082, 6075, 6035, 6040, 6075, 6075, 6082, 6077, 6082, 6083, 6110, 6077, 6082, 6110, 6082, 6079, 6083, 6117, 6084, 6085, 6047, 6081, 6085, 6047, 6085, 6084, 6085, 6081, 6129, 6085, 6129, 6117, 6086, 6087, 6055, 6086, 6088, 6087, 6088, 6089, 6090, 6088, 6090, 6087, 6089, 6092, 6090, 6061, 6014, 6093, 6094, 6062, 6061, 6093, 6094, 6061, 6055, 6065, 6095, 6066, 6096, 6095, 6096, 6086, 6095, 6086, 6055, 6095, 6097, 6094, 6093, 6088, 6099, 6098, 6086, 6099, 6088, 6086, 6100, 6099, 6086, 6096, 6100, 6101, 6093, 6015, 6101, 6097, 6093, 6088, 6098, 6102, 6089, 6102, 6100, 6089, 6088, 6102, 6101, 6015, 6120, 6107, 6097, 6101, 6120, 6107, 6101, 6092, 6104, 6103, 6091, 6092, 6103, 6089, 6105, 6092, 6105, 6104, 6092, 6062, 6094, 6074, 6074, 6094, 6106, 6094, 6097, 6106, 6099, 6102, 6098, 6099, 6100, 6102, 6106, 6097, 6107, 6103, 6104, 6108, 6105, 6109, 6104, 6104, 6109, 6108, 6074, 6106, 6129, 6112, 6080, 6110, 6111, 6079, 6080, 6112, 6111, 6080, 6114, 6111, 6112, 6106, 6107, 6129, 6113, 6112, 6110, 6108, 6112, 6113, 6112, 6108, 6109, 6112, 6109, 6114, 6114, 6109, 6115, 6079, 6111, 6083, 6083, 6116, 6110, 6111, 6114, 6116, 6083, 6111, 6116, 6110, 6116, 6113, 6128, 6117, 6129, 6118, 6092, 6091, 6090, 6092, 6118, 6121, 6118, 6091, 6119, 6118, 6121, 6103, 6121, 6091, 6120, 6015, 6122, 6122, 6107, 6120, 6122, 6015, 6016, 6121, 6123, 6119, 6103, 6108, 6121, 6108, 6113, 6121, 6121, 6113, 6123, 6113, 6125, 6123, 6125, 6137, 6123, 6124, 6114, 6115, 6126, 6114, 6124, 6107, 6122, 6129, 6129, 6122, 6130, 6116, 6126, 6113, 6116, 6114, 6126, 6113, 6126, 6125, 6126, 6127, 6125, 6126, 6124, 6127, 6130, 6128, 6129, 6217, 6128, 6130, 6132, 6119, 6123, 6131, 6132, 6123, 6218, 6119, 6132, 6132, 6131, 6133, 6133, 6134, 6132, 6132, 6134, 6218, 6122, 6016, 6135, 6137, 6131, 6123, 6136, 6133, 6131, 6122, 6135, 6141, 6141, 6135, 6157, 6138, 6127, 6124, 6140, 6127, 6138, 6122, 6141, 6130, 6137, 6139, 6131, 6131, 6139, 6136, 6142, 6140, 6138, 6143, 6140, 6142, 6136, 6139, 6156, 6127, 6143, 6137, 6125, 6127, 6137, 6127, 6140, 6143, 6137, 6143, 6139, 6144, 6130, 6141, 6145, 6130, 6144, 6147, 6134, 6133, 6146, 6147, 6133, 6149, 6147, 6146, 6148, 6149, 6146, 6149, 6148, 6150, 6135, 6016, 976, 976, 6152, 6135, 6133, 6136, 6146, 6151, 6153, 6136, 6146, 6153, 6148, 6136, 6153, 6146, 6154, 6153, 6151, 6148, 6153, 6154, 6148, 6154, 6150, 6150, 6154, 6155, 6135, 6152, 6157, 6136, 6156, 6151, 979, 6154, 978, 978, 6154, 6151, 6154, 981, 6155, 979, 981, 6154, 6151, 6156, 978, 6156, 987, 978, 6141, 6157, 6159, 6139, 6143, 6156, 6143, 6158, 6156, 6142, 988, 6158, 6143, 6142, 6158, 6156, 6158, 987, 6141, 6159, 6144, 6145, 6144, 6160, 6160, 6144, 6159, 6159, 6157, 6161, 6160, 6159, 6161, 6161, 6157, 993, 993, 992, 6161, 6161, 992, 6160, 6239, 6160, 992, 6165, 6240, 6162, 6165, 6164, 6163, 6164, 6165, 6166, 6166, 6165, 6162, 6167, 6165, 6163, 6240, 6165, 6167, 6163, 6164, 6017, 6017, 6164, 6169, 6166, 6169, 6164, 6162, 6168, 6166, 6166, 6168, 6169, 6163, 6018, 6167, 6169, 6172, 6017, 6171, 6170, 6169, 6171, 6169, 6168, 6017, 6172, 6026, 6173, 6175, 6172, 6173, 6172, 6169, 6169, 6174, 6173, 6170, 6171, 6174, 6169, 6170, 6174, 6179, 6175, 6173, 6026, 6172, 6175, 6175, 6034, 6026, 6175, 6179, 6034, 6179, 6030, 6034, 6179, 6037, 6030, 6180, 6177, 6181, 6177, 6176, 6181, 6177, 6180, 6178, 6178, 6180, 6046, 6044, 6180, 6043, 6043, 6180, 6181, 6176, 6260, 6181, 6044, 6046, 6180, 6181, 6049, 6043, 6181, 6260, 6049, 6260, 6355, 6000, 6187, 6183, 6240, 6184, 6185, 6167, 6240, 6167, 6185, 6052, 6185, 6184, 6185, 6186, 6187, 6185, 6187, 6240, 6186, 6189, 6187, 6052, 6054, 6190, 6052, 6190, 6185, 6190, 6054, 6064, 6190, 6186, 6185, 6191, 6186, 6190, 6188, 6189, 6192, 6188, 6192, 6054, 6188, 6054, 6055, 6064, 6054, 6192, 6191, 6189, 6186, 6192, 6189, 6191, 6064, 6063, 6190, 6063, 6193, 6190, 6193, 6191, 6190, 6194, 6191, 6193, 6208, 6171, 6195, 6064, 6192, 6196, 6064, 6196, 6066, 6192, 6197, 6196, 6192, 6191, 6197, 6191, 6194, 6197, 6199, 6179, 6173, 6198, 6193, 6073, 6193, 6063, 6073, 6198, 6194, 6193, 6171, 6208, 6174, 6194, 6198, 6197, 6174, 6199, 6173, 6179, 6076, 6037, 6198, 6073, 6076, 6198, 6076, 6201, 6201, 6076, 6179, 6179, 6199, 6201, 6201, 6212, 6207, 6198, 6201, 6207, 6201, 6199, 6212, 6084, 6178, 6046, 6117, 6211, 6084, 6178, 6084, 6211, 6087, 6189, 6188, 6187, 6189, 6183, 6183, 6189, 6087, 6183, 6087, 6090, 6055, 6087, 6188, 6196, 6202, 6066, 6202, 6096, 6066, 6203, 6196, 6197, 6202, 6196, 6203, 6204, 6208, 6195, 6205, 6100, 6202, 6100, 6096, 6202, 6203, 6205, 6202, 6213, 6208, 6204, 6206, 6100, 6205, 6089, 6100, 6206, 6215, 6208, 6213, 6206, 6105, 6089, 6203, 6198, 6207, 6197, 6198, 6203, 6174, 6208, 6199, 6205, 6203, 6207, 6205, 6207, 6210, 6199, 6209, 6212, 6208, 6209, 6199, 6205, 6210, 6206, 6210, 6105, 6206, 6109, 6105, 6210, 6214, 6212, 6209, 6208, 6215, 6209, 6207, 6212, 6210, 6109, 6210, 6212, 6109, 6212, 6115, 6211, 6117, 6128, 6211, 6128, 6217, 6273, 6090, 6118, 6273, 6118, 6119, 6220, 6214, 6209, 6215, 6220, 6209, 6223, 6214, 6220, 6212, 6216, 6115, 6216, 6124, 6115, 6212, 6214, 6216, 6214, 6223, 6216, 6119, 6218, 6273, 6273, 6218, 6225, 6218, 6147, 6225, 6218, 6134, 6147, 6221, 6213, 6219, 6233, 6221, 6219, 6222, 6223, 6220, 6213, 6221, 6215, 6215, 6221, 6220, 6221, 6222, 6220, 6221, 6233, 6222, 6216, 6223, 6124, 6223, 6138, 6124, 6223, 6237, 6138, 6237, 6142, 6138, 6223, 6222, 6237, 6145, 6224, 6217, 6217, 6224, 6277, 6217, 6130, 6145, 1351, 6225, 6226, 6149, 6225, 6147, 6225, 6149, 6227, 6225, 6227, 6226, 1090, 1092, 6149, 1094, 6228, 6227, 1094, 6227, 1092, 1092, 6227, 6149, 6228, 6226, 6227, 6228, 1096, 6226, 6149, 6229, 1090, 6149, 6150, 6229, 1106, 1090, 6229, 1106, 6228, 1094, 1106, 6229, 6228, 6229, 6230, 6228, 6230, 1096, 6228, 1107, 1096, 6230, 6231, 6233, 6219, 6235, 6233, 6231, 6232, 6235, 6231, 6232, 6281, 6235, 1112, 6229, 6155, 6229, 6150, 6155, 6229, 1113, 6230, 1112, 1113, 6229, 6230, 1113, 1107, 6233, 6234, 6222, 6237, 6222, 6234, 6233, 6235, 6234, 1116, 6237, 6234, 6235, 6281, 6236, 6155, 981, 1112, 6235, 1116, 6234, 6236, 1116, 6235, 6238, 6277, 6224, 6237, 988, 6142, 6145, 6239, 6224, 6224, 6239, 6238, 992, 6238, 6239, 6145, 6160, 6239, 6240, 6284, 6241, 6284, 6240, 6325, 6162, 6240, 6242, 6241, 6242, 6240, 6241, 6243, 6242, 6242, 6244, 6162, 6242, 6245, 6244, 6246, 6245, 6243, 6243, 6245, 6242, 6162, 6247, 6168, 6248, 6247, 6244, 6244, 6247, 6162, 6248, 6244, 6245, 6245, 6249, 6248, 6245, 6246, 6249, 6168, 6247, 6250, 6171, 6168, 6250, 6247, 6248, 6250, 6251, 6253, 6252, 6258, 6251, 6252, 6250, 6254, 6171, 6248, 6263, 6254, 6250, 6248, 6254, 6255, 6251, 6258, 6253, 6251, 6302, 6302, 6251, 6255, 6254, 6195, 6171, 6256, 6257, 6258, 6258, 6257, 6255, 6177, 6257, 6256, 6177, 6256, 6176, 6176, 6256, 6259, 6258, 6259, 6256, 6252, 6259, 6258, 6177, 6178, 6257, 6259, 6260, 6176, 6240, 6183, 6273, 6261, 6263, 6248, 6262, 6261, 6248, 6249, 6262, 6248, 6254, 6263, 6195, 6302, 6255, 6316, 6200, 6255, 6257, 6316, 6255, 6200, 6178, 6200, 6257, 6325, 6240, 6273, 6183, 6090, 6273, 6264, 6266, 6262, 6265, 6266, 6264, 6267, 6265, 6328, 6268, 6263, 6261, 6262, 6266, 6261, 6270, 6268, 6261, 6266, 6267, 6261, 6267, 6270, 6261, 6266, 6265, 6267, 6263, 6204, 6195, 6263, 6268, 6204, 6268, 6213, 6204, 6324, 6316, 6200, 6211, 6324, 6200, 6178, 6211, 6200, 6328, 6269, 6267, 6267, 6271, 6270, 6267, 6269, 6271, 6272, 6268, 6270, 6268, 6272, 6213, 6324, 6211, 6277, 6324, 6277, 6323, 6217, 6277, 6211, 6270, 6274, 6272, 6270, 6271, 6274, 6275, 6274, 6271, 6280, 6276, 6274, 6275, 6279, 6274, 6272, 6219, 6213, 6272, 6274, 6276, 6272, 6276, 6219, 6276, 6280, 6219, 6277, 6342, 6337, 6273, 6225, 1351, 6278, 6280, 6274, 6279, 6278, 6274, 1271, 6280, 6278, 6279, 1271, 6278, 6280, 6231, 6219, 6282, 6232, 6231, 6280, 6282, 6231, 6281, 6232, 6282, 6280, 1276, 6282, 6280, 1271, 1276, 1275, 6281, 6282, 1274, 6281, 1275, 1276, 1275, 6282, 6342, 6277, 6238, 6281, 1274, 6236, 6284, 6283, 6285, 6286, 6283, 6284, 6286, 6284, 6287, 6285, 6241, 6284, 6283, 6288, 6285, 6285, 6288, 6241, 6288, 6283, 6286, 6289, 6288, 6286, 6286, 6287, 6289, 6241, 6290, 6243, 6294, 6290, 6288, 6288, 6290, 6241, 6288, 6289, 6294, 6289, 6287, 6291, 6294, 6289, 6291, 6287, 6343, 6291, 6343, 6310, 6291, 6290, 6294, 6243, 6249, 6246, 6295, 6246, 6243, 6295, 6294, 6295, 6243, 6294, 6296, 6295, 6291, 6310, 6296, 6294, 6291, 6296, 6297, 6293, 6302, 6298, 6299, 6300, 6299, 6293, 6300, 6293, 6292, 6300, 6300, 6292, 6301, 6293, 6299, 6302, 6253, 6299, 6298, 6253, 6298, 6252, 6252, 6298, 6303, 6300, 6303, 6298, 6300, 6301, 6303, 6299, 6253, 6302, 6301, 6350, 6303, 6303, 6259, 6252, 6303, 6350, 6259, 6259, 6350, 6260, 6355, 6364, 6182, 6356, 6357, 6355, 6355, 6357, 6364, 6364, 6304, 6182, 6364, 6305, 6304, 6304, 6306, 6182, 6304, 6307, 6306, 6305, 6308, 6304, 6304, 6308, 6307, 6307, 6309, 6306, 6309, 6307, 6308, 6308, 6380, 6309, 6182, 6306, 3947, 6309, 3944, 6306, 6309, 3945, 3944, 6317, 6284, 6325, 6284, 6317, 6287, 6287, 6317, 6318, 6287, 6318, 6343, 6314, 6296, 6310, 6343, 6311, 6310, 6319, 6314, 6310, 6311, 6319, 6310, 6312, 6249, 6295, 6262, 6249, 6312, 6296, 6312, 6295, 6316, 6313, 6297, 6296, 6314, 6312, 6321, 6313, 6316, 6297, 6302, 6316, 6321, 6316, 6315, 6326, 6317, 6325, 6317, 6326, 6318, 6311, 6318, 6399, 6399, 6320, 6319, 6311, 6399, 6319, 6312, 6264, 6262, 6312, 6314, 6264, 6314, 6322, 6264, 6322, 6265, 6264, 6319, 6320, 6322, 6314, 6319, 6322, 6322, 6328, 6265, 6321, 6315, 6324, 6321, 6324, 6323, 6316, 6324, 6315, 6327, 6320, 6399, 6329, 6320, 6327, 6333, 6329, 6327, 6399, 6333, 6327, 6322, 6329, 6328, 6322, 6320, 6329, 6271, 6269, 6328, 6329, 6330, 6328, 6329, 6333, 6330, 6336, 6323, 6337, 6337, 6323, 6277, 6338, 6326, 6325, 6273, 6338, 6325, 6326, 6338, 6331, 6338, 6273, 1351, 6326, 6331, 6334, 6331, 6332, 6334, 6399, 6334, 6333, 6340, 6333, 6332, 6334, 6332, 6333, 6335, 6271, 6328, 6330, 6335, 6328, 6330, 6340, 6335, 6330, 6333, 6340, 6340, 1358, 6335, 6271, 6335, 6275, 6335, 1358, 6275, 6331, 6338, 1354, 6332, 6331, 6339, 6331, 1354, 6339, 6339, 6340, 6332, 1355, 6340, 6339, 6275, 1358, 6279, 6336, 6337, 6341, 6337, 6342, 6341, 6341, 6342, 1359, 6342, 1363, 1359, 6292, 6293, 6344, 6297, 6344, 6293, 6344, 6347, 6345, 6344, 6345, 6292, 6292, 6345, 6301, 6346, 6347, 6344, 6345, 6348, 6301, 6301, 6348, 6350, 6349, 6348, 6345, 6345, 6347, 6349, 6349, 6347, 6351, 6351, 6347, 6346, 6348, 6349, 6350, 6353, 6351, 6346, 6350, 6352, 6260, 6349, 6352, 6350, 6349, 6351, 6352, 6352, 6355, 6260, 6353, 6352, 6351, 6495, 6352, 6353, 6353, 6401, 6354, 6495, 6353, 6354, 6352, 6356, 6355, 6356, 6358, 6357, 6357, 6359, 6364, 6358, 6360, 6357, 6357, 6360, 6359, 6361, 6360, 6358, 6366, 6360, 6361, 6363, 6366, 6361, 6362, 6366, 6363, 6507, 6362, 6363, 6359, 6365, 6364, 6365, 6359, 6366, 6359, 6360, 6366, 6365, 6366, 6367, 6366, 6362, 6367, 6368, 6305, 6364, 6364, 6365, 6368, 6365, 6369, 6368, 6365, 6367, 6369, 6367, 6370, 6369, 6370, 6367, 6389, 6371, 6305, 6368, 6368, 6372, 6371, 6372, 6368, 6369, 6369, 6370, 6372, 6372, 6370, 6373, 6370, 6389, 6373, 6373, 6389, 6374, 6308, 6305, 6371, 6371, 6375, 6308, 6372, 6375, 6371, 6372, 6373, 6375, 6375, 6373, 6376, 6376, 6374, 6377, 6376, 6373, 6374, 6375, 6378, 6308, 6378, 6375, 6376, 6378, 6376, 6379, 6376, 6377, 6379, 6308, 6378, 6380, 6378, 6379, 6380, 6380, 6379, 6382, 6380, 6381, 6309, 6309, 6381, 3945, 6381, 6380, 6382, 6382, 6379, 6383, 6383, 6394, 6396, 6383, 6379, 6394, 4514, 3945, 6381, 6382, 4514, 6381, 6382, 4515, 4514, 6382, 6383, 4515, 4515, 6383, 4518, 6311, 6343, 6318, 6313, 6344, 6297, 6344, 6313, 6346, 6353, 6346, 6401, 6384, 6385, 6362, 6507, 6384, 6362, 6507, 6516, 6384, 6384, 6516, 6387, 6362, 6385, 6367, 6385, 6384, 6387, 6387, 6386, 6385, 6386, 6404, 6405, 6386, 6387, 6404, 6388, 6404, 6387, 6367, 6385, 6389, 6385, 6386, 6389, 6389, 6386, 6390, 6405, 6390, 6386, 6374, 6389, 6391, 6391, 6389, 6390, 6391, 6390, 6392, 6374, 6391, 6393, 6391, 6395, 6393, 6377, 6374, 6394, 6377, 6394, 6379, 6374, 6393, 6394, 6394, 6393, 6395, 6394, 6395, 6396, 6396, 6395, 6397, 6397, 6395, 6398, 4518, 6383, 6396, 4518, 6396, 4778, 6396, 6397, 4778, 4778, 6397, 6420, 6400, 6313, 6321, 6323, 6400, 6321, 6422, 6400, 6323, 6336, 6422, 6323, 6313, 6400, 6346, 6400, 6401, 6346, 6400, 6422, 6401, 6522, 6402, 6403, 6423, 6402, 6522, 6403, 6404, 6388, 6406, 6405, 6404, 6404, 6403, 6406, 6407, 6405, 6406, 6403, 6402, 6406, 6402, 6423, 6406, 6408, 6390, 6405, 6409, 6408, 6405, 6405, 6407, 6410, 6409, 6405, 6410, 6390, 6408, 6392, 6392, 6408, 6411, 6409, 6412, 6411, 6409, 6411, 6408, 6413, 6412, 6409, 6409, 6410, 6413, 6413, 6410, 6414, 6391, 6392, 6415, 6392, 6411, 6415, 6412, 6415, 6411, 6416, 6415, 6412, 6413, 6416, 6412, 6413, 6417, 6416, 6413, 6414, 6417, 6415, 6418, 6391, 6418, 6395, 6391, 6418, 6415, 6416, 6437, 6416, 6417, 6416, 6437, 6419, 6395, 6418, 6398, 6418, 6416, 6420, 6416, 6419, 6420, 6398, 6418, 6420, 6397, 6398, 6420, 6420, 6419, 6421, 6420, 6421, 6443, 4869, 6420, 4870, 4870, 6420, 6443, 6326, 6399, 6318, 6527, 6424, 6423, 6448, 6424, 6527, 6424, 6448, 6425, 6423, 6407, 6406, 6423, 6424, 6407, 6407, 6424, 6427, 6425, 6427, 6424, 6427, 6410, 6407, 6425, 6428, 6427, 6425, 6426, 6428, 6410, 6427, 6414, 6414, 6427, 6429, 6427, 6431, 6430, 6427, 6430, 6429, 6427, 6428, 6431, 6431, 6428, 6455, 6414, 6429, 6417, 6417, 6429, 6432, 6429, 6430, 6432, 6432, 6430, 6433, 6431, 6434, 6430, 6430, 6434, 6433, 6431, 6455, 6435, 6436, 6434, 6431, 6435, 6436, 6431, 6437, 6417, 6432, 6432, 6433, 6437, 6433, 6434, 6438, 6462, 6438, 6436, 6438, 6434, 6436, 6439, 6437, 6433, 6433, 6438, 6439, 6462, 6440, 6438, 6419, 6437, 6441, 6441, 6437, 6439, 6441, 6439, 6444, 6439, 6438, 6444, 6444, 6438, 6442, 6440, 6442, 6438, 6466, 6442, 6440, 6419, 6441, 6421, 6421, 6441, 6443, 6443, 6441, 6444, 6445, 6443, 6444, 6442, 6445, 6444, 6442, 6466, 6445, 6443, 6445, 6446, 6446, 6445, 6447, 6334, 6399, 6326, 6470, 6422, 6336, 6470, 6401, 6422, 6533, 6448, 6527, 6449, 6448, 6530, 6449, 6471, 6451, 6448, 6449, 6451, 6450, 6426, 6425, 6448, 6450, 6425, 6451, 6450, 6448, 6428, 6426, 6452, 6426, 6450, 6452, 6452, 6450, 6453, 6450, 6451, 6453, 6453, 6451, 6454, 6428, 6452, 6455, 6455, 6452, 6458, 6458, 6453, 6456, 6458, 6452, 6453, 6458, 6456, 6460, 6456, 6454, 6478, 6453, 6454, 6456, 6478, 6460, 6456, 6435, 6455, 6458, 6436, 6435, 6457, 6457, 6435, 6458, 6457, 6534, 6436, 6457, 6458, 6459, 6534, 6457, 6459, 6460, 6459, 6458, 6461, 6459, 6460, 6460, 6478, 6482, 6482, 6463, 6461, 6482, 6461, 6460, 6462, 6464, 6440, 6464, 6466, 6440, 6445, 6466, 6467, 6539, 6467, 6466, 6487, 6467, 6539, 6468, 6467, 6487, 6447, 6467, 6469, 6445, 6467, 6447, 6469, 6468, 6492, 6467, 6468, 6469, 6341, 6470, 6336, 1464, 6401, 6470, 6401, 1464, 6524, 1464, 6540, 6524, 6540, 1464, 1669, 1466, 1469, 6471, 1468, 1469, 1466, 6474, 6451, 6472, 6472, 6451, 6471, 6471, 1469, 6472, 6472, 1469, 6473, 1469, 1467, 6473, 6473, 1467, 1472, 6451, 6474, 6454, 6474, 6472, 6475, 6475, 6472, 6476, 6476, 6472, 6473, 1472, 6476, 6473, 6478, 6454, 6477, 6454, 6474, 6477, 6474, 6475, 6477, 6477, 6475, 6479, 6475, 6480, 6479, 6480, 6475, 6476, 1472, 1483, 6476, 1483, 6480, 6476, 6477, 6481, 6478, 6482, 6478, 6481, 6477, 6479, 6481, 6481, 6479, 6483, 6480, 6483, 6479, 1483, 1487, 6483, 6483, 6480, 1483, 6486, 6482, 6481, 6486, 6463, 6482, 6483, 6484, 6481, 6483, 1487, 1492, 6483, 1492, 1488, 1488, 1485, 6544, 6465, 6463, 6486, 6481, 6484, 6486, 6483, 1488, 6484, 6544, 6485, 1488, 6465, 6486, 6487, 6486, 6484, 6488, 6484, 6489, 6488, 6484, 1488, 6489, 1488, 6485, 6489, 6487, 6486, 6468, 6486, 6490, 6468, 6488, 6490, 6486, 6491, 6490, 6488, 6491, 6488, 6489, 1497, 1496, 6489, 1496, 6491, 6489, 6468, 6490, 6492, 6490, 6493, 6492, 6493, 6494, 6492, 6493, 6490, 6491, 4686, 6494, 6491, 6494, 6493, 6491, 6491, 1496, 4686, 6499, 6495, 6354, 6495, 6499, 6496, 6496, 6352, 6495, 6352, 6496, 6356, 6496, 6497, 6356, 6498, 6497, 6496, 6496, 6499, 6498, 6497, 6501, 6356, 6501, 6497, 6498, 6502, 6501, 6498, 6502, 6498, 6503, 6498, 6499, 6503, 6503, 6499, 6504, 6499, 6500, 6504, 6504, 6500, 6505, 6502, 6506, 6358, 6502, 6358, 6501, 6501, 6358, 6356, 6503, 6506, 6502, 6504, 6506, 6503, 6507, 6504, 6505, 6358, 6506, 6361, 6506, 6504, 6363, 6361, 6506, 6363, 6504, 6507, 6363, 6354, 6508, 6499, 6508, 6500, 6499, 6509, 6500, 6508, 6510, 6509, 6508, 6500, 6509, 6511, 6511, 6509, 6512, 6512, 6509, 6513, 6510, 6521, 6513, 6510, 6513, 6509, 6500, 6511, 6505, 6505, 6511, 6514, 6511, 6512, 6514, 6505, 6514, 6507, 6512, 6513, 6515, 6507, 6514, 6516, 6516, 6514, 6517, 6514, 6512, 6517, 6518, 6387, 6516, 6516, 6517, 6518, 6512, 6518, 6517, 6512, 6515, 6518, 6515, 6403, 6518, 6518, 6403, 6388, 6518, 6388, 6387, 6420, 4869, 4778, 6519, 6510, 6508, 6354, 6519, 6508, 6401, 6519, 6354, 6525, 6519, 6401, 6510, 6519, 6520, 6520, 6519, 6525, 6520, 6521, 6510, 6522, 6521, 6520, 6525, 6522, 6520, 6525, 6526, 6522, 6515, 6513, 6523, 6513, 6521, 6523, 6521, 6522, 6523, 6523, 6522, 6403, 6515, 6523, 6403, 6525, 6401, 6524, 6525, 6524, 6528, 6527, 6526, 6525, 6528, 6527, 6525, 6522, 6526, 6423, 6526, 6527, 6423, 6443, 6446, 4919, 4919, 4870, 6443, 4919, 6446, 6447, 6524, 6540, 6529, 6529, 6528, 6524, 6530, 6528, 6531, 6531, 6528, 6529, 6531, 6529, 6532, 6528, 6530, 6527, 6527, 6530, 6533, 6530, 6448, 6533, 6531, 6449, 6530, 6532, 6449, 6531, 6449, 6532, 6471, 6535, 6534, 6459, 6536, 6535, 6461, 6461, 6535, 6459, 6536, 6461, 6463, 6462, 6436, 6534, 6537, 6462, 6534, 6535, 6537, 6534, 6535, 6536, 6537, 6536, 6538, 6537, 6538, 6536, 6463, 6464, 6462, 6537, 6465, 6538, 6463, 6464, 6537, 6466, 6466, 6537, 6539, 6539, 6537, 6538, 6465, 6487, 6538, 6538, 6487, 6539, 4963, 6447, 6469, 4963, 4919, 6447, 4963, 6469, 4964, 4964, 6469, 6492, 6529, 6540, 6541, 6540, 1669, 6541, 6532, 6529, 6542, 6529, 6541, 6542, 6532, 6542, 6471, 1671, 6471, 6542, 6471, 6543, 1466, 1466, 6543, 1671, 6471, 1671, 6543, 1702, 6544, 1485, 1708, 6544, 1702, 1722, 6544, 1708, 6544, 6545, 6485, 6545, 6544, 1722, 6485, 6546, 6489, 1729, 6546, 6545, 6545, 6546, 6485, 6545, 1722, 1729, 6489, 6546, 1497, 6546, 1729, 1497]}}, {"UUID": "A1FC5FB0FBE83CCC2644868FC4A439A4", "SemanticClassifications": ["CEILING"], "Transform": {"Translation": [0.375472784, 2.415001, 0.08878064], "Rotation": [90.0, 160.119614, 0.0], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-1.445751, -1.525418], "Max": [1.446459, 1.52313209]}, "PlaneBoundary2D": [[1.446459, 1.33666253], [-1.07590425, 1.52313221], [-1.14183021, -0.043736577], [-1.35849738, -0.0362997055], [-1.445751, -1.52529025], [1.40293145, -1.525418]]}, {"UUID": "9E0728072A37AD5E52C10630FBD8F046", "SemanticClassifications": ["WALL_FACE"], "Transform": {"Translation": [-0.9964247, 1.20908475, -0.3069039], "Rotation": [8.10464371e-07, 70.99091, 1.9791145e-05], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-1.43116188, -1.208197], "Max": [1.4312501, 1.20591652]}, "PlaneBoundary2D": [[1.4312501, 1.2059164], [-1.43116188, 1.2059164], [-1.43116188, -1.208197], [1.4312501, -1.208197]]}, {"UUID": "C0041C08C7ED9E5574792487AB370E4A", "SemanticClassifications": ["OTHER"], "Transform": {"Translation": [-0.7388835, 0.691174865, -0.485314], "Rotation": [270.0, 68.68071, 0.0], "Scale": [1.0, 1.0, 1.0]}, "VolumeBounds": {"Min": [-0.41204, -0.185823083, -0.693432152], "Max": [0.41204, 0.185823083, 0.0]}}, {"UUID": "8707854BFCC45FF7132706FEA760B02A", "SemanticClassifications": ["OTHER"], "Transform": {"Translation": [-1.0381217, 0.8613781, 0.5008015], "Rotation": [270.0, 71.91871, 0.0], "Scale": [1.0, 1.0, 1.0]}, "VolumeBounds": {"Min": [-0.58338666, -0.230821848, -0.864590049], "Max": [0.58338666, 0.230821848, 0.0]}}, {"UUID": "EF47C5A4216E7A42504B31E228185A7F", "SemanticClassifications": ["SCREEN"], "Transform": {"Translation": [0.154281616, 1.53407526, -1.27201915], "Rotation": [270.0, 339.778656, 0.0], "Scale": [1.0, 1.0, 1.0]}, "VolumeBounds": {"Min": [-0.407521129, -0.055000186, -0.495673656], "Max": [0.407521129, 0.055000186, 0.0]}}, {"UUID": "9A198B4708653DDB000AAD44F4B9B585", "SemanticClassifications": ["WALL_FACE"], "Transform": {"Translation": [-0.123565674, 1.20866013, 1.53034139], "Rotation": [-1.86179841e-05, 160.1222, 8.989649e-07], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-1.42483425, -1.20777321], "Max": [1.42384815, 1.20634031]}, "PlaneBoundary2D": [[1.42384815, 1.20634031], [-1.42483425, 1.20634031], [-1.42483425, -1.20777321], [1.42384815, -1.20777321]]}, {"UUID": "BAAAB801795C4FC3AC487BA182A549EA", "SemanticClassifications": ["LAMP"], "Transform": {"Translation": [-1.22863245, 1.426512, 0.875592947], "Rotation": [270.0, 76.81259, 0.0], "Scale": [1.0, 1.0, 1.0]}, "VolumeBounds": {"Min": [-0.155107379, -0.169127345, -0.5660759], "Max": [0.155107379, 0.169127345, 0.0]}}, {"UUID": "0B45191E9CF59C1D85F909D8606113F8", "SemanticClassifications": ["STORAGE"], "Transform": {"Translation": [1.42034018, 1.32032156, 0.6823858], "Rotation": [270.0, 252.266754, 0.0], "Scale": [1.0, 1.0, 1.0]}, "VolumeBounds": {"Min": [-0.1791727, -0.147483349, -1.3179096], "Max": [0.1791727, 0.147483349, 0.0]}}, {"UUID": "292346587ABDB5DC784A3EE8322F284C", "SemanticClassifications": ["WALL_ART"], "Transform": {"Translation": [-1.03865552, 1.51992321, 1.17857814], "Rotation": [-2.21707542e-05, 160.107834, 1.35347591e-06], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-0.191193819, -0.224595785], "Max": [0.191193819, 0.224595755]}, "PlaneBoundary2D": [[0.191193819, 0.2245957], [-0.191193819, 0.224595755], [-0.191193819, -0.224595785], [0.191193819, -0.224595785]]}, {"UUID": "2F1139AC527AA7B62FB0A831C044D16A", "SemanticClassifications": ["STORAGE"], "Transform": {"Translation": [-0.0016078949, 0.952749133, -1.24095237], "Rotation": [270.0, 344.3625, 0.0], "Scale": [1.0, 1.0, 1.0]}, "VolumeBounds": {"Min": [-0.6731781, -0.2810794, -0.950639963], "Max": [0.6731781, 0.2810794, 0.0]}}, {"UUID": "1DB2D3B732CDFB01E2D645C9E7EF584A", "SemanticClassifications": ["WALL_FACE"], "Transform": {"Translation": [1.53789258, 1.20721734, 0.551660061], "Rotation": [1.9673711e-05, 342.085327, 3.78923869e-06], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-0.108785272, -1.2063297], "Max": [0.108009577, 1.20778382]}, "PlaneBoundary2D": [[0.108009577, 1.2077837], [-0.108785272, 1.2077837], [-0.108785272, -1.2063297], [0.108009577, -1.2063297]]}, {"UUID": "BD460F821B48A45B71ED111D91B190FF", "SemanticClassifications": ["FLOOR"], "Transform": {"Translation": [0.272729158, 0.00088763237, 0.1586191], "Rotation": [270.0, 164.3377, 0.0], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-1.43222141, -1.53485465], "Max": [1.43125677, 1.53251338]}, "PlaneBoundary2D": [[1.43125677, 1.32311237], [-1.409719, 1.5325135], [-1.43222129, 0.04113865], [-1.215594, 0.03261888], [-1.26509464, -1.53485465], [1.26415157, -1.53441739]]}, {"UUID": "3A33F379F4F9DBBDBBDB0B0A69A6DF36", "SemanticClassifications": ["DOOR_FRAME"], "Transform": {"Translation": [0.08118892, 1.00593019, 1.60276425], "Rotation": [-1.63365075e-05, 160.118378, 9.043586e-07], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-0.669873357, -1.00360262], "Max": [0.6698735, 1.00360262]}, "PlaneBoundary2D": [[0.6698735, 1.00360262], [-0.669873357, 1.00360262], [-0.669873357, -1.00360262], [0.6698735, -1.00360262]]}, {"UUID": "555C7B6D42DE0C950C6A6C03E437E247", "SemanticClassifications": ["WALL_FACE"], "Transform": {"Translation": [0.6879184, 1.20653057, -1.31878614], "Rotation": [2.45602369e-05, 344.3476, 2.56704152e-06], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-1.265074, -1.2056421], "Max": [1.26417255, 1.208471]}, "PlaneBoundary2D": [[1.26417255, 1.20847106], [-1.265074, 1.20847106], [-1.265074, -1.2056421], [1.26417255, -1.2056421]]}, {"UUID": "013D9ADBB92120F5FBCBCC5DA14678EE", "SemanticClassifications": ["OTHER"], "Transform": {"Translation": [1.29780018, 1.00415516, -0.827092767], "Rotation": [270.0, 341.045715, 0.0], "Scale": [1.0, 1.0, 1.0]}, "VolumeBounds": {"Min": [-0.161425412, -0.170847684, -0.2636727], "Max": [0.161425412, 0.170847684, 0.0]}}, {"UUID": "F59C4D91C8D90D303B9501564B3D9124", "SemanticClassifications": ["WALL_FACE"], "Transform": {"Translation": [1.66987884, 1.20691741, -0.230019808], "Rotation": [1.61363823e-06, 252.5289, -1.5110596e-05], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-0.783851862, -1.20602942], "Max": [0.784402966, 1.20808411]}, "PlaneBoundary2D": [[0.784402966, 1.208084], [-0.783851862, 1.208084], [-0.783851862, -1.20602942], [0.784402966, -1.20602942]]}, {"UUID": "8DCF3A8E7CB14F394D5372AE535B8AD2", "SemanticClassifications": ["TABLE"], "Transform": {"Translation": [1.19177938, 0.7403732, -0.834802866], "Rotation": [270.0, 344.36145, 0.0], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-0.6101577, -0.350542426], "Max": [0.610157967, 0.3505425]}, "VolumeBounds": {"Min": [-0.6101577, -0.350542367, -0.738751054], "Max": [0.6101581, 0.350542, 1.1920929e-07]}, "PlaneBoundary2D": [[0.610157967, 0.3505425], [-0.6101577, 0.35054177], [-0.6101575, -0.350542426], [0.610157967, -0.350542]]}, {"UUID": "70965D0CF267AF51349EF7C804129656", "SemanticClassifications": ["WINDOW_FRAME"], "Transform": {"Translation": [1.12891281, 1.57366765, -1.19823408], "Rotation": [2.9381632e-05, 344.4268, 2.72558941e-06], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-0.8249998, -0.475000054], "Max": [0.8250003, 0.475]}, "PlaneBoundary2D": [[0.8250003, 0.475000024], [-0.8249998, 0.475000024], [-0.8249998, -0.475000054], [0.8250003, -0.475000054]]}, {"UUID": "AA65A76759969B77BFA9C374548F78BF", "SemanticClassifications": ["WALL_FACE"], "Transform": {"Translation": [1.42855763, 1.20818555, 1.29972243], "Rotation": [-3.89915931e-06, 253.473267, -2.94855545e-05], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-0.745643854, -1.207298], "Max": [0.745901, 1.20681524]}, "PlaneBoundary2D": [[0.745901, 1.20681524], [-0.745643854, 1.20681524], [-0.745643854, -1.207298], [0.745901, -1.207298]]}, {"UUID": "A082BE997B4FE8C60CAFE483F26BC010", "SemanticClassifications": ["DOOR_FRAME"], "Transform": {"Translation": [1.36979711, 1.01257038, 1.49190044], "Rotation": [3.7171385e-06, 253.950775, -1.95072735e-05], "Scale": [1.0, 1.0, 1.0]}, "PlaneBounds": {"Min": [-0.405159473, -1.01070952], "Max": [0.4051596, 1.01070952]}, "PlaneBoundary2D": [[0.4051596, 1.01070952], [-0.405159473, 1.01070952], [-0.405159473, -1.01070952], [0.4051596, -1.01070952]]}]}]}