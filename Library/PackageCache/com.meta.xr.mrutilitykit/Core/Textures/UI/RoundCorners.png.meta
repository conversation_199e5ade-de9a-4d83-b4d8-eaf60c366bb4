fileFormatVersion: 2
guid: 6329baa70222fee4ea544dd84b7610b2
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 12
  mipmaps:
    mipMapMode: 0
    enableMipMap: 1
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMasterTextureLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 1
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 160
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 0
  alphaUsage: 1
  alphaIsTransparency: 0
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Server
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: RoundCorners_0
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 2, y: 2, z: 2, w: 2}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3a8035f9fe9f1b248b811fb5fb2779ac
      internalID: -1699348145
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RoundCorners_1
      rect:
        serializedVersion: 2
        x: 66
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 4, y: 4, z: 4, w: 4}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 24c05d8d018737642b372cbfd8afeb35
      internalID: -1672551875
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RoundCorners_2
      rect:
        serializedVersion: 2
        x: 132
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 8, y: 8, z: 8, w: 8}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c310631844efff345986f4e1f6f16be0
      internalID: 843850486
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RoundCorners_3
      rect:
        serializedVersion: 2
        x: 198
        y: 0
        width: 65
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 12, y: 12, z: 12, w: 12}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c6a1c759eead855458f969ec6bca98a5
      internalID: -390783979
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RoundCorners_4
      rect:
        serializedVersion: 2
        x: 264
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 16, y: 16, z: 16, w: 16}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a69991f02c1aa9645a24ff5b4554cbf7
      internalID: -1442444011
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RoundCorners_5
      rect:
        serializedVersion: 2
        x: 330
        y: 0
        width: 64
        height: 64
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 30, y: 30, z: 30, w: 30}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5c6157466f5e52949b577759bdd91d19
      internalID: 1413655889
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: a9a52ff58a1bf704895b85a6ed953b9f
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      RoundCorners_0: -1699348145
      RoundCorners_1: -1672551875
      RoundCorners_2: 843850486
      RoundCorners_3: -390783979
      RoundCorners_4: -1442444011
      RoundCorners_5: 1413655889
  spritePackingTag: 
  pSDRemoveMatte: 0
  pSDShowRemoveMatteOption: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
