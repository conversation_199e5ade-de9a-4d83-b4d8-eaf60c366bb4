fileFormatVersion: 2
guid: c933168a9a0f1e04bb793786b96e2f4b
ModelImporter:
  serializedVersion: 20200
  internalIDToNameTable:
  - first:
      74: 1827226128182048838
    second: right_touchpro_controller_animation
  - first:
      74: 1566619013183129559
    second: right_touchpro_controller_button01
  - first:
      74: 3914968466975713192
    second: right_touchpro_controller_stickSE
  - first:
      74: -6338187929255524150
    second: right_touchpro_controller_trigger
  - first:
      74: 2509944413163972655
    second: right_touchpro_controller_button02
  - first:
      74: -5227244007551698105
    second: right_touchpro_controller_button03
  - first:
      74: -4949944247965877373
    second: right_touchpro_controller_stickSW
  - first:
      74: 6860885293025420570
    second: right_touchpro_controller_stickE
  - first:
      74: -7415750269733489216
    second: right_touchpro_controller_grip
  - first:
      74: -6869761263975083632
    second: right_touchpro_controller_stickNW
  - first:
      74: -2550205761096473782
    second: right_touchpro_controller_stickNE
  - first:
      74: 5508014731428483579
    second: right_touchpro_controller_stickW
  - first:
      74: 846660437211551335
    second: right_touchpro_controller_stickS
  - first:
      74: -260043828329313287
    second: right_touchpro_controller_neutral_button01
  - first:
      74: -5421574445139181965
    second: right_touchpro_controller_neutral_button02
  - first:
      74: -3673678809249851200
    second: right_touchpro_controller_neutral_button03
  - first:
      74: -8077461655156753347
    second: right_touchpro_controller_neutral_grip
  - first:
      74: -2510586379632383826
    second: right_touchpro_controller_neutral_trigger
  - first:
      74: 8983535773375949353
    second: right_touchpro_controller_stickN
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: batteryIndicator_mat
    second: {fileID: 2100000, guid: 3abd38e0d18db6c49b2c96961d69d024, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: controllerMATphongRT
    second: {fileID: 2100000, guid: dfee80e1d2a38ea45a01698af1c06034, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: controllerMATstingrayRT
    second: {fileID: 2100000, guid: dfee80e1d2a38ea45a01698af1c06034, type: 2}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: right_touchpro_controller_animation
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 54
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: right_touchpro_controller_button01
      takeName: Take 001
      internalID: 0
      firstFrame: 6
      lastFrame: 7
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 1
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 0
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_stickSE
      takeName: Take 001
      internalID: 0
      firstFrame: 50
      lastFrame: 51
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 1
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_trigger
      takeName: Take 001
      internalID: 0
      firstFrame: 20
      lastFrame: 21
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 0
      - path: right_oculus_controller_world/b_trigger_front
        weight: 1
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_button02
      takeName: Take 001
      internalID: 0
      firstFrame: 10
      lastFrame: 11
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 1
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 0
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_button03
      takeName: Take 001
      internalID: 0
      firstFrame: 30
      lastFrame: 31
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 1
      - path: right_oculus_controller_world/b_thumbstick
        weight: 0
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_stickSW
      takeName: Take 001
      internalID: 0
      firstFrame: 44
      lastFrame: 45
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 1
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_stickE
      takeName: Take 001
      internalID: 0
      firstFrame: 42
      lastFrame: 43
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 1
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_grip
      takeName: Take 001
      internalID: 0
      firstFrame: 26
      lastFrame: 27
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 0
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 1
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_stickNW
      takeName: Take 001
      internalID: 0
      firstFrame: 46
      lastFrame: 47
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 1
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_stickNE
      takeName: Take 001
      internalID: 0
      firstFrame: 48
      lastFrame: 49
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 1
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_stickW
      takeName: Take 001
      internalID: 0
      firstFrame: 40
      lastFrame: 41
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 1
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_stickS
      takeName: Take 001
      internalID: 0
      firstFrame: 38
      lastFrame: 39
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 1
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_neutral_button01
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 1
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 0
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_neutral_button02
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 1
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 0
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_neutral_button03
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 1
      - path: right_oculus_controller_world/b_thumbstick
        weight: 0
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_neutral_grip
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 0
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 1
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_neutral_trigger
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 0
      - path: right_oculus_controller_world/b_trigger_front
        weight: 1
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    - serializedVersion: 16
      name: right_touchpro_controller_stickN
      takeName: Take 001
      internalID: 0
      firstFrame: 36
      lastFrame: 37
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: right_batteryIndicatorQuad
        weight: 0
      - path: right_nub
        weight: 0
      - path: right_oculus_controller_mesh
        weight: 0
      - path: right_oculus_controller_world
        weight: 0
      - path: right_oculus_controller_world/b_button_a
        weight: 0
      - path: right_oculus_controller_world/b_button_b
        weight: 0
      - path: right_oculus_controller_world/b_button_oculus
        weight: 0
      - path: right_oculus_controller_world/b_thumbstick
        weight: 1
      - path: right_oculus_controller_world/b_trigger_front
        weight: 0
      - path: right_oculus_controller_world/b_trigger_grip
        weight: 0
      - path: right_oculus_controller_world/laser_begin
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 1
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    fileIdsGeneration: 2
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 1
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
