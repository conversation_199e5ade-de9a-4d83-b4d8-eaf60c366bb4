fileFormatVersion: 2
guid: 32fbc8bcebb64584da5868aeeb086cda
ModelImporter:
  serializedVersion: 23
  fileIDToRecycleName:
    100000: //RootNode
    100002: b_button_oculus
    100004: b_button_x
    100006: b_button_y
    100008: b_thumbstick
    100010: b_trigger_front
    100012: b_trigger_grip
    100014: l_controller_ply
    100016: left_monterey_controller_world
    100018: prp00002questController_animrig:f_world
    100020: prp00002questController_animrig:left_monterey_controller_rig
    100022: prp00002questController_animrig:RIG_left_monterey_controller_world
    400000: //RootNode
    400002: b_button_oculus
    400004: b_button_x
    400006: b_button_y
    400008: b_thumbstick
    400010: b_trigger_front
    400012: b_trigger_grip
    400014: l_controller_ply
    400016: left_monterey_controller_world
    400018: prp00002questController_animrig:f_world
    400020: prp00002questController_animrig:left_monterey_controller_rig
    400022: prp00002questController_animrig:RIG_left_monterey_controller_world
    2100000: controllerMTL
    2100002: controlerMAT
    2300000: //RootNode
    3300000: //RootNode
    4300000: l_controller_ply
    7400000: left_quest_controller_animation
    7400002: left_quest_controller_stickW
    7400004: left_quest_controller_stickE
    7400006: left_quest_controller_trigger
    7400008: left_quest_controller_stickN
    7400010: left_quest_controller_stickSE
    7400012: left_quest_controller_stickSW
    7400014: left_quest_controller_stickNW
    7400016: left_quest_controller_stickNE
    7400018: left_quest_controller_grip
    7400020: left_quest_controller_stickS
    7400022: left_quest_controller_button01
    7400024: left_quest_controller_button02
    7400026: left_quest_controller_button03
    7400028: left_quest_controller_button01_neutral
    7400030: left_quest_controller_button02_neutral
    7400032: left_quest_controller_button03_neutral
    7400034: left_quest_controller_trigger_neutral
    7400036: left_quest_controller_grip_neutral
    9500000: //RootNode
    13700000: l_controller_ply
  externalObjects:
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: controlerMAT
    second: {fileID: 2100000, guid: 01ac879f6ce09924e9c26663f798beaf, type: 2}
  - first:
      type: UnityEngine:Material
      assembly: UnityEngine.CoreModule
      name: controllerMTL
    second: {fileID: 2100000, guid: 01ac879f6ce09924e9c26663f798beaf, type: 2}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: left_quest_controller_animation
      takeName: Take 001
      firstFrame: 0
      lastFrame: 60
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_stickW
      takeName: Take 001
      firstFrame: 40
      lastFrame: 41
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 1
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_stickE
      takeName: Take 001
      firstFrame: 42
      lastFrame: 43
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 1
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_trigger
      takeName: Take 001
      firstFrame: 20
      lastFrame: 21
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 0
      - path: left_monterey_controller_world/b_trigger_front
        weight: 1
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_stickN
      takeName: Take 001
      firstFrame: 36
      lastFrame: 37
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 1
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_stickSE
      takeName: Take 001
      firstFrame: 50
      lastFrame: 51
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 1
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_stickSW
      takeName: Take 001
      firstFrame: 44
      lastFrame: 45
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 1
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_stickNW
      takeName: Take 001
      firstFrame: 46
      lastFrame: 47
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 1
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_stickNE
      takeName: Take 001
      firstFrame: 48
      lastFrame: 49
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 1
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_grip
      takeName: Take 001
      firstFrame: 26
      lastFrame: 27
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 0
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 1
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_stickS
      takeName: Take 001
      firstFrame: 38
      lastFrame: 39
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 1
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_button01
      takeName: Take 001
      firstFrame: 6
      lastFrame: 7
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 1
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 0
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_button02
      takeName: Take 001
      firstFrame: 10
      lastFrame: 11
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 1
      - path: left_monterey_controller_world/b_thumbstick
        weight: 0
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_button03
      takeName: Take 001
      firstFrame: 30
      lastFrame: 31
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 1
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 0
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_button01_neutral
      takeName: Take 001
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 1
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 0
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_button02_neutral
      takeName: Take 001
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 1
      - path: left_monterey_controller_world/b_thumbstick
        weight: 0
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_button03_neutral
      takeName: Take 001
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 1
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 0
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_trigger_neutral
      takeName: Take 001
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 0
      - path: left_monterey_controller_world/b_trigger_front
        weight: 1
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    - serializedVersion: 16
      name: left_quest_controller_grip_neutral
      takeName: Take 001
      firstFrame: 0
      lastFrame: 1
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask:
      - path: 
        weight: 1
      - path: l_controller_ply
        weight: 0
      - path: left_monterey_controller_world
        weight: 0
      - path: left_monterey_controller_world/b_button_oculus
        weight: 0
      - path: left_monterey_controller_world/b_button_x
        weight: 0
      - path: left_monterey_controller_world/b_button_y
        weight: 0
      - path: left_monterey_controller_world/b_thumbstick
        weight: 0
      - path: left_monterey_controller_world/b_trigger_front
        weight: 0
      - path: left_monterey_controller_world/b_trigger_grip
        weight: 1
      - path: prp00002questController_animrig:left_monterey_controller_rig
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world
        weight: 0
      - path: prp00002questController_animrig:left_monterey_controller_rig/prp00002questController_animrig:RIG_left_monterey_controller_world/prp00002questController_animrig:f_world
        weight: 0
      maskType: 0
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
    previousCalculatedGlobalScale: 0.01
    hasPreviousCalculatedGlobalScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 1
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 2
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
