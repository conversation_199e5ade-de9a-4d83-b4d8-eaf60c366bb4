%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1bc5d2b9fdb71584da4c3c1ac746be7d, type: 3}
  m_Name: PassthroughWindow
  m_EditorClassIdentifier: 
  id: 4f06f550-8209-46c8-a4c9-879a368560f6
  version: 1
  blockName: Passthrough Window
  description: A window into the VR scene to reveal Passthrough behind it.
  tags:
    array:
    - name: Passthrough
    - name: Experimental
  thumbnail: {fileID: 2800000, guid: 337ab3654a1281249b7a1342a0d7aa2e, type: 3}
  order: 13
  prefab: {fileID: 4333025852127092228, guid: 9bd0c2ef3b2202e4cba99b4fb5ecd752, type: 3}
  externalBlockDependencies: []
  dependencies:
  - e47682b9-c270-40b1-b16d-90b627a5ce1b
  packageDependencies: []
  isSingleton: 0
  usageInstructions: 'This block can be drag and drop over a GameObject with MeshRenderer
    to use that GameObject as a Passthrough window.


    If the block imported in
    an empty space, a Quad will be spawned to show Passthrough feed.'
  featureDocumentationName: 
  featureDocumentationUrl: 
