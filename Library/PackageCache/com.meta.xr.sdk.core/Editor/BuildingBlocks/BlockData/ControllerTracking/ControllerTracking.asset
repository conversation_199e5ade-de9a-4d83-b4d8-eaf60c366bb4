%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5ba3a7314636e041b3458809c08deef, type: 3}
  m_Name: ControllerTracking
  m_EditorClassIdentifier: 
  id: 5817f7c0-f2a5-45f9-a5ca-64264e0166e8
  version: 1
  blockName: Controller Tracking
  description: Track controller movement
  tags:
    array:
    - name: Core
    - name: Tracking
  thumbnail: {fileID: 2800000, guid: 7fb9c19c262a3bf4884528a7804f8ff9, type: 3}
  order: -5
  prefab: {fileID: 112276, guid: d9809c5e8418bb047bf2c8ba1d1a2cec, type: 3}
  selectedGuidedSetupIndex: 0
  externalBlockDependencies: []
  dependencies:
  - e47682b9-c270-40b1-b16d-90b627a5ce1b
  guidedSetupName: 
  packageDependencies: []
  isSingleton: 1
  usageInstructions: 
  featureDocumentationName: 
  featureDocumentationUrl: 
