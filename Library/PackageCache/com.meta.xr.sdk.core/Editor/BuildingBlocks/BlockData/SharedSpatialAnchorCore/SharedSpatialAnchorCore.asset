%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3ce8e2a6d032b924baf5d6affb3116d6, type: 3}
  m_Name: SharedSpatialAnchorCore
  m_EditorClassIdentifier: 
  id: 975428dc-db04-4e43-be4a-232f192cd3eb
  version: 1
  blockName: Shared Spatial Anchor Core
  description: Core functionalities for creating, loading, erasing, and sharing a
    shared spatial anchor.
  tags:
    array:
    - name: Multiplayer
    - name: Spatial Anchor
    - name: Advanced
  thumbnail: {fileID: 2800000, guid: 2a52470dda46ac64788ce268d26170e2, type: 3}
  order: 11
  prefab: {fileID: 4898121156137818306, guid: 7bc8dfffd9922b94ba33926c8a808719, type: 3}
  selectedGuidedSetupIndex: 0
  externalBlockDependencies: []
  dependencies:
  - e47682b9-c270-40b1-b16d-90b627a5ce1b
  - f0540b20-dfd6-420e-b20d-c270f88dc77e
  guidedSetupName: None
  packageDependencies: []
  isSingleton: 1
  usageInstructions: "Use the following public methods to create, load, erase, and
    share anchors.\r\n\r\n- void InstantiateSpatialAnchor(GameObject prefab, Vector3
    position, Quaternion rotation)\r\n- void LoadAndInstantiateAnchors(GameObject
    prefab, List<Guid> uuids)\r\n- void EraseAllAnchors()\r\n- void EraseAnchorByUuid(Guid
    uuid)\r\n- void ShareSpatialAnchors(List<OVRSpatialAnchor> anchors, List<OVRSpaceUser>
    users)\r\n\r\n\r\nAnd, the following event shall be fired on completion of the
    jobs.\r\r\n\r\n- OnAnchorCreateCompleted(OVRSpatialAnchor, OVRSpatialAnchor.OperationResult)\r\n-
    OnAnchorsLoadCompleted(List<OVRSpatialAnchor>)\r\n- OnAnchorsEraseAllCompleted(OVRSpatialAnchor.OperationResult)\r\n-
    OnAnchorEraseCompleted(OVRSpatialAnchor, OVRSpatialAnchor.OperationResult)\r\n-
    OnSpatialAnchorsShareCompleted(List<OVRSpatialAnchor>, OVRSpatialAnchor.OperationResult)"
  featureDocumentationName: 
  featureDocumentationUrl: 
