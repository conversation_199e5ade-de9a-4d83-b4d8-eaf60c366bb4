/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * All rights reserved.
 *
 * Licensed under the Oculus SDK License Agreement (the "License");
 * you may not use the Oculus SDK except in compliance with the License,
 * which is provided at the time of installation or download, or which
 * otherwise accompanies this software in either electronic or hard copy form.
 *
 * You may obtain a copy of the License at
 *
 * https://developer.oculus.com/licenses/oculussdk/
 *
 * Unless required by applicable law or agreed to in writing, the Oculus SDK
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

using Meta.XR.Editor.Id;
using UnityEditor;
using Meta.XR.Guides.Editor;

namespace Meta.XR.BuildingBlocks.Editor
{
    [CustomEditor(typeof(SharedSpatialAnchorCoreBuildingBlock))]
    public class SharedSpatialAnchorBuildingBlockEditor : BuildingBlockEditor
    {
#if USING_META_XR_PLATFORM_SDK
        private MetaAccountSetupGuide _metaAccountSetupGuide;
        private MetaAccountSetupGuide MetaAccountSetupGuide => _metaAccountSetupGuide ??= new MetaAccountSetupGuide();
#endif // USING_META_XR_PLATFORM_SDK

        protected override void ShowAdditionals()
        {
            base.ShowAdditionals();

            var block = target as BuildingBlock;
            if (block != null && !block.BlockId.Equals(BlockDataIds.SharedSpatialAnchorCore))
                return;

            DrawAppIdRequirementInfo("Shared Spatial Anchor", () =>
            {
#if USING_META_XR_PLATFORM_SDK
                MetaAccountSetupGuide.ShowWindow(Origins.Component, true);
#endif // USING_META_XR_PLATFORM_SDK
            });
        }
    }
}
