%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 35c4ad19f06c67e44a2d7ba8d05f31b7, type: 3}
  m_Name: SurfaceProjectedPassthrough
  m_EditorClassIdentifier: 
  id: 19c06269-24b5-4657-a433-21a6f80dbabf
  version: 1
  blockName: Surface Projected Passthrough
  description: Displays a live feed of your room on top of a surface
  tags:
    array:
    - name: Passthrough
  thumbnail: {fileID: 2800000, guid: aed137c3d795a864d81b421c25ec504e, type: 3}
  order: -7
  prefab: {fileID: 5018295258445772226, guid: d86075f5e938af942905e75439b41cb7, type: 3}
  dependencies:
  - e47682b9-c270-40b1-b16d-90b627a5ce1b
  - 40e08c51-14aa-4822-927d-5fe79943b5b4
  packageDependencies: []
  isSingleton: 0
  usageInstructions: 
