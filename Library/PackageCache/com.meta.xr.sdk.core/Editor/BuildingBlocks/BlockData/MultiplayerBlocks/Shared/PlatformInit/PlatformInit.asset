%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 35c4ad19f06c67e44a2d7ba8d05f31b7, type: 3}
  m_Name: PlatformInit
  m_EditorClassIdentifier:
  id: *************-4ae3-a056-d9ca1ccaa35a
  version: 1
  blockName: Platform Init
  description: OVRPlatform helper that is used for entitlement
  tags:
    array:
    - name: Core
    - name: Multiplayer
    - name: Hidden
  thumbnail: {fileID: 0}
  order: 0
  prefab: {fileID: 7453874736456596395, guid: 75b04b4ea3b49b14f889a599a73f2534, type: 3}
  externalBlockDependencies: []
  dependencies: []
  packageDependencies:
  - com.meta.xr.sdk.platform
  isSingleton: 1
  usageInstructions:
