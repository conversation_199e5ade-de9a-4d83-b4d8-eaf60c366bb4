%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &3609187071157327920
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6932903175601683518}
  - component: {fileID: 6461651326605645664}
  m_Layer: 0
  m_Name: FriendsMatchmaking
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6932903175601683518
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3609187071157327920}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6461651326605645664
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3609187071157327920}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0043f787439adaa4eb75049cff929530, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  destinationApi: destinationApi
  inviteMessage: Let's play together!
  maxRetries: 3
  onMatchRequestFound:
    m_PersistentCalls:
      m_Calls: []
  onInvitationsSent:
    m_PersistentCalls:
      m_Calls: []
  onLeaveIntentReceived:
    m_PersistentCalls:
      m_Calls: []
