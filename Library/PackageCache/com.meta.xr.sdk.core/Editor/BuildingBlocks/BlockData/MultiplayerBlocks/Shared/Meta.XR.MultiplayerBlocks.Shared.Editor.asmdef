{"name": "Meta.XR.MultiplayerBlocks.Shared.Editor", "rootNamespace": "", "references": ["Meta.XR.BuildingBlocks.Editor", "Meta.XR.MultiplayerBlocks.Shared", "Oculus.VR", "Oculus.VR.Editor", "Oculus.Interaction", "Meta.XR.BuildingBlocks", "Oculus.Interaction.OVR.Editor", "meta.xr.mrutilitykit"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.meta.xr.sdk.interaction.ovr", "expression": "", "define": "META_INTERACTION_SDK_DEFINED"}, {"name": "com.meta.xr.sdk.avatars", "expression": "28.0.0", "define": "META_AVATAR_SDK_28_OR_NEWER"}, {"name": "com.meta.xr.mrutilitykit", "expression": "", "define": "META_MR_UTILITY_KIT_DEFINED"}, {"define": "OVR_UNITY_PACKAGE_MANAGER", "name": "com.meta.xr.sdk.core", "expression": ""}], "noEngineReferences": false}