%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 81d5758cfb47823489080981979f33be, type: 3}
  m_Name: IAutoMatchmaking
  m_EditorClassIdentifier: 
  id: d45b4309-f823-42e4-b871-0f58e8e647d8
  version: 1
  blockName: Auto Matchmaking
  description: Automatically join all players running the app into the same room
  tags:
    array:
    - name: Multiplayer
    - name: Prototyping
  thumbnail: {fileID: 2800000, guid: 7cb8b47d1a3124681980f3ca0ae6daec, type: 3}
  order: 0
  prefab: {fileID: 0}
  externalBlockDependencies: []
  dependencies:
  - 1d8db162-54f6-43df-b4ef-b499df1f6769
  packageDependencies: []
  isSingleton: 1
  usageInstructions: 'For quick prototyping only, you can freely remove this block
    and use your own matchmaking mechanism later.

    Session name in the block
    would be deciding which room player will join on the same server, would be wise
    to setup it differently for testing if multiple developers sharing the same cloud
    server.

    Maximum number of players are limited by specific networking solution''s
    service, please check the selected Network Implementation''s official website
    for more information.'
  featureDocumentationName: Multiplayer Building Blocks Setup Guide
  featureDocumentationUrl: https://developers.meta.com/horizon/documentation/unity/bb-multiplayer-blocks
