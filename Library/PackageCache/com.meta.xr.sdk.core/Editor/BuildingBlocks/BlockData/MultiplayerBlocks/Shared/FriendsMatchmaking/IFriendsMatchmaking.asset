%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 81d5758cfb47823489080981979f33be, type: 3}
  m_Name: IFriendsMatchmaking
  m_EditorClassIdentifier: 
  id: a29fd151-3fe0-46e4-8ae6-c899e8b9be35
  version: 1
  blockName: Friends Matchmaking
  description: "Friends Matchmaking allows a player to invite the friends from their
    Meta account to play together or to join existing invites instead. \nThis Building
    Block uses Custom Matchmaking to create and join game rooms in a seamless way. "
  tags:
    array:
    - name: Multiplayer
  thumbnail: {fileID: 2800000, guid: 7fa8dd9e5f4c1f848880d8210f584895, type: 3}
  order: 0
  prefab: {fileID: 0}
  externalBlockDependencies: []
  dependencies:
  - *************-4ae3-a056-d9ca1ccaa35a
  - c5bea02f-8079-4142-87f9-56da5993f861
  packageDependencies:
  - com.meta.xr.sdk.platform
  isSingleton: 1
  usageInstructions: '1. Create your app in https://developer.oculus.com/

    2.
    Under Requirements > Data Use Checkup make sure the following permissions are
    granted: User ID, User Profile, Deep Linking, Friends, Invites and Parties

    3.
    Create a new destination under Engagement > Destinations

    4. Paste the destination''s
    API name in the Friends Matchmaking component of your Friends Matchmaking Building
    Block

    5. Build your app and upload it to one of the release channels under
    Distribution > Release Channels

    6. Add your users to that release channel


    The
    full instructions can be found in https://developers.meta.com/horizon/documentation/unity/ps-group-presence-overview.'
  featureDocumentationName: Group Presence Overview
  featureDocumentationUrl: https://developers.meta.com/horizon/documentation/unity/ps-group-presence-overview
