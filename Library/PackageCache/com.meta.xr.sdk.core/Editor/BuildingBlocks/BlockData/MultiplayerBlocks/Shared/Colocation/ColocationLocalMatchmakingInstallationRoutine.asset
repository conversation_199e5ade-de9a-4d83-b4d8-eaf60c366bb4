%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4dbbd590b0159439d8992347275f0b14, type: 3}
  m_Name: ColocationLocalMatchmakingInstallationRoutine
  m_EditorClassIdentifier: 
  id: 4ec10206-4327-44cd-ae75-ff2b80d3422e
  targetBlockDataId: f308c8f0-7a4b-4cd5-88be-c15b6399f823
  prefab: {fileID: 3864631397855534807, guid: efd22e462908f4273926801f340e39f1, type: 3}
  packageDependencies: []
  selectedGuidedSetupIndex: 3
  guidedSetupName: Meta.XR.Guides.Editor.MetaColocationSessionSetupGuide, Meta.XR.Guides.Editor,
    Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
  implementation: 0
  installMatchmaking: 0
  useColocationSession: 1
  shareSpaceToGuests: 1
