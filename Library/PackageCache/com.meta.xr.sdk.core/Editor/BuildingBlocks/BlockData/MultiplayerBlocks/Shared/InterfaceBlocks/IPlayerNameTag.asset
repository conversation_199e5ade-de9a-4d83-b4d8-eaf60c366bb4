%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &********
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: ********, guid: 81d5758cfb47823489080981979f33be, type: 3}
  m_Name: IPlayerNameTag
  m_EditorClassIdentifier: 
  id: 97a7e1ae-ac65-4ee6-9167-10b3b94782f6
  version: 1
  blockName: Player Name Tag
  description: Show a nametag with the player's Horizon account name over the position
    they're in during a game session
  tags:
    array:
    - name: Multiplayer
    - name: UI
    - name: Prototyping
  thumbnail: {fileID: 2800000, guid: db24c53695d934b4c8d3b67e240cc7cd, type: 3}
  order: 0
  prefab: {fileID: 0}
  selectedGuidedSetupIndex: 0
  externalBlockDependencies: []
  dependencies:
  - 1d8db162-54f6-43df-b4ef-b499df1f6769
  - *************-4ae3-a056-d9ca1ccaa35a
  - e47682b9-c270-40b1-b16d-90b627a5ce1b
  guidedSetupName: 
  packageDependencies:
  - com.meta.xr.sdk.platform
  isSingleton: 1
  usageInstructions: 'If any player is not entitled or anonymous, you can setup randomized
    names in the block''s PlayerNameTagSpawner component.

    To show valid Oculus
    name for the player, this app must setup valid app id in the project. See more
    guidance in menu of Oculus > Tools > Meta Account Guided Setup.'
  featureDocumentationName: Multiplayer Building Blocks Setup Guide
  featureDocumentationUrl: https://developers.meta.com/horizon/documentation/unity/bb-multiplayer-blocks
