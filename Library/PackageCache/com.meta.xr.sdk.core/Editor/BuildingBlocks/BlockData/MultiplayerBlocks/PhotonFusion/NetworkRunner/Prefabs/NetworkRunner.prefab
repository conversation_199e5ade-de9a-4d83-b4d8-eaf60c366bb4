%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &4212332791640743721
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4383316764039406391}
  - component: {fileID: 2823099757792146460}
  - component: {fileID: 8866195336675230116}
  - component: {fileID: -4143375670476342593}
  - component: {fileID: -7366458935369515707}
  m_Layer: 0
  m_Name: NetworkRunner
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4383316764039406391
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4212332791640743721}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2823099757792146460
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4212332791640743721}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1199893898, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &8866195336675230116
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4212332791640743721}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -232609262, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  OnInput:
    m_PersistentCalls:
      m_Calls: []
  OnInputMissing:
    m_PersistentCalls:
      m_Calls: []
  OnConnectedToServer:
    m_PersistentCalls:
      m_Calls: []
  OnDisconnectedFromServer:
    m_PersistentCalls:
      m_Calls: []
  OnConnectRequest:
    m_PersistentCalls:
      m_Calls: []
  OnConnectFailed:
    m_PersistentCalls:
      m_Calls: []
  PlayerJoined:
    m_PersistentCalls:
      m_Calls: []
  PlayerLeft:
    m_PersistentCalls:
      m_Calls: []
  OnSimulationMessage:
    m_PersistentCalls:
      m_Calls: []
  OnShutdown:
    m_PersistentCalls:
      m_Calls: []
  OnSessionListUpdate:
    m_PersistentCalls:
      m_Calls: []
  OnCustomAuthenticationResponse:
    m_PersistentCalls:
      m_Calls: []
  OnHostMigration:
    m_PersistentCalls:
      m_Calls: []
  OnSceneLoadDone:
    m_PersistentCalls:
      m_Calls: []
  OnSceneLoadStart:
    m_PersistentCalls:
      m_Calls: []
  OnReliableData:
    m_PersistentCalls:
      m_Calls: []
  OnReliableProgress:
    m_PersistentCalls:
      m_Calls: []
  OnObjectEnterAOI:
    m_PersistentCalls:
      m_Calls: []
  OnObjectExitAOI:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &-4143375670476342593
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4212332791640743721}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 83052ea2d6bb311488211916605e716b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &-7366458935369515707
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4212332791640743721}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a9f4b1b5ef87cf0419535d7ce28337d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
