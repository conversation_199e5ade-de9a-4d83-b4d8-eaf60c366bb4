%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7213903bd88645d0abe71b35ad3b994f, type: 3}
  m_Name: AutoMatchmakingFusionInstallationRoutine
  m_EditorClassIdentifier: 
  id: 6436b892-b7d4-4073-8113-00ab41af3be1
  routineName: AutoMatchmakingFusionInstallationRoutine
  description: Installation rountine for Fusion Auto Matchmaking
  targetBlockDataId: d45b4309-f823-42e4-b871-0f58e8e647d8
  prefab: {fileID: 596904243652561245, guid: ba1bb1b05204b024787843fb15faa209, type: 3}
  packageDependencies:
  - com.exitgames.photonfusion
  implementation: 1
