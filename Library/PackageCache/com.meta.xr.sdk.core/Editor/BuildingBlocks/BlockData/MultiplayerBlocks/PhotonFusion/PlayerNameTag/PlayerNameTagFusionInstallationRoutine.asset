%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 940cc35ab3492024a89571d632a07b0a, type: 3}
  m_Name: PlayerNameTagFusionInstallationRoutine
  m_EditorClassIdentifier: 
  id: d2603dd8-0302-481a-a5e8-29f8e329a3e0
  routineName: Player Name Tag Fusion Installation Routine
  description: installation routine for Fusion Player Name Tag
  targetBlockDataId: 97a7e1ae-ac65-4ee6-9167-10b3b94782f6
  prefab: {fileID: 4574500822177858770, guid: b66b1c283176bfd40b83ea131a0b077e, type: 3}
  packageDependencies:
  - com.exitgames.photonfusion
  implementation: 1
