%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 940cc35ab3492024a89571d632a07b0a, type: 3}
  m_Name: NetworkManagerFusionInstallationRoutine
  m_EditorClassIdentifier: 
  id: 676820bd-24bb-4da2-9785-74ad32626885
  routineName: NetworkManagerFusionInstallationRoutine
  description: Installation routine for network manager setup for Fusion
  targetBlockDataId: 1d8db162-54f6-43df-b4ef-b499df1f6769
  prefab: {fileID: 4212332791640743721, guid: 6cd7bdcd06076d14699bc33910aa0048, type: 3}
  packageDependencies:
  - com.exitgames.photonfusion
  implementation: 1
