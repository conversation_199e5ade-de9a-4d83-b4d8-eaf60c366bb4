%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 37f7c39848f351b4e8725b90727f6c07, type: 3}
  m_Name: PlayerVoiceChatInstallationRoutine
  m_EditorClassIdentifier:
  id: ca29a203-ad6d-4c65-ae37-ffab1dc8a165
  routineName: Player Voice Chat Installation Routine
  description: Installation Routine for PlayerVoiceChat (only available in Fusion)
  targetBlockDataId: 5523ee0f-69d0-4e9f-b4b3-1ca61874f631
  prefab: {fileID: 8039809577568287267, guid: ee7aa31c863517340be0ce3f66d2e03d, type: 3}
  packageDependencies:
  - com.exitgames.photonfusion
  implementation: 1
