//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef LENSFLAREDATASRP_CS_HLSL
#define LENSFLAREDATASRP_CS_HLSL
//
// UnityEngine.Rendering.SRPLensFlareColorType:  static fields
//
#define SRPLENSFLARECOLORTYPE_CONSTANT (0)
#define SRPLENSFLARECOLORTYPE_RADIAL_GRADIENT (1)
#define SRPLENSFLARECOLORTYPE_ANGULAR_GRADIENT (2)

//
// UnityEngine.Rendering.SRPLensFlareType:  static fields
//
#define SRPLENSFLARETYPE_IMAGE (0)
#define SRPLENSFLARETYPE_CIRCLE (1)
#define SRPL<PERSON>SFLARETYPE_POLYGON (2)
#define SRPLENSFLARETYPE_RING (3)
#define SRPLENSFLARETYPE_LENS_FLARE_DATA_SRP (4)


#endif
