%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &7781047562868582520
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 26970999318962292}
  - component: {fileID: 1460897311393639429}
  m_Layer: 0
  m_Name: ComplexGrabStoneResize
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &26970999318962292
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7781047562868582520}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 8648058279705214320}
  - {fileID: 1697523270304702631}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1460897311393639429
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7781047562868582520}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ced30fb0d3378c442934280394576509, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactableView: {fileID: 0}
  _trackedTransform: {fileID: 0}
  _stepSize: 0.6
  _maxEventFreq: 25
  _whenScalingStarted:
    m_PersistentCalls:
      m_Calls: []
  _whenScalingEnded:
    m_PersistentCalls:
      m_Calls: []
  _whenScaledUp:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6806396742635279434}
        m_TargetAssemblyTypeName: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
        m_MethodName: PlayAudio
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _whenScaledDown:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3602860472118413725}
        m_TargetAssemblyTypeName: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
        m_MethodName: PlayAudio
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!1001 &4081338660335015891
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 26970999318962292}
    m_Modifications:
    - target: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4674537905600659506, guid: e35c14d64fd3af744baab6342ffa5d15,
        type: 3}
      propertyPath: m_Name
      value: ObjectResizeUpAudio
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: e35c14d64fd3af744baab6342ffa5d15, type: 3}
--- !u!4 &8648058279705214320 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4658904325700070051, guid: e35c14d64fd3af744baab6342ffa5d15,
    type: 3}
  m_PrefabInstance: {fileID: 4081338660335015891}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &6806396742635279434 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7410389675181900697, guid: e35c14d64fd3af744baab6342ffa5d15,
    type: 3}
  m_PrefabInstance: {fileID: 4081338660335015891}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 925ef87c5bafc37469a2f7ec825dee4b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &6280569466864175620
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 26970999318962292}
    m_Modifications:
    - target: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4674537905600659506, guid: 5e5f065a0da50174f930fd138f3751ab,
        type: 3}
      propertyPath: m_Name
      value: ObjectResizeDownAudio
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 5e5f065a0da50174f930fd138f3751ab, type: 3}
--- !u!4 &1697523270304702631 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4658904325700070051, guid: 5e5f065a0da50174f930fd138f3751ab,
    type: 3}
  m_PrefabInstance: {fileID: 6280569466864175620}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &3602860472118413725 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7410389675181900697, guid: 5e5f065a0da50174f930fd138f3751ab,
    type: 3}
  m_PrefabInstance: {fileID: 6280569466864175620}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 925ef87c5bafc37469a2f7ec825dee4b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
