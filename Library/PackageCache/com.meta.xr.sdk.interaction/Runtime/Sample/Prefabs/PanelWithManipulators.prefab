%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &14475805497848304
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 826618371636800330}
  - component: {fileID: 3071362075783773732}
  m_Layer: 0
  m_Name: scale_affordance_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &826618371636800330
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 14475805497848304}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4604206103672811242}
  m_Father: {fileID: 5949587551380793747}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &3071362075783773732
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 14475805497848304}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 0f3ba09b00c0ed34e8f15473873948aa, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &132684418725051814
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 725278033861247257}
  - component: {fileID: 7171764535097105323}
  - component: {fileID: 1624465691330993432}
  m_Layer: 0
  m_Name: PanelVisuals
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &725278033861247257
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 132684418725051814}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8077691079682373167}
  - {fileID: 4604551928782560472}
  - {fileID: 7677856436967682397}
  - {fileID: 1002443365531634498}
  - {fileID: 2343935411283750911}
  - {fileID: 1312668880398728018}
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7171764535097105323
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 132684418725051814}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8b801f8db2a1e6743a3c5aa89ca305b7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _grabInteractable: {fileID: 1805075665103634440}
  _handGrabInteractable: {fileID: 5535750581351400760}
  _rayInteractable: {fileID: 0}
  _stateSignaler: {fileID: 7928194057166543090}
  _panelHoverState: {fileID: 4094494049803084778}
  _grabbale: {fileID: 5949587553195814334}
  _boneTransform: {fileID: 3311984797145180129}
  _cornerArcRadius: 0.04
  _railOpacityAnimator: {fileID: 858295818281038814}
  _railOpacityTransform: {fileID: 1346352817505665296}
  _affordances:
  - _geometry: {fileID: 7677856436967682397}
    _opacityTransform: {fileID: 1081413021998596174}
    _animators:
    - {fileID: 2334889815892895401}
  - _geometry: {fileID: 1002443365531634498}
    _opacityTransform: {fileID: 679243932408937771}
    _animators:
    - {fileID: 7876460100867697054}
  - _geometry: {fileID: 2343935411283750911}
    _opacityTransform: {fileID: 2887100817527905345}
    _animators:
    - {fileID: 6207403032683078866}
  - _geometry: {fileID: 1312668880398728018}
    _opacityTransform: {fileID: 3489072346201784984}
    _animators:
    - {fileID: 523573523263302231}
  _railRenderer: {fileID: 4799099527865053409}
--- !u!114 &1624465691330993432
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 132684418725051814}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &312295015806180918
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5226680308581292598}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5226680308581292598
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 312295015806180918}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 879492422290928399}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &374903995446718482
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9196995253960765569}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9196995253960765569
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 374903995446718482}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5422222340411602209}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &391650044348350858
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5283934947824210007}
  - component: {fileID: 1412384455173633477}
  - component: {fileID: 2567527811211811835}
  - component: {fileID: 4544724887157016398}
  - component: {fileID: 1897993555858124583}
  - component: {fileID: 1805075665103634440}
  - component: {fileID: 5535750581351400760}
  - component: {fileID: 7297805744651613776}
  - component: {fileID: 7755183058943330620}
  - component: {fileID: 6028288142767427067}
  m_Layer: 0
  m_Name: PanelInteractable
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5283934947824210007
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391650044348350858}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.2, y: 0.2, z: 0.01}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5949587552056431493}
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &1412384455173633477
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391650044348350858}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2567527811211811835
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391650044348350858}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!54 &4544724887157016398
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391650044348350858}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &1897993555858124583
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391650044348350858}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1.1, y: 1.1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!114 &1805075665103634440
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391650044348350858}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a1bc992571301d4a9602ac95ef4c71a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814334}
  _rigidbody: {fileID: 4544724887157016398}
  _grabSource: {fileID: 0}
  _useClosestPointAsGrabSource: 0
  _releaseDistance: 0
  _resetGrabOnGrabsUpdated: 1
  _physicsGrabbable: {fileID: 0}
--- !u!114 &5535750581351400760
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391650044348350858}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9a7676b01585ce43908639a27765dfc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814334}
  _rigidbody: {fileID: 4544724887157016398}
  _physicsGrabbable: {fileID: 0}
  _resetGrabOnGrabsUpdated: 1
  _scoringModifier:
    _positionRotationWeight: 0.8
  _slippiness: 0
  _supportedGrabTypes: 3
  _pinchGrabRules:
    _thumbRequirement: 0
    _indexRequirement: 2
    _middleRequirement: 0
    _ringRequirement: 0
    _pinkyRequirement: 0
    _unselectMode: 1
  _palmGrabRules:
    _thumbRequirement: 1
    _indexRequirement: 2
    _middleRequirement: 2
    _ringRequirement: 2
    _pinkyRequirement: 1
    _unselectMode: 1
  _movementProvider: {fileID: 0}
  _handAligment: 0
  _handGrabPoses: []
--- !u!114 &7297805744651613776
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391650044348350858}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1464721f2283eb14e94a33e812b47be4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactableView: {fileID: 6028288142767427067}
  _whenHover:
    m_PersistentCalls:
      m_Calls: []
  _whenUnhover:
    m_PersistentCalls:
      m_Calls: []
  _whenSelect:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselect:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewAdded:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls: []
  _whenSelectingInteractorViewAdded:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6300239997087639387}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _whenSelectingInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3029866607647373476}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &7755183058943330620
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391650044348350858}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 947e35f79c504ff4a977e49fcf45dac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _parentAnchor: {fileID: 6028011848650400756}
  _localAnchor: {fileID: 5949587552056431493}
  _scalingMode: 0
--- !u!114 &6028288142767427067
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 391650044348350858}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4bff16bb2b8cae9409e88ec4d3527c9b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactables:
  - {fileID: 5535750581351400760}
  - {fileID: 1805075665103634440}
  _data: {fileID: 0}
--- !u!1 &973583354103373161
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8580337645494836730}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8580337645494836730
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 973583354103373161}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4868016993338579034}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1228836476364792114
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1893311671661001608}
  - component: {fileID: 6941687788410982366}
  m_Layer: 0
  m_Name: scale_affordance_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1893311671661001608
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228836476364792114}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3376491245619068456}
  m_Father: {fileID: 5949587552490898145}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &6941687788410982366
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228836476364792114}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 0f3ba09b00c0ed34e8f15473873948aa, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &1231027095121368713
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6923734220639632159}
  - component: {fileID: 718365567990310936}
  - component: {fileID: 7491817131827818480}
  m_Layer: 0
  m_Name: sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6923734220639632159
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1231027095121368713}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1671131856520450754}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &718365567990310936
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1231027095121368713}
  m_Mesh: {fileID: -8908299743826163017, guid: a09465b609758fe4b8ac049ea245d647, type: 3}
--- !u!23 &7491817131827818480
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1231027095121368713}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1311451919492279641
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1835470002449758179}
  - component: {fileID: 965877444300871835}
  - component: {fileID: 2593634694899267775}
  m_Layer: 0
  m_Name: capsule_affordance_geometry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1835470002449758179
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1311451919492279641}
  m_LocalRotation: {x: 0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.2, y: 0.20000003, z: 0.20000003}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5504941981115962105}
  m_Father: {fileID: 5949587552371862402}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &965877444300871835
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1311451919492279641}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: ce346326520fc08479c1233475adea43, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &2593634694899267775
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1311451919492279641}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 2372936341594700615}
  _opacityTransform: {fileID: 5226680308581292598}
--- !u!1 &1369537280877555058
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8749632964116720353}
  - component: {fileID: 1578465789410345823}
  m_Layer: 0
  m_Name: Icosphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8749632964116720353
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1369537280877555058}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3202461191181160443}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1578465789410345823
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1369537280877555058}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 289301614986012343, guid: dffb75200a9c0da4ea0ddfa9d7060cb3, type: 3}
  m_Bones: []
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 0}
  m_AABB:
    m_Center: {x: -4.656613e-10, y: 0, z: 0}
    m_Extent: {x: 0.01, y: 0.009999999, z: 0.024691815}
  m_DirtyAABB: 0
--- !u!1 &1412669008539412223
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1788243882253859909}
  - component: {fileID: 6821412675901220674}
  - component: {fileID: 7704975733818560241}
  m_Layer: 0
  m_Name: scale_affordance_geometry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1788243882253859909
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1412669008539412223}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8067662517600310680}
  m_Father: {fileID: 5949587552490898145}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &6821412675901220674
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1412669008539412223}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 590f59dcfaeb01c4eb65216dd8e7524a, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &7704975733818560241
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1412669008539412223}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 7500837792402129271}
  _opacityTransform: {fileID: 3376491245619068456}
--- !u!1 &1440379496208099000
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2182034318370113452}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2182034318370113452
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1440379496208099000}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 323138011967445777}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1451250809845843622
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7286991210466241328}
  - component: {fileID: 922879342489647159}
  - component: {fileID: 7119869464681873375}
  m_Layer: 0
  m_Name: sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7286991210466241328
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1451250809845843622}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1299167421830431469}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &922879342489647159
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1451250809845843622}
  m_Mesh: {fileID: -8908299743826163017, guid: a09465b609758fe4b8ac049ea245d647, type: 3}
--- !u!23 &7119869464681873375
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1451250809845843622}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1468461656301865765
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4604551928782560472}
  - component: {fileID: 858295818281038814}
  m_Layer: 0
  m_Name: skinned_rounded_rectangle_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4604551928782560472
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1468461656301865765}
  m_LocalRotation: {x: -0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: -0.057, y: 0.057, z: 0.057}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1346352817505665296}
  m_Father: {fileID: 725278033861247257}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &858295818281038814
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1468461656301865765}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 7b3067858eb934c4c828bad24361601e, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &1822903759207323735
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1299167421830431469}
  - component: {fileID: 2696750237853466750}
  - component: {fileID: 4312534198941788385}
  m_Layer: 0
  m_Name: scale_affordance_geometry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1299167421830431469
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1822903759207323735}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7286991210466241328}
  m_Father: {fileID: 5949587551380793747}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &2696750237853466750
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1822903759207323735}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 590f59dcfaeb01c4eb65216dd8e7524a, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &4312534198941788385
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1822903759207323735}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 7119869464681873375}
  _opacityTransform: {fileID: 4604206103672811242}
--- !u!1 &2052195835660060792
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1671131856520450754}
  - component: {fileID: 3900105464920794316}
  - component: {fileID: 248255309023296332}
  m_Layer: 0
  m_Name: scale_affordance_geometry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1671131856520450754
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2052195835660060792}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6923734220639632159}
  m_Father: {fileID: 5949587551690473437}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &3900105464920794316
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2052195835660060792}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 590f59dcfaeb01c4eb65216dd8e7524a, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &248255309023296332
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2052195835660060792}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 7491817131827818480}
  _opacityTransform: {fileID: 8580337645494836730}
--- !u!1 &2230811619492575246
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8067662517600310680}
  - component: {fileID: 439160793768068767}
  - component: {fileID: 7500837792402129271}
  m_Layer: 0
  m_Name: sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8067662517600310680
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2230811619492575246}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1788243882253859909}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &439160793768068767
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2230811619492575246}
  m_Mesh: {fileID: -8908299743826163017, guid: a09465b609758fe4b8ac049ea245d647, type: 3}
--- !u!23 &7500837792402129271
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2230811619492575246}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2232150960401697871
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2887100817527905345}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2887100817527905345
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2232150960401697871}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2343935411283750911}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2313382032422193006
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3501029921465458116}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3501029921465458116
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2313382032422193006}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4084008261375093396}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2373831576249356382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2725540670565825194}
  - component: {fileID: 1004414636399014922}
  m_Layer: 0
  m_Name: AnchorCenter
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2725540670565825194
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2373831576249356382}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1004414636399014922
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2373831576249356382}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &2505729200196964594
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6028011848650400756}
  - component: {fileID: 8486990151350068572}
  m_Layer: 0
  m_Name: AnchorTopLeft
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6028011848650400756
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2505729200196964594}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.14, y: 0.14, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8486990151350068572
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2505729200196964594}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &2671113278716166229
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2343935411283750911}
  - component: {fileID: 6207403032683078866}
  m_Layer: 0
  m_Name: PanelBorderHandleAffordance (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2343935411283750911
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2671113278716166229}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2887100817527905345}
  m_Father: {fileID: 725278033861247257}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &6207403032683078866
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2671113278716166229}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 7a6ef4c66f11662479ff742cd09d15ac, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &2740468569277502826
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5504941981115962105}
  - component: {fileID: 2372936341594700615}
  m_Layer: 0
  m_Name: Icosphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5504941981115962105
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2740468569277502826}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1835470002449758179}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &2372936341594700615
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2740468569277502826}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 289301614986012343, guid: dffb75200a9c0da4ea0ddfa9d7060cb3, type: 3}
  m_Bones: []
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 0}
  m_AABB:
    m_Center: {x: -4.656613e-10, y: 0, z: 0}
    m_Extent: {x: 0.01, y: 0.009999999, z: 0.024691815}
  m_DirtyAABB: 0
--- !u!1 &2773053278219474201
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3256117851763855267}
  - component: {fileID: 22277217160299427}
  - component: {fileID: 2230601843046102418}
  m_Layer: 0
  m_Name: scale_affordance_geometry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3256117851763855267
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2773053278219474201}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6491670487024543358}
  m_Father: {fileID: 5949587551324850980}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &22277217160299427
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2773053278219474201}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 590f59dcfaeb01c4eb65216dd8e7524a, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &2230601843046102418
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2773053278219474201}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 6771522138487960209}
  _opacityTransform: {fileID: 9196995253960765569}
--- !u!1 &2826780421237478721
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3202461191181160443}
  - component: {fileID: 2965714507873194092}
  - component: {fileID: 2904694035383671213}
  m_Layer: 0
  m_Name: capsule_affordance_geometry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3202461191181160443
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2826780421237478721}
  m_LocalRotation: {x: 0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.2, y: 0.20000003, z: 0.20000003}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8749632964116720353}
  m_Father: {fileID: 5949587551981348425}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &2965714507873194092
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2826780421237478721}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: ce346326520fc08479c1233475adea43, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &2904694035383671213
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2826780421237478721}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 1578465789410345823}
  _opacityTransform: {fileID: 2640935537801876704}
--- !u!1 &2975595213605325008
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1346352817505665296}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1346352817505665296
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2975595213605325008}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.8, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4604551928782560472}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3104243345151216616
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6491670487024543358}
  - component: {fileID: 3745066155038209401}
  - component: {fileID: 6771522138487960209}
  m_Layer: 0
  m_Name: sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6491670487024543358
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3104243345151216616}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3256117851763855267}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3745066155038209401
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3104243345151216616}
  m_Mesh: {fileID: -8908299743826163017, guid: a09465b609758fe4b8ac049ea245d647, type: 3}
--- !u!23 &6771522138487960209
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3104243345151216616}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3143799535885074031
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3082091016640999264}
  - component: {fileID: 1685909497184902532}
  m_Layer: 0
  m_Name: arc_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3082091016640999264
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3143799535885074031}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3308621696637773859}
  m_Father: {fileID: 5949587551981348425}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!95 &1685909497184902532
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3143799535885074031}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: c6bda3b0ac0f27748883fdebf6eaef6a, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &3556841638298021504
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5462259306938461715}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5462259306938461715
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3556841638298021504}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9173841164451720115}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3655034415722031164
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1081413021998596174}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1081413021998596174
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3655034415722031164}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7677856436967682397}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4025961602992438189
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4857236710543586390}
  - component: {fileID: 9100198400254058189}
  m_Layer: 0
  m_Name: AnchorBottomRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4857236710543586390
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4025961602992438189}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.14, y: -0.14, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9100198400254058189
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4025961602992438189}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &4127949951039508268
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 679243932408937771}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &679243932408937771
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4127949951039508268}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1002443365531634498}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4644419377634733979
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5422222340411602209}
  - component: {fileID: 2058264996959722643}
  m_Layer: 0
  m_Name: scale_affordance_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5422222340411602209
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4644419377634733979}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9196995253960765569}
  m_Father: {fileID: 5949587551324850980}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &2058264996959722643
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4644419377634733979}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 0f3ba09b00c0ed34e8f15473873948aa, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &4645680956004001237
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9090655435056767839}
  - component: {fileID: 2938407269129440727}
  m_Layer: 0
  m_Name: AnchorTopRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9090655435056767839
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4645680956004001237}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.14, y: 0.14, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2938407269129440727
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4645680956004001237}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &4802697354821187025
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8685372042213346182}
  - component: {fileID: 50338352380188172}
  m_Layer: 0
  m_Name: capsule_affordance_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8685372042213346182
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4802697354821187025}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6803542765225942074}
  m_Father: {fileID: 5949587551528604054}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!95 &50338352380188172
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4802697354821187025}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 0edbf3bdbf7178349bb22f29b86612d6, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &4821664183758367378
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6810221666402882945}
  - component: {fileID: 6300239997087639387}
  - component: {fileID: 3029866607647373476}
  m_Layer: 0
  m_Name: Audio
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6810221666402882945
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4821664183758367378}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &6300239997087639387
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4821664183758367378}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: 19ca98216ac18cc41bbde71ff7f10147, type: 3}
  m_PlayOnAwake: 0
  m_Volume: 0.35
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!82 &3029866607647373476
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4821664183758367378}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: a9cd4298ed6249a4aa775ac1eaa4269e, type: 3}
  m_PlayOnAwake: 0
  m_Volume: 0.35
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 40
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &4988425876268055481
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3489072346201784984}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3489072346201784984
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4988425876268055481}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1312668880398728018}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5004847499088457337
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4604206103672811242}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4604206103672811242
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5004847499088457337}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 826618371636800330}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5243732604597994208
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4868016993338579034}
  - component: {fileID: 500881978398777573}
  m_Layer: 0
  m_Name: scale_affordance_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4868016993338579034
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5243732604597994208}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8580337645494836730}
  m_Father: {fileID: 5949587551690473437}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &500881978398777573
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5243732604597994208}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 0f3ba09b00c0ed34e8f15473873948aa, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &5462763490840453463
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2423710964611376836}
  - component: {fileID: 5560228998496243578}
  m_Layer: 0
  m_Name: Icosphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2423710964611376836
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5462763490840453463}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8412545885426877406}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &5560228998496243578
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5462763490840453463}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 289301614986012343, guid: dffb75200a9c0da4ea0ddfa9d7060cb3, type: 3}
  m_Bones: []
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 0}
  m_AABB:
    m_Center: {x: -4.656613e-10, y: 0, z: 0}
    m_Extent: {x: 0.01, y: 0.009999999, z: 0.024691815}
  m_DirtyAABB: 0
--- !u!1 &5521583005005758539
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4084008261375093396}
  - component: {fileID: 7475674645444320095}
  m_Layer: 0
  m_Name: arc_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4084008261375093396
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5521583005005758539}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3501029921465458116}
  m_Father: {fileID: 5949587551528604054}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!95 &7475674645444320095
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5521583005005758539}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: c6bda3b0ac0f27748883fdebf6eaef6a, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &5648157982574886837
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 411748885624581612}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &411748885624581612
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5648157982574886837}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4683584557097269467}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5937809812453763066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1002443365531634498}
  - component: {fileID: 7876460100867697054}
  m_Layer: 0
  m_Name: PanelBorderHandleAffordance (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1002443365531634498
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5937809812453763066}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 679243932408937771}
  m_Father: {fileID: 725278033861247257}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &7876460100867697054
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5937809812453763066}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 7a6ef4c66f11662479ff742cd09d15ac, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &5949587551324850981
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5949587551324850980}
  - component: {fileID: 5949587551324851001}
  - component: {fileID: 5949587551324851002}
  - component: {fileID: 5949587551324851003}
  - component: {fileID: 5949587551324851000}
  - component: {fileID: 5949587551324851005}
  - component: {fileID: 5949587551324851007}
  - component: {fileID: 4320741004257662224}
  - component: {fileID: 5949587551324851006}
  - component: {fileID: 8179291316564608077}
  - component: {fileID: 3806655799698913258}
  - component: {fileID: 7406897074608697906}
  m_Layer: 0
  m_Name: ScalerTopLeft
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5949587551324850980
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.13999999, y: 0.13999999, z: 0}
  m_LocalScale: {x: 0.02, y: 0.02, z: 0.02}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3256117851763855267}
  - {fileID: 5422222340411602209}
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 12
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5949587551324851001
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5949587551324851002
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5949587551324851003
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &5949587551324851000
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &5949587551324851005
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a1bc992571301d4a9602ac95ef4c71a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814327}
  _rigidbody: {fileID: 5949587551324851000}
  _grabSource: {fileID: 0}
  _useClosestPointAsGrabSource: 0
  _releaseDistance: 0
  _resetGrabOnGrabsUpdated: 1
  _physicsGrabbable: {fileID: 0}
--- !u!114 &5949587551324851007
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9a7676b01585ce43908639a27765dfc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814327}
  _rigidbody: {fileID: 5949587551324851000}
  _physicsGrabbable: {fileID: 0}
  _resetGrabOnGrabsUpdated: 1
  _scoringModifier:
    _positionRotationWeight: 0.8
  _slippiness: 0
  _supportedGrabTypes: 3
  _pinchGrabRules:
    _thumbRequirement: 0
    _indexRequirement: 2
    _middleRequirement: 0
    _ringRequirement: 0
    _pinkyRequirement: 0
    _unselectMode: 1
  _palmGrabRules:
    _thumbRequirement: 1
    _indexRequirement: 2
    _middleRequirement: 2
    _ringRequirement: 2
    _pinkyRequirement: 1
    _unselectMode: 1
  _movementProvider: {fileID: 0}
  _handAligment: 0
  _handGrabPoses: []
--- !u!114 &4320741004257662224
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1464721f2283eb14e94a33e812b47be4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactableView: {fileID: 7406897074608697906}
  _whenHover:
    m_PersistentCalls:
      m_Calls: []
  _whenUnhover:
    m_PersistentCalls:
      m_Calls: []
  _whenSelect:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselect:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewAdded:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls: []
  _whenSelectingInteractorViewAdded:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6300239997087639387}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _whenSelectingInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3029866607647373476}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &5949587551324851006
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &8179291316564608077
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f9c7ae05d6efb54e90162d3cf5a7982, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _grabInteractable: {fileID: 5949587551324851005}
  _handGrabInteractable: {fileID: 5949587551324851007}
  _rayInteractable: {fileID: 0}
  _stateSignaler: {fileID: 7928194057166543090}
  _animators:
  - {fileID: 22277217160299427}
  - {fileID: 2058264996959722643}
  _panelHoverState: {fileID: 4094494049803084778}
--- !u!1818360608 &3806655799698913258
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: -0.14, y: 0.14, z: 0}
  m_TranslationOffset: {x: 0, y: 0, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 6028011848650400756}
    weight: 1
--- !u!114 &7406897074608697906
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551324850981}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4bff16bb2b8cae9409e88ec4d3527c9b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactables:
  - {fileID: 5949587551324851005}
  - {fileID: 5949587551324851007}
  _data: {fileID: 0}
--- !u!1 &5949587551380793756
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5949587551380793747}
  - component: {fileID: 5949587551380793744}
  - component: {fileID: 5949587551380793745}
  - component: {fileID: 5949587551380793746}
  - component: {fileID: 5949587551380793751}
  - component: {fileID: 5949587551380793748}
  - component: {fileID: 5949587551380793750}
  - component: {fileID: 837311256895814827}
  - component: {fileID: 5949587551380793749}
  - component: {fileID: 8038702100154853265}
  - component: {fileID: 7860008448811873932}
  - component: {fileID: 1041891692495171516}
  m_Layer: 0
  m_Name: ScalerTopRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5949587551380793747
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.13999999, y: 0.13999999, z: 0}
  m_LocalScale: {x: 0.02, y: 0.02, z: 0.02}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1299167421830431469}
  - {fileID: 826618371636800330}
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 13
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5949587551380793744
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5949587551380793745
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5949587551380793746
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &5949587551380793751
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &5949587551380793748
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a1bc992571301d4a9602ac95ef4c71a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814327}
  _rigidbody: {fileID: 5949587551380793751}
  _grabSource: {fileID: 0}
  _useClosestPointAsGrabSource: 0
  _releaseDistance: 0
  _resetGrabOnGrabsUpdated: 1
  _physicsGrabbable: {fileID: 0}
--- !u!114 &5949587551380793750
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9a7676b01585ce43908639a27765dfc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814327}
  _rigidbody: {fileID: 5949587551380793751}
  _physicsGrabbable: {fileID: 0}
  _resetGrabOnGrabsUpdated: 1
  _scoringModifier:
    _positionRotationWeight: 0.8
  _slippiness: 0
  _supportedGrabTypes: 3
  _pinchGrabRules:
    _thumbRequirement: 0
    _indexRequirement: 2
    _middleRequirement: 0
    _ringRequirement: 0
    _pinkyRequirement: 0
    _unselectMode: 1
  _palmGrabRules:
    _thumbRequirement: 1
    _indexRequirement: 2
    _middleRequirement: 2
    _ringRequirement: 2
    _pinkyRequirement: 1
    _unselectMode: 1
  _movementProvider: {fileID: 0}
  _handAligment: 0
  _handGrabPoses: []
--- !u!114 &837311256895814827
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1464721f2283eb14e94a33e812b47be4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactableView: {fileID: 1041891692495171516}
  _whenHover:
    m_PersistentCalls:
      m_Calls: []
  _whenUnhover:
    m_PersistentCalls:
      m_Calls: []
  _whenSelect:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselect:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewAdded:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls: []
  _whenSelectingInteractorViewAdded:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6300239997087639387}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _whenSelectingInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3029866607647373476}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &5949587551380793749
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &8038702100154853265
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f9c7ae05d6efb54e90162d3cf5a7982, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _grabInteractable: {fileID: 5949587551380793748}
  _handGrabInteractable: {fileID: 5949587551380793750}
  _rayInteractable: {fileID: 0}
  _stateSignaler: {fileID: 7928194057166543090}
  _animators:
  - {fileID: 2696750237853466750}
  - {fileID: 3071362075783773732}
  _panelHoverState: {fileID: 4094494049803084778}
--- !u!1818360608 &7860008448811873932
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: 0.14, y: 0.14, z: 0}
  m_TranslationOffset: {x: 0, y: 0, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 9090655435056767839}
    weight: 1
--- !u!114 &1041891692495171516
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551380793756}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4bff16bb2b8cae9409e88ec4d3527c9b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactables:
  - {fileID: 5949587551380793748}
  - {fileID: 5949587551380793750}
  _data: {fileID: 0}
--- !u!1 &5949587551528604055
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5949587551528604054}
  - component: {fileID: 5949587551528604075}
  - component: {fileID: 5949587551528604052}
  - component: {fileID: 5949587551528604053}
  - component: {fileID: 5949587551528604074}
  - component: {fileID: 5949587551528604079}
  - component: {fileID: 5949587551528604073}
  - component: {fileID: 3037259046961174248}
  - component: {fileID: 5949587551528604072}
  - component: {fileID: 2422591612188876735}
  - component: {fileID: 3718378310297057081}
  - component: {fileID: 692147577422778769}
  m_Layer: 0
  m_Name: RotatorHorizontalRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5949587551528604054
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.13999999, y: 0, z: 0}
  m_LocalScale: {x: 0.02, y: 0.02, z: 0.02}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4008961446470975557}
  - {fileID: 4084008261375093396}
  - {fileID: 6773383062611747973}
  - {fileID: 8685372042213346182}
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 11
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5949587551528604075
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5949587551528604052
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5949587551528604053
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &5949587551528604074
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &5949587551528604079
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a1bc992571301d4a9602ac95ef4c71a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814321}
  _rigidbody: {fileID: 5949587551528604074}
  _grabSource: {fileID: 0}
  _useClosestPointAsGrabSource: 0
  _releaseDistance: 0
  _resetGrabOnGrabsUpdated: 1
  _physicsGrabbable: {fileID: 0}
--- !u!114 &5949587551528604073
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9a7676b01585ce43908639a27765dfc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814321}
  _rigidbody: {fileID: 5949587551528604074}
  _physicsGrabbable: {fileID: 0}
  _resetGrabOnGrabsUpdated: 1
  _scoringModifier:
    _positionRotationWeight: 0.8
  _slippiness: 0
  _supportedGrabTypes: 3
  _pinchGrabRules:
    _thumbRequirement: 0
    _indexRequirement: 2
    _middleRequirement: 0
    _ringRequirement: 0
    _pinkyRequirement: 0
    _unselectMode: 1
  _palmGrabRules:
    _thumbRequirement: 1
    _indexRequirement: 2
    _middleRequirement: 2
    _ringRequirement: 2
    _pinkyRequirement: 1
    _unselectMode: 1
  _movementProvider: {fileID: 0}
  _handAligment: 0
  _handGrabPoses: []
--- !u!114 &3037259046961174248
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1464721f2283eb14e94a33e812b47be4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactableView: {fileID: 5949587551528604073}
  _whenHover:
    m_PersistentCalls:
      m_Calls: []
  _whenUnhover:
    m_PersistentCalls:
      m_Calls: []
  _whenSelect:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselect:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewAdded:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls: []
  _whenSelectingInteractorViewAdded:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6300239997087639387}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _whenSelectingInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3029866607647373476}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &5949587551528604072
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &2422591612188876735
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f9c7ae05d6efb54e90162d3cf5a7982, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _grabInteractable: {fileID: 5949587551528604079}
  _handGrabInteractable: {fileID: 5949587551528604073}
  _rayInteractable: {fileID: 0}
  _stateSignaler: {fileID: 7928194057166543090}
  _animators:
  - {fileID: 7490403055653535494}
  - {fileID: 50338352380188172}
  - {fileID: 7475674645444320095}
  _panelHoverState: {fileID: 4094494049803084778}
--- !u!1818360608 &3718378310297057081
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: 0.14, y: 0, z: 0}
  m_TranslationOffset: {x: 0, y: 0, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 9090655435056767839}
    weight: 1
  - sourceTransform: {fileID: 4857236710543586390}
    weight: 1
--- !u!114 &692147577422778769
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551528604055}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4bff16bb2b8cae9409e88ec4d3527c9b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactables:
  - {fileID: 5949587551528604079}
  - {fileID: 5949587551528604073}
  _data: {fileID: 0}
--- !u!1 &5949587551683044683
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5949587551683044682}
  - component: {fileID: 5949587551683044687}
  - component: {fileID: 5949587551683044680}
  - component: {fileID: 5949587551683044681}
  - component: {fileID: 5949587551683044686}
  - component: {fileID: 5949587551683044675}
  - component: {fileID: 5949587551683044685}
  - component: {fileID: 1624663924073777634}
  - component: {fileID: 5949587551683044684}
  - component: {fileID: 8280293325618449222}
  - component: {fileID: 327566623931446891}
  - component: {fileID: 7528471063957046898}
  m_Layer: 0
  m_Name: RotatorVerticalTop
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5949587551683044682
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.13999999, z: 0}
  m_LocalScale: {x: 0.02, y: 0.02, z: 0.02}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8892950201609566960}
  - {fileID: 323138011967445777}
  - {fileID: 8412545885426877406}
  - {fileID: 9173841164451720115}
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 8
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5949587551683044687
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5949587551683044680
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5949587551683044681
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &5949587551683044686
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &5949587551683044675
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a1bc992571301d4a9602ac95ef4c71a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814323}
  _rigidbody: {fileID: 5949587551683044686}
  _grabSource: {fileID: 0}
  _useClosestPointAsGrabSource: 0
  _releaseDistance: 0
  _resetGrabOnGrabsUpdated: 1
  _physicsGrabbable: {fileID: 0}
--- !u!114 &5949587551683044685
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9a7676b01585ce43908639a27765dfc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814323}
  _rigidbody: {fileID: 5949587551683044686}
  _physicsGrabbable: {fileID: 0}
  _resetGrabOnGrabsUpdated: 1
  _scoringModifier:
    _positionRotationWeight: 0.8
  _slippiness: 0
  _supportedGrabTypes: 3
  _pinchGrabRules:
    _thumbRequirement: 0
    _indexRequirement: 2
    _middleRequirement: 0
    _ringRequirement: 0
    _pinkyRequirement: 0
    _unselectMode: 1
  _palmGrabRules:
    _thumbRequirement: 1
    _indexRequirement: 2
    _middleRequirement: 2
    _ringRequirement: 2
    _pinkyRequirement: 1
    _unselectMode: 1
  _movementProvider: {fileID: 0}
  _handAligment: 0
  _handGrabPoses: []
--- !u!114 &1624663924073777634
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1464721f2283eb14e94a33e812b47be4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactableView: {fileID: 7528471063957046898}
  _whenHover:
    m_PersistentCalls:
      m_Calls: []
  _whenUnhover:
    m_PersistentCalls:
      m_Calls: []
  _whenSelect:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselect:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewAdded:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls: []
  _whenSelectingInteractorViewAdded:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6300239997087639387}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _whenSelectingInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3029866607647373476}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &5949587551683044684
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &8280293325618449222
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f9c7ae05d6efb54e90162d3cf5a7982, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _grabInteractable: {fileID: 5949587551683044675}
  _handGrabInteractable: {fileID: 5949587551683044685}
  _rayInteractable: {fileID: 0}
  _stateSignaler: {fileID: 7928194057166543090}
  _animators:
  - {fileID: 146793855244148306}
  - {fileID: 8308686915551687036}
  - {fileID: 6327419090315472222}
  _panelHoverState: {fileID: 4094494049803084778}
--- !u!1818360608 &327566623931446891
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: 0, y: 0.14, z: 0}
  m_TranslationOffset: {x: 0, y: 0, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 6028011848650400756}
    weight: 1
  - sourceTransform: {fileID: 9090655435056767839}
    weight: 1
--- !u!114 &7528471063957046898
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551683044683}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4bff16bb2b8cae9409e88ec4d3527c9b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactables:
  - {fileID: 5949587551683044675}
  - {fileID: 5949587551683044685}
  _data: {fileID: 0}
--- !u!1 &5949587551690473438
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5949587551690473437}
  - component: {fileID: 5949587551690473426}
  - component: {fileID: 5949587551690473427}
  - component: {fileID: 5949587551690473436}
  - component: {fileID: 5949587551690473425}
  - component: {fileID: 5949587551690473428}
  - component: {fileID: 5949587551690473430}
  - component: {fileID: 8345213146126802257}
  - component: {fileID: 5949587551690473429}
  - component: {fileID: 1788379752299262796}
  - component: {fileID: 351291541741304717}
  - component: {fileID: 6590391896169669105}
  m_Layer: 0
  m_Name: ScalerBottomRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5949587551690473437
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.13999999, y: -0.13999999, z: 0}
  m_LocalScale: {x: 0.02, y: 0.02, z: 0.02}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1671131856520450754}
  - {fileID: 4868016993338579034}
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 15
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5949587551690473426
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5949587551690473427
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5949587551690473436
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &5949587551690473425
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &5949587551690473428
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a1bc992571301d4a9602ac95ef4c71a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814327}
  _rigidbody: {fileID: 5949587551690473425}
  _grabSource: {fileID: 0}
  _useClosestPointAsGrabSource: 0
  _releaseDistance: 0
  _resetGrabOnGrabsUpdated: 1
  _physicsGrabbable: {fileID: 0}
--- !u!114 &5949587551690473430
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9a7676b01585ce43908639a27765dfc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814327}
  _rigidbody: {fileID: 5949587551690473425}
  _physicsGrabbable: {fileID: 0}
  _resetGrabOnGrabsUpdated: 1
  _scoringModifier:
    _positionRotationWeight: 0.8
  _slippiness: 0
  _supportedGrabTypes: 3
  _pinchGrabRules:
    _thumbRequirement: 0
    _indexRequirement: 2
    _middleRequirement: 0
    _ringRequirement: 0
    _pinkyRequirement: 0
    _unselectMode: 1
  _palmGrabRules:
    _thumbRequirement: 1
    _indexRequirement: 2
    _middleRequirement: 2
    _ringRequirement: 2
    _pinkyRequirement: 1
    _unselectMode: 1
  _movementProvider: {fileID: 0}
  _handAligment: 0
  _handGrabPoses: []
--- !u!114 &8345213146126802257
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1464721f2283eb14e94a33e812b47be4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactableView: {fileID: 6590391896169669105}
  _whenHover:
    m_PersistentCalls:
      m_Calls: []
  _whenUnhover:
    m_PersistentCalls:
      m_Calls: []
  _whenSelect:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselect:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewAdded:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls: []
  _whenSelectingInteractorViewAdded:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6300239997087639387}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _whenSelectingInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3029866607647373476}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &5949587551690473429
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1788379752299262796
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f9c7ae05d6efb54e90162d3cf5a7982, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _grabInteractable: {fileID: 5949587551690473428}
  _handGrabInteractable: {fileID: 5949587551690473430}
  _rayInteractable: {fileID: 0}
  _stateSignaler: {fileID: 7928194057166543090}
  _animators:
  - {fileID: 3900105464920794316}
  - {fileID: 500881978398777573}
  _panelHoverState: {fileID: 4094494049803084778}
--- !u!1818360608 &351291541741304717
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: 0.14, y: -0.14, z: 0}
  m_TranslationOffset: {x: 0, y: 0, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 4857236710543586390}
    weight: 1
--- !u!114 &6590391896169669105
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551690473438}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4bff16bb2b8cae9409e88ec4d3527c9b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactables:
  - {fileID: 5949587551690473428}
  - {fileID: 5949587551690473430}
  _data: {fileID: 0}
--- !u!1 &5949587551981348426
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5949587551981348425}
  - component: {fileID: 5949587551981348430}
  - component: {fileID: 5949587551981348431}
  - component: {fileID: 5949587551981348424}
  - component: {fileID: 5949587551981348429}
  - component: {fileID: 5949587551981348418}
  - component: {fileID: 5949587551981348428}
  - component: {fileID: 8228654041223398092}
  - component: {fileID: 5949587551981348419}
  - component: {fileID: 7284736147984339241}
  - component: {fileID: 4237388519511805208}
  - component: {fileID: 8528411506654703565}
  m_Layer: 0
  m_Name: RotatorVerticalBottom
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5949587551981348425
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: -0.13999999, z: 0}
  m_LocalScale: {x: 0.02, y: 0.02, z: 0.02}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8484175671254695250}
  - {fileID: 3082091016640999264}
  - {fileID: 3202461191181160443}
  - {fileID: 719123900899608914}
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 9
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5949587551981348430
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5949587551981348431
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5949587551981348424
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &5949587551981348429
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &5949587551981348418
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a1bc992571301d4a9602ac95ef4c71a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814323}
  _rigidbody: {fileID: 5949587551981348429}
  _grabSource: {fileID: 0}
  _useClosestPointAsGrabSource: 0
  _releaseDistance: 0
  _resetGrabOnGrabsUpdated: 1
  _physicsGrabbable: {fileID: 0}
--- !u!114 &5949587551981348428
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9a7676b01585ce43908639a27765dfc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814323}
  _rigidbody: {fileID: 5949587551981348429}
  _physicsGrabbable: {fileID: 0}
  _resetGrabOnGrabsUpdated: 1
  _scoringModifier:
    _positionRotationWeight: 0.8
  _slippiness: 0
  _supportedGrabTypes: 3
  _pinchGrabRules:
    _thumbRequirement: 0
    _indexRequirement: 2
    _middleRequirement: 0
    _ringRequirement: 0
    _pinkyRequirement: 0
    _unselectMode: 1
  _palmGrabRules:
    _thumbRequirement: 1
    _indexRequirement: 2
    _middleRequirement: 2
    _ringRequirement: 2
    _pinkyRequirement: 1
    _unselectMode: 1
  _movementProvider: {fileID: 0}
  _handAligment: 0
  _handGrabPoses: []
--- !u!114 &8228654041223398092
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1464721f2283eb14e94a33e812b47be4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactableView: {fileID: 8528411506654703565}
  _whenHover:
    m_PersistentCalls:
      m_Calls: []
  _whenUnhover:
    m_PersistentCalls:
      m_Calls: []
  _whenSelect:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselect:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewAdded:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls: []
  _whenSelectingInteractorViewAdded:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6300239997087639387}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _whenSelectingInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3029866607647373476}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &5949587551981348419
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &7284736147984339241
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f9c7ae05d6efb54e90162d3cf5a7982, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _grabInteractable: {fileID: 5949587551981348418}
  _handGrabInteractable: {fileID: 5949587551981348428}
  _rayInteractable: {fileID: 0}
  _stateSignaler: {fileID: 7928194057166543090}
  _animators:
  - {fileID: 2965714507873194092}
  - {fileID: 787118724822766471}
  - {fileID: 1685909497184902532}
  _panelHoverState: {fileID: 4094494049803084778}
--- !u!1818360608 &4237388519511805208
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: 0, y: -0.14, z: 0}
  m_TranslationOffset: {x: 0, y: 0, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 3792201369273974851}
    weight: 1
  - sourceTransform: {fileID: 4857236710543586390}
    weight: 1
--- !u!114 &8528411506654703565
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587551981348426}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4bff16bb2b8cae9409e88ec4d3527c9b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactables:
  - {fileID: 5949587551981348418}
  - {fileID: 5949587551981348428}
  _data: {fileID: 0}
--- !u!1 &5949587552056431494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5949587552056431493}
  m_Layer: 0
  m_Name: ul_anchor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5949587552056431493
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552056431494}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.5, y: 0.5, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5283934947824210007}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5949587552371862403
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5949587552371862402}
  - component: {fileID: 5949587552371862407}
  - component: {fileID: 5949587552371862400}
  - component: {fileID: 5949587552371862401}
  - component: {fileID: 5949587552371862406}
  - component: {fileID: 5949587552371862427}
  - component: {fileID: 5949587552371862405}
  - component: {fileID: 1967797670845931356}
  - component: {fileID: 5949587552371862404}
  - component: {fileID: 1246908729211788598}
  - component: {fileID: 3602970967674731844}
  - component: {fileID: 4080297831153722261}
  m_Layer: 0
  m_Name: RotatorHorizontalLeft
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5949587552371862402
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.13999999, y: 0, z: 0}
  m_LocalScale: {x: 0.02, y: 0.02, z: 0.02}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3017881461936282176}
  - {fileID: 4683584557097269467}
  - {fileID: 1835470002449758179}
  - {fileID: 879492422290928399}
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 10
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5949587552371862407
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5949587552371862400
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5949587552371862401
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &5949587552371862406
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &5949587552371862427
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a1bc992571301d4a9602ac95ef4c71a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814321}
  _rigidbody: {fileID: 5949587552371862406}
  _grabSource: {fileID: 0}
  _useClosestPointAsGrabSource: 0
  _releaseDistance: 0
  _resetGrabOnGrabsUpdated: 1
  _physicsGrabbable: {fileID: 0}
--- !u!114 &5949587552371862405
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9a7676b01585ce43908639a27765dfc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814321}
  _rigidbody: {fileID: 5949587552371862406}
  _physicsGrabbable: {fileID: 0}
  _resetGrabOnGrabsUpdated: 1
  _scoringModifier:
    _positionRotationWeight: 0.8
  _slippiness: 0
  _supportedGrabTypes: 3
  _pinchGrabRules:
    _thumbRequirement: 0
    _indexRequirement: 2
    _middleRequirement: 0
    _ringRequirement: 0
    _pinkyRequirement: 0
    _unselectMode: 1
  _palmGrabRules:
    _thumbRequirement: 1
    _indexRequirement: 2
    _middleRequirement: 2
    _ringRequirement: 2
    _pinkyRequirement: 1
    _unselectMode: 1
  _movementProvider: {fileID: 0}
  _handAligment: 0
  _handGrabPoses: []
--- !u!114 &1967797670845931356
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1464721f2283eb14e94a33e812b47be4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactableView: {fileID: 4080297831153722261}
  _whenHover:
    m_PersistentCalls:
      m_Calls: []
  _whenUnhover:
    m_PersistentCalls:
      m_Calls: []
  _whenSelect:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselect:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewAdded:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls: []
  _whenSelectingInteractorViewAdded:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6300239997087639387}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _whenSelectingInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3029866607647373476}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &5949587552371862404
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1246908729211788598
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f9c7ae05d6efb54e90162d3cf5a7982, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _grabInteractable: {fileID: 5949587552371862427}
  _handGrabInteractable: {fileID: 5949587552371862405}
  _rayInteractable: {fileID: 0}
  _stateSignaler: {fileID: 7928194057166543090}
  _animators:
  - {fileID: 965877444300871835}
  - {fileID: 1897849598996892371}
  - {fileID: 5781484356335568377}
  _panelHoverState: {fileID: 4094494049803084778}
--- !u!1818360608 &3602970967674731844
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: -0.14, y: 0, z: 0}
  m_TranslationOffset: {x: 0, y: 0, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 6028011848650400756}
    weight: 1
  - sourceTransform: {fileID: 3792201369273974851}
    weight: 1
--- !u!114 &4080297831153722261
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552371862403}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4bff16bb2b8cae9409e88ec4d3527c9b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactables:
  - {fileID: 5949587552371862427}
  - {fileID: 5949587552371862405}
  _data: {fileID: 0}
--- !u!1 &5949587552490898146
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5949587552490898145}
  - component: {fileID: 5949587552490898150}
  - component: {fileID: 5949587552490898151}
  - component: {fileID: 5949587552490898144}
  - component: {fileID: 5949587552490898149}
  - component: {fileID: 5949587552490898170}
  - component: {fileID: 5949587552490898148}
  - component: {fileID: 7535364003298665268}
  - component: {fileID: 5949587552490898171}
  - component: {fileID: 8861975857970758697}
  - component: {fileID: 628821812278196406}
  - component: {fileID: 8606092243888470295}
  m_Layer: 0
  m_Name: ScalerBottomLeft
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5949587552490898145
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.13999999, y: -0.13999999, z: 0}
  m_LocalScale: {x: 0.02, y: 0.02, z: 0.02}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1788243882253859909}
  - {fileID: 1893311671661001608}
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 14
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &5949587552490898150
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &5949587552490898151
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &5949587552490898144
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &5949587552490898149
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &5949587552490898170
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a1bc992571301d4a9602ac95ef4c71a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814327}
  _rigidbody: {fileID: 5949587552490898149}
  _grabSource: {fileID: 0}
  _useClosestPointAsGrabSource: 0
  _releaseDistance: 0
  _resetGrabOnGrabsUpdated: 1
  _physicsGrabbable: {fileID: 0}
--- !u!114 &5949587552490898148
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e9a7676b01585ce43908639a27765dfc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters: []
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 5949587553195814327}
  _rigidbody: {fileID: 5949587552490898149}
  _physicsGrabbable: {fileID: 0}
  _resetGrabOnGrabsUpdated: 1
  _scoringModifier:
    _positionRotationWeight: 0.8
  _slippiness: 0
  _supportedGrabTypes: 3
  _pinchGrabRules:
    _thumbRequirement: 0
    _indexRequirement: 2
    _middleRequirement: 0
    _ringRequirement: 0
    _pinkyRequirement: 0
    _unselectMode: 1
  _palmGrabRules:
    _thumbRequirement: 1
    _indexRequirement: 2
    _middleRequirement: 2
    _ringRequirement: 2
    _pinkyRequirement: 1
    _unselectMode: 1
  _movementProvider: {fileID: 0}
  _handAligment: 0
  _handGrabPoses: []
--- !u!114 &7535364003298665268
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1464721f2283eb14e94a33e812b47be4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactableView: {fileID: 8606092243888470295}
  _whenHover:
    m_PersistentCalls:
      m_Calls: []
  _whenUnhover:
    m_PersistentCalls:
      m_Calls: []
  _whenSelect:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselect:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewAdded:
    m_PersistentCalls:
      m_Calls: []
  _whenInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls: []
  _whenSelectingInteractorViewAdded:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 6300239997087639387}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  _whenSelectingInteractorViewRemoved:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3029866607647373476}
        m_TargetAssemblyTypeName: UnityEngine.AudioSource, UnityEngine
        m_MethodName: Play
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &5949587552490898171
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &8861975857970758697
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f9c7ae05d6efb54e90162d3cf5a7982, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _grabInteractable: {fileID: 5949587552490898170}
  _handGrabInteractable: {fileID: 5949587552490898148}
  _rayInteractable: {fileID: 0}
  _stateSignaler: {fileID: 7928194057166543090}
  _animators:
  - {fileID: 6821412675901220674}
  - {fileID: 6941687788410982366}
  _panelHoverState: {fileID: 4094494049803084778}
--- !u!1818360608 &628821812278196406
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: -0.14, y: -0.14, z: 0}
  m_TranslationOffset: {x: 0, y: 0, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 3792201369273974851}
    weight: 1
--- !u!114 &8606092243888470295
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587552490898146}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4bff16bb2b8cae9409e88ec4d3527c9b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactables:
  - {fileID: 5949587552490898170}
  - {fileID: 5949587552490898148}
  _data: {fileID: 0}
--- !u!1 &5949587553195814330
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5949587553195814329}
  - component: {fileID: 5949587553195814328}
  - component: {fileID: 5949587553195814334}
  - component: {fileID: 5934362454628486909}
  - component: {fileID: 5949587553195814323}
  - component: {fileID: 5949587553195814332}
  - component: {fileID: 5949587553195814321}
  - component: {fileID: 5949587553195814322}
  - component: {fileID: 5949587553195814327}
  - component: {fileID: 5949587553195814320}
  - component: {fileID: 7928194057166543090}
  - component: {fileID: 4094494049803084778}
  m_Layer: 0
  m_Name: PanelWithManipulators
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5949587553195814329
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6810221666402882945}
  - {fileID: 2725540670565825194}
  - {fileID: 6028011848650400756}
  - {fileID: 9090655435056767839}
  - {fileID: 3792201369273974851}
  - {fileID: 4857236710543586390}
  - {fileID: 5283934947824210007}
  - {fileID: 725278033861247257}
  - {fileID: 5949587551683044682}
  - {fileID: 5949587551981348425}
  - {fileID: 5949587552371862402}
  - {fileID: 5949587551528604054}
  - {fileID: 5949587551324850980}
  - {fileID: 5949587551380793747}
  - {fileID: 5949587552490898145}
  - {fileID: 5949587551690473437}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &5949587553195814328
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &5949587553195814334
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43f86b14a27b52f4f9298c33015b5c26, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _transferOnSecondSelection: 0
  _addNewPointsToFront: 0
  _forwardElement: {fileID: 0}
  _oneGrabTransformer: {fileID: 5934362454628486909}
  _twoGrabTransformer: {fileID: 5934362454628486909}
  _targetTransform: {fileID: 0}
  _maxGrabPoints: -1
  _generateThrow: 0
--- !u!114 &5934362454628486909
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: bbb7085c111fcb44493d57d4e336d3fd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _positionConstraints:
    ConstraintsAreRelative: 0
    XAxis:
      ConstrainAxis: 0
      AxisRange:
        Min: 0
        Max: 0
    YAxis:
      ConstrainAxis: 0
      AxisRange:
        Min: 0
        Max: 0
    ZAxis:
      ConstrainAxis: 0
      AxisRange:
        Min: 0
        Max: 0
  _rotationConstraints:
    XAxis:
      ConstrainAxis: 0
      AxisRange:
        Min: 0
        Max: 0
    YAxis:
      ConstrainAxis: 0
      AxisRange:
        Min: 0
        Max: 0
    ZAxis:
      ConstrainAxis: 0
      AxisRange:
        Min: 0
        Max: 0
  _scaleConstraints:
    ConstraintsAreRelative: 1
    XAxis:
      ConstrainAxis: 1
      AxisRange:
        Min: 0.7
        Max: Infinity
    YAxis:
      ConstrainAxis: 1
      AxisRange:
        Min: 0.7
        Max: Infinity
    ZAxis:
      ConstrainAxis: 0
      AxisRange:
        Min: 1
        Max: 1
--- !u!114 &5949587553195814323
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43f86b14a27b52f4f9298c33015b5c26, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _transferOnSecondSelection: 0
  _addNewPointsToFront: 0
  _forwardElement: {fileID: 0}
  _oneGrabTransformer: {fileID: 5949587553195814332}
  _twoGrabTransformer: {fileID: 0}
  _targetTransform: {fileID: 0}
  _maxGrabPoints: -1
  _generateThrow: 0
--- !u!114 &5949587553195814332
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _pivotTransform: {fileID: 0}
  _rotationAxis: 0
  _constraints:
    MinAngle:
      Constrain: 0
      Value: 0
    MaxAngle:
      Constrain: 0
      Value: 0
--- !u!114 &5949587553195814321
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43f86b14a27b52f4f9298c33015b5c26, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _transferOnSecondSelection: 0
  _addNewPointsToFront: 0
  _forwardElement: {fileID: 0}
  _oneGrabTransformer: {fileID: 5949587553195814322}
  _twoGrabTransformer: {fileID: 0}
  _targetTransform: {fileID: 0}
  _maxGrabPoints: -1
  _generateThrow: 0
--- !u!114 &5949587553195814322
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _pivotTransform: {fileID: 0}
  _rotationAxis: 1
  _constraints:
    MinAngle:
      Constrain: 0
      Value: 0
    MaxAngle:
      Constrain: 0
      Value: 0
--- !u!114 &5949587553195814327
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 43f86b14a27b52f4f9298c33015b5c26, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _transferOnSecondSelection: 0
  _addNewPointsToFront: 0
  _forwardElement: {fileID: 0}
  _oneGrabTransformer: {fileID: 5949587553195814320}
  _twoGrabTransformer: {fileID: 0}
  _targetTransform: {fileID: 0}
  _maxGrabPoints: -1
  _generateThrow: 0
--- !u!114 &5949587553195814320
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 91767808988d184499270224b7f98f73, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _constraints:
    IgnoreFixedAxes: 0
    ConstrainXYAspectRatio: 0
    MinX:
      Constrain: 1
      Value: 0.7
    MaxX:
      Constrain: 0
      Value: 0
    MinY:
      Constrain: 1
      Value: 0.7
    MaxY:
      Constrain: 0
      Value: 0
    MinZ:
      Constrain: 1
      Value: 1
    MaxZ:
      Constrain: 1
      Value: 1
--- !u!114 &7928194057166543090
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2787d25d76739ff4ba25a4e317c0e546, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &4094494049803084778
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5949587553195814330}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 307d8fc012af8da4799702611555778c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  grabbables:
  - {fileID: 5949587553195814334}
  - {fileID: 5949587553195814323}
  - {fileID: 5949587553195814321}
  - {fileID: 5949587553195814327}
--- !u!1 &5970266228168725849
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 323138011967445777}
  - component: {fileID: 6327419090315472222}
  m_Layer: 0
  m_Name: arc_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &323138011967445777
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5970266228168725849}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2182034318370113452}
  m_Father: {fileID: 5949587551683044682}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!95 &6327419090315472222
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5970266228168725849}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: c6bda3b0ac0f27748883fdebf6eaef6a, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &6073688626358009531
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3376491245619068456}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3376491245619068456
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6073688626358009531}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1893311671661001608}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6247676508021636671
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6773383062611747973}
  - component: {fileID: 7490403055653535494}
  - component: {fileID: 4493544708234449229}
  m_Layer: 0
  m_Name: capsule_affordance_geometry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6773383062611747973
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6247676508021636671}
  m_LocalRotation: {x: 0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.2, y: 0.20000003, z: 0.20000003}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 640207878844875167}
  m_Father: {fileID: 5949587551528604054}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &7490403055653535494
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6247676508021636671}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: ce346326520fc08479c1233475adea43, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &4493544708234449229
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6247676508021636671}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 7235418987618904097}
  _opacityTransform: {fileID: 6803542765225942074}
--- !u!1 &6567102867526257000
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3308621696637773859}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3308621696637773859
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6567102867526257000}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3082091016640999264}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6641880027991213139
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7677856436967682397}
  - component: {fileID: 2334889815892895401}
  m_Layer: 0
  m_Name: PanelBorderHandleAffordance
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7677856436967682397
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6641880027991213139}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1081413021998596174}
  m_Father: {fileID: 725278033861247257}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &2334889815892895401
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6641880027991213139}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 7a6ef4c66f11662479ff742cd09d15ac, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &6746737680295973126
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1312668880398728018}
  - component: {fileID: 523573523263302231}
  m_Layer: 0
  m_Name: PanelBorderHandleAffordance (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1312668880398728018
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6746737680295973126}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3489072346201784984}
  m_Father: {fileID: 725278033861247257}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &523573523263302231
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6746737680295973126}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 7a6ef4c66f11662479ff742cd09d15ac, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &6982198144154074020
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3792201369273974851}
  - component: {fileID: 7131927135309994705}
  m_Layer: 0
  m_Name: AnchorBottomLeft
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3792201369273974851
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6982198144154074020}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.14, y: -0.14, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5949587553195814329}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7131927135309994705
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6982198144154074020}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 85eb938ed8a4109478fefc902994c1f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &7101925480668939788
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 640207878844875167}
  - component: {fileID: 7235418987618904097}
  m_Layer: 0
  m_Name: Icosphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &640207878844875167
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7101925480668939788}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6773383062611747973}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &7235418987618904097
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7101925480668939788}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 289301614986012343, guid: dffb75200a9c0da4ea0ddfa9d7060cb3, type: 3}
  m_Bones: []
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 0}
  m_AABB:
    m_Center: {x: -4.656613e-10, y: 0, z: 0}
    m_Extent: {x: 0.01, y: 0.009999999, z: 0.024691815}
  m_DirtyAABB: 0
--- !u!1 &8366517417742987529
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9173841164451720115}
  - component: {fileID: 8308686915551687036}
  m_Layer: 0
  m_Name: capsule_affordance_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9173841164451720115
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8366517417742987529}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5462259306938461715}
  m_Father: {fileID: 5949587551683044682}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!95 &8308686915551687036
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8366517417742987529}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 0edbf3bdbf7178349bb22f29b86612d6, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &8536740788364623794
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6803542765225942074}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6803542765225942074
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8536740788364623794}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8685372042213346182}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8706850248996166605
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 879492422290928399}
  - component: {fileID: 1897849598996892371}
  m_Layer: 0
  m_Name: capsule_affordance_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &879492422290928399
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8706850248996166605}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5226680308581292598}
  m_Father: {fileID: 5949587552371862402}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!95 &1897849598996892371
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8706850248996166605}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 0edbf3bdbf7178349bb22f29b86612d6, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &8834051137420613458
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 719123900899608914}
  - component: {fileID: 787118724822766471}
  m_Layer: 0
  m_Name: capsule_affordance_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &719123900899608914
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8834051137420613458}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2640935537801876704}
  m_Father: {fileID: 5949587551981348425}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!95 &787118724822766471
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8834051137420613458}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: 0edbf3bdbf7178349bb22f29b86612d6, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &9017148609138304566
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2640935537801876704}
  m_Layer: 0
  m_Name: opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2640935537801876704
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9017148609138304566}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 719123900899608914}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9193210194048552997
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4683584557097269467}
  - component: {fileID: 5781484356335568377}
  m_Layer: 0
  m_Name: arc_opacity
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4683584557097269467
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9193210194048552997}
  m_LocalRotation: {x: 0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.3, y: 0.3, z: 0.3}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 411748885624581612}
  m_Father: {fileID: 5949587552371862402}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 90, y: 0, z: 0}
--- !u!95 &5781484356335568377
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9193210194048552997}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: c6bda3b0ac0f27748883fdebf6eaef6a, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!1 &9220149668371716452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8412545885426877406}
  - component: {fileID: 146793855244148306}
  - component: {fileID: 7342235862613173216}
  m_Layer: 0
  m_Name: capsule_affordance_geometry
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8412545885426877406
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9220149668371716452}
  m_LocalRotation: {x: 0.7071068, y: -0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.2, y: 0.20000003, z: 0.20000003}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2423710964611376836}
  m_Father: {fileID: 5949587551683044682}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &146793855244148306
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9220149668371716452}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: ce346326520fc08479c1233475adea43, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &7342235862613173216
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9220149668371716452}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 5560228998496243578}
  _opacityTransform: {fileID: 5462259306938461715}
--- !u!1001 &3344787275514887595
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 5949587552371862402}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.70710677
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.70710677
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_Name
      value: skinned_articulated_arc
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5074512353664290214, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 39983ac309c64a346b605e69511af288, type: 3}
--- !u!1 &2497944869822169338 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 3344787275514887595}
  m_PrefabAsset: {fileID: 0}
--- !u!95 &6845540729769523325
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2497944869822169338}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: e68951fda9eb0da4aa0c7fdaa7cc1219, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &578897683367651622
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2497944869822169338}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 7496057238523637773}
  _opacityTransform: {fileID: 411748885624581612}
--- !u!114 &3449333283887019213
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2497944869822169338}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 234066a1e88fa5f418586761e9db044d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _animator: {fileID: 6845540729769523325}
  _pivot: {fileID: 2725540670565825194}
  _distanceToCurvatureCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: -5.24129
      outSlope: -5.24129
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0.4580741
    - serializedVersion: 3
      time: 0.028494084
      value: 0.7269551
      inSlope: -14.132845
      outSlope: -14.132845
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.05479904
    - serializedVersion: 3
      time: 0.08609264
      value: 0.3736785
      inSlope: -1.8228536
      outSlope: -1.8228536
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.32194555
      outWeight: 0.07942293
    - serializedVersion: 3
      time: 0.699817
      value: 0.044162206
      inSlope: -0.054307304
      outSlope: -0.054307304
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.13137826
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1.0051323
      value: 0.039462507
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  _renderer: {fileID: 7496057238523637773}
  _topBone: {fileID: 7891527025537573339}
  _bottomBone: {fileID: 8145451846641973169}
--- !u!4 &3017881461936282176 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 3344787275514887595}
  m_PrefabAsset: {fileID: 0}
--- !u!137 &7496057238523637773 stripped
SkinnedMeshRenderer:
  m_CorrespondingSourceObject: {fileID: 5074512353664290214, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 3344787275514887595}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &7891527025537573339 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4895206408800197744, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 3344787275514887595}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &8145451846641973169 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -2350484270337013222, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 3344787275514887595}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3470032637708396462
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 5949587551528604054}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.70710677
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.70710677
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_Name
      value: skinned_articulated_arc
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5074512353664290214, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 39983ac309c64a346b605e69511af288, type: 3}
--- !u!4 &4008961446470975557 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 3470032637708396462}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &4389146640625425151 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 3470032637708396462}
  m_PrefabAsset: {fileID: 0}
--- !u!95 &5691437931817407541
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4389146640625425151}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: e68951fda9eb0da4aa0c7fdaa7cc1219, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &2736148784787237423
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4389146640625425151}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 234066a1e88fa5f418586761e9db044d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _animator: {fileID: 5691437931817407541}
  _pivot: {fileID: 2725540670565825194}
  _distanceToCurvatureCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: -5.24129
      outSlope: -5.24129
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0.4580741
    - serializedVersion: 3
      time: 0.028494084
      value: 0.7269551
      inSlope: -14.132845
      outSlope: -14.132845
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.05479904
    - serializedVersion: 3
      time: 0.08609264
      value: 0.3736785
      inSlope: -1.8228536
      outSlope: -1.8228536
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.32194555
      outWeight: 0.07942293
    - serializedVersion: 3
      time: 0.699817
      value: 0.044162206
      inSlope: -0.054307304
      outSlope: -0.054307304
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.13137826
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1.0051323
      value: 0.039462507
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  _renderer: {fileID: 8522009327763139080}
  _topBone: {fileID: 8342720425316236254}
  _bottomBone: {fileID: 8019062996501638580}
--- !u!114 &7663233254859016122
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4389146640625425151}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 8522009327763139080}
  _opacityTransform: {fileID: 3501029921465458116}
--- !u!4 &8019062996501638580 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -2350484270337013222, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 3470032637708396462}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &8342720425316236254 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4895206408800197744, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 3470032637708396462}
  m_PrefabAsset: {fileID: 0}
--- !u!137 &8522009327763139080 stripped
SkinnedMeshRenderer:
  m_CorrespondingSourceObject: {fileID: 5074512353664290214, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 3470032637708396462}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8230154248685935289
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 5949587551981348425}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_Name
      value: skinned_articulated_arc
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5074512353664290214, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 39983ac309c64a346b605e69511af288, type: 3}
--- !u!4 &3266805921546997923 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -2350484270337013222, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 8230154248685935289}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &3591662372287710921 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4895206408800197744, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 8230154248685935289}
  m_PrefabAsset: {fileID: 0}
--- !u!137 &3772657176219555615 stripped
SkinnedMeshRenderer:
  m_CorrespondingSourceObject: {fileID: 5074512353664290214, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 8230154248685935289}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &8484175671254695250 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 8230154248685935289}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &9148509034395330536 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 8230154248685935289}
  m_PrefabAsset: {fileID: 0}
--- !u!95 &5441594525825433631
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9148509034395330536}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: e68951fda9eb0da4aa0c7fdaa7cc1219, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &3588283879461722354
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9148509034395330536}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 3772657176219555615}
  _opacityTransform: {fileID: 3308621696637773859}
--- !u!114 &7146932404240545215
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9148509034395330536}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 234066a1e88fa5f418586761e9db044d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _animator: {fileID: 5441594525825433631}
  _pivot: {fileID: 2725540670565825194}
  _distanceToCurvatureCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: -5.24129
      outSlope: -5.24129
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0.4580741
    - serializedVersion: 3
      time: 0.028494084
      value: 0.7269551
      inSlope: -14.132845
      outSlope: -14.132845
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.05479904
    - serializedVersion: 3
      time: 0.08609264
      value: 0.3736785
      inSlope: -1.8228536
      outSlope: -1.8228536
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.32194555
      outWeight: 0.07942293
    - serializedVersion: 3
      time: 0.699817
      value: 0.044162206
      inSlope: -0.054307304
      outSlope: -0.054307304
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.13137826
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1.0051323
      value: 0.039462507
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  _renderer: {fileID: 3772657176219555615}
  _topBone: {fileID: 3591662372287710921}
  _bottomBone: {fileID: 3266805921546997923}
--- !u!1001 &8616230787763818948
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 725278033861247257}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalScale.x
      value: -0.04
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.04
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.04
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -5401271877751119579, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_AABB.m_Extent.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: -5401271877751119579, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_AABB.m_Extent.y
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: -5401271877751119579, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_AABB.m_Extent.z
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: -5401271877751119579, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: 31bae555d91c37c47a1181868917e61d, type: 2}
    - target: {fileID: -4932099093455679918, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.019999992
      objectReference: {fileID: 0}
    - target: {fileID: -4932099093455679918, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.019999996
      objectReference: {fileID: 0}
    - target: {fileID: -4932099093455679918, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_Name
      value: skinned_rounded_rectangle
      objectReference: {fileID: 0}
    - target: {fileID: 1980960758582919647, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.019999992
      objectReference: {fileID: 0}
    - target: {fileID: 1980960758582919647, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.019999996
      objectReference: {fileID: 0}
    - target: {fileID: 1980960758582919647, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2628600266646532890, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.019999992
      objectReference: {fileID: 0}
    - target: {fileID: 2628600266646532890, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.019999996
      objectReference: {fileID: 0}
    - target: {fileID: 2628600266646532890, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6513762932657452581, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.019999992
      objectReference: {fileID: 0}
    - target: {fileID: 6513762932657452581, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.019999996
      objectReference: {fileID: 0}
    - target: {fileID: 6513762932657452581, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8879528566716807029, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 8879528566716807029, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.7071068
      objectReference: {fileID: 0}
    - target: {fileID: 8879528566716807029, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8879528566716807029, guid: d373038051eb6dd4e914d28686e13545,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: d373038051eb6dd4e914d28686e13545, type: 3}
--- !u!1 &1529328206432893003 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -2113802692603631217, guid: d373038051eb6dd4e914d28686e13545,
    type: 3}
  m_PrefabInstance: {fileID: 8616230787763818948}
  m_PrefabAsset: {fileID: 0}
--- !u!1818360608 &4567592966424019430
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1529328206432893003}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: 0.02, y: 0.02, z: 0}
  m_TranslationOffset: {x: -0.015000001, y: -0.015000008, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 6028011848650400756}
    weight: 1
--- !u!4 &3311984797145180129 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 6513762932657452581, guid: d373038051eb6dd4e914d28686e13545,
    type: 3}
  m_PrefabInstance: {fileID: 8616230787763818948}
  m_PrefabAsset: {fileID: 0}
--- !u!137 &4799099527865053409 stripped
SkinnedMeshRenderer:
  m_CorrespondingSourceObject: {fileID: -5401271877751119579, guid: d373038051eb6dd4e914d28686e13545,
    type: 3}
  m_PrefabInstance: {fileID: 8616230787763818948}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &6781784378767108004 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -6228833744052636064, guid: d373038051eb6dd4e914d28686e13545,
    type: 3}
  m_PrefabInstance: {fileID: 8616230787763818948}
  m_PrefabAsset: {fileID: 0}
--- !u!1818360608 &4163026460663813286
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6781784378767108004}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: 0.02, y: -0.02, z: 0}
  m_TranslationOffset: {x: -0.015000001, y: 0.015000008, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 3792201369273974851}
    weight: 1
--- !u!4 &8077691079682373167 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: d373038051eb6dd4e914d28686e13545,
    type: 3}
  m_PrefabInstance: {fileID: 8616230787763818948}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &8495908693756835136 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 176915482756326532, guid: d373038051eb6dd4e914d28686e13545,
    type: 3}
  m_PrefabInstance: {fileID: 8616230787763818948}
  m_PrefabAsset: {fileID: 0}
--- !u!1818360608 &5175795798153210261
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8495908693756835136}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: -0.02, y: -0.02, z: 0}
  m_TranslationOffset: {x: 0.015000001, y: 0.015000008, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 4857236710543586390}
    weight: 1
--- !u!1 &8886281089660710037 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: d373038051eb6dd4e914d28686e13545,
    type: 3}
  m_PrefabInstance: {fileID: 8616230787763818948}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &3133940955117038275
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8886281089660710037}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 4799099527865053409}
  _opacityTransform: {fileID: 1346352817505665296}
--- !u!1 &9211642648331940085 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: -8627396676432453327, guid: d373038051eb6dd4e914d28686e13545,
    type: 3}
  m_PrefabInstance: {fileID: 8616230787763818948}
  m_PrefabAsset: {fileID: 0}
--- !u!1818360608 &6914720613429022942
PositionConstraint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9211642648331940085}
  m_Enabled: 1
  m_Weight: 1
  m_TranslationAtRest: {x: -0.02, y: 0.02, z: 0}
  m_TranslationOffset: {x: 0.015000001, y: -0.015000008, z: 0}
  m_AffectTranslationX: 1
  m_AffectTranslationY: 1
  m_AffectTranslationZ: 1
  m_IsContraintActive: 1
  m_IsLocked: 1
  m_Sources:
  - sourceTransform: {fileID: 9090655435056767839}
    weight: 1
--- !u!1001 &8998370631947935003
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 5949587551683044682}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -1
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.5
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_Name
      value: skinned_articulated_arc
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5074512353664290214, guid: 39983ac309c64a346b605e69511af288,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 2100000, guid: b05405039f3d8ed4d9d92ac82fccddfe, type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 39983ac309c64a346b605e69511af288, type: 3}
--- !u!4 &2558543115451045633 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -2350484270337013222, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 8998370631947935003}
  m_PrefabAsset: {fileID: 0}
--- !u!137 &4219002534157455549 stripped
SkinnedMeshRenderer:
  m_CorrespondingSourceObject: {fileID: 5074512353664290214, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 8998370631947935003}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &4544103578051019115 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4895206408800197744, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 8998370631947935003}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &8079963214636212298 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 919132149155446097, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 8998370631947935003}
  m_PrefabAsset: {fileID: 0}
--- !u!95 &5434730441153376794
Animator:
  serializedVersion: 5
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8079963214636212298}
  m_Enabled: 1
  m_Avatar: {fileID: 0}
  m_Controller: {fileID: 9100000, guid: e68951fda9eb0da4aa0c7fdaa7cc1219, type: 2}
  m_CullingMode: 0
  m_UpdateMode: 0
  m_ApplyRootMotion: 0
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!114 &3236988000745328321
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8079963214636212298}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4888fb2674bcd4942b606a4535315bce, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderer: {fileID: 4219002534157455549}
  _opacityTransform: {fileID: 2182034318370113452}
--- !u!114 &6792930160506907047
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8079963214636212298}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 234066a1e88fa5f418586761e9db044d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _animator: {fileID: 5434730441153376794}
  _pivot: {fileID: 2725540670565825194}
  _distanceToCurvatureCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: -5.24129
      outSlope: -5.24129
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0.4580741
    - serializedVersion: 3
      time: 0.028494084
      value: 0.7269551
      inSlope: -14.132845
      outSlope: -14.132845
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.05479904
    - serializedVersion: 3
      time: 0.08609264
      value: 0.3736785
      inSlope: -1.8228536
      outSlope: -1.8228536
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.32194555
      outWeight: 0.07942293
    - serializedVersion: 3
      time: 0.699817
      value: 0.044162206
      inSlope: -0.054307304
      outSlope: -0.054307304
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.13137826
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1.0051323
      value: 0.039462507
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  _renderer: {fileID: 4219002534157455549}
  _topBone: {fileID: 4544103578051019115}
  _bottomBone: {fileID: 2558543115451045633}
--- !u!4 &8892950201609566960 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: 39983ac309c64a346b605e69511af288,
    type: 3}
  m_PrefabInstance: {fileID: 8998370631947935003}
  m_PrefabAsset: {fileID: 0}
