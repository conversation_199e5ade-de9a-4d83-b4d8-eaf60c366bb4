%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &334585919437040955
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3668952689077263116}
  - component: {fileID: 229537448399160316}
  - component: {fileID: 4404141408187726226}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3668952689077263116
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 334585919437040955}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3545885034791629778}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 128, y: -116}
  m_SizeDelta: {x: 210, y: 28}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &229537448399160316
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 334585919437040955}
  m_CullTransparentMesh: 1
--- !u!114 &4404141408187726226
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 334585919437040955}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Select a Sample scene
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: eb44739c484b1b54bbc1a0d4d9dd1a50, type: 2}
  m_sharedMaterial: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4293980400
  m_fontColor: {r: 0.9411765, g: 0.9411765, b: 0.9411765, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 20
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &1665387254588144940
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5274914993832454811}
  - component: {fileID: 5299156443796844469}
  - component: {fileID: 5367513488863171058}
  m_Layer: 0
  m_Name: Primary Text (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &5274914993832454811
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665387254588144940}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4951791328163808189}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 160, y: -64}
  m_SizeDelta: {x: 284, y: 24}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &5299156443796844469
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665387254588144940}
  m_CullTransparentMesh: 1
--- !u!114 &5367513488863171058
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665387254588144940}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 'Interaction SDK Samples '
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: eb44739c484b1b54bbc1a0d4d9dd1a50, type: 2}
  m_sharedMaterial: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4293980400
  m_fontColor: {r: 0.9411765, g: 0.9411765, b: 0.9411765, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 25
  m_fontSizeBase: 25
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 256
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: -14.953228}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &1848995201964736745
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4951791328163808189}
  - component: {fileID: 8321586730164646980}
  - component: {fileID: 2121225419248287487}
  - component: {fileID: 2343010660568787144}
  - component: {fileID: 3247209304902734259}
  - component: {fileID: 777030856775977291}
  - component: {fileID: 3145011658261286974}
  - component: {fileID: 9200014466177496774}
  m_Layer: 0
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &4951791328163808189
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848995201964736745}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0.01}
  m_LocalScale: {x: 0.003112245, y: 0.0018711655, z: 0.00061000005}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7909120988866759706}
  - {fileID: 5274914993832454811}
  - {fileID: 3154507624802441843}
  - {fileID: 3545885034791629778}
  - {fileID: 7378433908484095872}
  m_Father: {fileID: 6536286299756309002}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 320, y: 534}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!223 &8321586730164646980
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848995201964736745}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!114 &2121225419248287487
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848995201964736745}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 1
--- !u!114 &2343010660568787144
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848995201964736745}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!114 &3247209304902734259
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848995201964736745}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 18
    m_Right: 18
    m_Top: 18
    m_Bottom: 18
  m_ChildAlignment: 0
  m_Spacing: 0
  m_ChildForceExpandWidth: 1
  m_ChildForceExpandHeight: 0
  m_ChildControlWidth: 1
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!222 &777030856775977291
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848995201964736745}
  m_CullTransparentMesh: 1
--- !u!114 &3145011658261286974
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848995201964736745}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.14901961, g: 0.15686275, b: 0.16470589, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 677149704152372326, guid: f576801ac9141ab4c8899dfdada97e19, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &9200014466177496774
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1848995201964736745}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3245ec927659c4140ac4f8d17403cc18, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_HorizontalFit: 0
  m_VerticalFit: 2
--- !u!1 &2034992692521974331
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3583042432202986915}
  - component: {fileID: 5415764154236750833}
  - component: {fileID: 1578372745682419700}
  - component: {fileID: 4858918117223452439}
  m_Layer: 0
  m_Name: Version
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3583042432202986915
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2034992692521974331}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.008, y: 0.008, z: 0.008}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2956604756026971515}
  m_RootOrder: 16
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: -0.37}
  m_SizeDelta: {x: 20, y: 5}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!23 &5415764154236750833
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2034992692521974331}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: -9221056233183343877, guid: a8ff26fc8eee4e64f84d729a22088af5, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &1578372745682419700
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2034992692521974331}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9541d86e2fd84c1d9990edf0852d74ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Version #
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: a8ff26fc8eee4e64f84d729a22088af5, type: 2}
  m_sharedMaterial: {fileID: -9221056233183343877, guid: a8ff26fc8eee4e64f84d729a22088af5,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 3372220415
  m_fontColor: {r: 1, g: 1, b: 1, a: 0.78431374}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 20
  m_fontSizeBase: 20
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 0
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  _SortingLayer: 0
  _SortingLayerID: 0
  _SortingOrder: 0
  m_hasFontAssetChanged: 0
  m_renderer: {fileID: 5415764154236750833}
  m_maskType: 0
--- !u!114 &4858918117223452439
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2034992692521974331}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a6e92325d3f262043b29d3fb96464192, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _text: {fileID: 1578372745682419700}
--- !u!1 &2956604755644281879
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2956604755644281872}
  - component: {fileID: 2956604755644281869}
  - component: {fileID: 2956604755644281868}
  - component: {fileID: 2956604755644281875}
  - component: {fileID: 2956604755644281874}
  m_Layer: 0
  m_Name: MenuPanel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2956604755644281872
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2956604755644281879}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.09, z: 0}
  m_LocalScale: {x: 0.34600002, y: 0.506, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2956604756026971515}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2956604755644281869
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2956604755644281879}
  m_Mesh: {fileID: 10210, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2956604755644281868
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2956604755644281879}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d9665615562752448b5140038771d26c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &2956604755644281875
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2956604755644281879}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d5e48d93b64a9ae4f9fd5a728c8f51af, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderers:
  - {fileID: 2956604755644281868}
  _vectorProperties: []
  _colorProperties: []
  _floatProperties: []
  _updateEveryFrame: 1
--- !u!114 &2956604755644281874
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2956604755644281879}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0a0ad4ecf30771d44bf163058922924b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _editor: {fileID: 2956604755644281875}
  _width: 0.34
  _height: 0.5
  _color: {r: 0, g: 0, b: 0, a: 0}
  _borderColor: {r: 0.3019608, g: 0.29411766, b: 0.28235295, a: 0.31764707}
  _radiusTopLeft: 0.015
  _radiusTopRight: 0.015
  _radiusBottomLeft: 0.015
  _radiusBottomRight: 0.015
  _borderInnerRadius: 0
  _borderOuterRadius: 0.003
--- !u!1 &2956604756026971513
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2956604756026971515}
  - component: {fileID: 2956604756026971514}
  m_Layer: 0
  m_Name: OculusInteractionSamplesButtonMenu
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2956604756026971515
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2956604756026971513}
  m_LocalRotation: {x: 0.09229593, y: -0.7010574, z: 0.09229593, w: 0.7010574}
  m_LocalPosition: {x: -0.598, y: 0.954, z: -0.208}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2956604755644281872}
  - {fileID: 3702246391893479347}
  - {fileID: 2750966032700234481}
  - {fileID: 4704050578149839651}
  - {fileID: 3319334036249952452}
  - {fileID: 3121620572106950464}
  - {fileID: 4201199032095678455}
  - {fileID: 1117717504046306990}
  - {fileID: 564885483794746568}
  - {fileID: 2592480246982458183}
  - {fileID: 113516263930846234}
  - {fileID: 967757323913424466}
  - {fileID: 8760979221775812723}
  - {fileID: 7742607238697044693}
  - {fileID: 7633234212804562280}
  - {fileID: 3186180914284037498}
  - {fileID: 3583042432202986915}
  - {fileID: 6536286299756309002}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 15, y: -90, z: 0}
--- !u!114 &2956604756026971514
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2956604756026971513}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8c4c1b38c49ffa449b585d54fcdc4a8c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &3817821213555168172
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7378433908484095872}
  - component: {fileID: 8680467721122692791}
  m_Layer: 0
  m_Name: More Info Button
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7378433908484095872
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3817821213555168172}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 430661997804954688}
  m_Father: {fileID: 4951791328163808189}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 160, y: -306}
  m_SizeDelta: {x: 284, y: 210}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &8680467721122692791
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3817821213555168172}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 59f8146938fff824cb5fd77236b75775, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 7
  m_Spacing: 0
  m_ChildForceExpandWidth: 0
  m_ChildForceExpandHeight: 0
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1 &3967975517389702408
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9158457109819786479}
  - component: {fileID: 9150915083465544602}
  - component: {fileID: 6023159343361899491}
  m_Layer: 0
  m_Name: Icon
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &9158457109819786479
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3967975517389702408}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3545885034791629778}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 247, y: -116}
  m_SizeDelta: {x: 28, y: 28}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &9150915083465544602
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3967975517389702408}
  m_CullTransparentMesh: 1
--- !u!114 &6023159343361899491
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3967975517389702408}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 8422188109611388855, guid: 64f884351318b694f942625580e64a8b,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &4427874573741788534
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6536286299756309002}
  - component: {fileID: 7863799357629973941}
  - component: {fileID: 8527429440641928543}
  - component: {fileID: 285764804364605237}
  - component: {fileID: 2266716703851890961}
  - component: {fileID: 1082381123487396408}
  m_Layer: 0
  m_Name: InfoPanel
  m_TagString: SamplesInfoPanel
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &6536286299756309002
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4427874573741788534}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.28, y: 0, z: 0}
  m_LocalScale: {x: 0.206, y: 0.326, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4951791328163808189}
  - {fileID: 3848724281939209195}
  m_Father: {fileID: 2956604756026971515}
  m_RootOrder: 17
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &7863799357629973941
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4427874573741788534}
  m_Mesh: {fileID: 10210, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &8527429440641928543
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4427874573741788534}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d9665615562752448b5140038771d26c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &285764804364605237
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4427874573741788534}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d5e48d93b64a9ae4f9fd5a728c8f51af, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _renderers:
  - {fileID: 8527429440641928543}
  _vectorProperties: []
  _colorProperties: []
  _floatProperties: []
  _updateEveryFrame: 1
--- !u!114 &2266716703851890961
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4427874573741788534}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0a0ad4ecf30771d44bf163058922924b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _editor: {fileID: 285764804364605237}
  _width: 0.2
  _height: 0.32
  _color: {r: 0.3019608, g: 0.29411766, b: 0.28235295, a: 0.32156864}
  _borderColor: {r: 0.3019608, g: 0.29411766, b: 0.28235295, a: 0.31764707}
  _radiusTopLeft: 0.015
  _radiusTopRight: 0.015
  _radiusBottomLeft: 0.015
  _radiusBottomRight: 0.015
  _borderInnerRadius: 0
  _borderOuterRadius: 0.003
--- !u!114 &1082381123487396408
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4427874573741788534}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc3d610027e65de40bcf0a2bc84ff198, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &5280754769573120716
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7909120988866759706}
  - component: {fileID: 1416064206667098934}
  - component: {fileID: 5335927052822908325}
  m_Layer: 0
  m_Name: Primary Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7909120988866759706
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5280754769573120716}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4951791328163808189}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 160, y: -35}
  m_SizeDelta: {x: 284, y: 34}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1416064206667098934
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5280754769573120716}
  m_CullTransparentMesh: 1
--- !u!114 &5335927052822908325
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5280754769573120716}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Welcome to
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: eb44739c484b1b54bbc1a0d4d9dd1a50, type: 2}
  m_sharedMaterial: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4293980400
  m_fontColor: {r: 0.9411765, g: 0.9411765, b: 0.9411765, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 22
  m_fontSizeBase: 22
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &8380012894417389167
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3154507624802441843}
  - component: {fileID: 1997105079520693018}
  - component: {fileID: 639228932767507449}
  m_Layer: 0
  m_Name: Secondary Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3154507624802441843
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8380012894417389167}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4951791328163808189}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 160, y: -138.5}
  m_SizeDelta: {x: 284, y: 125}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1997105079520693018
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8380012894417389167}
  m_CullTransparentMesh: 1
--- !u!114 &639228932767507449
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8380012894417389167}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: In the Metaverse, there are various ways to interact with virtual content.
    Here you can try out the features we use to build experiences.
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: dda8d4d183f898241969906ee2a03d81, type: 2}
  m_sharedMaterial: {fileID: 7032093815205813286, guid: dda8d4d183f898241969906ee2a03d81,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 2583691263
  m_fontColor: {r: 1, g: 1, b: 1, a: 0.6}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 18
  m_fontSizeBase: 18
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 1024
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &8421711603324929841
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 430661997804954688}
  - component: {fileID: 3334996605877935369}
  - component: {fileID: 141158860113810918}
  m_Layer: 0
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &430661997804954688
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8421711603324929841}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0.000030535164}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7378433908484095872}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 142, y: -185}
  m_SizeDelta: {x: 284, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &3334996605877935369
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8421711603324929841}
  m_CullTransparentMesh: 1
--- !u!114 &141158860113810918
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8421711603324929841}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 'Interaction SDK is free to download, to learn more click here:'
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: dda8d4d183f898241969906ee2a03d81, type: 2}
  m_sharedMaterial: {fileID: 7032093815205813286, guid: dda8d4d183f898241969906ee2a03d81,
    type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 2583691263
  m_fontColor: {r: 1, g: 1, b: 1, a: 0.6}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 18
  m_fontSizeBase: 18
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &8751655367734118751
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3545885034791629778}
  - component: {fileID: 2381622890766350377}
  m_Layer: 0
  m_Name: Call to Action
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &3545885034791629778
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8751655367734118751}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3668952689077263116}
  - {fileID: 9158457109819786479}
  m_Father: {fileID: 4951791328163808189}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 160, y: -264}
  m_SizeDelta: {x: 284, y: 130}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2381622890766350377
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8751655367734118751}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 30649d3a9faa99c48a7b1166b86bf2a0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding:
    m_Left: 0
    m_Right: 0
    m_Top: 0
    m_Bottom: 0
  m_ChildAlignment: 7
  m_Spacing: 0
  m_ChildForceExpandWidth: 0
  m_ChildForceExpandHeight: 0
  m_ChildControlWidth: 0
  m_ChildControlHeight: 0
  m_ChildScaleWidth: 0
  m_ChildScaleHeight: 0
  m_ReverseArrangement: 0
--- !u!1001 &265349183396383420
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: TransformerExamples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: SnapExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Snap
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.18
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4625356660798671994, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _doubleSided
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Snap
      objectReference: {fileID: 0}
    - target: {fileID: 8292861188116821575, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
        type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &4201199032095678455 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 265349183396383420}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &757194717875624696
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: HandGrabExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Hand Grab
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.12
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Hand Grab
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &3702246391893479347 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 757194717875624696}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &903666308009639584
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 6536286299756309002}
    m_Modifications:
    - target: {fileID: 2778893221137343601, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _width
      value: 3.6
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 1082381123487396408}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: HandleUrlButton
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SamplesInfoPanel, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: https://developer.oculus.com/documentation/unity/unity-isdk-interaction-sdk-overview/
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Button
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.24271841
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.15337421
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.05
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.368
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Interaction SDK
      objectReference: {fileID: 0}
    - target: {fileID: 6781307535824638274, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 3.6
      objectReference: {fileID: 0}
    - target: {fileID: 9132882590141288153, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 3.6
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &3848724281939209195 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 903666308009639584}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1346898105667392011
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: TransformerExamples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: TransformerExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Transformer
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.12
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4625356660798671994, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _doubleSided
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Transformers
      objectReference: {fileID: 0}
    - target: {fileID: 8292861188116821575, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
        type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &3121620572106950464 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 1346898105667392011}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1573035306801205297
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: TransformerExamples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: PanelWithManipulators
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: PanelWithManipulators
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 15
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.3
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4625356660798671994, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _doubleSided
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Panel With Manipulators
      objectReference: {fileID: 0}
    - target: {fileID: 8292861188116821575, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
        type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &3186180914284037498 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 1573035306801205297}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1725926868789876111
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: TransformerExamples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: HandGrabUseExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Snap
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.06
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4625356660798671994, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _doubleSided
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Hand Grab Use
      objectReference: {fileID: 0}
    - target: {fileID: 8292861188116821575, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
        type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &3319334036249952452 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 1725926868789876111}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1880616660295233036
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: PoseExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Poses
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 9
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Poses
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &2592480246982458183 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 1880616660295233036}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2291902198127022010
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: TouchGrabExamples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: TouchGrabExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Touch Grab
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.06
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4625356660798671994, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _doubleSided
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Touch Grab
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_fontSizeBase
      value: 36
      objectReference: {fileID: 0}
    - target: {fileID: 8292861188116821575, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
        type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &2750966032700234481 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 2291902198127022010}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3786744190796004121
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: LocomotionExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Locomotion
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 11
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.12
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Locomotion
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &967757323913424466 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 3786744190796004121}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3918681199847244773
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: PokeExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Poke
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.12
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Poke
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &1117717504046306990 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 3918681199847244773}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4066846804478314833
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: GestureExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Gestures
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.06
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Gestures
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &113516263930846234 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 4066846804478314833}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4482767958540769667
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: RayExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Ray
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.06
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Ray
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &564885483794746568 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 4482767958540769667}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4645003148476915000
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: TransformerExamples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: BodyPoseDetectionExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: BodyPoses
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 12
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.18
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4625356660798671994, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _doubleSided
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Body Poses
      objectReference: {fileID: 0}
    - target: {fileID: 8292861188116821575, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
        type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &8760979221775812723 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 4645003148476915000}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &5768460199003953187
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: TransformerExamples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: ComprehensiveRigExample
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: ComprehensiveRig
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 14
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.24
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4625356660798671994, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _doubleSided
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Comprehensive Rig
      objectReference: {fileID: 0}
    - target: {fileID: 8292861188116821575, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
        type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &7633234212804562280 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 5768460199003953187}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &5949353707772108702
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: TransformerExamples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: ConcurrentHandsControllersExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: ConcurrentHandsControllers
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 13
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0.24
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4625356660798671994, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _doubleSided
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: 'Concurrent Hands

        and Controllers'
      objectReference: {fileID: 0}
    - target: {fileID: 8292861188116821575, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
        type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &7742607238697044693 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 5949353707772108702}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8695106160805054056
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2956604756026971515}
    m_Modifications:
    - target: {fileID: 1406319260258202752, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: DistanceGrabExamples
      objectReference: {fileID: 0}
    - target: {fileID: 1784097173527018851, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenUnselect.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 2956604756026971514}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: Load
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.SceneLoader, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_StringArgument
      value: DistanceGrabExamples
      objectReference: {fileID: 0}
    - target: {fileID: 3718601612112823234, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _whenRelease.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234762, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Name
      value: Distance Grab
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: -0.08
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4625356660798671994, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: _doubleSided
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6198825956437652943, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_text
      value: Distance Grab
      objectReference: {fileID: 0}
    - target: {fileID: 8292861188116821575, guid: 323cb65d2594c79499a11a99ff0fb347,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: -5839354330806206608, guid: eb44739c484b1b54bbc1a0d4d9dd1a50,
        type: 2}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 323cb65d2594c79499a11a99ff0fb347, type: 3}
--- !u!4 &4704050578149839651 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4171210596153234763, guid: 323cb65d2594c79499a11a99ff0fb347,
    type: 3}
  m_PrefabInstance: {fileID: 8695106160805054056}
  m_PrefabAsset: {fileID: 0}
