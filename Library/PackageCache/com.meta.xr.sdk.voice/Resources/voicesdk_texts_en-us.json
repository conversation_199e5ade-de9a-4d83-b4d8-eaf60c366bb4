{"LanguageID": "en-us", "SetupTitleLabel": "Welcome to Voice SDK", "SetupHeaderLabel": "Build Natural Language Experiences", "SetupSubheaderLabel": "Empower people to use your product with voice and text.", "SetupLanguageLabel": "Select language to use Built-In NLP", "AboutTitleLabel": "About", "AboutCloseLabel": "Close", "AboutVoiceSdkVersionLabel": "Voice SDK Version", "AboutWitSdkVersionLabel": "Wit.ai SDK Version", "AboutWitApiVersionLabel": "Wit.ai API Version", "AboutTutorialButtonLabel": "Tutorials Website", "AboutTutorialButtonUrl": "https://developer.oculus.com/documentation/unity/voice-sdk-tutorials-overview/", "SettingsTitleLabel": "Voice SDK Settings", "UnderstandingViewerTitleLabel": "Understanding Viewer", "BuiltInAppBtnLabel": "Open Built-In NLP Documentation", "BuiltInAppUrl": "https://developer.oculus.com/documentation/unity/voice-sdk-built-in", "VoiceDocsUrl": "https://developer.oculus.com/documentation/unity/voice-sdk-integrate-voice"}