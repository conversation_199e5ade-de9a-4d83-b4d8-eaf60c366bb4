{"name": "Meta.WitAi.Editor", "rootNamespace": "", "references": ["Meta.WitAi.Lib", "Meta.WitAi", "Meta.WitAi.Conduit", "Meta.WitAi.Conduit.Editor", "Meta.WitAI.Lib.Editor", "Meta.VoiceSDK.Mic.Other", "VoiceSDK.Telemetry", "Meta.VoiceSDK.Mic.Common"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"define": "OVR_UNITY_PACKAGE_MANAGER", "name": "com.meta.xr.sdk.voice", "expression": ""}], "noEngineReferences": false}