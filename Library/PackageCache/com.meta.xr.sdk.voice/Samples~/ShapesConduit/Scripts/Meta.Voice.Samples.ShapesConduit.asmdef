{"name": "Meta.Voice.Samples.ShapesConduit", "rootNamespace": "", "references": ["Meta.WitAi", "Meta.WitAi.Lib", "VoiceSDK.Runtime", "Meta.WitAi.Conduit"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"define": "OVR_UNITY_PACKAGE_MANAGER", "name": "com.meta.xr.sdk.voice", "expression": ""}], "noEngineReferences": false}