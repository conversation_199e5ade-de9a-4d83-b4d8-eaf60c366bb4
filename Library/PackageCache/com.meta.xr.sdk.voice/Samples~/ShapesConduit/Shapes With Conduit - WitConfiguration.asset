%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ae5a46bda3295124c99b0e6537ac7252, type: 3}
  m_Name: Shapes With Conduit - WitConfiguration
  m_EditorClassIdentifier: 
  _clientAccessToken: JYSZFUVTNXFZ4N3LAHJMDS6JOET2IBVP
  _appInfo:
    name: voicesdk_samples_shapes
    id: 04b6d9fc-60b7-4d9e-8473-9290b9d1243f
    lang: en
    isPrivate: 0
    createdAt: 2023-04-04T10:35:19-0700
    trainingStatus: 1
    lastTrainDuration: 36
    lastTrainedAt: 2023-04-04T10:36:17-0700
    nextTrainAt: 2023-04-04T10:35:41-0700
    intents:
    - id: 196347333139222
      name: change_color
      entities:
      - name: color:color
        id: 629054192387147
      - name: shape:shape
        id: 239839848416213
    entities:
    - name: color
      id: 183423817885903
      lookups:
      - free-text
      - keywords
      roles:
      - name: color
        id: 629054192387147
      keywords:
      - keyword: black
        synonyms:
        - black
      - keyword: blue
        synonyms:
        - blue
      - keyword: brown
        synonyms:
        - brown
      - keyword: gold
        synonyms:
        - gold
      - keyword: green
        synonyms:
        - green
      - keyword: magenta
        synonyms:
        - magenta
      - keyword: maroon
        synonyms:
        - maroon
        - red
      - keyword: orange
        synonyms:
        - orange
      - keyword: pink
        synonyms:
        - pink
      - keyword: purple
        synonyms:
        - purple
      - keyword: red
        synonyms:
        - red
      - keyword: violet
        synonyms:
        - violet
      - keyword: white
        synonyms:
        - white
      - keyword: yellow
        synonyms:
        - yellow
    - name: shape
      id: 2277400329106527
      lookups:
      - keywords
      roles:
      - name: shape
        id: 239839848416213
      keywords:
      - keyword: capsule
        synonyms:
        - capsule
      - keyword: cube
        synonyms:
        - cube
        - square
      - keyword: cylinder
        synonyms:
        - cylinder
        - tube
      - keyword: sphere
        synonyms:
        - ball
        - sphere
    traits: []
    versionTags: []
    voices:
    - name: billie
      locale: en_US
      gender: female
      styles: []
      supported_features: []
    - name: bru
      locale: en_US
      gender: male
      styles: []
      supported_features: []
    - name: dungeon
      locale: en_US
      gender: male
      styles: []
      supported_features: []
    - name: jane_austen
      locale: en_GB
      gender: female
      styles: []
      supported_features: []
    - name: Maria
      locale: es_ES
      gender: female
      styles:
      - default
      - soft
      - projected
      - fast
      - formal
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: Skully
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - projected
      - fast
      - formal
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$British Butler
      locale: en_GB
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Cael
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Cam
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Carl
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Cartoon Baby
      locale: en_US
      gender: nonbinary
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Cartoon Kid
      locale: en_US
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Cartoon Villain
      locale: en_US
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Charlie
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Cockney Accent
      locale: en_GB
      gender: nonbinary
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Cody
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Colin
      locale: en_CA
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Connor
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Cooper
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Disaffected
      locale: en_US
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Hollywood
      locale: en_US
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Kenyan Accent
      locale: en_US
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Overconfident
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Pirate
      locale: en_GB
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Prospector
      locale: en_US
      gender: male
      styles:
      - default
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Railey
      locale: en_US
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Rebecca
      locale: en_US
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Remi
      locale: en_US
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Rosie
      locale: en_CA
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Rubie
      locale: en_US
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Southern Accent
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Surfer
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Trendy
      locale: en_US
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Vampire
      locale: en_US
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Whimsical
      locale: en_GB
      gender: female
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: wit$Wizard
      locale: en_GB
      gender: male
      styles:
      - default
      - soft
      - formal
      - fast
      - projected
      supported_features:
      - style
      - pitch
      - speed
      - sfx
      - viseme_events
      - phoneme_events
      - word_events
    - name: zach
      locale: en_US
      gender: male
      styles: []
      supported_features: []
  _configData:
  - {fileID: 8582304952152201332}
  - {fileID: 8943820230535105028}
  _configurationId: 4e21a24e472ab684e84db753427d5687
  timeoutMS: 10000
  endpointConfiguration:
    _uriScheme: 
    _authority: 
    _port: 0
    _witApiVersion: 
    _message: 
    _speech: 
    _dictation: 
    _synthesize: 
    _event: 
    _converse: 
  isDemoOnly: 1
  useConduit: 1
  _manifestLocalPath: ConduitManifest-6fde361e-cbeb-4eea-bebf-1bf6086b5823.json
  excludedAssemblies:
  - Meta.Voice.Samples.Chess, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
  - Meta.Voice.Samples.WitShapes, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
  relaxedResolution: 0
--- !u!114 &8582304952152201332
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4095e5fe4b954920a6705817d3fd6744, type: 3}
  m_Name: WitCharacterData
  m_EditorClassIdentifier: 
  characters: []
--- !u!114 &8943820230535105028
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d1c2918ba0e7451694416d42d69e0944, type: 3}
  m_Name: WitComposerData
  m_EditorClassIdentifier: 
  canvases: []
