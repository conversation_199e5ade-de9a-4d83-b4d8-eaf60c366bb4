{"name": "Meta.Voice.Samples.LiveUnderstanding", "rootNamespace": "", "references": ["Meta.WitAi", "Meta.WitAi.Lib"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"define": "OVR_UNITY_PACKAGE_MANAGER", "name": "com.meta.xr.sdk.voice", "expression": ""}], "noEngineReferences": false}