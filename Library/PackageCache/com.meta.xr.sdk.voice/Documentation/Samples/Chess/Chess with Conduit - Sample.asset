%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0e3df96a2504a0085121315689deb30, type: 3}
  m_Name: Chess with Conduit - Sample
  m_EditorClassIdentifier: 
  title: Chess with Conduit Sample
  description: A sample that uses voice interactions to perform simple chess interactions. 
    This demo is also set up with Conduit for easier implementation and quicker performance.
  tileImage: {fileID: 2800000, guid: 8d4779f100af3f5458fcd579406b3c9a, type: 3}
  screenshot: {fileID: 0}
  sceneReference: {fileID: 102900000, guid: bc6c1dc4788d56e40bd6ed2286344a1b, type: 3}
  packageSampleName: Chess with Conduit
  sampleSetId: Voice SDK Samples
  priority: 6
