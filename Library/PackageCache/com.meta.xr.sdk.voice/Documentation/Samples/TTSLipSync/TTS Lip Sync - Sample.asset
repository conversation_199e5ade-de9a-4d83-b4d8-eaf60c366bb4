%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0e3df96a2504a0085121315689deb30, type: 3}
  m_Name: TTS Lip Sync - Sample
  m_EditorClassIdentifier: 
  title: TTS Lip Sync Sample
  description: A sample scene showing multiple lip sync approaches using streamed
    viseme data from Wit TTS.
  tileImage: {fileID: 2800000, guid: 918fa9a663d1c2a469ab70a83ed2a45d, type: 3}
  sceneReference: {fileID: 102900000, guid: cff456c93217ae44284e70f0a74dd626, type: 3}
  packageSampleName: TTS Lip Sync
  sampleSetId: TTS Samples
  priority: 2
