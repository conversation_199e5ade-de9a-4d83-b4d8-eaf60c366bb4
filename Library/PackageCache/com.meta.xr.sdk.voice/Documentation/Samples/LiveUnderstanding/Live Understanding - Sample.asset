%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0e3df96a2504a0085121315689deb30, type: 3}
  m_Name: Live Understanding - Sample
  m_EditorClassIdentifier: 
  title: Live Understanding Sample
  description: A sample that demonstrates short response handling via live understanding. 
    Simply activate a voice interaction and state a color for an immediate deactivation
    & color application.
  tileImage: {fileID: 2800000, guid: 46f63a0a90188954eb3a578b4defe785, type: 3}
  screenshot: {fileID: 0}
  sceneReference: {fileID: 102900000, guid: 39670f1c7d5f53949b4cbbd6ae757ba8, type: 3}
  packageSampleName: Live Understanding
  sampleSetId: Voice SDK Samples
  priority: 4
