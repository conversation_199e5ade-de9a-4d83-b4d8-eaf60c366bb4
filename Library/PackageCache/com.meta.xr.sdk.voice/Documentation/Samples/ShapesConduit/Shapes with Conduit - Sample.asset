%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0e3df96a2504a0085121315689deb30, type: 3}
  m_Name: Shapes with Conduit - Sample
  m_EditorClassIdentifier: 
  title: Shapes with Conduit Sample
  description: 'This demo showcases the capabilities of the Voice SDK in recognizing
    voice commands and translating them into actions within a 2D scene using Conduit
    to trigger methods directly with [MatchIntent] annotations.


    It is best
    used in play mode in the editor so you can see how voice commands are processed
    in real time.


    In this demo, users can activate and say the name of a shape
    and the color you would like it to change to. For example, "Make the Cube Red"'
  tileImage: {fileID: 2800000, guid: 50d3d6b80001a124e80bddda429f963c, type: 3}
  screenshot: {fileID: 0}
  sceneReference: {fileID: 102900000, guid: 5aa24c94acb775043bd5c44d48c21d68, type: 3}
  packageSampleName: Shapes with Conduit
  sampleSetId: Voice SDK Samples
  priority: 1
