%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a0e3df96a2504a0085121315689deb30, type: 3}
  m_Name: TTS Voices - Sample
  m_EditorClassIdentifier: 
  title: TTS Voices Sample
  description: A sample scene for performing text-to-speech requests by customizing
    two voices and having them speak.
  tileImage: {fileID: 2800000, guid: 6a3a8152e0be7454fa26d9ba0ad6c0a7, type: 3}
  sceneReference: {fileID: 102900000, guid: e99baad8337e84a4f93e76c76b8ce49b, type: 3}
  packageSampleName: TTS Voices
  sampleSetId: TTS Samples
  priority: 1
