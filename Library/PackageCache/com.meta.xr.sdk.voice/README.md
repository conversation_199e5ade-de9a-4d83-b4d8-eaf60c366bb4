![alt_text](Resources/voicesdk_heroart.png "image_tooltip")

# Voice SDK
The Voice SDK enables you to bring voice interactions to your app experiences. Use the Voice SDK to enhance the AR/VR experience with more natural and flexible ways for people to interact with the app. For example, voice commands can shortcut controller actions with a single phrase, or interactive conversation can make the app more engaging.

Powered by the [Wit.ai](https://wit.ai/) Natural Language Understanding (NLU) service, the Voice SDK is compatible with Oculus headsets, mobile devices, and other third-party platforms. Wit.ai is easy to sign up for, and it’s free. Using Wit, you can easily train apps to use voice commands with no prior AI/ML knowledge required. The combination of the Voice SDK and Wit.ai empowers you to focus on the creative and functional aspects of the app, while enabling powerful voice interactions.

### **Features**
* Enable custom app voice experiences with [Wit.ai](https://wit.ai/)
* Easy-to use Unity plugin, with other toolkit support coming in the future.
* Automatic speech recognition to process voice requests into text.
* Natural language processing (NLP) for processing text into user intents for app voice experiences.
* 50+ common built-in intents, entities, and traits immediately ready for use.
* Built-in activation methods
* Cross-platform support so that you can integrate voice once, and then make it work for Oculus and other AR/VR devices.
* Personalize voice requests based on the user and your app state with dynamic entities.
* High quality voice experience with low latency, with real-time transcription
* High quality English language support, along with an early preview of 12 other Oculus device languages.

### Extensions
There are further extensions to Voice SDK available on the store, including packages for handling Wit.ai's visual conversation composer, and fast dictation.

### **Common Use Cases**
Using the Voice SDK, you can turn their game into a powerful voice experience that will help create an even greater sense of immersion.

Here are a few examples of what you can enable with voice experiences:
* Voice navigation and search. Navigating unfamiliar nested menus can be time-consuming and difficult, especially when trying to type into VR keyboards with a controller. Voice SDK can help your users get where they want to go quickly and with far less effort.
* Voice FAQ. Looking for instructions is a good way to frustrate your user and break their focus on your app. But what if new players could just ask when they need a hint? Not only could that be helpful, but it might also keep them engaged longer.
* Voice-driven gameplay and experiences. Immersing yourself completely in your game can significantly increase your enjoyment. Imagine winning a battle by activating a magic spell with your voice or actually talking to the characters in your story. Voice-driven gameplay can make experiences more immersive, even close to magical.

## Dependencies
This package requires the Voice SDK Telemetry package, which will install automatically when installing this package from the Unity Asset Store.
