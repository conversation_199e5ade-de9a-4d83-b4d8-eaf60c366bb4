{"name": "Oculus.Interaction.OVR", "rootNamespace": "", "references": ["GUID:2a230cb87a1d3ba4a98bdc0ddae76e6c", "GUID:f64c9ebcd7899c3448a08dc9f9ddbe30"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "Unity", "expression": "", "define": "ISDK_OPENXR_HAND"}, {"define": "OVR_UNITY_PACKAGE_MANAGER", "name": "com.meta.xr.sdk.interaction.ovr", "expression": ""}], "noEngineReferences": false}