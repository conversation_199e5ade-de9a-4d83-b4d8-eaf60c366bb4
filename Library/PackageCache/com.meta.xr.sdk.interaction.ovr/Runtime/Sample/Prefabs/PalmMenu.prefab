%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &93322829
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 93322830}
  - component: {fileID: 93322833}
  - component: {fileID: 93322832}
  - component: {fileID: 93322831}
  m_Layer: 5
  m_Name: Icon down
  m_TagString: QDSUIIcon
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &93322830
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 93322829}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4688635637041728498}
  m_RootOrder: 6
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 24, y: 24}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &93322833
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 93322829}
  m_CullTransparentMesh: 1
--- !u!114 &93322832
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 93322829}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: -902118921973858234, guid: 64f884351318b694f942625580e64a8b,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &93322831
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 93322829}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 306cc8c2b49d7114eaa3623786fc2126, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreLayout: 0
  m_MinWidth: -1
  m_MinHeight: -1
  m_PreferredWidth: 24
  m_PreferredHeight: -1
  m_FlexibleWidth: -1
  m_FlexibleHeight: -1
  m_LayoutPriority: 1
--- !u!1 &361245620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 361245621}
  - component: {fileID: 361245623}
  - component: {fileID: 361245622}
  m_Layer: 5
  m_Name: Label (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &361245621
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 361245620}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5486153885739963364}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 83.77, y: 16}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &361245623
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 361245620}
  m_CullTransparentMesh: 1
--- !u!114 &361245622
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 361245620}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Rotate
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 11
  m_fontSizeBase: 11
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: -0.32943538, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &1297185750
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1297185751}
  - component: {fileID: 1297185754}
  - component: {fileID: 1297185753}
  - component: {fileID: 1297185752}
  m_Layer: 5
  m_Name: Icon up
  m_TagString: QDSUIIcon
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1297185751
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1297185750}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4688635637041728498}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 24, y: 24}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1297185754
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1297185750}
  m_CullTransparentMesh: 1
--- !u!114 &1297185753
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1297185750}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: -9161077761368379178, guid: 64f884351318b694f942625580e64a8b,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &1297185752
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1297185750}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 306cc8c2b49d7114eaa3623786fc2126, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreLayout: 0
  m_MinWidth: -1
  m_MinHeight: -1
  m_PreferredWidth: 24
  m_PreferredHeight: -1
  m_FlexibleWidth: -1
  m_FlexibleHeight: -1
  m_LayoutPriority: 1
--- !u!1 &2050668066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2050668067}
  - component: {fileID: 2050668070}
  - component: {fileID: 2050668069}
  - component: {fileID: 2050668068}
  m_Layer: 5
  m_Name: Icon right
  m_TagString: QDSUIIcon
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2050668067
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050668066}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4688635637041728498}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 24, y: 24}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2050668070
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050668066}
  m_CullTransparentMesh: 1
--- !u!114 &2050668069
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050668066}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 8422188109611388855, guid: 64f884351318b694f942625580e64a8b,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &2050668068
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2050668066}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 306cc8c2b49d7114eaa3623786fc2126, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreLayout: 0
  m_MinWidth: -1
  m_MinHeight: -1
  m_PreferredWidth: 24
  m_PreferredHeight: -1
  m_FlexibleWidth: -1
  m_FlexibleHeight: -1
  m_LayoutPriority: 1
--- !u!1 &456091117121181287
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7679244915230753668}
  - component: {fileID: 4903066374885599388}
  - component: {fileID: 688835361857153789}
  m_Layer: 5
  m_Name: Label
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &7679244915230753668
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 456091117121181287}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1950695118892756423}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 83.77, y: 16}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &4903066374885599388
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 456091117121181287}
  m_CullTransparentMesh: 1
--- !u!114 &688835361857153789
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 456091117121181287}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 'Elevation: ?'
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 11
  m_fontSizeBase: 11
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: -0.32943538, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &920657816654705496
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 920657816654705503}
  - component: {fileID: 920657816654705500}
  - component: {fileID: 920657816654705501}
  - component: {fileID: 920657816654705502}
  m_Layer: 5
  m_Name: IconOn
  m_TagString: QDSUIIcon
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &920657816654705503
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 920657816654705496}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5486153885739963364}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 24, y: 24}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &920657816654705500
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 920657816654705496}
  m_CullTransparentMesh: 1
--- !u!114 &920657816654705501
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 920657816654705496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 5685811874231246715, guid: 64f884351318b694f942625580e64a8b,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &920657816654705502
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 920657816654705496}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 306cc8c2b49d7114eaa3623786fc2126, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreLayout: 0
  m_MinWidth: -1
  m_MinHeight: -1
  m_PreferredWidth: 24
  m_PreferredHeight: -1
  m_FlexibleWidth: -1
  m_FlexibleHeight: -1
  m_LayoutPriority: 1
--- !u!1 &920657816902415285
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 920657816902415284}
  m_Layer: 4
  m_Name: DirectionToggle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &920657816902415284
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 920657816902415285}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8743731164162534091}
  m_Father: {fileID: 1717163970241906090}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 100, y: 0}
  m_SizeDelta: {x: 50, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &920657817034863631
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 920657817034863630}
  - component: {fileID: 920657817034863628}
  - component: {fileID: 920657817034863629}
  m_Layer: 5
  m_Name: DirectionLabel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &920657817034863630
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 920657817034863631}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4688635637041728498}
  m_RootOrder: 7
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 83.77, y: 16}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &920657817034863628
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 920657817034863631}
  m_CullTransparentMesh: 1
--- !u!114 &920657817034863629
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 920657817034863631}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Direction
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 11
  m_fontSizeBase: 11
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: -0.32943538, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &920657817689648403
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 920657817689648402}
  m_Layer: 4
  m_Name: ColorButton
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &920657817689648402
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 920657817689648403}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8743731162625172927}
  m_Father: {fileID: 1717163970241906090}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 50, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &920657818545653934
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 920657818545653929}
  m_Layer: 4
  m_Name: RotateToggle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &920657818545653929
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 920657818545653934}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8386867489180447453}
  m_Father: {fileID: 1717163970241906090}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 50, y: 0}
  m_SizeDelta: {x: 50, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1516118225342696420
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5694578098572428551}
  - component: {fileID: 5901107231644713723}
  - component: {fileID: 1797342255779503652}
  m_Layer: 4
  m_Name: Surface
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5694578098572428551
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1516118225342696420}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163969150618336}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5901107231644713723
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1516118225342696420}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: efd927768041afd4d90e5d822283f0f4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _planeSurface: {fileID: 1717163969065839228}
  _clippers:
  - {fileID: 1797342255779503652}
--- !u!114 &1797342255779503652
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1516118225342696420}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e08ab46e8fb05dc46b34e54466dc11e3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _position: {x: 0, y: 0, z: 0}
  _size: {x: 1, y: 1, z: 1}
--- !u!1 &1717163968311915349
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163968311915338}
  m_Layer: 4
  m_Name: Pagination
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717163968311915338
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163968311915349}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1717163969868409401}
  - {fileID: 1717163968731331226}
  - {fileID: 1717163968862996931}
  - {fileID: 1717163968675631230}
  - {fileID: 1717163969124497908}
  - {fileID: 1717163970158409573}
  m_Father: {fileID: 1717163969684085809}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 1}
  m_AnchorMax: {x: 0.5, y: 1}
  m_AnchoredPosition: {x: 0, y: -6}
  m_SizeDelta: {x: 80, y: 8}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1717163968675631225
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163968675631230}
  - component: {fileID: 1717163968675631228}
  - component: {fileID: 1717163968675631231}
  m_Layer: 4
  m_Name: Dot4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717163968675631230
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163968675631225}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163968311915338}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 4, y: 0}
  m_SizeDelta: {x: 2.5, y: 2.5}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1717163968675631228
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163968675631225}
  m_CullTransparentMesh: 1
--- !u!114 &1717163968675631231
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163968675631225}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: -1938207125527859653, guid: f576801ac9141ab4c8899dfdada97e19,
    type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &1717163968731331301
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163968731331226}
  - component: {fileID: 1717163968731331224}
  - component: {fileID: 1717163968731331227}
  m_Layer: 4
  m_Name: Dot2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717163968731331226
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163968731331301}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163968311915338}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -4, y: 0}
  m_SizeDelta: {x: 2.5, y: 2.5}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1717163968731331224
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163968731331301}
  m_CullTransparentMesh: 1
--- !u!114 &1717163968731331227
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163968731331301}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: -1938207125527859653, guid: f576801ac9141ab4c8899dfdada97e19,
    type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &1717163968862996930
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163968862996931}
  - component: {fileID: 1717163968862996929}
  - component: {fileID: 1717163968862996928}
  m_Layer: 4
  m_Name: Dot3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717163968862996931
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163968862996930}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163968311915338}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 2.5, y: 2.5}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1717163968862996929
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163968862996930}
  m_CullTransparentMesh: 1
--- !u!114 &1717163968862996928
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163968862996930}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: -1938207125527859653, guid: f576801ac9141ab4c8899dfdada97e19,
    type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &1717163969006891934
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163969006891932}
  - component: {fileID: 1717163969006891935}
  m_Layer: 4
  m_Name: Swipe
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1717163969006891932
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969006891934}
  m_LocalRotation: {x: 0.087894686, y: -0.9957202, z: 0.02778364, w: 0.0066275457}
  m_LocalPosition: {x: 0.09900001, y: -0.110000014, z: -0.02900002}
  m_LocalScale: {x: 0.027999995, y: 0.014000005, z: 0.014000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163970010240501}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &1717163969006891935
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969006891934}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: 152aafa4bc75a894f80f0da543555c4b, type: 3}
  m_PlayOnAwake: 0
  m_Volume: 0.34
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &1717163969065839230
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163969065839222}
  - component: {fileID: 1717163969065839231}
  - component: {fileID: 1717163969065839217}
  - component: {fileID: 1717163969065839216}
  - component: {fileID: 1717163969065839219}
  - component: {fileID: 1717163969065839218}
  - component: {fileID: 1717163969065839229}
  - component: {fileID: 1717163969065839228}
  m_Layer: 4
  m_Name: Unity Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717163969065839222
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969065839230}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.001, y: 0.001, z: 0.001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1717163969735147373}
  - {fileID: 1717163969684085809}
  m_Father: {fileID: 1717163969150618336}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 80, y: 80}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!223 &1717163969065839231
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969065839230}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: 0
  m_SortingOrder: 1
  m_TargetDisplay: 0
--- !u!114 &1717163969065839217
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969065839230}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 1
--- !u!114 &1717163969065839216
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969065839230}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4294967295
--- !u!222 &1717163969065839219
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969065839230}
  m_CullTransparentMesh: 1
--- !u!114 &1717163969065839218
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969065839230}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 72494806423646354, guid: f576801ac9141ab4c8899dfdada97e19, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &1717163969065839229
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969065839230}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ********************************, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_ShowMaskGraphic: 1
--- !u!114 &1717163969065839228
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969065839230}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9cf2a74d69b1c1e41916d2a7afdff5be, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _facing: 0
  _doubleSided: 0
--- !u!1 &1717163969124497911
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163969124497908}
  - component: {fileID: 1717163969124497898}
  - component: {fileID: 1717163969124497909}
  m_Layer: 4
  m_Name: Dot5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717163969124497908
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969124497911}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163968311915338}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 8, y: 0}
  m_SizeDelta: {x: 2.5, y: 2.5}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1717163969124497898
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969124497911}
  m_CullTransparentMesh: 1
--- !u!114 &1717163969124497909
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969124497911}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: -1938207125527859653, guid: f576801ac9141ab4c8899dfdada97e19,
    type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &1717163969137348950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163969137348951}
  - component: {fileID: 1717163969137348948}
  m_Layer: 4
  m_Name: OpenMenu
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1717163969137348951
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969137348950}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163970010240501}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &1717163969137348948
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969137348950}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: 44420cc82247b8c41b2a11399c3dda75, type: 3}
  m_PlayOnAwake: 0
  m_Volume: 0.34
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &1717163969150618348
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163969150618336}
  - component: {fileID: 1717163969150618339}
  - component: {fileID: 1717163969150618338}
  - component: {fileID: 1717163969150618349}
  m_Layer: 4
  m_Name: CanvasMenu
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1717163969150618336
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969150618348}
  m_LocalRotation: {x: -0, y: 1, z: -0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: 0.03}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1717163969065839222}
  - {fileID: 5694578098572428551}
  m_Father: {fileID: 3769177771002880972}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 180, z: 0}
--- !u!114 &1717163969150618339
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969150618348}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 317e663e2bb60ea408fe22b908b59295, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _interactorFilters:
  - {fileID: 3769177771078722729}
  _maxInteractors: -1
  _maxSelectingInteractors: -1
  _data: {fileID: 0}
  _pointableElement: {fileID: 1717163969150618338}
  _surfacePatch: {fileID: 5901107231644713723}
  _enterHoverNormal: 0.02
  _enterHoverTangent: 0
  _exitHoverNormal: 0.03
  _exitHoverTangent: 0
  _cancelSelectNormal: 0
  _cancelSelectTangent: 0.01
  _minThresholds:
    Enabled: 0
    MinNormal: 0.01
  _dragThresholds:
    Enabled: 1
    DragNormal: 0.005
    DragTangent: 0.005
    DragEaseCurve:
      _animationCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _animationLength: 0.05
  _positionPinning:
    Enabled: 0
    MaxPinDistance: 0.002
    PinningEaseCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0.2
        value: 0
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    ResyncCurve:
      _animationCurve:
        serializedVersion: 2
        m_Curve:
        - serializedVersion: 3
          time: 0
          value: 0
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        - serializedVersion: 3
          time: 1
          value: 1
          inSlope: 0
          outSlope: 0
          tangentMode: 0
          weightedMode: 0
          inWeight: 0
          outWeight: 0
        m_PreInfinity: 2
        m_PostInfinity: 2
        m_RotationOrder: 4
      _animationLength: 0.2
  _recoilAssist:
    Enabled: 1
    UseDynamicDecay: 0
    DynamicDecayCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 50
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 0.9
        value: 0.5
        inSlope: -47
        outSlope: -47
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    UseVelocityExpansion: 0
    VelocityExpansionMinSpeed: 0.4
    VelocityExpansionMaxSpeed: 1.4
    VelocityExpansionDistance: 0.055
    VelocityExpansionDecayRate: 0.125
    ExitDistance: 0.005
    ReEnterDistance: 0.005
  _closeDistanceThreshold: 0.001
  _tiebreakerScore: 0
--- !u!114 &1717163969150618338
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969150618348}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3ffe41fe81087fa41a2062cc69b99615, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _transferOnSecondSelection: 0
  _addNewPointsToFront: 0
  _forwardElement: {fileID: 0}
  _canvas: {fileID: 1717163969065839231}
--- !u!114 &1717163969150618349
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969150618348}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f5626a0b1dc955a43be59ce7ea116678, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _pointableCanvas: {fileID: 1717163969150618338}
  _suppressWhileDragging: 1
  _whenBeginHighlight:
    m_PersistentCalls:
      m_Calls: []
  _whenEndHighlight:
    m_PersistentCalls:
      m_Calls: []
  _whenSelectedHovered:
    m_PersistentCalls:
      m_Calls: []
  _whenSelectedEmpty:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselectedHovered:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselectedEmpty:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &1717163969207122123
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163969207122120}
  - component: {fileID: 1717163969207122121}
  - component: {fileID: 1717163969207122126}
  m_Layer: 4
  m_Name: BasicPokeCanvasPressAudio
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1717163969207122120
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969207122123}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163970010240501}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &1717163969207122121
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969207122123}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &1717163969207122126
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969207122123}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 925ef87c5bafc37469a2f7ec825dee4b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _audioSource: {fileID: 0}
  _audioClips:
  - {fileID: 8300000, guid: 0ae074243bcbf57468d433ad4f78acbb, type: 3}
  _volume: 0.5
  _volumeRandomization:
    _useRandomRange: 0
    _min: 0
    _max: 0
  _pitch: 1
  _pitchRandomization:
    _useRandomRange: 0
    _min: 0
    _max: 0
  _spatialize: 1
  _loop: 0
  _chanceToPlay: 100
  _playOnStart: 0
--- !u!1 &1717163969535427428
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163969535427354}
  - component: {fileID: 1717163969535427429}
  m_Layer: 4
  m_Name: HideMenu
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1717163969535427354
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969535427428}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163970010240501}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &1717163969535427429
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969535427428}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 8300000, guid: 4393389c4e9969940bfda2dc7f786204, type: 3}
  m_PlayOnAwake: 0
  m_Volume: 0.34
  m_Pitch: 1
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!1 &1717163969684085808
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163969684085809}
  - component: {fileID: 1717163969684085812}
  - component: {fileID: 1717163969684085815}
  - component: {fileID: 1717163969684085814}
  - component: {fileID: 1717163969684085813}
  m_Layer: 4
  m_Name: Scroll View
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717163969684085809
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969684085808}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1717163970241906090}
  - {fileID: 1717163968311915338}
  m_Father: {fileID: 1717163969065839222}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1717163969684085812
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969684085808}
  m_CullTransparentMesh: 1
--- !u!114 &1717163969684085815
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969684085808}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 72494806423646354, guid: f576801ac9141ab4c8899dfdada97e19, type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!114 &1717163969684085814
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969684085808}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1aa08ab6e0800fa44ae55d278d1423e3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Content: {fileID: 1717163970241906090}
  m_Horizontal: 1
  m_Vertical: 0
  m_MovementType: 0
  m_Elasticity: 0.1
  m_Inertia: 0
  m_DecelerationRate: 0.1
  m_ScrollSensitivity: 0.2
  m_Viewport: {fileID: 0}
  m_HorizontalScrollbar: {fileID: 0}
  m_VerticalScrollbar: {fileID: 0}
  m_HorizontalScrollbarVisibility: 2
  m_VerticalScrollbarVisibility: 2
  m_HorizontalScrollbarSpacing: -3
  m_VerticalScrollbarSpacing: -3
  m_OnValueChanged:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &1717163969684085813
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969684085808}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0b148fe25e99eb48b9724523833bab1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Delegates:
  - eventID: 0
    callback:
      m_PersistentCalls:
        m_Calls: []
  - eventID: 13
    callback:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 0}
          m_TargetAssemblyTypeName: ScrollRectSnap, Assembly-CSharp
          m_MethodName: StartDrag
          m_Mode: 6
          m_Arguments:
            m_ObjectArgument: {fileID: 0}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 1
          m_CallState: 2
  - eventID: 14
    callback:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 0}
          m_TargetAssemblyTypeName: ScrollRectSnap, Assembly-CSharp
          m_MethodName: StartDrag
          m_Mode: 6
          m_Arguments:
            m_ObjectArgument: {fileID: 0}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
--- !u!1 &1717163969735147372
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163969735147373}
  m_Layer: 4
  m_Name: CenterToCompare
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717163969735147373
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969735147372}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163969065839222}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 10, y: 10}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1717163969868409400
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163969868409401}
  - component: {fileID: 1717163969868409407}
  - component: {fileID: 1717163969868409406}
  m_Layer: 4
  m_Name: Dot1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717163969868409401
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969868409400}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163968311915338}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -8, y: 0}
  m_SizeDelta: {x: 2.5, y: 2.5}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1717163969868409407
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969868409400}
  m_CullTransparentMesh: 1
--- !u!114 &1717163969868409406
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163969868409400}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: -1938207125527859653, guid: f576801ac9141ab4c8899dfdada97e19,
    type: 3}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &1717163970010240500
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163970010240501}
  m_Layer: 4
  m_Name: Audio
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1717163970010240501
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163970010240500}
  m_LocalRotation: {x: -0, y: 1, z: -0, w: 0}
  m_LocalPosition: {x: 0, y: 0, z: 0.03}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1717163969006891932}
  - {fileID: 1717163969207122120}
  - {fileID: 1717163969137348951}
  - {fileID: 1717163969535427354}
  m_Father: {fileID: 3769177770922079760}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1717163970158409572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163970158409573}
  - component: {fileID: 1717163970158409499}
  - component: {fileID: 1717163970158409498}
  m_Layer: 4
  m_Name: DotSelected
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717163970158409573
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163970158409572}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1717163968311915338}
  m_RootOrder: 5
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 8, y: 0}
  m_SizeDelta: {x: 6, y: 6}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &1717163970158409499
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163970158409572}
  m_CullTransparentMesh: 1
--- !u!114 &1717163970158409498
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163970158409572}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: dc71c444d9062474fb77b4171c1a4a03, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!1 &1717163970241906101
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1717163970241906090}
  m_Layer: 4
  m_Name: ScollablePanel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1717163970241906090
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1717163970241906101}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 920657817689648402}
  - {fileID: 920657818545653929}
  - {fileID: 920657816902415284}
  - {fileID: 1950695118892756423}
  - {fileID: 1617063034824653878}
  m_Father: {fileID: 1717163969684085809}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &1950695118892756420
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1950695118892756423}
  m_Layer: 4
  m_Name: ElevationStepper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1950695118892756423
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1950695118892756420}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7679244915230753668}
  - {fileID: 5684847809967862177}
  - {fileID: 746812808416444291}
  m_Father: {fileID: 1717163970241906090}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 152.1, y: 0}
  m_SizeDelta: {x: 54, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &2845530693635626604
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: ************71163}
  - component: {fileID: 5648724821232008042}
  - component: {fileID: 8379878754143234862}
  m_Layer: 5
  m_Name: ShapeName
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &************71163
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2845530693635626604}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1617063034824653878}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 83.77, y: 16}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &5648724821232008042
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2845530693635626604}
  m_CullTransparentMesh: 1
--- !u!114 &8379878754143234862
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2845530693635626604}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Cube
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 11
  m_fontSizeBase: 11
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 0
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: -0.32943538, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1 &3769177770474531205
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3769177770474531210}
  - component: {fileID: 3769177770474531209}
  - component: {fileID: 3769177770474531208}
  - component: {fileID: 3769177770474531211}
  m_Layer: 4
  m_Name: ControlledObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3769177770474531210
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177770474531205}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.5, z: 3}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3769177771078722730}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3769177770474531209
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177770474531205}
  m_Mesh: {fileID: 0}
--- !u!23 &3769177770474531208
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177770474531205}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10303, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &3769177770474531211
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177770474531205}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 65307bdd58ef73943b02570244339263, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _controlledObject: {fileID: 3769177770474531205}
  _colors:
  - {r: 1, g: 0, b: 0, a: 0}
  - {r: 1, g: 0.5019608, b: 0, a: 0}
  - {r: 1, g: 1, b: 0, a: 0}
  - {r: 0, g: 1, b: 0, a: 0}
  - {r: 0, g: 0, b: 1, a: 0}
  - {r: 0.5019608, g: 0, b: 1, a: 0}
  _rotationEnabledIcon: {fileID: 920657816654705496}
  _rotationDisabledIcon: {fileID: 2983950710257855679}
  _rotationLerpSpeed: 1
  _rotationDirectionText: {fileID: 920657817034863629}
  _rotationDirectionNames:
  - Left
  - Right
  - Up
  - Down
  _rotationDirectionIcons:
  - {fileID: 2619623827403273385}
  - {fileID: 2050668066}
  - {fileID: 1297185750}
  - {fileID: 93322829}
  _rotationDirections:
  - {x: 0, y: 0.70710677, z: 0, w: 0.70710677}
  - {x: 0, y: -0.70710677, z: 0, w: 0.70710677}
  - {x: 0.70710677, y: 0, z: 0, w: 0.70710677}
  - {x: -0.70710677, y: 0, z: 0, w: 0.70710677}
  _elevationText: {fileID: 688835361857153789}
  _elevationChangeIncrement: 0.1
  _elevationChangeLerpSpeed: 1
  _shapeNameText: {fileID: 8379878754143234862}
  _shapeNames:
  - Cube
  - Dodecahedron
  - Icosahedron
  - Octahedron
  - Tetrahedron
  - Trapezohedron
  _shapes:
  - {fileID: 1008060561697206567, guid: 6800e9c67be6f054384523c73d34583f, type: 3}
  - {fileID: -2194042186925421486, guid: 6800e9c67be6f054384523c73d34583f, type: 3}
  - {fileID: -2309404035205077305, guid: 6800e9c67be6f054384523c73d34583f, type: 3}
  - {fileID: 4944435276719414011, guid: 6800e9c67be6f054384523c73d34583f, type: 3}
  - {fileID: -645592108707704995, guid: 6800e9c67be6f054384523c73d34583f, type: 3}
  - {fileID: 8918106655620760605, guid: 6800e9c67be6f054384523c73d34583f, type: 3}
--- !u!1 &3769177770922079763
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3769177770922079760}
  - component: {fileID: 6371043789005963827}
  - component: {fileID: 3769177770922079766}
  - component: {fileID: 3769177770922079761}
  - component: {fileID: 3769177770922079771}
  - component: {fileID: 2075385229}
  m_Layer: 4
  m_Name: Menu
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3769177770922079760
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177770922079763}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3769177771002880972}
  - {fileID: 1717163970010240501}
  m_Father: {fileID: 3769177771078722730}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6371043789005963827
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177770922079763}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4a8b912df038412428ce84448044e01e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _button: 256
--- !u!114 &3769177770922079766
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177770922079763}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1cd9780be7e512049b4d33d5c9d0ac92, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _activeState: {fileID: 6371043789005963827}
--- !u!114 &3769177770922079761
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177770922079763}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: de76f7169412b8f4896235a1585d8939, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _selector: {fileID: 3769177770922079766}
  _whenSelected:
    m_PersistentCalls:
      m_Calls: []
  _whenUnselected:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 3769177770922079771}
        m_TargetAssemblyTypeName: Oculus.Interaction.Samples.PalmMenuExample, Assembly-CSharp
        m_MethodName: ToggleMenu
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &3769177770922079771
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177770922079763}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c8ecef668d3a67a469961fc24349d6df, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _menuInteractable: {fileID: 1717163969150618339}
  _menuParent: {fileID: 3769177771002880975}
  _menuPanel: {fileID: 1717163970241906090}
  _buttons:
  - {fileID: 920657817689648402}
  - {fileID: 920657818545653929}
  - {fileID: 920657816902415284}
  - {fileID: 1950695118892756423}
  - {fileID: 1617063034824653878}
  _paginationDots:
  - {fileID: 1717163969868409401}
  - {fileID: 1717163968731331226}
  - {fileID: 1717163968862996931}
  - {fileID: 1717163968675631230}
  - {fileID: 1717163969124497908}
  _selectionIndicatorDot: {fileID: 1717163970158409573}
  _paginationButtonScaleCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0.024387581
      outSlope: 0.024387581
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0.3639607
    - serializedVersion: 3
      time: 0.8
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  _defaultButtonDistance: 50
  _paginationSwipeAudio: {fileID: 1717163969006891935}
  _showMenuAudio: {fileID: 1717163969137348948}
  _hideMenuAudio: {fileID: 1717163969535427429}
--- !u!114 &2075385229
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177770922079763}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d61ea23b9fd13af40afe326bb23ac1cd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _hand: {fileID: 1628870280}
  _offset: {x: -0.08, y: 0, z: -0.001}
  _rotation: {x: -0.00000003090862, y: 0.70710677, z: -0.70710677, w: -0.00000003090862}
  _posOffset: {x: -0.001, y: 0, z: 0.08}
  _rotOffset: {x: 0.50000006, y: -0.49999997, z: 0.5, w: 0.5}
  _mirrorOffsetsForLeftHand: 1
  _freezeRotationX: 0
  _freezeRotationY: 0
  _freezeRotationZ: 1
--- !u!1 &3769177771002880975
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3769177771002880972}
  m_Layer: 4
  m_Name: Visuals
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3769177771002880972
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177771002880975}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1717163969150618336}
  m_Father: {fileID: 3769177770922079760}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3769177771078722725
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3769177771078722730}
  - component: {fileID: 3769177771078722728}
  - component: {fileID: 3769177771078722731}
  - component: {fileID: 3769177771078722729}
  - component: {fileID: 1628870280}
  m_Layer: 4
  m_Name: PalmMenu
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3769177771078722730
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177771078722725}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3769177770922079760}
  - {fileID: 3769177770474531210}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3769177771078722728
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177771078722725}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b14164f8f23faae4293baeb84485b3d6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _hand: {fileID: 0}
--- !u!114 &3769177771078722731
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177771078722725}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b14164f8f23faae4293baeb84485b3d6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _hand: {fileID: 0}
--- !u!114 &3769177771078722729
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177771078722725}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7f87c900294bbee44b7f74920f84bb29, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _leftHand: {fileID: 3769177771078722728}
  _leftHandedGameObjects:
  - {fileID: 0}
  _rightHandedGameObjects:
  - {fileID: 0}
--- !u!114 &1628870280
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3769177771078722725}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 950dffe3d53eb1d48adb067b2b3d1c2d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _leftHand: {fileID: 3769177771078722728}
  _rightHand: {fileID: 3769177771078722731}
  _selectDominant: 0
--- !u!1 &5474814573772746809
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1617063034824653878}
  m_Layer: 4
  m_Name: ShapeSelector
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1617063034824653878
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5474814573772746809}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: ************71163}
  - {fileID: 6057943088145052471}
  - {fileID: 7328996066057370957}
  m_Father: {fileID: 1717163970241906090}
  m_RootOrder: 4
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 206.4, y: 0}
  m_SizeDelta: {x: 54, y: 50}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1001 &690305150
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 920657817689648402}
    m_Modifications:
    - target: {fileID: 407529217658591449, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 422199976237708701, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 478065341682505637, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 558722171093103054, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1861521771991926428, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Sprite
      value: 
      objectReference: {fileID: -88526126890289535, guid: 64f884351318b694f942625580e64a8b,
        type: 3}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5614022670084783633, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_text
      value: Color
      objectReference: {fileID: 0}
    - target: {fileID: 5614022670084783633, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_sharedMaterial
      value: 
      objectReference: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
    - target: {fileID: 5812396322074810329, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6454634035808976204, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.size
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 1717163969207122126}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Target
      value: 
      objectReference: {fileID: 3769177770474531211}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: PlayAudio
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_MethodName
      value: CycleColor
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: ButtonShelf_Button
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 72
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366, type: 3}
    - {fileID: 8057140834807564895, guid: 040567cd4431b484e9999e28a102d366, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 040567cd4431b484e9999e28a102d366, type: 3}
--- !u!1 &7922623384210369646 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 690305150}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &896204152
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7922623384210369646}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 0
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 3
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 0}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1717163969207122126}
        m_TargetAssemblyTypeName: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
        m_MethodName: PlayAudio
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 3769177770474531211}
        m_TargetAssemblyTypeName: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers,
          Oculus.Interaction.Samples
        m_MethodName: CycleColor
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!224 &8743731162625172927 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 690305150}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1300756234
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 920657816902415284}
    m_Modifications:
    - target: {fileID: 407529217658591449, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 422199976237708701, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 478065341682505637, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 558722171093103054, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1861521771991926428, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Sprite
      value: 
      objectReference: {fileID: -493823384402833086, guid: 64f884351318b694f942625580e64a8b,
        type: 3}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: Icon left
      objectReference: {fileID: 0}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5614022670084783633, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_text
      value: Label
      objectReference: {fileID: 0}
    - target: {fileID: 5812396322074810329, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6454634035808976204, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.size
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 1717163969207122126}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Target
      value: 
      objectReference: {fileID: 3769177770474531211}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: PlayAudio
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_MethodName
      value: CycleColor
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 50.5
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -48
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: ButtonShelf_MultiToggle
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 72
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366, type: 3}
    - {fileID: 8057140834807564895, guid: 040567cd4431b484e9999e28a102d366, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 040567cd4431b484e9999e28a102d366, type: 3}
--- !u!1 &2619623827403273385 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 1300756234}
  m_PrefabAsset: {fileID: 0}
--- !u!224 &4688635637041728498 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 4688635638039969016, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 1300756234}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &7922623382677168922 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 1300756234}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1512470763
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7922623382677168922}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 0
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 3
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 0}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1717163969207122126}
        m_TargetAssemblyTypeName: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
        m_MethodName: PlayAudio
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 3769177770474531211}
        m_TargetAssemblyTypeName: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers,
          Oculus.Interaction.Samples
        m_MethodName: CycleRotationDirection
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!224 &8743731164162534091 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 1300756234}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &951344331601686300
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 920657818545653929}
    m_Modifications:
    - target: {fileID: 407529217658591449, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 422199976237708701, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 478065341682505637, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 558722171093103054, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 558722171093103054, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1861521771991926428, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Sprite
      value: 
      objectReference: {fileID: -2000962494228882322, guid: 64f884351318b694f942625580e64a8b,
        type: 3}
    - target: {fileID: 1861521771991926428, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Color.b
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1861521771991926428, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Color.g
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1861521771991926428, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Color.r
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1861521771991926428, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_RaycastTarget
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: IconOff
      objectReference: {fileID: 0}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5614022670084783633, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_text
      value: Rotate
      objectReference: {fileID: 0}
    - target: {fileID: 5812396322074810329, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6454634035808976204, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsOn
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: graphic
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.size
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 1717163969207122126}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Target
      value: 
      objectReference: {fileID: 3769177770474531211}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: PlayAudio
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_MethodName
      value: ToggleRotationEnabled
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: ButtonShelf_Toggle
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8039122817312228722, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8039122817312228722, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8039122817312228722, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 50
      objectReference: {fileID: 0}
    - target: {fileID: 8039122817312228722, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 72
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 8057140834807564895, guid: 040567cd4431b484e9999e28a102d366, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 040567cd4431b484e9999e28a102d366, type: 3}
--- !u!1 &2983950710257855679 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 951344331601686300}
  m_PrefabAsset: {fileID: 0}
--- !u!224 &5486153885739963364 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 4688635638039969016, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 951344331601686300}
  m_PrefabAsset: {fileID: 0}
--- !u!224 &8386867489180447453 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 951344331601686300}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2081272422693724300
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1617063034824653878}
    m_Modifications:
    - target: {fileID: 407529217658591449, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 422199976237708701, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 422199976237708701, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 478065341682505637, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 558722171093103054, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1861521771991926428, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Sprite
      value: 
      objectReference: {fileID: -4225235479564634476, guid: 64f884351318b694f942625580e64a8b,
        type: 3}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: Icon
      objectReference: {fileID: 0}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5614022670084783633, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_text
      value: Label
      objectReference: {fileID: 0}
    - target: {fileID: 5812396322074810329, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 50.5
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -48
      objectReference: {fileID: 0}
    - target: {fileID: 6454634035808976204, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.size
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 1717163969207122126}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Target
      value: 
      objectReference: {fileID: 3769177770474531211}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: PlayAudio
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_MethodName
      value: CycleColor
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 50.5
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -48
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: ButtonShelf_Down
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 44
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -18
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366, type: 3}
    - {fileID: 8057140834807564895, guid: 040567cd4431b484e9999e28a102d366, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 040567cd4431b484e9999e28a102d366, type: 3}
--- !u!224 &7328996066057370957 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 2081272422693724300}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &8147264339168146588 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 2081272422693724300}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &9218025677030014160
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8147264339168146588}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 0
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 3
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 0}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1717163969207122126}
        m_TargetAssemblyTypeName: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
        m_MethodName: PlayAudio
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 3769177770474531211}
        m_TargetAssemblyTypeName: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers,
          Oculus.Interaction.Samples
        m_MethodName: CycleShape
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
--- !u!1001 &3262253261994290934
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1617063034824653878}
    m_Modifications:
    - target: {fileID: 407529217658591449, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 422199976237708701, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 422199976237708701, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 478065341682505637, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 558722171093103054, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1861521771991926428, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Sprite
      value: 
      objectReference: {fileID: -8133290119634698585, guid: 64f884351318b694f942625580e64a8b,
        type: 3}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: Icon
      objectReference: {fileID: 0}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5614022670084783633, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_text
      value: Label
      objectReference: {fileID: 0}
    - target: {fileID: 5812396322074810329, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 50.5
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -48
      objectReference: {fileID: 0}
    - target: {fileID: 6454634035808976204, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.size
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 1717163969207122126}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Target
      value: 
      objectReference: {fileID: 3769177770474531211}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: PlayAudio
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_MethodName
      value: CycleColor
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 50.5
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -48
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: ButtonShelf_Up
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 44
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 18
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366, type: 3}
    - {fileID: 8057140834807564895, guid: 040567cd4431b484e9999e28a102d366, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 040567cd4431b484e9999e28a102d366, type: 3}
--- !u!1 &4663220072866495206 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 3262253261994290934}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &9113956197135281062
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4663220072866495206}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 0
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 3
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 0}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1717163969207122126}
        m_TargetAssemblyTypeName: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
        m_MethodName: PlayAudio
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 3769177770474531211}
        m_TargetAssemblyTypeName: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers,
          Oculus.Interaction.Samples
        m_MethodName: CycleShape
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!224 &6057943088145052471 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 3262253261994290934}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &4013652693463002208
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1950695118892756423}
    m_Modifications:
    - target: {fileID: 407529217658591449, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 422199976237708701, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 422199976237708701, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 478065341682505637, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 558722171093103054, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1861521771991926428, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Sprite
      value: 
      objectReference: {fileID: -8133290119634698585, guid: 64f884351318b694f942625580e64a8b,
        type: 3}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: Icon
      objectReference: {fileID: 0}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5614022670084783633, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_text
      value: Label
      objectReference: {fileID: 0}
    - target: {fileID: 5812396322074810329, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 50.5
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -48
      objectReference: {fileID: 0}
    - target: {fileID: 6454634035808976204, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.size
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 1717163969207122126}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Target
      value: 
      objectReference: {fileID: 3769177770474531211}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: PlayAudio
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_MethodName
      value: CycleColor
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 50.5
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -48
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: ButtonShelf_Up
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 44
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 18
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366, type: 3}
    - {fileID: 8057140834807564895, guid: 040567cd4431b484e9999e28a102d366, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 040567cd4431b484e9999e28a102d366, type: 3}
--- !u!224 &5684847809967862177 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 4013652693463002208}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &6503642209937274992 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 4013652693463002208}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &549757814020451570
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6503642209937274992}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 0
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 3
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 0}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1717163969207122126}
        m_TargetAssemblyTypeName: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
        m_MethodName: PlayAudio
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 3769177770474531211}
        m_TargetAssemblyTypeName: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers,
          Oculus.Interaction.Samples
        m_MethodName: IncrementElevation
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
--- !u!1001 &8289666077260243522
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 1950695118892756423}
    m_Modifications:
    - target: {fileID: 407529217658591449, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 422199976237708701, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 422199976237708701, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 466049075092810254, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 478065341682505637, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 558722171093103054, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1396279134995418964, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1861521771991926428, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Sprite
      value: 
      objectReference: {fileID: -4225235479564634476, guid: 64f884351318b694f942625580e64a8b,
        type: 3}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: Icon
      objectReference: {fileID: 0}
    - target: {fileID: 2619623826102846371, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 2753119493817362737, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5614022670084783633, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_text
      value: Label
      objectReference: {fileID: 0}
    - target: {fileID: 5812396322074810329, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 50.5
      objectReference: {fileID: 0}
    - target: {fileID: 6299816011748658447, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -48
      objectReference: {fileID: 0}
    - target: {fileID: 6454634035808976204, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_hasFontAssetChanged
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.size
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Mode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Target
      value: 
      objectReference: {fileID: 1717163969207122126}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Target
      value: 
      objectReference: {fileID: 3769177770474531211}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_CallState
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_MethodName
      value: PlayAudio
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_MethodName
      value: CycleColor
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_TargetAssemblyTypeName
      value: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_TargetAssemblyTypeName
      value: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers, Oculus.Interaction.Samples
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[1].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: onValueChanged.m_PersistentCalls.m_Calls.Array.data[2].m_Arguments.m_ObjectArgumentAssemblyTypeName
      value: UnityEngine.Object, UnityEngine
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6955941791144292522, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 50.5
      objectReference: {fileID: 0}
    - target: {fileID: 7730992381216224886, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -48
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Name
      value: ButtonShelf_Down
      objectReference: {fileID: 0}
    - target: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Layer
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 100
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 44
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -18
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
        type: 3}
      propertyPath: m_ConstrainProportionsScale
      value: 1
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 6705262112095271069, guid: 040567cd4431b484e9999e28a102d366, type: 3}
    - {fileID: 8057140834807564895, guid: 040567cd4431b484e9999e28a102d366, type: 3}
  m_SourcePrefab: {fileID: 100100000, guid: 040567cd4431b484e9999e28a102d366, type: 3}
--- !u!224 &746812808416444291 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 8743731163281776065, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 8289666077260243522}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &2231534292383118930 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7922623383524267024, guid: 040567cd4431b484e9999e28a102d366,
    type: 3}
  m_PrefabInstance: {fileID: 8289666077260243522}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &5524608308109382907
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2231534292383118930}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 0
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 3
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 0}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1717163969207122126}
        m_TargetAssemblyTypeName: Oculus.Interaction.AudioTrigger, Oculus.Interaction.Samples
        m_MethodName: PlayAudio
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
      - m_Target: {fileID: 3769177770474531211}
        m_TargetAssemblyTypeName: Oculus.Interaction.Samples.PalmMenu.PalmMenuExampleButtonHandlers,
          Oculus.Interaction.Samples
        m_MethodName: IncrementElevation
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
