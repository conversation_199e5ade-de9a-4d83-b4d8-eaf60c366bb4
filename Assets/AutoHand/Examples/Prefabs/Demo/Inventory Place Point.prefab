%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &5534502338747597264
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5213522182785716736}
  - component: {fileID: 4466973607033861818}
  - component: {fileID: 376428461563943621}
  m_Layer: 0
  m_Name: Animation
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5213522182785716736
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5534502338747597264}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8515371878481805302}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4466973607033861818
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5534502338747597264}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 313e47353b0f88446a5cd49c2c41577b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  animationTargets:
  - renderer: {fileID: 0}
    spriteRenderer: {fileID: 376428461563943621}
    ignoreColor: 0
    ignorePosition: 0
    ignoreScale: 0
    ignoreRotation: 0
    colorDampener: 0
    positionDampener: 0
    scaleDampener: 0
    rotationDampener: 0
    waveOffset: 0
    transform: {fileID: 0}
  onEnableTransition: 0
  onEnableTransitionTime: 0.15
  onEnableTransitionCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 1
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  updateColor: 1
  highlightAnimationColorTime: 0.4
  unhighlightAnimationColorTime: 0.4
  unhighlightColor: {r: 0.78930813, g: 0.78930813, b: 0.78930813, a: 0.47058824}
  highlightColor: {r: 0.9213836, g: 0.9213836, b: 0.9213836, a: 0.76862746}
  activateColor: {r: 1, g: 1, b: 1, a: 0.1882353}
  highlightColorCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 2
      outSlope: 2
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  colorWaveFrequency: 1
  colorWaveAmplitude: 0
  colorWaveOffset: 0.5
  updatePosition: 1
  highlightAnimationPositionTime: 0.5
  unhighlightAnimationPositionTime: 0.5
  highlightPosition: {x: 0, y: 0, z: 0.2}
  activatePosition: {x: 0, y: 0, z: 0}
  positionAnimationCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 2
      outSlope: 2
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  positionWaveFrequency: 1
  positionWaveAmplitude: 0.25
  positionWaveOffset: 0.5
  updateScale: 1
  highlightAnimationScaleTime: 0.25
  unhighlightAnimationScaleTime: 0.25
  highlightScaleOffset: 0.1
  activateScaleOffset: -1
  scaleAnimationCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 2
      outSlope: 2
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  scaleWaveFrequency: 1
  scaleWaveAmplitude: 0.25
  scaleWaveOffset: 0.25
  updateRotation: 0
  highlightAnimationRotationTime: 0.5
  unhighlightAnimationRotationTime: 0.5
  highlightRotationOffset: {x: 0, y: 0, z: 0}
  activateRotationOffset: {x: 0, y: 0, z: 0}
  rotationAnimationCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 1
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  rotationWaveFrequency: 1
  rotationWaveAmplitude: 0
  rotationWaveOffset: 0.5
  placePoint: {fileID: 4757014595846239659}
--- !u!212 &376428461563943621
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5534502338747597264}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 9dfc825aed78fcd4ba02077103263b40, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 12b36a74bc759df4082a6b56296cf91c, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 0.39607844}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 5, y: 5}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!1 &5767607329794681374
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8126166840619016266}
  - component: {fileID: 8460626287744791393}
  - component: {fileID: 1570624925514054671}
  m_Layer: 0
  m_Name: Sounds
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8126166840619016266
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5767607329794681374}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8515371878481805302}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!82 &8460626287744791393
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5767607329794681374}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_Resource: {fileID: 0}
  m_PlayOnAwake: 1
  m_Volume: 0.35
  m_Pitch: 0.75
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &1570624925514054671
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5767607329794681374}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d9ec4dfc370076d4f8e478d26e0b407b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  placePoint: {fileID: 4757014595846239659}
  audioSource: {fileID: 8460626287744791393}
  highlightSound: {fileID: 8300000, guid: 4d5b81083972abb4db663f90454ca61a, type: 3}
  unhighlightSound: {fileID: 0}
  placeSound: {fileID: 8300000, guid: c6f7eba64e7593945bc555b997d82c74, type: 3}
  removeSound: {fileID: 8300000, guid: 23bf9dfeb0c244a4a9db5cdceb32b3fc, type: 3}
--- !u!1 &9101538457139890465
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8515371878481805302}
  - component: {fileID: 4757014595846239659}
  - component: {fileID: 2147391168191678546}
  m_Layer: 0
  m_Name: Inventory Place Point
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8515371878481805302
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9101538457139890465}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: 0, w: 0.7071068}
  m_LocalPosition: {x: 0.2, y: -0.033, z: 0.08}
  m_LocalScale: {x: 0.028000003, y: 0.028000003, z: 0.028000003}
  m_ConstrainProportionsScale: 1
  m_Children:
  - {fileID: 5213522182785716736}
  - {fileID: 8126166840619016266}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: -90, y: 0, z: 0}
--- !u!114 &4757014595846239659
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9101538457139890465}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7d9c288aa20513c4aafa0228d4cfd80e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreMe: 0
  showPlaceSettings: 1
  startPlaced: {fileID: 0}
  placedOffset: {fileID: 8515371878481805302}
  shapeType: 0
  placeRadius: 4
  placeSize: {x: 0.1, y: 0.1, z: 0.1}
  shapeOffset: {x: 0, y: 0, z: 0}
  grabbablePlacePoint: 1
  forcePlace: 0
  forceHandRelease: 1
  parentOnPlace: 1
  matchPosition: 1
  matchRotation: 0
  resizeOnPlace: 1
  resizeOffset: -1
  disableRigidbodyOnPlace: 1
  disableGrabOnPlace: 0
  disablePlacePointOnPlace: 0
  destroyObjectOnPlace: 0
  makePlacedKinematic: 1
  placedJointLink: {fileID: 0}
  jointBreakForce: 1000
  showPlaceRequirements: 1
  heldPlaceOnly: 1
  nameCompareType: 0
  placeNames: []
  blacklistNames: []
  onlyAllows: []
  dontAllows: []
  placeLayers:
    serializedVersion: 2
    m_Bits: 0
  showEvents: 1
  OnPlace:
    m_PersistentCalls:
      m_Calls: []
  OnRemove:
    m_PersistentCalls:
      m_Calls: []
  OnHighlight:
    m_PersistentCalls:
      m_Calls: []
  OnStopHighlight:
    m_PersistentCalls:
      m_Calls: []
--- !u!135 &2147391168191678546
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9101538457139890465}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 2.5
  m_Center: {x: 0, y: 0.0000076293945, z: 0}
