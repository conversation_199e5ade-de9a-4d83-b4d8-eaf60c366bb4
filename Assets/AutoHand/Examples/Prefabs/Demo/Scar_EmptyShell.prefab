%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2325549420832801876
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3102366942639070958}
  - component: {fileID: 3208651119405824263}
  - component: {fileID: 3208651119405824262}
  - component: {fileID: 3208651119405824257}
  - component: {fileID: 3208651119405824256}
  - component: {fileID: -1714287755328876118}
  - component: {fileID: -8005734903158185999}
  m_Layer: 0
  m_Name: Scar_EmptyShell
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3102366942639070958
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325549420832801876}
  m_LocalRotation: {x: 0.000000081460335, y: 0, z: -0, w: 1}
  m_LocalPosition: {x: 19.0009, y: 0.9291, z: -7.80685}
  m_LocalScale: {x: 2, y: 2, z: 2}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6051430273785967249}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &3208651119405824263
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325549420832801876}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &3208651119405824262
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325549420832801876}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 0.031730812, y: 0.007219305, z: 0.0072193146}
  m_Center: {x: 0.0028613135, y: -0.00000047683716, z: -0.000007019029}
--- !u!82 &3208651119405824257
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325549420832801876}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_PlayOnAwake: 1
  m_Volume: 0.4
  m_Pitch: 1.5
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 1
  MaxDistance: 500
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    - serializedVersion: 3
      time: 1
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &3208651119405824256
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325549420832801876}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1c498b8ff53f43d43bb5f3b6001c2364, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  collisionTriggers:
    serializedVersion: 2
    m_Bits: 4294967295
  source: {fileID: 3208651119405824257}
  clip: {fileID: 8300000, guid: 4a4d51474a581e642b34fbf6d41c6184, type: 3}
  velocityVolumeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 1
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  volumeAmp: 0.25
  velocityAmp: 0.5
  soundRepeatDelay: 0.1
--- !u!114 &-1714287755328876118
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325549420832801876}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a70859c512807c2408f79d08adb4d8c2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  colliders:
  - {fileID: 3208651119405824262}
--- !u!114 &-8005734903158185999
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2325549420832801876}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 822bc9090447b9c40833509c4a32e597, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreMe: 0
  body: {fileID: 0}
  hightlightMaterial: {fileID: 0}
  isGrabbable: 1
  CopySettings: {fileID: 0}
  grabType: 0
  handType: 0
  singleHandOnly: 0
  allowHeldSwapping: 1
  instantGrab: 0
  useGentleGrab: 0
  heldNoFriction: 1
  maintainGrabOffset: 0
  ignoreWeight: 0
  parentOnGrab: 1
  throwPower: 1
  jointBreakForce: 3500
  showAdvancedSettings: 1
  makeChildrenGrabbable: 1
  grabPriorityWeight: 1
  ignoreReleaseTime: 0.5
  maxHeldVelocity: 10
  heldPositionOffset: {x: 0, y: 0, z: 0}
  heldRotationOffset: {x: 0, y: 0, z: 0}
  customGrabJoint: {fileID: 0}
  jointedBodies: []
  heldIgnoreColliders: []
  pullApartBreakOnly: 1
  showEvents: 1
  onGrab:
    m_PersistentCalls:
      m_Calls: []
  onRelease:
    m_PersistentCalls:
      m_Calls: []
  onSqueeze:
    m_PersistentCalls:
      m_Calls: []
  onUnsqueeze:
    m_PersistentCalls:
      m_Calls: []
  onHighlight:
    m_PersistentCalls:
      m_Calls: []
  onUnhighlight:
    m_PersistentCalls:
      m_Calls: []
  OnJointBreak:
    m_PersistentCalls:
      m_Calls: []
  lockHandOnGrab: 0
--- !u!1001 &6084440856200467834
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 3102366942639070958}
    m_Modifications:
    - target: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.9659259
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.258819
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: -30
      objectReference: {fileID: 0}
    - target: {fileID: -3770166350440609915, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 919132149155446097, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
      propertyPath: m_Name
      value: ScarBullet
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
--- !u!4 &6051430273785967249 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: -8679921383154817045, guid: f046f6c4fb5ec604a8d5b62318b2957c, type: 3}
  m_PrefabInstance: {fileID: 6084440856200467834}
  m_PrefabAsset: {fileID: 0}
