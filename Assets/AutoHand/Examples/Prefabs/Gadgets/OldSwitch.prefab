%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1420574257160300300
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3023367269499762009}
  m_Layer: 0
  m_Name: Switch
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3023367269499762009
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1420574257160300300}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 4543189318295770193}
  - {fileID: 4543189317677169108}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4543189317677169109
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4543189317677169108}
  - component: {fileID: 4543189317677169107}
  - component: {fileID: 4543189317677169104}
  - component: {fileID: 4543189317677169105}
  - component: {fileID: 4543189317677169110}
  - component: {fileID: 4543189317677169111}
  - component: {fileID: 4543189317677169106}
  - component: {fileID: 4543189317677169117}
  m_Layer: 0
  m_Name: Lever
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4543189317677169108
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189317677169109}
  m_LocalRotation: {x: -0, y: -0, z: 0.31774774, w: 0.9481753}
  m_LocalPosition: {x: 0.0619, y: 0.0849, z: 0}
  m_LocalScale: {x: 0.18187046, y: 0.05643153, z: 0.05512737}
  m_Children: []
  m_Father: {fileID: 3023367269499762009}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 37.054}
--- !u!33 &4543189317677169107
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189317677169109}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4543189317677169104
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189317677169109}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 1151844921754575795, guid: 56443c43b74715f4d97286c46d4c7fd6, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!65 &4543189317677169105
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189317677169109}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &4543189317677169110
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189317677169109}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 0
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!59 &4543189317677169111
HingeJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189317677169109}
  m_ConnectedBody: {fileID: 4543189318295770205}
  m_Anchor: {x: -0.5, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: -0.04412806, y: 0.27138275, z: 0}
  m_UseSpring: 0
  m_Spring:
    spring: 0
    damper: 100
    targetPosition: 0
  m_UseMotor: 0
  m_Motor:
    targetVelocity: 0
    force: 0
    freeSpin: 0
  m_UseLimits: 1
  m_Limits:
    min: 0
    max: 105
    bounciness: 0
    bounceMinVelocity: 0.2
    contactDistance: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!114 &4543189317677169106
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189317677169109}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 822bc9090447b9c40833509c4a32e597, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  body: {fileID: 0}
  heldPositionOffset: {x: 0, y: 0, z: 0}
  heldRotationOffset: {x: 0, y: 0, z: 0}
  parentOnGrab: 1
  handType: 0
  singleHandOnly: 0
  releaseOnTeleport: 0
  hightlightMaterial: {fileID: 0}
  throwMultiplyer: 1
  pullApartBreakOnly: 1
  makeChildrenGrabbable: 1
  jointBreakForce: 1200
  jointBreakTorque: 1000
  onGrab:
    m_PersistentCalls:
      m_Calls: []
  onRelease:
    m_PersistentCalls:
      m_Calls: []
  onSqueeze:
    m_PersistentCalls:
      m_Calls: []
  onUnsqueeze:
    m_PersistentCalls:
      m_Calls: []
  OnJointBreak:
    m_PersistentCalls:
      m_Calls: []
  hideEvents: 0
--- !u!114 &4543189317677169117
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189317677169109}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3f2580c2349119b46b7b6be979d1ad61, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  buffer: 0.1
  SwitchOn:
    m_PersistentCalls:
      m_Calls: []
  SwitchOff:
    m_PersistentCalls:
      m_Calls: []
--- !u!1 &4543189318295770198
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4543189318295770193}
  - component: {fileID: 4543189318295770194}
  - component: {fileID: 4543189318295770195}
  - component: {fileID: 4543189318295770192}
  - component: {fileID: 4543189318295770205}
  m_Layer: 0
  m_Name: LeverBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4543189318295770193
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189318295770198}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.24186267, y: 0.11093545, z: 0.1085676}
  m_Children: []
  m_Father: {fileID: 3023367269499762009}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4543189318295770194
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189318295770198}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4543189318295770195
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189318295770198}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 786c7caa508d1f344a2b05806f53730c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
--- !u!65 &4543189318295770192
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189318295770198}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &4543189318295770205
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4543189318295770198}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
