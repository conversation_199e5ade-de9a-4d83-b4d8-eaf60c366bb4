%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2839866806803338127
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2839866806803338124}
  - component: {fileID: 2839866806803338123}
  - component: {fileID: 2839866806803338122}
  - component: {fileID: 2839866806803338125}
  m_Layer: 0
  m_Name: Base (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2839866806803338124
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806803338127}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: -0, z: -0, w: -0.7071068}
  m_LocalPosition: {x: 0.07499999, y: 0.026499987, z: 0}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2839866807195842793}
  m_LocalEulerAnglesHint: {x: 270, y: 0, z: 0}
--- !u!33 &2839866806803338123
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806803338127}
  m_Mesh: {fileID: -8678823145569952518, guid: ce4ee5ad8d0414948927c67349bafdd4, type: 3}
--- !u!23 &2839866806803338122
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806803338127}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 9c22cd5290acd8641b99c59749f36280, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2839866806803338125
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806803338127}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.0050000018, y: 0.021323645, z: 0.0050000036}
  m_Center: {x: -1.1641532e-10, y: 0, z: -2.328307e-10}
--- !u!1 &2839866806845585692
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2839866806845585693}
  - component: {fileID: 2839866806845585688}
  - component: {fileID: 2839866806845585691}
  - component: {fileID: 2839866806845585690}
  m_Layer: 0
  m_Name: Base (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2839866806845585693
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806845585692}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: -0, z: -0, w: -0.7071068}
  m_LocalPosition: {x: -0.07499999, y: 0.026499987, z: 0}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2839866807195842793}
  m_LocalEulerAnglesHint: {x: 270, y: 0, z: 0}
--- !u!33 &2839866806845585688
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806845585692}
  m_Mesh: {fileID: -8678823145569952518, guid: ce4ee5ad8d0414948927c67349bafdd4, type: 3}
--- !u!23 &2839866806845585691
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806845585692}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 9c22cd5290acd8641b99c59749f36280, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2839866806845585690
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806845585692}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.0050000018, y: 0.021323645, z: 0.0050000036}
  m_Center: {x: 0.000000003608875, y: 0, z: -2.328307e-10}
--- !u!1 &2839866806956173417
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2839866806956173414}
  - component: {fileID: 2839866806956173413}
  - component: {fileID: 2839866806956173412}
  - component: {fileID: 2839866806956173415}
  m_Layer: 0
  m_Name: Cube (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2839866806956173414
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806956173417}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -0.8383846, y: -0.83333325, z: 0}
  m_LocalScale: {x: 0.22222218, y: 1.1111107, z: 0.70707065}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2839866808768398351}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2839866806956173413
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806956173417}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2839866806956173412
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806956173417}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7ddcdf368990e454ca3a6f892b1c0ce9, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2839866806956173415
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866806956173417}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000004, y: 1.0000006, z: 1.0000001}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &2839866807195842792
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2839866807195842793}
  - component: {fileID: 2839866807195842790}
  m_Layer: 0
  m_Name: Base
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2839866807195842793
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807195842792}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2839866806845585693}
  - {fileID: 2839866806803338124}
  m_Father: {fileID: 2839866808124084459}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &2839866807195842790
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807195842792}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &2839866807270805383
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2839866807270805380}
  - component: {fileID: 2839866807270805376}
  - component: {fileID: 2839866807270805378}
  - component: {fileID: 2839866807270805379}
  - component: {fileID: 2839866807270805381}
  - component: {fileID: 2839866807270805377}
  - component: {fileID: 8913793188326431229}
  - component: {fileID: 4354831851926983266}
  - component: {fileID: 838355948667608913}
  - component: {fileID: 3863732110451042912}
  m_Layer: 0
  m_Name: Grab
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2839866807270805380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807270805383}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.0366, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2839866808768398351}
  m_Father: {fileID: 2839866808124084459}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2839866807270805376
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807270805383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 822bc9090447b9c40833509c4a32e597, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreMe: 0
  body: {fileID: 0}
  hightlightMaterial: {fileID: 0}
  isGrabbable: 1
  CopySettings: {fileID: 0}
  grabType: 0
  grabPoseType: 0
  handType: 0
  singleHandOnly: 1
  instantGrab: 0
  useGentleGrab: 0
  maintainGrabOffset: 1
  parentOnGrab: 0
  heldNoFriction: 1
  ignoreWeight: 0
  allowHeldSwapping: 1
  throwPower: 1
  jointBreakForce: 5000
  showAdvancedSettings: 1
  makeChildrenGrabbable: 1
  grabPriorityWeight: 1
  ignoreReleaseTime: 0.25
  minHeldDrag: 1.5
  minHeldAngleDrag: 3
  minHeldMass: 0.1
  maxHeldVelocity: 10
  heldPositionOffset: {x: 0, y: 0, z: 0}
  heldRotationOffset: {x: 0, y: 0, z: 0}
  customGrabJoint: {fileID: 0}
  jointedBodies: []
  heldIgnoreColliders: []
  pullApartBreakOnly: 1
  showEvents: 1
  onGrab:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: Autohand.Demo.TextChanger, Assembly-CSharp
        m_MethodName: UpdateText
        m_Mode: 5
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: This Gadget uses "maintain grab offset" so the hand doesn't
            return on grab
          m_BoolArgument: 0
        m_CallState: 2
  onRelease:
    m_PersistentCalls:
      m_Calls: []
  onSqueeze:
    m_PersistentCalls:
      m_Calls: []
  onUnsqueeze:
    m_PersistentCalls:
      m_Calls: []
  onHighlight:
    m_PersistentCalls:
      m_Calls: []
  onUnhighlight:
    m_PersistentCalls:
      m_Calls: []
  OnJointBreak:
    m_PersistentCalls:
      m_Calls: []
  lockHandOnGrab: 0
--- !u!54 &2839866807270805378
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807270805383}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 3
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &2839866807270805379
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807270805383}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.0275
  m_Height: 0.2
  m_Direction: 0
  m_Center: {x: 0, y: 0.0725, z: 0}
--- !u!153 &2839866807270805381
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807270805383}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 2839866807195842790}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0.000000015341357, y: 0.036600005, z: 0.000000032715434}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 1
  m_AngularXMotion: 0
  m_AngularYMotion: 0
  m_AngularZMotion: 0
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0.07
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 500
    positionDamper: 35
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 2
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.01
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!114 &2839866807270805377
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807270805383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 18d9c58a36556874fad9e6f1666c394f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  invertValue: 0
  playRange: 0.025
--- !u!114 &8913793188326431229
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807270805383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c49b27d5a0e0d4dd8ab20e2bed42e835, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreMe: 0
  rightPose:
    rotationOffset: {x: 0, y: 0, z: 0}
    posePositions:
    - {x: -0.024664905, y: -0.030219551, z: -0.016220368}
    - {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
    - {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
    - {x: -0.0151, y: 0.0034, z: -0.0008}
    - {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
    - {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
    - {x: -0.029940333, y: 0, z: 0.000000023841856}
    - {x: -0.0207, y: -0.0021, z: 0.0019}
    - {x: -0.096640125, y: -0.008566598, z: 0.002615863}
    - {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
    - {x: -0.031118786, y: 0, z: 0.00000006854534}
    - {x: -0.0186, y: -0.0016, z: 0.0016}
    - {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
    - {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
    - {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
    - {x: -0.0166, y: -0.0016, z: 0.0007}
    - {x: -0.080928504, y: 0.034541752, z: -0.008505775}
    - {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
    - {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
    - {x: -0.01333, y: -0.00131, z: 0.00011}
    poseRotations:
    - {x: 0.824486, y: -0.17598078, z: 0.45968366, w: 0.27918532}
    - {x: 0.01166284, y: -0.12668674, z: -0.036069844, w: 0.99121815}
    - {x: -0.0019309819, y: -0.47830346, z: -0.0056621954, w: 0.8781743}
    - {x: -0.0000061402106, y: -0.0000049649398, z: 0.0000024675394, w: 1}
    - {x: 0.08363079, y: -0.4195727, z: 0.05875995, w: 0.901949}
    - {x: 0.0031034432, y: -0.33462396, z: -0.0064132046, w: 0.9423248}
    - {x: -0.00092819205, y: -0.47602734, z: 0.0031441716, w: 0.8794244}
    - {x: -0.0000034403058, y: -0.000003005378, z: -0.000007307157, w: 1}
    - {x: 0.002680663, y: -0.4448686, z: -0.011437493, w: 0.8955188}
    - {x: 0.006574654, y: -0.4416015, z: 0.010494141, w: 0.89712584}
    - {x: 0.0041950913, y: -0.33077687, z: 0.005871194, w: 0.9436814}
    - {x: -0.0000072037797, y: -0.0000022402965, z: -0.0000032116654, w: 1}
    - {x: -0.11289556, y: -0.37279072, z: -0.100843385, w: 0.9154847}
    - {x: 0.016764885, y: -0.39613065, z: 0.03337158, w: 0.9174344}
    - {x: 0.03473689, y: -0.28754064, z: -0.006937729, w: 0.9571132}
    - {x: -0.0000075772396, y: 0.0000004423782, z: -0.000001301989, w: 1}
    - {x: -0.22711365, y: -0.29835564, z: -0.13008006, w: 0.9178685}
    - {x: 0.04236246, y: -0.39535898, z: 0.03159301, w: 0.91700524}
    - {x: 0.038744368, y: -0.1637663, z: 0.008015475, w: 0.98570544}
    - {x: -0.000008029863, y: 0.0000010747461, z: -0.0000007860363, w: 1}
    handOffset: {x: -0.00880003, y: 0.11870002, z: 0.008199692}
    localQuaternionOffset: {x: -0.72870857, y: -0.67237914, z: -0.08591046, w: 0.09751715}
    globalHandScale: {x: -0.8, y: 0.8, z: 0.8}
    fingerPoses:
    - poseRelativeMatrix:
      - e00: -1.0164325
        e01: -0.087936066
        e02: -0.72224116
        e03: 0.0045705796
        e10: -0.06520001
        e11: -1.224838
        e12: 0.24088874
        e13: 0.04100829
        e20: -0.72464406
        e21: 0.2335499
        e22: 0.9913882
        e23: 0.012535214
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.7759712
        e01: 0.010009737
        e02: -0.63069105
        e03: -0.033106785
        e10: -0.014163669
        e11: 0.9999024
        e12: -0.0015569591
        e13: 0.000000021061037
        e20: 0.63060874
        e21: 0.010140852
        e22: 0.776037
        e23: 0.000000049230916
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.54677606
        e01: -0.0046465555
        e02: -0.83726764
        e03: -0.02994037
        e10: 0.0064137424
        e11: 0.99998456
        e12: -0.0013608622
        e13: -0.00000004269213
        e20: 0.83725435
        e21: -0.004626058
        e22: 0.54679537
        e23: 0.000000017221446
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000004
        e01: 0.000014542153
        e02: -0.000005954618
        e03: -0.018000085
        e10: -0.000014714657
        e11: 1.0000006
        e12: 0.0000069028442
        e13: -0.0021000518
        e20: 0.0000060289317
        e21: -0.000006866926
        e22: 1.0000007
        e23: 0.0020999245
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: 0.3056195, y: 0.100213505, z: 0.94685316, w: -0.004802389}
      - {x: 0.0031034646, y: 0.33462292, z: 0.00641315, w: 0.9423252}
      - {x: -0.0009283238, y: 0.4760277, z: -0.0031442398, w: 0.87942415}
      - {x: -0.00000345512, y: 0.0000029428973, z: 0.000007286787, w: 1}
    - poseRelativeMatrix:
      - e00: -1.0515561
        e01: 0.0016483569
        e02: -0.6758162
        e03: 0.002378602
        e10: 0.0077193854
        e11: -1.2498882
        e12: -0.015059848
        e13: 0.009125877
        e20: -0.67577183
        e21: -0.016842533
        e22: 1.0514532
        e23: 0.017153978
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.6097563
        e01: -0.024635967
        e02: -0.7922079
        e03: -0.03862443
        e10: 0.013022386
        e11: 0.9996968
        e12: -0.021065084
        e13: 0.000000038602703
        e20: 0.7924826
        e21: 0.0025281396
        e22: 0.6098909
        e23: 0.00000014682202
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.7811069
        e01: -0.013856417
        e02: -0.62424695
        e03: -0.031118818
        e10: 0.008305828
        e11: 0.9999013
        e12: -0.011801782
        e13: -0.0000000033681185
        e20: 0.6243472
        e21: 0.004033571
        e22: 0.7811385
        e23: 0.00000008930084
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000005
        e01: 0.0000064350174
        e02: -0.000004456234
        e03: -0.017700238
        e10: -0.0000064163496
        e11: 1.000001
        e12: 0.000014406671
        e13: -0.0016000043
        e20: 0.000004477836
        e21: -0.000014414661
        e22: 1.0000007
        e23: 0.0012998845
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: 0.28173673, y: -0.00665, z: 0.9594679, w: -0.0012655008}
      - {x: 0.0065746843, y: 0.4416008, z: -0.010494134, w: 0.8971262}
      - {x: 0.004195143, y: 0.33077732, z: -0.0058712396, w: 0.9436813}
      - {x: -0.0000072026432, y: 0.0000021946862, z: 0.0000032162864, w: 1}
    - poseRelativeMatrix:
      - e00: -0.9488449
        e01: 0.15899438
        e02: -0.7980707
        e03: 0.0075293398
        e10: 0.10609861
        e11: -1.1912752
        e12: -0.36347386
        e13: -0.01785109
        e20: -0.80680597
        e21: -0.34364355
        e22: 0.8907733
        e23: 0.007617831
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.68393457
        e01: -0.074514925
        e02: -0.72573084
        e03: -0.033451386
        e10: 0.04795046
        e11: 0.9972145
        e12: -0.057200424
        e13: -0.000000024629117
        e20: 0.72796744
        e21: 0.0043224543
        e22: 0.6856006
        e23: 0.00000012514082
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.83454704
        e01: -0.0066961083
        e02: -0.5509001
        e03: -0.026750822
        e10: -0.03325691
        e11: 0.99749583
        e12: -0.0625045
        e13: 0.000000011469194
        e20: 0.5499374
        e21: 0.070484444
        e22: 0.8322276
        e23: -0.000000036948297
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000004
        e01: 0.0000026624766
        e02: 0.0000007700253
        e03: -0.014599964
        e10: -0.0000025007207
        e11: 1.0000008
        e12: 0.000015200159
        e13: -0.00160003
        e20: -0.00000093708206
        e21: -0.000015171996
        e22: 1.0000008
        e23: 0.00030032938
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: 0.34688795, y: -0.15284057, z: 0.92529875, w: 0.011433167}
      - {x: 0.016764946, y: 0.39613017, z: -0.033371482, w: 0.9174346}
      - {x: 0.03473712, y: 0.28754073, z: 0.006937662, w: 0.9571132}
      - {x: -0.000007595045, y: -0.00000037058794, z: 0.0000013070457, w: 1}
    - poseRelativeMatrix:
      - e00: -0.834447
        e01: 0.40570903
        e02: -0.8376169
        e03: 0.01567174
        e10: 0.111222446
        e11: -1.0734532
        e12: -0.63074106
        e13: -0.04460353
        e20: -0.9240282
        e21: -0.49558517
        e22: 0.6804925
        e23: -0.003317237
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.685388
        e01: -0.09143914
        e02: -0.7224177
        e03: -0.027588649
        e10: 0.024445275
        e11: 0.99441934
        e12: -0.10267445
        e13: 0.00000020104906
        e20: 0.7277711
        e21: 0.052712467
        e22: 0.68379515
        e23: -0.0000001647124
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.9462387
        e01: -0.028491925
        e02: -0.32222992
        e03: -0.019739985
        e10: 0.0031119233
        e11: 0.99687636
        e12: -0.07900641
        e13: -0.00000009638584
        e20: 0.32347384
        e21: 0.07375636
        e22: 0.9433599
        e23: 0.00000042869846
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000006
        e01: 0.0000016999458
        e02: 0.0000021786639
        e03: -0.013330014
        e10: -0.0000014398964
        e11: 1.0000007
        e12: 0.00001616324
        e13: -0.0010300397
        e20: -0.0000021259996
        e21: -0.000015982454
        e22: 1.0000006
        e23: 0.00019019585
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: 0.4021226, y: -0.25710106, z: 0.87617224, w: 0.06722102}
      - {x: 0.042362504, y: 0.39535856, z: -0.031592853, w: 0.9170054}
      - {x: 0.038744554, y: 0.16376644, z: -0.0080154305, w: 0.9857055}
      - {x: -0.000008087926, y: -0.0000010699395, z: 0.0000008283646, w: 1}
    - poseRelativeMatrix:
      - e00: -1.1174603
        e01: -0.33652872
        e02: 0.44780922
        e03: 0.018425511
        e10: 0.020258518
        e11: 0.97434986
        e12: 0.7827759
        e13: 0.03616684
        e20: -0.5597979
        e21: 0.70703447
        e22: -0.8655823
        e23: -0.07445717
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.9653007
        e01: 0.06855121
        e02: -0.25198984
        e03: -0.042079527
        e10: -0.07446133
        e11: 0.99712926
        e12: -0.013981537
        e13: -0.00000005674235
        e20: 0.25030747
        e21: 0.032260288
        e22: 0.96762925
        e23: -0.0000000070321016
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.542387
        e01: 0.011792031
        e02: -0.8400464
        e03: -0.031397037
        e10: -0.00809736
        e11: 0.9999323
        e12: 0.008808136
        e13: -0.00000004797083
        e20: 0.8400886
        e21: 0.0020251314
        e22: 0.54244435
        e23: -0.00000024106532
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000002
        e01: -0.0000047856806
        e02: -0.0000101590895
        e03: -0.013999947
        e10: 0.000005092077
        e11: 1.0000005
        e12: 0.000012381738
        e13: 0.0027000413
        e20: 0.000009770815
        e21: -0.000012139472
        e22: 1.0000005
        e23: 0.00010002101
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: 0.06895505, y: 0.91732097, z: 0.32481715, w: -0.21968457}
      - {x: 0.011662687, y: 0.12668666, z: 0.036069773, w: 0.9912182}
      - {x: 0.0019309702, y: -0.47830382, z: -0.0056625684, w: -0.87817407}
      - {x: -0.0000062184695, y: 0.000005124714, z: -0.0000023452387, w: 1.0000001}
    poseID:
      fingerLengths: []
  rightPoseSet: 1
  leftPose:
    rotationOffset: {x: 0, y: 0, z: 0}
    posePositions:
    - {x: -0.024664905, y: -0.030219551, z: -0.016220368}
    - {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
    - {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
    - {x: -0.0151, y: 0.0034, z: -0.0008}
    - {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
    - {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
    - {x: -0.029940333, y: 0, z: 0.000000023841856}
    - {x: -0.0207, y: -0.0021, z: 0.0019}
    - {x: -0.096640125, y: -0.008566598, z: 0.002615863}
    - {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
    - {x: -0.031118786, y: 0, z: 0.00000006854534}
    - {x: -0.0186, y: -0.0016, z: 0.0016}
    - {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
    - {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
    - {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
    - {x: -0.0166, y: -0.0016, z: 0.0007}
    - {x: -0.080928504, y: 0.034541752, z: -0.008505775}
    - {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
    - {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
    - {x: -0.01333, y: -0.00131, z: 0.00011}
    poseRotations:
    - {x: 0.824486, y: -0.17598078, z: 0.45968366, w: 0.27918532}
    - {x: 0.01166284, y: -0.12668674, z: -0.036069844, w: 0.99121815}
    - {x: -0.0019309819, y: -0.47830346, z: -0.0056621954, w: 0.8781743}
    - {x: -0.0000061402106, y: -0.0000049649398, z: 0.0000024675394, w: 1}
    - {x: 0.08363079, y: -0.4195727, z: 0.05875995, w: 0.901949}
    - {x: 0.0031034432, y: -0.33462396, z: -0.0064132046, w: 0.9423248}
    - {x: -0.00092819205, y: -0.47602734, z: 0.0031441716, w: 0.8794244}
    - {x: -0.0000034403058, y: -0.000003005378, z: -0.000007307157, w: 1}
    - {x: 0.002680663, y: -0.4448686, z: -0.011437493, w: 0.8955188}
    - {x: 0.006574654, y: -0.4416015, z: 0.010494141, w: 0.89712584}
    - {x: 0.0041950913, y: -0.33077687, z: 0.005871194, w: 0.9436814}
    - {x: -0.0000072037797, y: -0.0000022402965, z: -0.0000032116654, w: 1}
    - {x: -0.11289556, y: -0.37279072, z: -0.100843385, w: 0.9154847}
    - {x: 0.016764885, y: -0.39613065, z: 0.03337158, w: 0.9174344}
    - {x: 0.03473689, y: -0.28754064, z: -0.006937729, w: 0.9571132}
    - {x: -0.0000075772396, y: 0.0000004423782, z: -0.000001301989, w: 1}
    - {x: -0.22711365, y: -0.29835564, z: -0.13008006, w: 0.9178685}
    - {x: 0.04236246, y: -0.39535898, z: 0.03159301, w: 0.91700524}
    - {x: 0.038744368, y: -0.1637663, z: 0.008015475, w: 0.98570544}
    - {x: -0.000008029863, y: 0.0000010747461, z: -0.0000007860363, w: 1}
    handOffset: {x: 0.0019000173, y: 0.11890005, z: 0.008199692}
    localQuaternionOffset: {x: -0.70607865, y: 0.6961051, z: 0.0933502, w: 0.090423726}
    globalHandScale: {x: 0.8, y: 0.80000013, z: 0.79999995}
    fingerPoses:
    - poseRelativeMatrix:
      - e00: -1.0164325
        e01: -0.087936066
        e02: -0.72224116
        e03: 0.0045705796
        e10: -0.0652
        e11: -1.224838
        e12: 0.24088873
        e13: 0.041008286
        e20: -0.72464406
        e21: 0.2335499
        e22: 0.9913882
        e23: 0.012535631
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.7759712
        e01: 0.010009729
        e02: -0.6306911
        e03: -0.033106755
        e10: -0.014163653
        e11: 0.9999023
        e12: -0.0015569264
        e13: 0.000000018390594
        e20: 0.63060874
        e21: 0.01014087
        e22: 0.77603704
        e23: 0.00000022930968
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.54677606
        e01: -0.004646545
        e02: -0.83726764
        e03: -0.029940372
        e10: 0.0064137625
        e11: 0.99998444
        e12: -0.0013608587
        e13: -0.00000004605575
        e20: 0.83725435
        e21: -0.0046260287
        e22: 0.5467954
        e23: -0.00000004170854
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000004
        e01: 0.000014560486
        e02: -0.000005975876
        e03: -0.018000115
        e10: -0.000014706488
        e11: 1.0000006
        e12: 0.0000068996155
        e13: -0.0021000595
        e20: 0.0000060498564
        e21: -0.00000686236
        e22: 1.0000007
        e23: 0.0020998947
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: -0.3056195, y: 0.100213505, z: 0.94685316, w: 0.004802396}
      - {x: 0.0031034579, y: -0.33462292, z: -0.006413145, w: 0.9423252}
      - {x: -0.00092831487, y: -0.47602764, z: 0.0031442326, w: 0.8794242}
      - {x: -0.000003443494, y: -0.0000030236222, z: -0.0000072899056, w: 1}
    - poseRelativeMatrix:
      - e00: -1.0515561
        e01: 0.0016483569
        e02: -0.6758162
        e03: 0.002378602
        e10: 0.007719385
        e11: -1.2498882
        e12: -0.015059847
        e13: 0.009125877
        e20: -0.67577183
        e21: -0.016842533
        e22: 1.0514534
        e23: 0.017154396
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.6097563
        e01: -0.024635965
        e02: -0.7922079
        e03: -0.038624402
        e10: 0.013022386
        e11: 0.9996968
        e12: -0.021065084
        e13: 0.00000003938261
        e20: 0.7924826
        e21: 0.0025281399
        e22: 0.6098909
        e23: 0.00000011813056
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.7811069
        e01: -0.013856417
        e02: -0.62424695
        e03: -0.031118833
        e10: 0.008305828
        e11: 0.99990124
        e12: -0.011801778
        e13: -0.0000000016591851
        e20: 0.62434727
        e21: 0.004033571
        e22: 0.7811385
        e23: 0.00000003065867
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000005
        e01: 0.0000064333453
        e02: -0.0000044755993
        e03: -0.017700268
        e10: -0.0000064157184
        e11: 1.000001
        e12: 0.000014408382
        e13: -0.001600004
        e20: 0.0000044972667
        e21: -0.000014410837
        e22: 1.0000007
        e23: 0.0012998544
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: -0.28173667, y: -0.0066500003, z: 0.9594679, w: 0.0012654995}
      - {x: 0.006574687, y: -0.44160074, z: 0.010494132, w: 0.8971262}
      - {x: 0.004195145, y: -0.33077732, z: 0.005871242, w: 0.9436813}
      - {x: -0.0000072039047, y: -0.0000022206941, z: -0.0000032155635, w: 1}
    - poseRelativeMatrix:
      - e00: -0.9488449
        e01: 0.15899438
        e02: -0.7980707
        e03: 0.0075293398
        e10: 0.10609861
        e11: -1.1912752
        e12: -0.36347383
        e13: -0.01785109
        e20: -0.80680597
        e21: -0.34364358
        e22: 0.8907733
        e23: 0.007618247
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.68393457
        e01: -0.07451492
        e02: -0.7257309
        e03: -0.033451356
        e10: 0.047950454
        e11: 0.99721444
        e12: -0.057200447
        e13: -0.000000009587294
        e20: 0.72796744
        e21: 0.004322429
        e22: 0.6856006
        e23: 0.00000009609781
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.83454704
        e01: -0.0066961013
        e02: -0.5509001
        e03: -0.026750825
        e10: -0.03325692
        e11: 0.99749583
        e12: -0.06250451
        e13: 0.000000024857487
        e20: 0.5499374
        e21: 0.070484444
        e22: 0.8322276
        e23: -0.000000095523454
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000005
        e01: 0.0000026632006
        e02: 0.00000078144666
        e03: -0.014599994
        e10: -0.000002525078
        e11: 1.0000008
        e12: 0.000015197454
        e13: -0.0016000228
        e20: -0.0000008661004
        e21: -0.000015178716
        e22: 1.0000008
        e23: 0.00030026998
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: -0.34688795, y: -0.15284057, z: 0.92529875, w: -0.011433174}
      - {x: 0.016764943, y: -0.39613017, z: 0.033371463, w: 0.9174346}
      - {x: 0.034737125, y: -0.28754073, z: -0.006937654, w: 0.95711315}
      - {x: -0.000007602181, y: 0.00000039107064, z: -0.0000013420289, w: 1}
    - poseRelativeMatrix:
      - e00: -0.834447
        e01: 0.40570903
        e02: -0.8376169
        e03: 0.01567174
        e10: 0.11122243
        e11: -1.0734532
        e12: -0.63074106
        e13: -0.044603527
        e20: -0.9240282
        e21: -0.49558517
        e22: 0.6804925
        e23: -0.003316822
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.685388
        e01: -0.09143912
        e02: -0.7224178
        e03: -0.027588857
        e10: 0.024445243
        e11: 0.9944193
        e12: -0.102674514
        e13: 0.0000002271272
        e20: 0.7277711
        e21: 0.052712403
        e22: 0.68379515
        e23: -0.00000019707758
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.94623864
        e01: -0.02849194
        e02: -0.32222992
        e03: -0.019740012
        e10: 0.0031119254
        e11: 0.99687636
        e12: -0.079006426
        e13: -0.00000008207294
        e20: 0.32347384
        e21: 0.07375634
        e22: 0.94336
        e23: 0.00000036889233
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000006
        e01: 0.0000016686005
        e02: 0.0000021825008
        e03: -0.013330023
        e10: -0.0000014353841
        e11: 1.0000007
        e12: 0.00001617399
        e13: -0.0010300915
        e20: -0.000002116002
        e21: -0.000015993131
        e22: 1.0000007
        e23: 0.00019013658
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: -0.4021226, y: -0.25710112, z: 0.87617224, w: -0.067221045}
      - {x: 0.042362537, y: -0.3953586, z: 0.03159282, w: 0.9170054}
      - {x: 0.038744576, y: -0.16376644, z: 0.008015428, w: 0.9857055}
      - {x: -0.000008087051, y: 0.0000010903831, z: -0.0000007966211, w: 1}
    - poseRelativeMatrix:
      - e00: -1.1174603
        e01: -0.33652872
        e02: 0.44780922
        e03: 0.018425511
        e10: 0.020258518
        e11: 0.9743498
        e12: 0.7827759
        e13: 0.03616684
        e20: -0.5597979
        e21: 0.70703447
        e22: -0.8655823
        e23: -0.07445676
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.9653007
        e01: 0.06855122
        e02: -0.25198984
        e03: -0.042079628
        e10: -0.07446134
        e11: 0.9971292
        e12: -0.01398155
        e13: -0.00000009207348
        e20: 0.2503075
        e21: 0.032260247
        e22: 0.9676293
        e23: 0.00000005829919
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.542387
        e01: 0.011792041
        e02: -0.84004647
        e03: -0.031397004
        e10: -0.00809743
        e11: 0.9999322
        e12: 0.008808138
        e13: -0.00000008137669
        e20: 0.8400886
        e21: 0.0020251102
        e22: 0.54244435
        e23: -0.0000002098327
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000004
        e01: -0.0000048257425
        e02: -0.000010166134
        e03: -0.013999885
        e10: 0.000005021558
        e11: 1.0000004
        e12: 0.00001237561
        e13: 0.0027000096
        e20: 0.000009749627
        e21: -0.000012147145
        e22: 1.0000005
        e23: 0.000100005425
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: -0.06895507, y: 0.9173209, z: 0.32481724, w: 0.2196846}
      - {x: 0.011662762, y: -0.12668662, z: -0.036069773, w: 0.9912182}
      - {x: -0.001930978, y: -0.47830382, z: -0.0056625926, w: 0.87817407}
      - {x: -0.000006220047, y: -0.000005136335, z: 0.0000023778098, w: 1}
    poseID:
      fingerLengths: []
  leftPoseSet: 1
  poseName: 
  poseIndex: 0
  showEditorTools: 1
  editorHand: {fileID: 0}
  poseScriptable: {fileID: 0}
  poseEnabled: 1
  singleHanded: 0
  showAdvanced: 1
  positionWeight: 1
  rotationWeight: 1
  linkedPoses: []
  centerObject: {fileID: 2839866808768398351}
  up: {x: 0, y: 1, z: 0}
  useInvertPose: 0
  minAngle: -45
  maxAngle: 45
  maxRange: 0
  minRange: 0
  testAngle: 0
  testRange: 0
--- !u!114 &4354831851926983266
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807270805383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c49b27d5a0e0d4dd8ab20e2bed42e835, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreMe: 0
  rightPose:
    rotationOffset: {x: 0, y: 0, z: 0}
    posePositions:
    - {x: 0.12070954, y: -0.01766678, z: 0.021100923}
    - {x: 0.042893447, y: -0.0000084609355, z: 0.0000017032212}
    - {x: 0.033947673, y: 0.000014409911, z: 0.0000025844927}
    - {x: 0.0269, y: -0.0045, z: 0}
    - {x: 0.122446716, y: -0.012945374, z: -0.0057092216}
    - {x: 0.04645538, y: 0.00000011370516, z: 0.0000068752465}
    - {x: 0.03651106, y: 0.0000127191215, z: 0.000010192743}
    - {x: 0.0346, y: -0.0098, z: 0}
    - {x: 0.114982255, y: -0.017546834, z: -0.028469026}
    - {x: 0.044343784, y: -0.0000121684225, z: 0.0000071469185}
    - {x: 0.03477288, y: 0.000018948189, z: 0.0000029968849}
    - {x: 0.031, y: -0.00657, z: -0.005}
    - {x: 0.10429892, y: -0.02199284, z: -0.046480354}
    - {x: 0.03575829, y: 0.0000012743292, z: 0.0000049409896}
    - {x: 0.029866012, y: 0.000013231267, z: 0.0000070348688}
    - {x: 0.0278, y: -0.0051, z: -0.0043}
    - {x: 0.0353, y: -0.0064, z: 0.0216}
    - {x: 0.0392, y: -0.0025, z: 0.0016}
    - {x: 0.040632855, y: -0.000003362748, z: -0.0000025826553}
    - {x: 0.02917411, y: -0.0067583295, z: -0.0035}
    poseRotations:
    - {x: -0.01581704, y: -0.04051818, z: -0.4495866, w: 0.89217716}
    - {x: 0.007562753, y: 0.011700148, z: -0.46946594, w: 0.8828407}
    - {x: 0.012840044, y: -0.003053892, z: -0.31503755, w: 0.9489875}
    - {x: 0, y: 0, z: 0, w: 1}
    - {x: -0.06400788, y: 0.032462295, z: -0.40323535, w: 0.91227764}
    - {x: 0.005997106, y: 0.0010148803, z: -0.51042217, w: 0.8599025}
    - {x: -0.01365556, y: 0.03642583, z: -0.26280785, w: 0.96406364}
    - {x: 0, y: 0, z: 0, w: 1}
    - {x: -0.15602072, y: 0.10548767, z: -0.3672261, w: 0.91086495}
    - {x: 0.037246913, y: 0.071609326, z: -0.5863674, w: 0.8060137}
    - {x: -0.065885074, y: -0.008237899, z: -0.18521315, w: 0.98045266}
    - {x: 0, y: 0, z: 0, w: 1}
    - {x: -0.093504444, y: 0.17260307, z: -0.2630584, w: 0.944598}
    - {x: 0.012044844, y: 0.010415329, z: -0.521324, w: 0.85321033}
    - {x: -0.008055412, y: -0.016099108, z: -0.25370285, w: 0.9671147}
    - {x: 0, y: 0, z: 0, w: 1}
    - {x: 0.7691362, y: -0.57326084, z: 0.04926119, w: 0.27816337}
    - {x: -0.2426228, y: -0.031017084, z: -0.062154748, w: 0.9676306}
    - {x: 0.037737597, y: 0.033493724, z: -0.30163017, w: 0.95208895}
    - {x: 0, y: 0, z: 0, w: 1}
    handOffset: {x: 0.012000037, y: 0.09069999, z: 0.008599894}
    localQuaternionOffset: {x: 0.7251728, y: 0.6866705, z: 0.03395593, w: 0.038147148}
    globalHandScale: {x: 0.85000026, y: 0.8500002, z: 0.85}
    fingerPoses:
    - poseRelativeMatrix:
      - e00: -0.5422313
        e01: 0.84018755
        e02: -0.00838835
        e03: 0.022819377
        e10: 0.10252739
        e11: 0.0760702
        e12: 0.99181736
        e13: 0.008143723
        e20: 0.8339506
        e21: 0.5369344
        e22: -0.12738967
        e23: 0.01897738
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.55893016
        e01: 0.91201484
        e02: 0.014913708
        e03: 0.04289396
        e10: -0.8287509
        e11: 0.6149979
        e12: -0.026772892
        e13: -0.000008549222
        e20: -0.02775969
        e21: 0.0026045917
        e22: 1.0995735
        e23: 0.0000016816199
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.80148435
        e01: 0.5978555
        e02: -0.013886484
        e03: 0.033948023
        e10: -0.59801185
        e11: 0.8011733
        e12: -0.022445913
        e13: 0.0000145224885
        e20: -0.0022940652
        e21: 0.026294297
        e22: 0.99965197
        e23: 0.0000025561412
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000004
        e01: 0.00000029315294
        e02: -0.000000026885306
        e03: 0.026899952
        e10: 0.00000019278235
        e11: 1.0000007
        e12: 0.00000004447425
        e13: -0.004500168
        e20: -0.00000007042262
        e21: 0.00000003051567
        e22: 1.0000002
        e23: 0.00000003011322
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: 0.3567522, y: 0.66062295, z: 0.5785265, w: -0.3187669}
      - {x: 0.007562721, y: 0.011700209, z: -0.4694661, w: 0.88284063}
      - {x: 0.012582934, y: -0.002738687, z: -0.2936046, w: 0.9558402}
      - {x: -0.000000039043044, y: -0.000000022093353, z: -0.00000011038099, w: 1}
    - poseRelativeMatrix:
      - e00: -0.45222712
        e01: 0.8886576
        e02: 0.07601645
        e03: 0.029140942
        e10: 0.015449741
        e11: -0.0774117
        e12: 0.99687964
        e13: -0.018360527
        e20: 0.8917691
        e21: 0.45199054
        e22: 0.021278283
        e23: 0.020264216
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.47893676
        e01: 0.96562296
        e02: -0.0048143673
        e03: 0.046455763
        e10: -0.8778149
        e11: 0.526753
        e12: -0.012484923
        e13: -0.00000009621014
        e20: -0.007867511
        e21: 0.010205592
        e22: 1.0999193
        e23: 0.00000688762
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.8592104
        e01: 0.5057326
        e02: 0.07741125
        e03: 0.036511343
        e10: -0.5077216
        e11: 0.861491
        e12: 0.0071837837
        e13: 0.000012402659
        e20: -0.063056074
        e21: -0.045475677
        e22: 0.9969741
        e23: 0.000010265394
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000006
        e01: 0.00000033487686
        e02: -0.00000007307995
        e03: 0.03459997
        e10: 0.00000019323119
        e11: 1.0000005
        e12: 0.00000008406679
        e13: -0.0097998455
        e20: -0.0000000067444086
        e21: 0.00000004987505
        e22: 1.0000008
        e23: 0.000000053982593
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: 0.38855705, y: 0.5817081, z: 0.6226791, w: -0.35058504}
      - {x: 0.005997113, y: 0.001014894, z: -0.5104222, w: 0.8599024}
      - {x: -0.012063146, y: 0.033292267, z: -0.24327502, w: 0.9693108}
      - {x: -0.0000000343826, y: -0.00000003924406, z: -0.00000012275521, w: 1.0000001}
    - poseRelativeMatrix:
      - e00: -0.4008543
        e01: 0.8794053
        e02: 0.25683168
        e03: 0.023299908
        e10: -0.051043563
        e11: -0.30134338
        e12: 0.9521487
        e13: -0.04167006
        e20: 0.91471887
        e21: 0.36856326
        e22: 0.16568294
        e23: 0.016087031
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.3020911
        e01: 1.0456327
        e02: 0.078930974
        e03: 0.04434387
        e10: -0.93990654
        e11: 0.34052882
        e12: -0.158424
        e13: -0.000012122762
        e20: -0.15911707
        e21: -0.02632942
        e22: 1.0856669
        e23: 0.000007144914
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.9312564
        e01: 0.36427167
        e02: 0.008251925
        e03: 0.03477318
        e10: -0.36209956
        e11: 0.9227112
        e12: 0.13224585
        e13: 0.000019086408
        e20: 0.040559303
        e21: -0.12614292
        e22: 0.99118304
        e23: 0.0000029486532
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000005
        e01: 0.00000030258713
        e02: 0.00000009000704
        e03: 0.031000229
        e10: 0.00000036439445
        e11: 1.0000006
        e12: 0.000000026222558
        e13: -0.006569738
        e20: 0.000000062271795
        e21: 0.000000075558674
        e22: 1.0000005
        e23: -0.0050000553
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: 0.42860422, y: 0.48317388, z: 0.6833521, w: -0.34039876}
      - {x: 0.03724691, y: 0.07160931, z: -0.5863676, w: 0.8060136}
      - {x: -0.065941975, y: -0.0075777224, z: -0.16986941, w: 0.9832287}
      - {x: -0.000000016843174, y: 0.000000046880935, z: -0.0000001432113, w: 1}
    - poseRelativeMatrix:
      - e00: -0.19667439
        e01: 0.9658836
        e02: 0.16848831
        e03: 0.01623378
        e10: -0.23911233
        e11: -0.21390663
        e12: 0.9471374
        e13: -0.06044531
        e20: 0.9508652
        e21: 0.14599015
        e22: 0.27302474
        e23: 0.008612823
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.45622587
        e01: 0.97883403
        e02: 0.005735781
        e03: 0.035758916
        e10: -0.88934755
        e11: 0.5017678
        e12: -0.034554373
        e13: 0.0000012615243
        e20: -0.030331457
        e21: 0.0106632495
        e22: 1.0994428
        e23: 0.000005037261
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.87075216
        e01: 0.490979
        e02: -0.027052015
        e03: 0.029866064
        e10: -0.4904605
        e11: 0.87114
        e12: 0.023749772
        e13: 0.000013552247
        e20: 0.035226878
        e21: -0.007412366
        e22: 0.9993521
        e23: 0.00000696279
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000007
        e01: 0.00000018634245
        e02: 0.00000006860404
        e03: 0.02780024
        e10: 0.00000014649517
        e11: 1.0000002
        e12: 0.00000003628366
        e13: -0.0051002973
        e20: 0.00000013791666
        e21: -0.0000001735923
        e22: 1.0000006
        e23: -0.004300037
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: 0.4313373, y: 0.42123127, z: 0.6487692, w: -0.4643392}
      - {x: 0.012044905, y: 0.010415253, z: -0.521324, w: 0.8532104}
      - {x: -0.008662081, y: -0.014741275, z: -0.2344656, w: 0.9719741}
      - {x: -0.000000082519094, y: 0.00000004123439, z: -0.00000007040122, w: 1}
    - poseRelativeMatrix:
      - e00: -0.69728947
        e01: -0.5156986
        e02: -0.49783805
        e03: 0.0030538207
        e10: 0.39213234
        e11: 0.3069437
        e12: -0.86719
        e13: 0.0034602005
        e20: 0.6000166
        e21: -0.79990065
        e22: -0.011806688
        e23: -0.06474477
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0893854
        e01: 0.13533676
        e02: -0.02986553
        e03: 0.03920025
        e10: -0.115758
        e11: 0.87454206
        e12: 0.4733943
        e13: -0.002500157
        e20: 0.09920532
        e21: -0.46568307
        e22: 0.8803448
        e23: 0.0016000264
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.8157952
        e01: 0.57688594
        e02: 0.04101248
        e03: 0.04063314
        e10: -0.5718295
        e11: 0.8151906
        e12: -0.092064686
        e13: -0.000003595527
        e20: -0.08654334
        e21: 0.051653694
        e22: 0.99490863
        e23: -0.0000026729385
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000006
        e01: 0.00000019176834
        e02: 0.000000022333758
        e03: 0.029174339
        e10: 0.0000002051436
        e11: 1.0000007
        e12: -0.00000017519173
        e13: -0.0067581614
        e20: 0.0000002225159
        e21: 0.00000020690943
        e22: 1.0000005
        e23: -0.0034998667
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: -0.04351322, y: 0.7099366, z: -0.587056, w: -0.38660306}
      - {x: -0.24262263, y: -0.031016905, z: -0.062154833, w: 0.9676306}
      - {x: -0.036299977, y: -0.036278132, z: 0.3231083, w: -0.9449695}
      - {x: 0.000000117322244, y: -0.00000003558804, z: -0.00000007683349, w: 1.0000001}
    poseID:
      fingerLengths: []
  rightPoseSet: 1
  leftPose:
    rotationOffset: {x: 0, y: 0, z: 0}
    posePositions:
    - {x: 0.12070954, y: -0.01766678, z: 0.021100923}
    - {x: 0.042893447, y: -0.0000084609355, z: 0.0000017032212}
    - {x: 0.033947673, y: 0.000014409911, z: 0.0000025844927}
    - {x: 0.0269, y: -0.0045, z: 0}
    - {x: 0.122446716, y: -0.012945374, z: -0.0057092216}
    - {x: 0.04645538, y: 0.00000011370516, z: 0.0000068752465}
    - {x: 0.03651106, y: 0.0000127191215, z: 0.000010192743}
    - {x: 0.0346, y: -0.0098, z: 0}
    - {x: 0.114982255, y: -0.017546834, z: -0.028469026}
    - {x: 0.044343784, y: -0.0000121684225, z: 0.0000071469185}
    - {x: 0.03477288, y: 0.000018948189, z: 0.0000029968849}
    - {x: 0.031, y: -0.00657, z: -0.005}
    - {x: 0.10429892, y: -0.02199284, z: -0.046480354}
    - {x: 0.03575829, y: 0.0000012743292, z: 0.0000049409896}
    - {x: 0.029866012, y: 0.000013231267, z: 0.0000070348688}
    - {x: 0.0278, y: -0.0051, z: -0.0043}
    - {x: 0.0353, y: -0.0064, z: 0.0216}
    - {x: 0.0392, y: -0.0025, z: 0.0016}
    - {x: 0.040632855, y: -0.000003362748, z: -0.0000025826553}
    - {x: 0.02917411, y: -0.0067583295, z: -0.0035}
    poseRotations:
    - {x: -0.01581704, y: -0.04051818, z: -0.4495866, w: 0.89217716}
    - {x: 0.007562753, y: 0.011700148, z: -0.46946594, w: 0.8828407}
    - {x: 0.012840044, y: -0.003053892, z: -0.31503755, w: 0.9489875}
    - {x: 0, y: 0, z: 0, w: 1}
    - {x: -0.06400788, y: 0.032462295, z: -0.40323535, w: 0.91227764}
    - {x: 0.005997106, y: 0.0010148803, z: -0.51042217, w: 0.8599025}
    - {x: -0.01365556, y: 0.03642583, z: -0.26280785, w: 0.96406364}
    - {x: 0, y: 0, z: 0, w: 1}
    - {x: -0.15602072, y: 0.10548767, z: -0.3672261, w: 0.91086495}
    - {x: 0.037246913, y: 0.071609326, z: -0.5863674, w: 0.8060137}
    - {x: -0.065885074, y: -0.008237899, z: -0.18521315, w: 0.98045266}
    - {x: 0, y: 0, z: 0, w: 1}
    - {x: -0.093504444, y: 0.17260307, z: -0.2630584, w: 0.944598}
    - {x: 0.012044844, y: 0.010415329, z: -0.521324, w: 0.85321033}
    - {x: -0.008055412, y: -0.016099108, z: -0.25370285, w: 0.9671147}
    - {x: 0, y: 0, z: 0, w: 1}
    - {x: 0.7691362, y: -0.57326084, z: 0.04926119, w: 0.27816337}
    - {x: -0.2426228, y: -0.031017084, z: -0.062154748, w: 0.9676306}
    - {x: 0.037737597, y: 0.033493724, z: -0.30163017, w: 0.95208895}
    - {x: 0, y: 0, z: 0, w: 1}
    handOffset: {x: -0.011999952, y: 0.09069998, z: 0.0086001735}
    localQuaternionOffset: {x: 0.7251728, y: -0.6866705, z: -0.03395846, w: 0.038144756}
    globalHandScale: {x: -0.85, y: 0.85, z: 0.85}
    fingerPoses:
    - poseRelativeMatrix:
      - e00: -0.54223114
        e01: 0.88219666
        e02: -0.008807777
        e03: 0.022819424
        e10: 0.10252738
        e11: 0.07987369
        e12: 1.0414081
        e13: 0.008143668
        e20: 0.83395064
        e21: 0.56378114
        e22: -0.13375916
        e23: 0.018977538
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.55893
        e01: 0.9120147
        e02: 0.014913711
        e03: 0.042893533
        e10: -0.8287505
        e11: 0.6149978
        e12: -0.02677294
        e13: -0.000008393602
        e20: -0.027759716
        e21: 0.0026046515
        e22: 1.0995731
        e23: 0.000001660429
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.80148405
        e01: 0.59785527
        e02: -0.01388646
        e03: 0.0339478
        e10: -0.59801185
        e11: 0.8011732
        e12: -0.022445831
        e13: 0.000014875984
        e20: -0.0022939402
        e21: 0.026294298
        e22: 0.99965173
        e23: 0.0000025558688
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000002
        e01: 0.00000014413814
        e02: -0.000000020950296
        e03: 0.026899962
        e10: -0.00000011790242
        e11: 1.0000006
        e12: 0.000000039972125
        e13: -0.004499706
        e20: -0.00000002241725
        e21: 0.000000046875602
        e22: 1
        e23: -0.000000024254852
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: -0.35675207, y: 0.66062313, z: 0.5785265, w: 0.3187668}
      - {x: 0.0078439275, y: -0.011326538, z: 0.45937118, w: 0.8881377}
      - {x: 0.011968427, y: 0.002696739, z: 0.30455765, w: 0.952415}
      - {x: -0.000000043746876, y: 0.00000000834246, z: 0.0000000607382, w: 1.0000001}
    - poseRelativeMatrix:
      - e00: -0.45222697
        e01: 0.93309003
        e02: 0.079817295
        e03: 0.029140946
        e10: 0.015449754
        e11: -0.08128227
        e12: 1.0467232
        e13: -0.01836057
        e20: 0.8917691
        e21: 0.47459003
        e22: 0.022342263
        e23: 0.020264436
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.47893652
        e01: 0.9656229
        e02: -0.004814406
        e03: 0.046455905
        e10: -0.87781453
        e11: 0.526753
        e12: -0.012484874
        e13: 0.00000004976949
        e20: -0.007867524
        e21: 0.010205638
        e22: 1.099919
        e23: 0.0000069228035
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.85920995
        e01: 0.5057326
        e02: 0.07741123
        e03: 0.03651132
        e10: -0.50772136
        e11: 0.8614911
        e12: 0.0071837124
        e13: 0.000012719064
        e20: -0.063055985
        e21: -0.045475654
        e22: 0.99697375
        e23: 0.000010216032
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000002
        e01: 0.00000020810923
        e02: -0.00000003873095
        e03: 0.03460027
        e10: 0.00000022275138
        e11: 1.0000005
        e12: 0.00000008242459
        e13: -0.0097998
        e20: -0.000000034727016
        e21: 0.00000006606491
        e22: 1.0000005
        e23: 0.00000004730047
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: -0.388557, y: 0.58170813, z: 0.6226792, w: 0.35058495}
      - {x: 0.005955027, y: -0.0010426766, z: 0.50146, w: 0.86515975}
      - {x: -0.010791417, y: -0.033169936, z: 0.25319576, w: 0.966786}
      - {x: 0.000000002233734, y: 0.00000000910712, z: 0.00000011291811, w: 1}
    - poseRelativeMatrix:
      - e00: -0.40085414
        e01: 0.9233752
        e02: 0.26967314
        e03: 0.02329995
        e10: -0.051043548
        e11: -0.31641042
        e12: 0.9997558
        e13: -0.041670095
        e20: 0.91471875
        e21: 0.3869915
        e22: 0.17396711
        e23: 0.016087342
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.30209103
        e01: 1.0456326
        e02: 0.07893097
        e03: 0.04434424
        e10: -0.9399062
        e11: 0.34052888
        e12: -0.15842395
        e13: -0.000012285395
        e20: -0.15911707
        e21: -0.026329316
        e22: 1.0856665
        e23: 0.0000071727377
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.9312562
        e01: 0.36427134
        e02: 0.008251848
        e03: 0.03477318
        e10: -0.3620997
        e11: 0.9227112
        e12: 0.13224603
        e13: 0.000019747693
        e20: 0.040559217
        e21: -0.12614292
        e22: 0.9911824
        e23: 0.000002996344
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000005
        e01: 0.0000001898989
        e02: -0.000000030376135
        e03: 0.031000147
        e10: 0.00000019868688
        e11: 1.0000004
        e12: 0.00000008234636
        e13: -0.0065697697
        e20: -0.000000054018752
        e21: 0.00000009873777
        e22: 0.99999994
        e23: -0.0050000483
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: -0.42860416, y: 0.4831739, z: 0.68335223, w: 0.3403987}
      - {x: 0.03875456, y: -0.06996541, z: 0.580727, w: 0.81016}
      - {x: -0.063079186, y: 0.0075939945, z: 0.17752475, w: 0.98206335}
      - {x: -0.000000006054604, y: -0.000000005680926, z: 0.000000112957025, w: 1.0000001}
    - poseRelativeMatrix:
      - e00: -0.19667432
        e01: 1.0141774
        e02: 0.1769127
        e03: 0.01623377
        e10: -0.23911223
        e11: -0.22460191
        e12: 0.9944942
        e13: -0.060445357
        e20: 0.95086515
        e21: 0.15328965
        e22: 0.28667596
        e23: 0.008612781
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.45622578
        e01: 0.97883385
        e02: 0.005735827
        e03: 0.03575882
        e10: -0.8893475
        e11: 0.5017675
        e12: -0.034554385
        e13: 0.0000011237593
        e20: -0.030331414
        e21: 0.010663411
        e22: 1.0994421
        e23: 0.0000049722917
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.8707519
        e01: 0.49097908
        e02: -0.027051985
        e03: 0.029866217
        e10: -0.49046025
        e11: 0.87114006
        e12: 0.023749819
        e13: 0.000013310214
        e20: 0.035226755
        e21: -0.0074121677
        e22: 0.99935156
        e23: 0.0000069994167
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000005
        e01: 0.0000001477216
        e02: 0.000000018973497
        e03: 0.027800074
        e10: 0.00000014242256
        e11: 1.0000004
        e12: 0.00000006937121
        e13: -0.005099616
        e20: 0.00000003881058
        e21: -0.000000012023289
        e22: 1.0000002
        e23: -0.0042999224
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: -0.4313373, y: 0.4212313, z: 0.6487692, w: 0.4643392}
      - {x: 0.012212461, y: -0.010187288, z: 0.5127306, w: 0.8584023}
      - {x: -0.008581259, y: 0.014711173, z: 0.24419159, w: 0.96957755}
      - {x: -0.000000090977565, y: -0.000000013849776, z: 0.00000006332939, w: 1.0000001}
    - poseRelativeMatrix:
      - e00: -0.69728917
        e01: -0.51569843
        e02: -0.4978379
        e03: 0.003053812
        e10: 0.39213225
        e11: 0.30694357
        e12: -0.86718976
        e13: 0.0034601558
        e20: 0.6000166
        e21: -0.7999006
        e22: -0.011806711
        e23: -0.064744405
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0893852
        e01: 0.13533665
        e02: -0.02986568
        e03: 0.039200123
        e10: -0.11575814
        e11: 0.8745419
        e12: 0.4733942
        e13: -0.0025000563
        e20: 0.099205256
        e21: -0.4656829
        e22: 0.88034457
        e23: 0.001600006
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 0.81579506
        e01: 0.5768857
        e02: 0.041012414
        e03: 0.040633105
        e10: -0.5718295
        e11: 0.8151904
        e12: -0.09206457
        e13: -0.000003087088
        e20: -0.08654344
        e21: 0.051653627
        e22: 0.9949084
        e23: -0.0000024324531
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      - e00: 1.0000004
        e01: 0.00000014722988
        e02: -0.000000017022849
        e03: 0.029174365
        e10: 0.00000015352538
        e11: 1.0000004
        e12: -0.00000014134517
        e13: -0.0067582014
        e20: 0.00000015696352
        e21: 0.00000018005295
        e22: 1.0000002
        e23: -0.0034998225
        e30: 0
        e31: 0
        e32: 0
        e33: 1
      localRotations:
      - {x: 0.043513115, y: 0.7099366, z: -0.58705604, w: 0.38660297}
      - {x: -0.2426227, y: 0.031017026, z: 0.062154792, w: 0.9676306}
      - {x: 0.0363, y: -0.036278073, z: 0.32310843, w: 0.9449694}
      - {x: 0.000000031404404, y: 0.000000015015344, z: 0.00000004536865, w: 1}
    poseID:
      fingerLengths: []
  leftPoseSet: 1
  poseName: 
  poseIndex: 1
  showEditorTools: 1
  editorHand: {fileID: 0}
  poseScriptable: {fileID: 0}
  poseEnabled: 1
  singleHanded: 0
  showAdvanced: 1
  positionWeight: 1
  rotationWeight: 1
  linkedPoses: []
  centerObject: {fileID: 2839866808768398351}
  up: {x: 0, y: 1, z: 0}
  useInvertPose: 0
  minAngle: -45
  maxAngle: 45
  maxRange: 0
  minRange: 0
  testAngle: 0
  testRange: 0
--- !u!114 &838355948667608913
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807270805383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ac82bc3766612f34da61b06da6008fa9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  poses:
  - {fileID: 8913793188326431229}
  - {fileID: 4354831851926983266}
--- !u!114 &3863732110451042912
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807270805383}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 18f6faf550e7518449699c9a49dc7298, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  invertValue: 0
  playRange: 0.025
  threshold: 0.05
  stepCount: 7
  startStep: 0
  OnMax:
    m_PersistentCalls:
      m_Calls: []
  OnMid:
    m_PersistentCalls:
      m_Calls: []
  OnMin:
    m_PersistentCalls:
      m_Calls: []
  stepEvents:
  - step: 1
    OnStepEnter:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732110138787996}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 4f90fb2688849c54588be9a3d89d6f46, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
    OnStepExit:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732110138787996}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 3ca04dbd767079644aaa779bd2df9b91, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
  - step: 2
    OnStepEnter:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732109687929701}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 4f90fb2688849c54588be9a3d89d6f46, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
    OnStepExit:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732109687929701}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 3ca04dbd767079644aaa779bd2df9b91, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
  - step: 3
    OnStepEnter:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732110474509583}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 4f90fb2688849c54588be9a3d89d6f46, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
    OnStepExit:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732110474509583}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 3ca04dbd767079644aaa779bd2df9b91, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
  - step: 4
    OnStepEnter:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732111554214038}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 4f90fb2688849c54588be9a3d89d6f46, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
    OnStepExit:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732111554214038}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 3ca04dbd767079644aaa779bd2df9b91, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
  - step: 5
    OnStepEnter:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732111316154995}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 4f90fb2688849c54588be9a3d89d6f46, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
    OnStepExit:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732111316154995}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 3ca04dbd767079644aaa779bd2df9b91, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
  - step: 6
    OnStepEnter:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732111465791694}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 4f90fb2688849c54588be9a3d89d6f46, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
    OnStepExit:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732111465791694}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 3ca04dbd767079644aaa779bd2df9b91, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
  - step: 7
    OnStepEnter:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732110723698619}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 4f90fb2688849c54588be9a3d89d6f46, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
    OnStepExit:
      m_PersistentCalls:
        m_Calls:
        - m_Target: {fileID: 3863732110723698619}
          m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
          m_MethodName: set_material
          m_Mode: 2
          m_Arguments:
            m_ObjectArgument: {fileID: 2100000, guid: 3ca04dbd767079644aaa779bd2df9b91, type: 2}
            m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
            m_IntArgument: 0
            m_FloatArgument: 0
            m_StringArgument: 
            m_BoolArgument: 0
          m_CallState: 2
--- !u!1 &2839866807539772110
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2839866807539772111}
  - component: {fileID: 2839866807539772106}
  - component: {fileID: 2839866807539772109}
  - component: {fileID: 2839866807539772108}
  m_Layer: 0
  m_Name: Cube (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2839866807539772111
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807539772110}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -0.83838433, y: 0.83333343, z: 0}
  m_LocalScale: {x: 0.22222218, y: 1.1111107, z: 0.70707065}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2839866808768398351}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &2839866807539772106
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807539772110}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2839866807539772109
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807539772110}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 7ddcdf368990e454ca3a6f892b1c0ce9, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!65 &2839866807539772108
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866807539772110}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1.0000004, y: 1.0000006, z: 1.0000001}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &2839866808124084458
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2839866808124084459}
  m_Layer: 0
  m_Name: Double Slider Stepper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2839866808124084459
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866808124084458}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00006447471, y: -0.11902936, z: -0.0004315228, w: -0.99289066}
  m_LocalPosition: {x: -1.468, y: 0.88, z: -5.117}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2839866807270805380}
  - {fileID: 2839866807195842793}
  - {fileID: 3863732110684002570}
  - {fileID: 3863732110138787998}
  - {fileID: 3863732109687929703}
  - {fileID: 3863732110474509577}
  - {fileID: 3863732111554214032}
  - {fileID: 3863732111316155005}
  - {fileID: 3863732111465791688}
  - {fileID: 3863732110723698629}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: -0.013, y: -346.328, z: 0.048}
--- !u!1 &2839866808768398350
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2839866808768398351}
  - component: {fileID: 2839866808768398345}
  - component: {fileID: 2839866808768398344}
  m_Layer: 0
  m_Name: Handle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2839866808768398351
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866808768398350}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: 0.7071068, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0.072400026, z: 0}
  m_LocalScale: {x: 0.049500015, y: 0.08999998, z: 0.049500003}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2839866807539772111}
  - {fileID: 2839866806956173414}
  m_Father: {fileID: 2839866807270805380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
--- !u!33 &2839866808768398345
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866808768398350}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &2839866808768398344
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2839866808768398350}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c969267be26862c4eabf4a176d7c88ef, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3863732109687929702
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732109687929703}
  - component: {fileID: 3863732109687929690}
  - component: {fileID: 3863732109687929701}
  - component: {fileID: 3863732109687929700}
  m_Layer: 0
  m_Name: d (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732109687929703
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109687929702}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000028894283, y: 0.0043109953, z: 9.531504e-10, w: 0.9999907}
  m_LocalPosition: {x: -0.127, y: 0.007, z: 0.054}
  m_LocalScale: {x: 0.025000002, y: 0.01, z: 0.025000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3863732109925553896}
  m_Father: {fileID: 2839866808124084459}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732109687929690
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109687929702}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732109687929701
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109687929702}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732109687929700
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109687929702}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732109886911282
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732109886911283}
  - component: {fileID: 3863732109886911286}
  - component: {fileID: 3863732109886911281}
  - component: {fileID: 3863732109886911280}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732109886911283
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109886911282}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.15, z: 0}
  m_LocalScale: {x: 1.2, y: 1, z: 1.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3863732111316155005}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732109886911286
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109886911282}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732109886911281
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109886911282}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 786c7caa508d1f344a2b05806f53730c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732109886911280
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109886911282}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732109925553899
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732109925553896}
  - component: {fileID: 3863732109925553903}
  - component: {fileID: 3863732109925553902}
  - component: {fileID: 3863732109925553897}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732109925553896
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109925553899}
  serializedVersion: 2
  m_LocalRotation: {x: -1.1368684e-13, y: -0.000000014901161, z: 2.8189895e-11, w: 1}
  m_LocalPosition: {x: 0, y: -0.15000023, z: 0}
  m_LocalScale: {x: 1.1999999, y: 1, z: 1.1999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3863732109687929703}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732109925553903
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109925553899}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732109925553902
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109925553899}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 786c7caa508d1f344a2b05806f53730c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732109925553897
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732109925553899}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732110024718838
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732110024718839}
  - component: {fileID: 3863732110024718826}
  - component: {fileID: 3863732110024718837}
  - component: {fileID: 3863732110024718836}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732110024718839
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110024718838}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.15, z: 0}
  m_LocalScale: {x: 1.2, y: 1, z: 1.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3863732111554214032}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732110024718826
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110024718838}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732110024718837
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110024718838}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 786c7caa508d1f344a2b05806f53730c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732110024718836
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110024718838}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732110113898756
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732110113898757}
  - component: {fileID: 3863732110113899000}
  - component: {fileID: 3863732110113899003}
  - component: {fileID: 3863732110113899002}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732110113898757
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110113898756}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.15, z: 0}
  m_LocalScale: {x: 1.2, y: 1, z: 1.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3863732110474509577}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732110113899000
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110113898756}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732110113899003
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110113898756}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 786c7caa508d1f344a2b05806f53730c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732110113899002
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110113898756}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732110138787993
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732110138787998}
  - component: {fileID: 3863732110138787997}
  - component: {fileID: 3863732110138787996}
  - component: {fileID: 3863732110138787999}
  m_Layer: 0
  m_Name: d (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732110138787998
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110138787993}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000028894283, y: 0.0043109953, z: 9.531504e-10, w: 0.9999907}
  m_LocalPosition: {x: -0.127, y: 0.007, z: 0.084}
  m_LocalScale: {x: 0.025000002, y: 0.01, z: 0.025000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3863732111701444457}
  m_Father: {fileID: 2839866808124084459}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732110138787997
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110138787993}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732110138787996
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110138787993}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732110138787999
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110138787993}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732110474509576
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732110474509577}
  - component: {fileID: 3863732110474509580}
  - component: {fileID: 3863732110474509583}
  - component: {fileID: 3863732110474509582}
  m_Layer: 0
  m_Name: d (3)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732110474509577
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110474509576}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000028894283, y: 0.0043109953, z: 9.531504e-10, w: 0.9999907}
  m_LocalPosition: {x: -0.127, y: 0.007, z: 0.024}
  m_LocalScale: {x: 0.025000002, y: 0.01, z: 0.025000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3863732110113898757}
  m_Father: {fileID: 2839866808124084459}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732110474509580
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110474509576}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732110474509583
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110474509576}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732110474509582
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110474509576}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732110572327974
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732110572327975}
  - component: {fileID: 3863732110572327962}
  - component: {fileID: 3863732110572327973}
  - component: {fileID: 3863732110572327972}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732110572327975
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110572327974}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.15, z: 0}
  m_LocalScale: {x: 1.2, y: 1, z: 1.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3863732111465791688}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732110572327962
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110572327974}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732110572327973
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110572327974}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 786c7caa508d1f344a2b05806f53730c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732110572327972
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110572327974}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732110684002581
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732110684002570}
  - component: {fileID: 3863732110684002574}
  - component: {fileID: 3863732110684002569}
  - component: {fileID: 3863732110684002568}
  - component: {fileID: 3863732110684002571}
  m_Layer: 0
  m_Name: Text (TMP) (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &3863732110684002570
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110684002581}
  m_LocalRotation: {x: -0.00041675824, y: -0.7985247, z: 0.601962, w: 8.1490725e-10}
  m_LocalPosition: {x: 0, y: 0, z: 0.1474}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2839866808124084459}
  m_LocalEulerAnglesHint: {x: 74.021, y: -179.896, z: 0.139}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -0, y: 0.015}
  m_SizeDelta: {x: 0.4, y: 0.4}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!23 &3863732110684002574
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110684002581}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: -3438387809978036471, guid: f5ba820a8971fa449b12f4c76629933c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!114 &3863732110684002569
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110684002581}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9541d86e2fd84c1d9990edf0852d74ab, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: 0
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: f5ba820a8971fa449b12f4c76629933c, type: 2}
  m_sharedMaterial: {fileID: -3438387809978036471, guid: f5ba820a8971fa449b12f4c76629933c, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 1
  m_fontSizeBase: 1
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 1
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_ActiveFontFeatures: 00000000
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 0
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 1
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  _SortingLayer: 0
  _SortingLayerID: 0
  _SortingOrder: 0
  m_hasFontAssetChanged: 0
  m_renderer: {fileID: 3863732110684002574}
  m_maskType: 0
--- !u!114 &3863732110684002568
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110684002581}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0049c988a2ee3424d948efba0d6c4524, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: {fileID: 3863732110684002569}
  sliderReader: {fileID: 2839866807270805377}
--- !u!114 &3863732110684002571
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110684002581}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d48f091a3199d14084f6e23151ace29, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  size: 1
--- !u!1 &3863732110723698628
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732110723698629}
  - component: {fileID: 3863732110723698616}
  - component: {fileID: 3863732110723698619}
  - component: {fileID: 3863732110723698618}
  m_Layer: 0
  m_Name: d (7)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732110723698629
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110723698628}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000028894283, y: 0.0043109953, z: 9.531504e-10, w: 0.9999907}
  m_LocalPosition: {x: -0.127, y: 0.007, z: -0.096}
  m_LocalScale: {x: 0.025000002, y: 0.01, z: 0.025000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3863732111441119565}
  m_Father: {fileID: 2839866808124084459}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732110723698616
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110723698628}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732110723698619
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110723698628}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732110723698618
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732110723698628}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732111316155004
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732111316155005}
  - component: {fileID: 3863732111316154992}
  - component: {fileID: 3863732111316154995}
  - component: {fileID: 3863732111316154994}
  m_Layer: 0
  m_Name: d (5)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732111316155005
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111316155004}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000028894283, y: 0.0043109953, z: 9.531504e-10, w: 0.9999907}
  m_LocalPosition: {x: -0.127, y: 0.007, z: -0.036}
  m_LocalScale: {x: 0.025000002, y: 0.01, z: 0.025000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3863732109886911283}
  m_Father: {fileID: 2839866808124084459}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732111316154992
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111316155004}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732111316154995
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111316155004}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732111316154994
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111316155004}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732111441119564
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732111441119565}
  - component: {fileID: 3863732111441119552}
  - component: {fileID: 3863732111441119555}
  - component: {fileID: 3863732111441119554}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732111441119565
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111441119564}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: -0.15, z: 0}
  m_LocalScale: {x: 1.2, y: 1, z: 1.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3863732110723698629}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732111441119552
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111441119564}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732111441119555
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111441119564}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 786c7caa508d1f344a2b05806f53730c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732111441119554
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111441119564}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732111465791691
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732111465791688}
  - component: {fileID: 3863732111465791695}
  - component: {fileID: 3863732111465791694}
  - component: {fileID: 3863732111465791689}
  m_Layer: 0
  m_Name: d (6)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732111465791688
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111465791691}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000028894283, y: 0.0043109953, z: 9.531504e-10, w: 0.9999907}
  m_LocalPosition: {x: -0.127, y: 0.007, z: -0.066}
  m_LocalScale: {x: 0.025000002, y: 0.01, z: 0.025000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3863732110572327975}
  m_Father: {fileID: 2839866808124084459}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732111465791695
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111465791691}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732111465791694
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111465791691}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732111465791689
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111465791691}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732111554214035
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732111554214032}
  - component: {fileID: 3863732111554214039}
  - component: {fileID: 3863732111554214038}
  - component: {fileID: 3863732111554214033}
  m_Layer: 0
  m_Name: d (4)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732111554214032
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111554214035}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000028894283, y: 0.0043109953, z: 9.531504e-10, w: 0.9999907}
  m_LocalPosition: {x: -0.127, y: 0.007, z: -0.006}
  m_LocalScale: {x: 0.025000002, y: 0.01, z: 0.025000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3863732110024718839}
  m_Father: {fileID: 2839866808124084459}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732111554214039
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111554214035}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732111554214038
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111554214035}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732111554214033
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111554214035}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
--- !u!1 &3863732111701444456
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3863732111701444457}
  - component: {fileID: 3863732111701444460}
  - component: {fileID: 3863732111701444463}
  - component: {fileID: 3863732111701444462}
  m_Layer: 0
  m_Name: Cylinder
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3863732111701444457
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111701444456}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: -0.15, z: 0}
  m_LocalScale: {x: 1.2, y: 1, z: 1.2}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3863732110138787998}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &3863732111701444460
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111701444456}
  m_Mesh: {fileID: 10206, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &3863732111701444463
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111701444456}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 786c7caa508d1f344a2b05806f53730c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &3863732111701444462
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3863732111701444456}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5000001
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0.000000059604645, y: 0, z: -0.00000008940697}
