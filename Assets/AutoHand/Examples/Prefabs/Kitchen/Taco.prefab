%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2063587596306059683
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3096238095380472752}
  - component: {fileID: 6314902766324046214}
  - component: {fileID: 333129968511591807}
  - component: {fileID: 8035188000993757359}
  - component: {fileID: 8035188000993757358}
  - component: {fileID: 8035188000993757356}
  - component: {fileID: 8035188000993757345}
  - component: {fileID: 8035188000993757344}
  m_Layer: 0
  m_Name: Taco
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3096238095380472752
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063587596306059683}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6314902766324046214
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063587596306059683}
  m_Mesh: {fileID: -8870946231481924797, guid: 3cc40bba46667ff4f99585487337013c, type: 3}
--- !u!23 &333129968511591807
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063587596306059683}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 4310586713465477566, guid: 3cc40bba46667ff4f99585487337013c, type: 3}
  - {fileID: -5354028657143720620, guid: 3cc40bba46667ff4f99585487337013c, type: 3}
  - {fileID: 7211448773848044386, guid: 3cc40bba46667ff4f99585487337013c, type: 3}
  - {fileID: -3092847324252717431, guid: 3cc40bba46667ff4f99585487337013c, type: 3}
  - {fileID: 1151844921754575795, guid: 3cc40bba46667ff4f99585487337013c, type: 3}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!54 &8035188000993757359
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063587596306059683}
  serializedVersion: 2
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!64 &8035188000993757358
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063587596306059683}
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_Enabled: 1
  serializedVersion: 4
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: -8870946231481924797, guid: 3cc40bba46667ff4f99585487337013c, type: 3}
--- !u!114 &8035188000993757356
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063587596306059683}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 822bc9090447b9c40833509c4a32e597, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  body: {fileID: 0}
  handType: 0
  ignoreWeight: 0
  maintainGrabOffset: 0
  singleHandOnly: 1
  allowHeldSwapping: 1
  instantGrab: 0
  parentOnGrab: 1
  throwMultiplyer: 1
  lookAssistMultiplyer: 1
  hightlightMaterial: {fileID: 0}
  jointBreakForce: 1000
  showAdvancedSettings: 0
  lockHandOnGrab: 0
  makeChildrenGrabbable: 1
  jointedBodies: []
  ignoreReleaseTime: 0.25
  grabDistancePriority: 1
  heldPositionOffset: {x: 0, y: 0, z: 0}
  heldRotationOffset: {x: 0, y: 0, z: 0}
  showEvents: 0
  onGrab:
    m_PersistentCalls:
      m_Calls: []
  onRelease:
    m_PersistentCalls:
      m_Calls: []
  onSqueeze:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 8035188000993757345}
        m_TargetAssemblyTypeName: 
        m_MethodName: DoSmash
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  onUnsqueeze:
    m_PersistentCalls:
      m_Calls: []
  onHighlight:
    m_PersistentCalls:
      m_Calls: []
  onUnhighlight:
    m_PersistentCalls:
      m_Calls: []
  pullApartBreakOnly: 1
  OnJointBreak:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 8035188000993757345}
        m_TargetAssemblyTypeName: 
        m_MethodName: DoSmash
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  isGrabbable: 1
--- !u!114 &8035188000993757345
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063587596306059683}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 07d975bb4661eb244a698f2a75d13fe3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  smashForce: 1.5
  destroyOnSmash: 0
  releaseOnSmash: 0
  effect: {fileID: 1708889463548190791, guid: ae95971c2f52c7a49b273f7a6547f819, type: 3}
  createNewEffect: 1
  applyVelocityOnSmash: 1
  smashSound: {fileID: 0}
  smashVolume: 1
  OnSmash:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &8035188000993757344
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2063587596306059683}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 94010c3364a7d0a43a5e49e48d078a08, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  breakVelocity: 1
  collisionLayers:
    serializedVersion: 2
    m_Bits: 260571447
  OnBreak:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: DoSmash
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
