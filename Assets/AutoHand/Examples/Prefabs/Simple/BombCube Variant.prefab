%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &935730197
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4882985345417924759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 07d975bb4661eb244a698f2a75d13fe3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  smashForce: 1
  effect: {fileID: 607072170870981586, guid: 43515b9813eab4f49a2480707cd37d71, type: 3}
--- !u!114 &935730198
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4882985345417924759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4757631d9b63da44ba986db68e9cd6d5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  radius: 1
  force: 250
--- !u!114 &837652195895096559
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4882985345417924759}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 94010c3364a7d0a43a5e49e48d078a08, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  breakVelocity: 1
  OnBreak:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 935730197}
        m_MethodName: DoSmash
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
      - m_Target: {fileID: 935730198}
        m_MethodName: Explode
        m_Mode: 6
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 1
        m_CallState: 2
--- !u!1001 &9166583154925950822
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 4392671673004984305, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_Name
      value: BombCube Variant
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984306, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984306, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984306, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984306, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984306, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984306, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984306, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984306, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984306, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984306, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984306, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4392671673004984311, guid: 47470f4847a93aa43b98be35833fa1ac,
        type: 3}
      propertyPath: m_Materials.Array.data[0]
      value: 
      objectReference: {fileID: 7211448773848044386, guid: 46f7390b8a973f14db1682dc6a3600b1,
        type: 3}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 47470f4847a93aa43b98be35833fa1ac, type: 3}
--- !u!1 &4882985345417924759 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 4392671673004984305, guid: 47470f4847a93aa43b98be35833fa1ac,
    type: 3}
  m_PrefabInstance: {fileID: 9166583154925950822}
  m_PrefabAsset: {fileID: 0}
