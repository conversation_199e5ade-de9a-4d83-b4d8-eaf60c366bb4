%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &8068074285747382416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8068074285747382431}
  - component: {fileID: 8068074285747382424}
  - component: {fileID: 8068074285747382425}
  - component: {fileID: 8068074285747382426}
  - component: {fileID: 8068074285747382427}
  - component: {fileID: 8068074285747382428}
  - component: {fileID: 8068074285747382429}
  - component: {fileID: 8068074285747382430}
  - component: {fileID: 5987229381699333705}
  m_Layer: 0
  m_Name: Capsule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8068074285747382431
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8068074285747382416}
  serializedVersion: 2
  m_LocalRotation: {x: -0.6852532, y: 0.21667999, z: 0.17335524, w: 0.67336905}
  m_LocalPosition: {x: -0.769, y: 0.986, z: 5.567}
  m_LocalScale: {x: 0.117402, y: 0.11740204, z: 0.11740199}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: -93.641, y: -58.643005, z: 90.679}
--- !u!33 &8068074285747382424
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8068074285747382416}
  m_Mesh: {fileID: 10208, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &8068074285747382425
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8068074285747382416}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3ca04dbd767079644aaa779bd2df9b91, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!136 &8068074285747382426
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8068074285747382416}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.5
  m_Height: 2
  m_Direction: 1
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &8068074285747382427
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8068074285747382416}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0.2
  m_AngularDrag: 0.5
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &8068074285747382428
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8068074285747382416}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 822bc9090447b9c40833509c4a32e597, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreMe: 0
  body: {fileID: 0}
  hightlightMaterial: {fileID: 2100000, guid: 5f2dd013d4c137746ad1d43ef197affe, type: 2}
  isGrabbable: 1
  CopySettings: {fileID: 0}
  grabType: 0
  grabPoseType: 0
  handType: 0
  singleHandOnly: 0
  instantGrab: 0
  useGentleGrab: 0
  maintainGrabOffset: 0
  parentOnGrab: 1
  heldNoFriction: 1
  ignoreWeight: 0
  allowHeldSwapping: 1
  throwPower: 1
  jointBreakForce: 2500
  showAdvancedSettings: 0
  makeChildrenGrabbable: 1
  grabPriorityWeight: 1
  ignoreReleaseTime: 0.25
  minHeldDrag: 1.5
  minHeldAngleDrag: 3
  minHeldMass: 0.1
  maxHeldVelocity: 10
  heldPositionOffset: {x: 0, y: 0, z: 0}
  heldRotationOffset: {x: 0, y: 0, z: 0}
  customGrabJoint: {fileID: 0}
  jointedBodies: []
  heldIgnoreColliders: []
  pullApartBreakOnly: 1
  showEvents: 1
  onGrab:
    m_PersistentCalls:
      m_Calls: []
  onRelease:
    m_PersistentCalls:
      m_Calls: []
  onSqueeze:
    m_PersistentCalls:
      m_Calls: []
  onUnsqueeze:
    m_PersistentCalls:
      m_Calls: []
  onHighlight:
    m_PersistentCalls:
      m_Calls: []
  onUnhighlight:
    m_PersistentCalls:
      m_Calls: []
  OnJointBreak:
    m_PersistentCalls:
      m_Calls: []
  lockHandOnGrab: 0
--- !u!82 &8068074285747382429
AudioSource:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8068074285747382416}
  m_Enabled: 1
  serializedVersion: 4
  OutputAudioMixerGroup: {fileID: 0}
  m_audioClip: {fileID: 0}
  m_Resource: {fileID: 8300000, guid: 1a275ef2521b85b4088a74c254ded17d, type: 3}
  m_PlayOnAwake: 0
  m_Volume: 1
  m_Pitch: 1.5
  Loop: 0
  Mute: 0
  Spatialize: 0
  SpatializePostEffects: 0
  Priority: 128
  DopplerLevel: 1
  MinDistance: 0.25
  MaxDistance: 25
  Pan2D: 0
  rolloffMode: 0
  BypassEffects: 0
  BypassListenerEffects: 0
  BypassReverbZones: 0
  rolloffCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0.01
      value: 1
      inSlope: -10.442891
      outSlope: -10.442891
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 1
    - serializedVersion: 3
      time: 0.03714325
      value: 0.5
      inSlope: -25.00996
      outSlope: -25.00996
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.062857665
      value: 0.266922
      inSlope: -6.25249
      outSlope: -6.25249
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.12571533
      value: 0.13346863
      inSlope: -1.5631225
      outSlope: -1.5631225
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.21714416
      value: 0.0625
      inSlope: -0.39078063
      outSlope: -0.39078063
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.32
      value: 0.03125
      inSlope: -0.09769516
      outSlope: -0.09769516
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 0.64
      value: 0.015625
      inSlope: -0.02442379
      outSlope: -0.02442379
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 0.01
      inSlope: -0.010003988
      outSlope: -0.010003988
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  panLevelCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  spreadCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  reverbZoneMixCustomCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0.33333334
      outWeight: 0.33333334
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &8068074285747382430
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8068074285747382416}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1c498b8ff53f43d43bb5f3b6001c2364, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  collisionTriggers:
    serializedVersion: 2
    m_Bits: 1631584311
  source: {fileID: 8068074285747382429}
  clip: {fileID: 8300000, guid: 1ebd2a75d209f8c418cde26c90046827, type: 3}
  velocityVolumeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 1
      outSlope: 1
      tangentMode: 34
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 1
      outSlope: 1
      tangentMode: 34
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  volumeAmp: 1
  velocityAmp: 0.3
  soundRepeatDelay: 0.2
--- !u!114 &5987229381699333705
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8068074285747382416}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 935aaf7da50abf94697c8d70a7a71efe, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreMe: 0
  instantPull: 1
  grabType: 0
  archMultiplier: 0.6
  gravitationVelocity: 1
  rotate: 1
  rotationSpeed: 5
  ignoreHighlights: 0
  targetedMaterial: {fileID: 0}
  selectedMaterial: {fileID: 0}
  showEvents: 1
  OnPull:
    m_PersistentCalls:
      m_Calls: []
  StartTargeting:
    m_PersistentCalls:
      m_Calls: []
  StopTargeting:
    m_PersistentCalls:
      m_Calls: []
  StartSelecting:
    m_PersistentCalls:
      m_Calls: []
  StopSelecting:
    m_PersistentCalls:
      m_Calls: []
