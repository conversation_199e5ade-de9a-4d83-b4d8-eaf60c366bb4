%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &25635429
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 25635430}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &25635430
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 25635429}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000034403058, y: -0.000003005378, z: -0.000007307157, w: 1}
  m_LocalPosition: {x: -0.0207, y: -0.0021, z: 0.0019}
  m_LocalScale: {x: 1, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 984978722}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &30320666
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 30320667}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &30320667
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 30320666}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000008029863, y: 0.0000010747461, z: -0.0000007860363, w: 1}
  m_LocalPosition: {x: -0.01333, y: -0.00131, z: 0.00011}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 459248287}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &56547632
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 56547633}
  - component: {fileID: 56547634}
  m_Layer: 0
  m_Name: Ring1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &56547633
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 56547632}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0037359423, y: -0.04221879, z: 0.037158713, w: 0.99841017}
  m_LocalPosition: {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  m_LocalScale: {x: 1.0000008, y: 1.0000033, z: 1.0000023}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1212297660}
  m_Father: {fileID: 1894696657}
  m_LocalEulerAnglesHint: {x: -0.323, y: 7.6440005, z: 4.268}
--- !u!136 &56547634
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 56547632}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.01
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.005, y: 0, z: 0.002}
--- !u!1 &100774091
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 100774092}
  - component: {fileID: 100774095}
  - component: {fileID: 100774094}
  - component: {fileID: 100774093}
  m_Layer: 0
  m_Name: Ring
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &100774092
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 100774091}
  serializedVersion: 2
  m_LocalRotation: {x: -0.12729833, y: -0.045578506, z: -0.14354861, w: 0.98036295}
  m_LocalPosition: {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  m_LocalScale: {x: 0.9999996, y: 1.000002, z: 1.0000023}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1670949134}
  m_Father: {fileID: 773422090}
  m_LocalEulerAnglesHint: {x: -13.576, y: 8.870001, z: -18.892}
--- !u!114 &100774095
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 100774091}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1722413317}
  tipRadius: 0.009
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 100774092}
  - {fileID: 1670949134}
  - {fileID: 1509029465}
  poseData: []
  minGripRotPose:
  - {x: -0.12884803, y: 0.05640752, z: -0.15346065, w: 0.97809315}
  - {x: -0.00033126769, y: 0.06671702, z: 0.037344586, w: 0.9970728}
  - {x: 0.035008434, y: 0.058341164, z: 0.005403131, w: 0.9976681}
  minGripPosPose:
  - {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  - {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  - {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
  maxGripRotPose:
  - {x: -0.08535152, y: -0.66045374, z: -0.04609367, w: 0.7445746}
  - {x: 0.027625704, y: -0.699027, z: 0.025130453, w: 0.7141194}
  - {x: 0.03158009, y: -0.53494, z: -0.016046247, w: 0.8441472}
  maxGripPosPose:
  - {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  - {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  - {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
--- !u!136 &100774094
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 100774091}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.005, y: 0, z: 0}
--- !u!65 &100774093
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 100774091}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0.0015}
--- !u!1 &119766580
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 119766581}
  - component: {fileID: 119766582}
  m_Layer: 0
  m_Name: Index2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &119766581
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 119766580}
  serializedVersion: 2
  m_LocalRotation: {x: -0.002070638, y: -0.09939676, z: 0.0025416242, w: 0.9950425}
  m_LocalPosition: {x: -0.029940333, y: 0, z: 0.000000023841856}
  m_LocalScale: {x: 0.99999976, y: 1.0000054, z: 1.0000014}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1467414177}
  m_Father: {fileID: 1044079233}
  m_LocalEulerAnglesHint: {x: -0.27, y: 0.98200005, z: 0.26200002}
--- !u!136 &119766582
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 119766580}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.007
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.009, y: 0, z: 0.0025}
--- !u!1 &140272389
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 140272390}
  - component: {fileID: 140272392}
  - component: {fileID: 140272391}
  m_Layer: 0
  m_Name: Middle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &140272390
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140272389}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0020609081, y: -0.06488923, z: 0.0122108795, w: 0.99781567}
  m_LocalPosition: {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  m_LocalScale: {x: 1.0000002, y: 1.000003, z: 1.0000015}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1168953082}
  m_Father: {fileID: 1137609619}
  m_LocalEulerAnglesHint: {x: 0.035, y: 4.433, z: 1.419}
--- !u!136 &140272392
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140272389}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.01
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!65 &140272391
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140272389}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.035, y: 0.02, z: 0.018}
  m_Center: {x: -0.015, y: 0, z: 0.002}
--- !u!1 &155834668
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 155834669}
  - component: {fileID: 155834670}
  m_Layer: 0
  m_Name: InnerMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &155834669
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 155834668}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7414494, y: 0.016383987, z: -0.0066156765, w: 0.6707761}
  m_LocalPosition: {x: 0.06556724, y: -0.0066842884, z: -0.14039922}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1990414918}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &155834670
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 155834668}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93ad1d8ba4d32924d8350ad986f4c73a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 8936583283049568085, guid: 17a48d918cf75e74aa527c89697f3d15, type: 3}
  m_Bones:
  - {fileID: 1990414918}
  - {fileID: 2057608971}
  - {fileID: 1669061717}
  - {fileID: 1980302040}
  - {fileID: 1833199286}
  - {fileID: 1183926533}
  - {fileID: 492039277}
  - {fileID: 221408174}
  - {fileID: 348929178}
  - {fileID: 1854517316}
  - {fileID: 653956371}
  - {fileID: 1355618820}
  - {fileID: 1749422815}
  - {fileID: 1952727344}
  - {fileID: 1150899743}
  - {fileID: 1843319558}
  - {fileID: 1191312104}
  - {fileID: 1192699942}
  - {fileID: 965286014}
  - {fileID: 978501049}
  - {fileID: 1149393779}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1990414918}
  m_AABB:
    m_Center: {x: -0.09229668, y: -0.015225619, z: -0.018114224}
    m_Extent: {x: 0.080366306, y: 0.05974908, z: 0.04299726}
  m_DirtyAABB: 0
--- !u!1 &212105229
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 212105230}
  - component: {fileID: 212105232}
  - component: {fileID: 212105231}
  m_Layer: 0
  m_Name: Pinky1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &212105230
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 212105229}
  serializedVersion: 2
  m_LocalRotation: {x: 0.03042995, y: -0.090418056, z: 0.043205522, w: 0.9945009}
  m_LocalPosition: {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  m_LocalScale: {x: 1.000002, y: 1.000004, z: 1.0000021}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 459248287}
  m_Father: {fileID: 1305778227}
  m_LocalEulerAnglesHint: {x: 2.7570002, y: 2.983, z: 5.3970003}
--- !u!136 &212105232
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 212105229}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.03
  m_Direction: 0
  m_Center: {x: -0.004, y: 0, z: 0}
--- !u!65 &212105231
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 212105229}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.02, z: 0.018}
  m_Center: {x: -0.005, y: 0, z: 0.002}
--- !u!1 &213269457
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 213269458}
  - component: {fileID: 213269459}
  m_Layer: 0
  m_Name: Thumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &213269458
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 213269457}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00029713922, y: -0.120069474, z: -0.0059749987, w: 0.9927475}
  m_LocalPosition: {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
  m_LocalScale: {x: 0.9999988, y: 1.0000035, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1972922847}
  m_Father: {fileID: 1612374346}
  m_LocalEulerAnglesHint: {x: -0.57500005, y: -66.714005, z: -0.374}
--- !u!136 &213269459
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 213269457}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.004, y: 0.0025, z: 0}
--- !u!1 &221408173
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 221408174}
  - component: {fileID: 221408177}
  - component: {fileID: 221408176}
  - component: {fileID: 221408175}
  m_Layer: 0
  m_Name: Index2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &221408174
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221408173}
  serializedVersion: 2
  m_LocalRotation: {x: -0.002070638, y: -0.09939676, z: 0.0025416242, w: 0.9950425}
  m_LocalPosition: {x: -0.029940333, y: 0, z: 0.000000023841856}
  m_LocalScale: {x: 0.99999976, y: 1.0000054, z: 1.0000014}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 348929178}
  m_Father: {fileID: 492039277}
  m_LocalEulerAnglesHint: {x: -0.27, y: 0.98200005, z: 0.26200002}
--- !u!136 &221408177
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221408173}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.007
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.009, y: 0, z: 0.0025}
--- !u!65 &221408176
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221408173}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!65 &221408175
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 221408173}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &256103718
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 256103719}
  - component: {fileID: 256103721}
  - component: {fileID: 256103720}
  m_Layer: 0
  m_Name: Index1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &256103719
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256103718}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0048813173, y: -0.03711859, z: -0.005189765, w: 0.99928546}
  m_LocalPosition: {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  m_LocalScale: {x: 0.9999992, y: 1.0000033, z: 1.0000032}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 284372187}
  m_Father: {fileID: 710639190}
  m_LocalEulerAnglesHint: {x: 0.63500005, y: 5.677, z: -0.513}
--- !u!136 &256103721
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256103718}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.01, y: 0, z: 0}
--- !u!65 &256103720
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 256103718}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.035, y: 0.02, z: 0.018}
  m_Center: {x: -0.015, y: 0, z: 0.002}
--- !u!1 &271303542
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 271303543}
  - component: {fileID: 271303546}
  - component: {fileID: 271303545}
  - component: {fileID: 271303544}
  - component: {fileID: 3718451035093923374}
  - component: {fileID: 5250422442442827258}
  - component: {fileID: 5647655674616601286}
  m_Layer: 0
  m_Name: Grip Projection (L)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &271303543
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 271303542}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.8, y: 0.8, z: 0.8}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1990414918}
  m_Father: {fileID: 6162809413267167199}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &271303546
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 271303542}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a19d3478546e174ba4693f363a23073, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 1262313374629172509}
  handProjection: {fileID: 271303544}
  handProjectionVisuals:
  - {fileID: 1990414918}
  speed: 15
  hideHand: 1
  handVisuals:
  - {fileID: 1238642881241681853}
  - {fileID: 500649281596835877}
  useGrabTransition: 1
  grabTransitionOffset: 0
  grabDistanceMultiplyer: 2
  grabTransitionMultiplyer: 4
  grabPercent: 1
  OnStartProjection:
    m_PersistentCalls:
      m_Calls: []
  OnEndProjection:
    m_PersistentCalls:
      m_Calls: []
--- !u!54 &271303545
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 271303542}
  serializedVersion: 4
  m_Mass: 10
  m_Drag: 10
  m_AngularDrag: 30
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 2
--- !u!114 &271303544
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 271303542}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9413d460e98076241a1f46c91201217d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreMe: 0
  fingers:
  - {fileID: 2057608974}
  - {fileID: 1183926536}
  - {fileID: 1854517319}
  - {fileID: 1952727347}
  - {fileID: 1192699945}
  palmTransform: {fileID: 1926108554}
  pinchPointTransform: {fileID: 0}
  left: 1
  reachDistance: 0.2
  enableMovement: 0
  follow: {fileID: 0}
  throwPower: 2
  gentleGrabSpeed: 1
  advancedFollowSettings: 1
  enableIK: 0
  swayStrength: 0
  gripOffset: 0.14
  usingPoseAreas: 1
  queryTriggerInteraction: 1
  usingHighlight: 0
  highlightLayers:
    serializedVersion: 2
    m_Bits: 0
  defaultHighlight: {fileID: 0}
  showAdvanced: 1
  noHandFriction: 1
  ignoreGrabCheckLayers:
    serializedVersion: 2
    m_Bits: 0
  grabType: 0
  grabCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 2
      outSlope: 2
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  minGrabTime: 0.1
  maxGrabTime: 0.2
  velocityGrabHandAmplifier: 120
  velocityGrabObjectAmplifier: 10
  grabOpenHandPoint: 0.5
  poseIndex: 0
  ignoreMe1: 0
  copyFromHand: {fileID: 0}
--- !u!114 &3718451035093923374
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 271303542}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f45b4d990ab65e478cc05444d8e79a5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  maxMoveToDistance: 0.1
  maxMoveToAngle: 45
  maxFollowDistance: 0.5
  maxVelocity: 12
  followPositionStrength: 60
  startDrag: 20
  dragDamper: 3
  dragDamperDistance: 0.025
  minVelocityChange: 1
  minVelocityDistanceMulti: 5
  followRotationStrength: 100
  startAngularDrag: 20
  angleDragDamper: 3
  angleDragDamperDistance: 4
  minMass: 0.25
  maxMass: 10
  heldMassDivider: 2
  distanceMassDifference: 10
  distanceMassMaxDistance: 0.5
  angleMassDifference: 10
  angleMassMaxAngle: 45
  maxDistanceNoParentReleaseFrames: 1
  maxDistanceParentReleaseFrames: 5
--- !u!114 &5250422442442827258
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 271303542}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 098d5f45d7e666742b7295b5ab142cee, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  defaultPoseTransitionTime: 0.3
  defaultPoseTransitionCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 1
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &5647655674616601286
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 271303542}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 62282a7c920d8134092c8af4e7d85f92, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  palmForwardRightDirection: 0.65
  highlightQuery: 2
  highlightCollidersNonAlloc:
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  highlightColliderNonAllocCount: 0
  foundHighlightGrabbables: []
--- !u!1 &284372186
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 284372187}
  - component: {fileID: 284372190}
  - component: {fileID: 284372189}
  - component: {fileID: 284372188}
  m_Layer: 0
  m_Name: Index2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &284372187
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 284372186}
  serializedVersion: 2
  m_LocalRotation: {x: -0.002070638, y: -0.09939676, z: 0.0025416242, w: 0.9950425}
  m_LocalPosition: {x: -0.029940333, y: 0, z: 0.000000023841856}
  m_LocalScale: {x: 0.99999976, y: 1.0000054, z: 1.0000014}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 609451842}
  m_Father: {fileID: 256103719}
  m_LocalEulerAnglesHint: {x: -0.27, y: 0.98200005, z: 0.26200002}
--- !u!136 &284372190
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 284372186}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.007
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.009, y: 0, z: 0.0025}
--- !u!65 &284372189
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 284372186}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!65 &284372188
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 284372186}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &303320316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 303320317}
  - component: {fileID: 303320319}
  - component: {fileID: 303320318}
  m_Layer: 0
  m_Name: Thumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &303320317
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 303320316}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00029713922, y: -0.120069474, z: -0.0059749987, w: 0.9927475}
  m_LocalPosition: {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
  m_LocalScale: {x: 0.9999988, y: 1.0000035, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1282546509}
  m_Father: {fileID: 1342902481}
  m_LocalEulerAnglesHint: {x: -0.57500005, y: -66.714005, z: -0.374}
--- !u!136 &303320319
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 303320316}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.004, y: 0.0025, z: 0}
--- !u!65 &303320318
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 303320316}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.03, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0}
--- !u!1 &331204737
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 331204738}
  - component: {fileID: 331204740}
  - component: {fileID: 331204739}
  m_Layer: 0
  m_Name: Thumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &331204738
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 331204737}
  serializedVersion: 2
  m_LocalRotation: {x: 0.012234439, y: -0.11092072, z: -0.035879955, w: 0.99310607}
  m_LocalPosition: {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  m_LocalScale: {x: 1.0000014, y: 1.000003, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2046532826}
  m_Father: {fileID: 1185224720}
  m_LocalEulerAnglesHint: {x: -2.8560002, y: -66.236, z: -3.2760003}
--- !u!136 &331204740
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 331204737}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.012, y: 0, z: 0}
--- !u!65 &331204739
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 331204737}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0}
--- !u!1 &348929177
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 348929178}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &348929178
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 348929177}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000034403058, y: -0.000003005378, z: -0.000007307157, w: 1}
  m_LocalPosition: {x: -0.0207, y: -0.0021, z: 0.0019}
  m_LocalScale: {x: 1, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 221408174}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &350036081
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 350036082}
  m_Layer: 0
  m_Name: Palm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &350036082
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 350036081}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6416945, y: -0.7662578, z: -0.031648926, w: -0.008686245}
  m_LocalPosition: {x: -0.0602, y: -0.0065, z: -0.0255}
  m_LocalScale: {x: 0.9600002, y: 0.9600002, z: 0.96000046}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 773422090}
  m_LocalEulerAnglesHint: {x: 0.83, y: 188.079, z: -80.365}
--- !u!1 &358625791
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 358625792}
  - component: {fileID: 358625794}
  - component: {fileID: 358625793}
  m_Layer: 0
  m_Name: Ring2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &358625792
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 358625791}
  serializedVersion: 2
  m_LocalRotation: {x: 0.035337657, y: -0.02529748, z: 0.0024564678, w: 0.9990522}
  m_LocalPosition: {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
  m_LocalScale: {x: 1.0000026, y: 1.0000048, z: 0.9999995}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 921870397}
  m_Father: {fileID: 399543312}
  m_LocalEulerAnglesHint: {x: 3.969, y: 6.7230005, z: 0.85400003}
--- !u!136 &358625794
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 358625791}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.008
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.006, y: -0.001, z: 0.002}
--- !u!65 &358625793
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 358625791}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &364032247
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 364032248}
  - component: {fileID: 364032251}
  - component: {fileID: 364032250}
  - component: {fileID: 364032249}
  - component: {fileID: 3865355630686702196}
  - component: {fileID: 7059855265731173443}
  - component: {fileID: 4099250156127071829}
  m_Layer: 0
  m_Name: Grip Projection (R)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &364032248
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 364032247}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: -0.8, y: 0.8, z: 0.8}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1348132936}
  m_Father: {fileID: 6162809413267167199}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &364032251
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 364032247}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a19d3478546e174ba4693f363a23073, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 1262313376109179214}
  handProjection: {fileID: 364032249}
  handProjectionVisuals:
  - {fileID: 1348132936}
  speed: 15
  hideHand: 1
  handVisuals:
  - {fileID: 1238642880238791662}
  - {fileID: 500649280100068470}
  useGrabTransition: 1
  grabTransitionOffset: 0
  grabDistanceMultiplyer: 2
  grabTransitionMultiplyer: 4
  grabPercent: 1
  OnStartProjection:
    m_PersistentCalls:
      m_Calls: []
  OnEndProjection:
    m_PersistentCalls:
      m_Calls: []
--- !u!54 &364032250
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 364032247}
  serializedVersion: 4
  m_Mass: 10
  m_Drag: 10
  m_AngularDrag: 30
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 2
--- !u!114 &364032249
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 364032247}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9413d460e98076241a1f46c91201217d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreMe: 0
  fingers:
  - {fileID: 575532524}
  - {fileID: 1225283455}
  - {fileID: 2061034820}
  - {fileID: 1894696659}
  - {fileID: 1670991138}
  palmTransform: {fileID: 1524913898}
  pinchPointTransform: {fileID: 0}
  left: 0
  reachDistance: 0.2
  enableMovement: 0
  follow: {fileID: 0}
  throwPower: 2
  gentleGrabSpeed: 1
  advancedFollowSettings: 1
  enableIK: 0
  swayStrength: 0
  gripOffset: 0.14
  usingPoseAreas: 1
  queryTriggerInteraction: 1
  usingHighlight: 0
  highlightLayers:
    serializedVersion: 2
    m_Bits: 0
  defaultHighlight: {fileID: 0}
  showAdvanced: 1
  noHandFriction: 1
  ignoreGrabCheckLayers:
    serializedVersion: 2
    m_Bits: 0
  grabType: 0
  grabCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 2
      outSlope: 2
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  minGrabTime: 0.1
  maxGrabTime: 0.2
  velocityGrabHandAmplifier: 120
  velocityGrabObjectAmplifier: 10
  grabOpenHandPoint: 0.5
  poseIndex: 0
  ignoreMe1: 0
  copyFromHand: {fileID: 0}
--- !u!114 &3865355630686702196
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 364032247}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f45b4d990ab65e478cc05444d8e79a5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  maxMoveToDistance: 0.1
  maxMoveToAngle: 45
  maxFollowDistance: 0.5
  maxVelocity: 12
  followPositionStrength: 60
  startDrag: 20
  dragDamper: 3
  dragDamperDistance: 0.025
  minVelocityChange: 1
  minVelocityDistanceMulti: 5
  followRotationStrength: 100
  startAngularDrag: 20
  angleDragDamper: 3
  angleDragDamperDistance: 4
  minMass: 0.25
  maxMass: 10
  heldMassDivider: 2
  distanceMassDifference: 10
  distanceMassMaxDistance: 0.5
  angleMassDifference: 10
  angleMassMaxAngle: 45
  maxDistanceNoParentReleaseFrames: 1
  maxDistanceParentReleaseFrames: 5
--- !u!114 &7059855265731173443
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 364032247}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 098d5f45d7e666742b7295b5ab142cee, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  defaultPoseTransitionTime: 0.3
  defaultPoseTransitionCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 1
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &4099250156127071829
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 364032247}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 62282a7c920d8134092c8af4e7d85f92, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  palmForwardRightDirection: 0.65
  highlightQuery: 2
  highlightCollidersNonAlloc:
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  highlightColliderNonAllocCount: 0
  foundHighlightGrabbables: []
--- !u!1 &399543311
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 399543312}
  - component: {fileID: 399543314}
  - component: {fileID: 399543313}
  m_Layer: 0
  m_Name: Ring1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &399543312
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 399543311}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0037359423, y: -0.04221879, z: 0.037158713, w: 0.99841017}
  m_LocalPosition: {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  m_LocalScale: {x: 1.0000008, y: 1.0000033, z: 1.0000023}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 358625792}
  m_Father: {fileID: 1665432980}
  m_LocalEulerAnglesHint: {x: -0.323, y: 7.6440005, z: 4.268}
--- !u!136 &399543314
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 399543311}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.01
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.005, y: 0, z: 0.002}
--- !u!65 &399543313
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 399543311}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.03, y: 0.02, z: 0.018}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &459248286
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 459248287}
  - component: {fileID: 459248289}
  - component: {fileID: 459248288}
  m_Layer: 0
  m_Name: Pinky2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &459248287
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 459248286}
  serializedVersion: 2
  m_LocalRotation: {x: 0.037299104, y: -0.028552314, z: 0.013196694, w: 0.998809}
  m_LocalPosition: {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
  m_LocalScale: {x: 1.0000057, y: 1.0000064, z: 1.0000006}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 30320667}
  m_Father: {fileID: 212105230}
  m_LocalEulerAnglesHint: {x: 4.138, y: 3.0690002, z: 1.8570001}
--- !u!136 &459248289
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 459248286}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.006
  m_Height: 0.025
  m_Direction: 0
  m_Center: {x: -0.004, y: -0.001, z: 0.0005}
--- !u!65 &459248288
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 459248286}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.015, z: 0.015}
  m_Center: {x: -0.005, y: 0, z: 0.002}
--- !u!1 &492039276
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 492039277}
  - component: {fileID: 492039279}
  - component: {fileID: 492039278}
  m_Layer: 0
  m_Name: Index1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &492039277
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 492039276}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0048813173, y: -0.03711859, z: -0.005189765, w: 0.99928546}
  m_LocalPosition: {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  m_LocalScale: {x: 0.9999992, y: 1.0000033, z: 1.0000032}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 221408174}
  m_Father: {fileID: 1183926533}
  m_LocalEulerAnglesHint: {x: 0.63500005, y: 5.677, z: -0.513}
--- !u!136 &492039279
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 492039276}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.01, y: 0, z: 0}
--- !u!65 &492039278
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 492039276}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.035, y: 0.02, z: 0.018}
  m_Center: {x: -0.015, y: 0, z: 0.002}
--- !u!1 &566922976
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 566922977}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &566922977
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 566922976}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000072037797, y: -0.0000022402965, z: -0.0000032116654, w: 1}
  m_LocalPosition: {x: -0.0186, y: -0.0016, z: 0.0016}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1340084910}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &575532521
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 575532522}
  - component: {fileID: 575532524}
  - component: {fileID: 575532523}
  m_Layer: 0
  m_Name: Thumb
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &575532522
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 575532521}
  serializedVersion: 2
  m_LocalRotation: {x: 0.35080808, y: 0.13090943, z: 0.40735877, w: 0.8329798}
  m_LocalPosition: {x: -0.024448793, y: -0.03049879, z: -0.015017874}
  m_LocalScale: {x: 1.0000002, y: 1.000002, z: 1.0000012}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1612374346}
  m_Father: {fileID: 1348132936}
  m_LocalEulerAnglesHint: {x: 81.382, y: -115.203, z: -53.660004}
--- !u!114 &575532524
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 575532521}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1972922847}
  tipRadius: 0.012
  bendOffset: 0.03
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 575532522}
  - {fileID: 1612374346}
  - {fileID: 213269458}
  poseData: []
  minGripRotPose:
  - {x: 0.2964466, y: 0.21444798, z: 0.407665, w: 0.8366246}
  - {x: 0.01483373, y: -0.037764624, z: -0.034885716, w: 0.9985674}
  - {x: 0.0007297829, y: -0.047739774, z: -0.005937712, w: 0.9988419}
  minGripPosPose:
  - {x: -0.024700923, y: -0.030173011, z: -0.016420783}
  - {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  - {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
  maxGripRotPose:
  - {x: 0.60068303, y: -0.41362432, z: 0.307867, w: 0.6109933}
  - {x: -0.0052479487, y: -0.5465699, z: -0.037543483, w: 0.8365551}
  - {x: -0.002396468, y: -0.549858, z: -0.005481367, w: 0.8352368}
  maxGripPosPose:
  - {x: -0.0229, y: -0.0325, z: -0.0064}
  - {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  - {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
--- !u!136 &575532523
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 575532521}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.012
  m_Height: 0.045
  m_Direction: 0
  m_Center: {x: -0.02, y: 0, z: 0}
--- !u!1 &578994283
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 578994284}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &578994284
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 578994283}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000008029863, y: 0.0000010747461, z: -0.0000007860363, w: 1}
  m_LocalPosition: {x: -0.01333, y: -0.00131, z: 0.00011}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1099739308}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &609451841
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 609451842}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &609451842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 609451841}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000034403058, y: -0.000003005378, z: -0.000007307157, w: 1}
  m_LocalPosition: {x: -0.0207, y: -0.0021, z: 0.0019}
  m_LocalScale: {x: 1, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 284372187}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &653956370
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 653956371}
  - component: {fileID: 653956373}
  - component: {fileID: 653956372}
  m_Layer: 0
  m_Name: Middle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &653956371
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 653956370}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0020609081, y: -0.06488923, z: 0.0122108795, w: 0.99781567}
  m_LocalPosition: {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  m_LocalScale: {x: 1.0000002, y: 1.000003, z: 1.0000015}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1355618820}
  m_Father: {fileID: 1854517316}
  m_LocalEulerAnglesHint: {x: 0.035, y: 4.433, z: 1.419}
--- !u!136 &653956373
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 653956370}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.01
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!65 &653956372
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 653956370}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.035, y: 0.02, z: 0.018}
  m_Center: {x: -0.015, y: 0, z: 0.002}
--- !u!1 &710639189
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 710639190}
  - component: {fileID: 710639193}
  - component: {fileID: 710639192}
  - component: {fileID: 710639191}
  m_Layer: 0
  m_Name: Index
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &710639190
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 710639189}
  serializedVersion: 2
  m_LocalRotation: {x: 0.08284666, y: -0.01826318, z: 0.07108723, w: 0.9938559}
  m_LocalPosition: {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  m_LocalScale: {x: 0.9999983, y: 1.0000019, z: 1.0000032}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 256103719}
  m_Father: {fileID: 773422090}
  m_LocalEulerAnglesHint: {x: 8.345, y: 11.473001, z: 9.206}
--- !u!114 &710639193
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 710639189}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 609451842}
  tipRadius: 0.0084
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 710639190}
  - {fileID: 256103719}
  - {fileID: 284372187}
  poseData: []
  minGripRotPose:
  - {x: 0.08016173, y: 0.093553856, z: 0.072388016, w: 0.9897383}
  - {x: 0.005312354, y: 0.04954708, z: -0.0047476185, w: 0.9987464}
  - {x: -0.0023328613, y: 0.008577391, z: 0.0023032972, w: 0.99995786}
  minGripPosPose:
  - {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  - {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  - {x: -0.029940333, y: 0, z: 0.000000023841856}
  maxGripRotPose:
  - {x: 0.073544726, y: -0.6994529, z: 0.04096306, w: 0.70970345}
  - {x: 0.0013731268, y: -0.5629508, z: -0.0069910423, w: 0.82645977}
  - {x: 0.00013496191, y: -0.73411435, z: 0.003275542, w: 0.679018}
  maxGripPosPose:
  - {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  - {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  - {x: -0.029940333, y: 0, z: 0.000000023841856}
--- !u!136 &710639192
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 710639189}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.043
  m_Direction: 0
  m_Center: {x: -0.015, y: 0, z: 0}
--- !u!65 &710639191
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 710639189}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.02, y: 0, z: 0.0015}
--- !u!1 &773422089
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 773422090}
  m_Layer: 0
  m_Name: Pivot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &773422090
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 773422089}
  serializedVersion: 2
  m_LocalRotation: {x: 0.67921716, y: 0.004741866, z: -0.73388237, w: 0.0076374956}
  m_LocalPosition: {x: -0.0035, y: -0.0021, z: -0.1035}
  m_LocalScale: {x: 1.25, y: 1.25, z: 1.25}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1185224720}
  - {fileID: 710639190}
  - {fileID: 1510978140}
  - {fileID: 100774092}
  - {fileID: 1642937139}
  - {fileID: 1519989452}
  - {fileID: 1066402419}
  - {fileID: 350036082}
  - {fileID: 871328114}
  m_Father: {fileID: 1577838260}
  m_LocalEulerAnglesHint: {x: 180, y: 100.685, z: -0.000015258789}
--- !u!1 &844405220
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 844405221}
  - component: {fileID: 844405223}
  - component: {fileID: 844405222}
  m_Layer: 0
  m_Name: Middle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &844405221
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 844405220}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0020609081, y: -0.06488923, z: 0.0122108795, w: 0.99781567}
  m_LocalPosition: {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  m_LocalScale: {x: 1.0000002, y: 1.000003, z: 1.0000015}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1340084910}
  m_Father: {fileID: 1510978140}
  m_LocalEulerAnglesHint: {x: 0.035, y: 4.433, z: 1.419}
--- !u!136 &844405223
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 844405220}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.01
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!65 &844405222
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 844405220}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.035, y: 0.02, z: 0.018}
  m_Center: {x: -0.015, y: 0, z: 0.002}
--- !u!1 &871328113
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 871328114}
  - component: {fileID: 871328115}
  m_Layer: 0
  m_Name: Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &871328114
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871328113}
  serializedVersion: 2
  m_LocalRotation: {x: -0.006846468, y: -0.030227715, z: 0.09913848, w: -0.9945909}
  m_LocalPosition: {x: -0.0479, y: -0.0076, z: -0.0049}
  m_LocalScale: {x: 0.09, y: 0.09, z: 0.044347502}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 773422090}
  m_LocalEulerAnglesHint: {x: 1.124, y: 3.37, z: -11.352}
--- !u!64 &871328115
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871328113}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &875416240
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 875416241}
  - component: {fileID: 875416242}
  m_Layer: 0
  m_Name: Middle1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &875416241
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 875416240}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0020609081, y: -0.06488923, z: 0.0122108795, w: 0.99781567}
  m_LocalPosition: {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  m_LocalScale: {x: 1.0000002, y: 1.000003, z: 1.0000015}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1551377984}
  m_Father: {fileID: 2061034818}
  m_LocalEulerAnglesHint: {x: 0.035, y: 4.433, z: 1.419}
--- !u!136 &875416242
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 875416240}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.01
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &921870396
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 921870397}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &921870397
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 921870396}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000075772396, y: 0.0000004423782, z: -0.000001301989, w: 1}
  m_LocalPosition: {x: -0.0166, y: -0.0016, z: 0.0007}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 358625792}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &927954379
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 927954380}
  - component: {fileID: 927954381}
  m_Layer: 0
  m_Name: OuterMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &927954380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 927954379}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7414494, y: 0.016384002, z: -0.006615691, w: 0.6707761}
  m_LocalPosition: {x: 0.06556724, y: -0.0066842884, z: -0.14039922}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1780456924}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &927954381
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 927954379}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a7e7da7a43feb7e45bc3448f5679a9e4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -6962415767791754656, guid: 17a48d918cf75e74aa527c89697f3d15, type: 3}
  m_Bones:
  - {fileID: 1780456924}
  - {fileID: 1749001116}
  - {fileID: 1342902481}
  - {fileID: 303320317}
  - {fileID: 1282546509}
  - {fileID: 1628009438}
  - {fileID: 1600512752}
  - {fileID: 984978722}
  - {fileID: 25635430}
  - {fileID: 1137609619}
  - {fileID: 140272390}
  - {fileID: 1168953082}
  - {fileID: 1677025920}
  - {fileID: 1665432980}
  - {fileID: 399543312}
  - {fileID: 358625792}
  - {fileID: 921870397}
  - {fileID: 1305778227}
  - {fileID: 212105230}
  - {fileID: 459248287}
  - {fileID: 30320667}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1780456924}
  m_AABB:
    m_Center: {x: -0.08041836, y: -0.0032454282, z: -0.020966716}
    m_Extent: {x: 0.11415337, y: 0.07609155, z: 0.06161832}
  m_DirtyAABB: 0
--- !u!1 &965286013
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 965286014}
  - component: {fileID: 965286016}
  - component: {fileID: 965286015}
  m_Layer: 0
  m_Name: Pinky1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &965286014
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 965286013}
  serializedVersion: 2
  m_LocalRotation: {x: 0.03042995, y: -0.090418056, z: 0.043205522, w: 0.9945009}
  m_LocalPosition: {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  m_LocalScale: {x: 1.000002, y: 1.000004, z: 1.0000021}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 978501049}
  m_Father: {fileID: 1192699942}
  m_LocalEulerAnglesHint: {x: 2.7570002, y: 2.983, z: 5.3970003}
--- !u!136 &965286016
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 965286013}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.03
  m_Direction: 0
  m_Center: {x: -0.004, y: 0, z: 0}
--- !u!65 &965286015
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 965286013}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.02, z: 0.018}
  m_Center: {x: -0.005, y: 0, z: 0.002}
--- !u!1 &967168264
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 967168265}
  - component: {fileID: 967168267}
  - component: {fileID: 967168266}
  m_Layer: 0
  m_Name: Pinky1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &967168265
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 967168264}
  serializedVersion: 2
  m_LocalRotation: {x: 0.03042995, y: -0.090418056, z: 0.043205522, w: 0.9945009}
  m_LocalPosition: {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  m_LocalScale: {x: 1.000002, y: 1.000004, z: 1.0000021}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1600000621}
  m_Father: {fileID: 1642937139}
  m_LocalEulerAnglesHint: {x: 2.7570002, y: 2.983, z: 5.3970003}
--- !u!136 &967168267
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 967168264}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.03
  m_Direction: 0
  m_Center: {x: -0.004, y: 0, z: 0}
--- !u!65 &967168266
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 967168264}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.02, z: 0.018}
  m_Center: {x: -0.005, y: 0, z: 0.002}
--- !u!1 &978501048
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 978501049}
  - component: {fileID: 978501051}
  - component: {fileID: 978501050}
  m_Layer: 0
  m_Name: Pinky2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &978501049
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 978501048}
  serializedVersion: 2
  m_LocalRotation: {x: 0.037299104, y: -0.028552314, z: 0.013196694, w: 0.998809}
  m_LocalPosition: {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
  m_LocalScale: {x: 1.0000057, y: 1.0000064, z: 1.0000006}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1149393779}
  m_Father: {fileID: 965286014}
  m_LocalEulerAnglesHint: {x: 4.138, y: 3.0690002, z: 1.8570001}
--- !u!136 &978501051
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 978501048}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.006
  m_Height: 0.025
  m_Direction: 0
  m_Center: {x: -0.004, y: -0.001, z: 0.0005}
--- !u!65 &978501050
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 978501048}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.015, z: 0.015}
  m_Center: {x: -0.005, y: 0, z: 0.002}
--- !u!1 &984978721
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 984978722}
  - component: {fileID: 984978725}
  - component: {fileID: 984978724}
  - component: {fileID: 984978723}
  m_Layer: 0
  m_Name: Index2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &984978722
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 984978721}
  serializedVersion: 2
  m_LocalRotation: {x: -0.002070638, y: -0.09939676, z: 0.0025416242, w: 0.9950425}
  m_LocalPosition: {x: -0.029940333, y: 0, z: 0.000000023841856}
  m_LocalScale: {x: 0.99999976, y: 1.0000054, z: 1.0000014}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 25635430}
  m_Father: {fileID: 1600512752}
  m_LocalEulerAnglesHint: {x: -0.27, y: 0.98200005, z: 0.26200002}
--- !u!136 &984978725
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 984978721}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.007
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.009, y: 0, z: 0.0025}
--- !u!65 &984978724
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 984978721}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!65 &984978723
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 984978721}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &998136198
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 998136199}
  m_Layer: 0
  m_Name: Palm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &998136199
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 998136198}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6416945, y: -0.7662578, z: -0.031648926, w: -0.008686245}
  m_LocalPosition: {x: -0.0602, y: -0.0065, z: -0.0255}
  m_LocalScale: {x: 0.9600002, y: 0.9600002, z: 0.96000046}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1780456924}
  m_LocalEulerAnglesHint: {x: 0.83, y: 188.079, z: -80.365}
--- !u!1 &1029820512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1029820513}
  - component: {fileID: 1029820514}
  m_Layer: 0
  m_Name: InnerMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1029820513
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1029820512}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7414494, y: 0.016383987, z: -0.0066156765, w: 0.6707761}
  m_LocalPosition: {x: 0.06556724, y: -0.0066842884, z: -0.14039922}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1780456924}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1029820514
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1029820512}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a7e7da7a43feb7e45bc3448f5679a9e4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 8936583283049568085, guid: 17a48d918cf75e74aa527c89697f3d15, type: 3}
  m_Bones:
  - {fileID: 1780456924}
  - {fileID: 1749001116}
  - {fileID: 1342902481}
  - {fileID: 303320317}
  - {fileID: 1282546509}
  - {fileID: 1628009438}
  - {fileID: 1600512752}
  - {fileID: 984978722}
  - {fileID: 25635430}
  - {fileID: 1137609619}
  - {fileID: 140272390}
  - {fileID: 1168953082}
  - {fileID: 1677025920}
  - {fileID: 1665432980}
  - {fileID: 399543312}
  - {fileID: 358625792}
  - {fileID: 921870397}
  - {fileID: 1305778227}
  - {fileID: 212105230}
  - {fileID: 459248287}
  - {fileID: 30320667}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1780456924}
  m_AABB:
    m_Center: {x: -0.09229668, y: -0.015225619, z: -0.018114224}
    m_Extent: {x: 0.080366306, y: 0.05974908, z: 0.04299726}
  m_DirtyAABB: 0
--- !u!1 &1044079232
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1044079233}
  - component: {fileID: 1044079234}
  m_Layer: 0
  m_Name: Index1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1044079233
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044079232}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0048813173, y: -0.03711859, z: -0.005189765, w: 0.99928546}
  m_LocalPosition: {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  m_LocalScale: {x: 0.9999992, y: 1.0000033, z: 1.0000032}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 119766581}
  m_Father: {fileID: 1225283453}
  m_LocalEulerAnglesHint: {x: 0.63500005, y: 5.677, z: -0.513}
--- !u!136 &1044079234
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1044079232}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.01, y: 0, z: 0}
--- !u!1 &1066402418
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1066402419}
  - component: {fileID: 1066402420}
  m_Layer: 0
  m_Name: InnerMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1066402419
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1066402418}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7414494, y: 0.016383987, z: -0.0066156765, w: 0.6707761}
  m_LocalPosition: {x: 0.06556724, y: -0.0066842884, z: -0.14039922}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 773422090}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1066402420
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1066402418}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a7e7da7a43feb7e45bc3448f5679a9e4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 8936583283049568085, guid: 17a48d918cf75e74aa527c89697f3d15, type: 3}
  m_Bones:
  - {fileID: 773422090}
  - {fileID: 1185224720}
  - {fileID: 331204738}
  - {fileID: 2046532826}
  - {fileID: 1881497674}
  - {fileID: 710639190}
  - {fileID: 256103719}
  - {fileID: 284372187}
  - {fileID: 609451842}
  - {fileID: 1510978140}
  - {fileID: 844405221}
  - {fileID: 1340084910}
  - {fileID: 566922977}
  - {fileID: 100774092}
  - {fileID: 1670949134}
  - {fileID: 1509029465}
  - {fileID: 1722413317}
  - {fileID: 1642937139}
  - {fileID: 967168265}
  - {fileID: 1600000621}
  - {fileID: 1967358981}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 773422090}
  m_AABB:
    m_Center: {x: -0.09229668, y: -0.015225619, z: -0.018114224}
    m_Extent: {x: 0.080366306, y: 0.05974908, z: 0.04299726}
  m_DirtyAABB: 0
--- !u!1 &1080109414
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1080109415}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1080109415
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1080109414}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000072037797, y: -0.0000022402965, z: -0.0000032116654, w: 1}
  m_LocalPosition: {x: -0.0186, y: -0.0016, z: 0.0016}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1551377984}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1099739307
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1099739308}
  - component: {fileID: 1099739309}
  m_Layer: 0
  m_Name: Pinky2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1099739308
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1099739307}
  serializedVersion: 2
  m_LocalRotation: {x: 0.037299104, y: -0.028552314, z: 0.013196694, w: 0.998809}
  m_LocalPosition: {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
  m_LocalScale: {x: 1.0000057, y: 1.0000064, z: 1.0000006}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 578994284}
  m_Father: {fileID: 1672542735}
  m_LocalEulerAnglesHint: {x: 4.138, y: 3.0690002, z: 1.8570001}
--- !u!136 &1099739309
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1099739307}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.006
  m_Height: 0.025
  m_Direction: 0
  m_Center: {x: -0.004, y: -0.001, z: 0.0005}
--- !u!1 &1137609618
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1137609619}
  - component: {fileID: 1137609622}
  - component: {fileID: 1137609621}
  - component: {fileID: 1137609620}
  m_Layer: 0
  m_Name: Middle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1137609619
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1137609618}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00037423725, y: -0.024309829, z: -0.029526183, w: 0.9992683}
  m_LocalPosition: {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  m_LocalScale: {x: 0.99999905, y: 1.000002, z: 1.0000026}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 140272390}
  m_Father: {fileID: 1780456924}
  m_LocalEulerAnglesHint: {x: 0.20400001, y: 10.124001, z: -3.8260002}
--- !u!114 &1137609622
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1137609618}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1677025920}
  tipRadius: 0.00936
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1137609619}
  - {fileID: 140272390}
  - {fileID: 1168953082}
  poseData: []
  minGripRotPose:
  - {x: -0.001174541, y: 0.08824137, z: -0.033409286, w: 0.995538}
  - {x: 0.0007867855, y: 0.03867055, z: 0.012358556, w: 0.9991753}
  - {x: 0.0018642171, y: 0.02197182, z: 0.0069710515, w: 0.9997326}
  minGripPosPose:
  - {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  - {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  - {x: -0.031118786, y: 0, z: 0.00000006854534}
  maxGripRotPose:
  - {x: 0.0046602613, y: -0.7080081, z: 0.0036643716, w: 0.7061795}
  - {x: 0.00935024, y: -0.684121, z: 0.0081195, w: 0.7292634}
  - {x: 0.005361201, y: -0.52628064, z: 0.004829721, w: 0.85028034}
  maxGripPosPose:
  - {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  - {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  - {x: -0.031118786, y: 0, z: 0.00000006854534}
--- !u!136 &1137609621
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1137609618}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.042
  m_Direction: 0
  m_Center: {x: -0.015, y: 0, z: 0}
--- !u!65 &1137609620
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1137609618}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.02, y: 0, z: 0.0015}
--- !u!1 &1149393778
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1149393779}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1149393779
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1149393778}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000008029863, y: 0.0000010747461, z: -0.0000007860363, w: 1}
  m_LocalPosition: {x: -0.01333, y: -0.00131, z: 0.00011}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 978501049}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1150899742
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1150899743}
  - component: {fileID: 1150899745}
  - component: {fileID: 1150899744}
  m_Layer: 0
  m_Name: Ring1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1150899743
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1150899742}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0037359423, y: -0.04221879, z: 0.037158713, w: 0.99841017}
  m_LocalPosition: {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  m_LocalScale: {x: 1.0000008, y: 1.0000033, z: 1.0000023}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1843319558}
  m_Father: {fileID: 1952727344}
  m_LocalEulerAnglesHint: {x: -0.323, y: 7.6440005, z: 4.268}
--- !u!136 &1150899745
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1150899742}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.01
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.005, y: 0, z: 0.002}
--- !u!65 &1150899744
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1150899742}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.03, y: 0.02, z: 0.018}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &1168953081
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1168953082}
  - component: {fileID: 1168953084}
  - component: {fileID: 1168953083}
  m_Layer: 0
  m_Name: Middle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1168953082
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1168953081}
  serializedVersion: 2
  m_LocalRotation: {x: 0.002400933, y: -0.055880643, z: 0.006804867, w: 0.9984114}
  m_LocalPosition: {x: -0.031118786, y: 0, z: 0.00000006854534}
  m_LocalScale: {x: 1.000003, y: 1.0000048, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1677025920}
  m_Father: {fileID: 140272390}
  m_LocalEulerAnglesHint: {x: 0.19600001, y: 2.519, z: 0.80300003}
--- !u!136 &1168953084
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1168953081}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.0075
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.006, y: 0, z: 0.002}
--- !u!65 &1168953083
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1168953081}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &1183926532
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1183926533}
  - component: {fileID: 1183926536}
  - component: {fileID: 1183926535}
  - component: {fileID: 1183926534}
  m_Layer: 0
  m_Name: Index
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1183926533
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1183926532}
  serializedVersion: 2
  m_LocalRotation: {x: 0.08284666, y: -0.01826318, z: 0.07108723, w: 0.9938559}
  m_LocalPosition: {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  m_LocalScale: {x: 0.9999983, y: 1.0000019, z: 1.0000032}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 492039277}
  m_Father: {fileID: 1990414918}
  m_LocalEulerAnglesHint: {x: 8.345, y: 11.473001, z: 9.206}
--- !u!114 &1183926536
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1183926532}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 348929178}
  tipRadius: 0.0084
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1183926533}
  - {fileID: 492039277}
  - {fileID: 221408174}
  poseData: []
  minGripRotPose:
  - {x: 0.08016173, y: 0.093553856, z: 0.072388016, w: 0.9897383}
  - {x: 0.005312354, y: 0.04954708, z: -0.0047476185, w: 0.9987464}
  - {x: -0.0023328613, y: 0.008577391, z: 0.0023032972, w: 0.99995786}
  minGripPosPose:
  - {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  - {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  - {x: -0.029940333, y: 0, z: 0.000000023841856}
  maxGripRotPose:
  - {x: 0.073544726, y: -0.6994529, z: 0.04096306, w: 0.70970345}
  - {x: 0.0013731268, y: -0.5629508, z: -0.0069910423, w: 0.82645977}
  - {x: 0.00013496191, y: -0.73411435, z: 0.003275542, w: 0.679018}
  maxGripPosPose:
  - {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  - {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  - {x: -0.029940333, y: 0, z: 0.000000023841856}
--- !u!136 &1183926535
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1183926532}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.043
  m_Direction: 0
  m_Center: {x: -0.015, y: 0, z: 0}
--- !u!65 &1183926534
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1183926532}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.02, y: 0, z: 0.0015}
--- !u!1 &1185224719
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1185224720}
  - component: {fileID: 1185224723}
  - component: {fileID: 1185224722}
  - component: {fileID: 1185224721}
  m_Layer: 0
  m_Name: Thumb
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1185224720
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1185224719}
  serializedVersion: 2
  m_LocalRotation: {x: 0.35080808, y: 0.13090943, z: 0.40735877, w: 0.8329798}
  m_LocalPosition: {x: -0.024448793, y: -0.03049879, z: -0.015017874}
  m_LocalScale: {x: 1.0000002, y: 1.000002, z: 1.0000012}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 331204738}
  m_Father: {fileID: 773422090}
  m_LocalEulerAnglesHint: {x: 81.382, y: -115.203, z: -53.660004}
--- !u!114 &1185224723
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1185224719}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1881497674}
  tipRadius: 0.012
  bendOffset: 0.03
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1185224720}
  - {fileID: 331204738}
  - {fileID: 2046532826}
  poseData: []
  minGripRotPose:
  - {x: 0.2964466, y: 0.21444798, z: 0.407665, w: 0.8366246}
  - {x: 0.01483373, y: -0.037764624, z: -0.034885716, w: 0.9985674}
  - {x: 0.0007297829, y: -0.047739774, z: -0.005937712, w: 0.9988419}
  minGripPosPose:
  - {x: -0.024700923, y: -0.030173011, z: -0.016420783}
  - {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  - {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
  maxGripRotPose:
  - {x: 0.60068303, y: -0.41362432, z: 0.307867, w: 0.6109933}
  - {x: -0.0052479487, y: -0.5465699, z: -0.037543483, w: 0.8365551}
  - {x: -0.002396468, y: -0.549858, z: -0.005481367, w: 0.8352368}
  maxGripPosPose:
  - {x: -0.0229, y: -0.0325, z: -0.0064}
  - {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  - {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
--- !u!136 &1185224722
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1185224719}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.012
  m_Height: 0.045
  m_Direction: 0
  m_Center: {x: -0.02, y: 0, z: 0}
--- !u!65 &1185224721
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1185224719}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.05, y: 0.02, z: 0.02}
  m_Center: {x: -0.02, y: 0, z: 0}
--- !u!1 &1191312103
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1191312104}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1191312104
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1191312103}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000075772396, y: 0.0000004423782, z: -0.000001301989, w: 1}
  m_LocalPosition: {x: -0.0166, y: -0.0016, z: 0.0007}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1843319558}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1192699941
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1192699942}
  - component: {fileID: 1192699945}
  - component: {fileID: 1192699944}
  - component: {fileID: 1192699943}
  m_Layer: 0
  m_Name: Pinky
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1192699942
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192699941}
  serializedVersion: 2
  m_LocalRotation: {x: -0.24505861, y: -0.021443876, z: -0.20070784, w: 0.94826305}
  m_LocalPosition: {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  m_LocalScale: {x: 0.9999999, y: 1.0000018, z: 1.0000019}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 965286014}
  m_Father: {fileID: 1990414918}
  m_LocalEulerAnglesHint: {x: -25.218, y: 17.146, z: -30.575}
--- !u!114 &1192699945
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192699941}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1149393779}
  tipRadius: 0.0066
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1192699942}
  - {fileID: 965286014}
  - {fileID: 978501049}
  poseData: []
  minGripRotPose:
  - {x: -0.24657567, y: 0.08341347, z: -0.2230366, w: 0.9394133}
  - {x: 0.025245013, y: 0.02485968, z: 0.046426166, w: 0.9982932}
  - {x: 0.03652061, y: 0.026177457, z: 0.015219117, w: 0.99887407}
  minGripPosPose:
  - {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  - {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  - {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
  maxGripRotPose:
  - {x: -0.16609184, y: -0.659473, z: -0.006502538, w: 0.73312116}
  - {x: 0.052083563, y: -0.7682547, z: 0.0089439, w: 0.63795924}
  - {x: 0.039564617, y: -0.36282298, z: -0.000117167816, w: 0.93101776}
  maxGripPosPose:
  - {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  - {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  - {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
--- !u!136 &1192699944
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192699941}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.03
  m_Direction: 0
  m_Center: {x: -0.009, y: 0, z: 0}
--- !u!65 &1192699943
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1192699941}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0.0015}
--- !u!1 &1212297659
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1212297660}
  - component: {fileID: 1212297661}
  m_Layer: 0
  m_Name: Ring2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1212297660
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1212297659}
  serializedVersion: 2
  m_LocalRotation: {x: 0.035337657, y: -0.02529748, z: 0.0024564678, w: 0.9990522}
  m_LocalPosition: {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
  m_LocalScale: {x: 1.0000026, y: 1.0000048, z: 0.9999995}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1416829110}
  m_Father: {fileID: 56547633}
  m_LocalEulerAnglesHint: {x: 3.969, y: 6.7230005, z: 0.85400003}
--- !u!136 &1212297661
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1212297659}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.008
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.006, y: -0.001, z: 0.002}
--- !u!1 &1225283452
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1225283453}
  - component: {fileID: 1225283455}
  - component: {fileID: 1225283454}
  m_Layer: 0
  m_Name: Index
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1225283453
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1225283452}
  serializedVersion: 2
  m_LocalRotation: {x: 0.08284666, y: -0.01826318, z: 0.07108723, w: 0.9938559}
  m_LocalPosition: {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  m_LocalScale: {x: 0.9999983, y: 1.0000019, z: 1.0000032}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1044079233}
  m_Father: {fileID: 1348132936}
  m_LocalEulerAnglesHint: {x: 8.345, y: 11.473001, z: 9.206}
--- !u!114 &1225283455
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1225283452}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1467414177}
  tipRadius: 0.0084
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1225283453}
  - {fileID: 1044079233}
  - {fileID: 119766581}
  poseData: []
  minGripRotPose:
  - {x: 0.08016173, y: 0.093553856, z: 0.072388016, w: 0.9897383}
  - {x: 0.005312354, y: 0.04954708, z: -0.0047476185, w: 0.9987464}
  - {x: -0.0023328613, y: 0.008577391, z: 0.0023032972, w: 0.99995786}
  minGripPosPose:
  - {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  - {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  - {x: -0.029940333, y: 0, z: 0.000000023841856}
  maxGripRotPose:
  - {x: 0.073544726, y: -0.6994529, z: 0.04096306, w: 0.70970345}
  - {x: 0.0013731268, y: -0.5629508, z: -0.0069910423, w: 0.82645977}
  - {x: 0.00013496191, y: -0.73411435, z: 0.003275542, w: 0.679018}
  maxGripPosPose:
  - {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  - {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  - {x: -0.029940333, y: 0, z: 0.000000023841856}
--- !u!136 &1225283454
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1225283452}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.043
  m_Direction: 0
  m_Center: {x: -0.015, y: 0, z: 0}
--- !u!1 &1282546508
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1282546509}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1282546509
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1282546508}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000061402106, y: -0.0000049649398, z: 0.0000024675394, w: 1}
  m_LocalPosition: {x: -0.0151, y: 0.0034, z: -0.0008}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 303320317}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1305778226
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1305778227}
  - component: {fileID: 1305778230}
  - component: {fileID: 1305778229}
  - component: {fileID: 1305778228}
  m_Layer: 0
  m_Name: Pinky
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1305778227
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1305778226}
  serializedVersion: 2
  m_LocalRotation: {x: -0.24505861, y: -0.021443876, z: -0.20070784, w: 0.94826305}
  m_LocalPosition: {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  m_LocalScale: {x: 0.9999999, y: 1.0000018, z: 1.0000019}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 212105230}
  m_Father: {fileID: 1780456924}
  m_LocalEulerAnglesHint: {x: -25.218, y: 17.146, z: -30.575}
--- !u!114 &1305778230
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1305778226}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 30320667}
  tipRadius: 0.0066
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1305778227}
  - {fileID: 212105230}
  - {fileID: 459248287}
  poseData: []
  minGripRotPose:
  - {x: -0.24657567, y: 0.08341347, z: -0.2230366, w: 0.9394133}
  - {x: 0.025245013, y: 0.02485968, z: 0.046426166, w: 0.9982932}
  - {x: 0.03652061, y: 0.026177457, z: 0.015219117, w: 0.99887407}
  minGripPosPose:
  - {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  - {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  - {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
  maxGripRotPose:
  - {x: -0.16609184, y: -0.659473, z: -0.006502538, w: 0.73312116}
  - {x: 0.052083563, y: -0.7682547, z: 0.0089439, w: 0.63795924}
  - {x: 0.039564617, y: -0.36282298, z: -0.000117167816, w: 0.93101776}
  maxGripPosPose:
  - {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  - {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  - {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
--- !u!136 &1305778229
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1305778226}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.03
  m_Direction: 0
  m_Center: {x: -0.009, y: 0, z: 0}
--- !u!65 &1305778228
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1305778226}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0.0015}
--- !u!1 &1340084909
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1340084910}
  - component: {fileID: 1340084912}
  - component: {fileID: 1340084911}
  m_Layer: 0
  m_Name: Middle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1340084910
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1340084909}
  serializedVersion: 2
  m_LocalRotation: {x: 0.002400933, y: -0.055880643, z: 0.006804867, w: 0.9984114}
  m_LocalPosition: {x: -0.031118786, y: 0, z: 0.00000006854534}
  m_LocalScale: {x: 1.000003, y: 1.0000048, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 566922977}
  m_Father: {fileID: 844405221}
  m_LocalEulerAnglesHint: {x: 0.19600001, y: 2.519, z: 0.80300003}
--- !u!136 &1340084912
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1340084909}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.0075
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.006, y: 0, z: 0.002}
--- !u!65 &1340084911
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1340084909}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &1342902480
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1342902481}
  - component: {fileID: 1342902483}
  - component: {fileID: 1342902482}
  m_Layer: 0
  m_Name: Thumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1342902481
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1342902480}
  serializedVersion: 2
  m_LocalRotation: {x: 0.012234439, y: -0.11092072, z: -0.035879955, w: 0.99310607}
  m_LocalPosition: {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  m_LocalScale: {x: 1.0000014, y: 1.000003, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 303320317}
  m_Father: {fileID: 1749001116}
  m_LocalEulerAnglesHint: {x: -2.8560002, y: -66.236, z: -3.2760003}
--- !u!136 &1342902483
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1342902480}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.012, y: 0, z: 0}
--- !u!65 &1342902482
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1342902480}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0}
--- !u!1 &1348132935
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1348132936}
  m_Layer: 0
  m_Name: Pivot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1348132936
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1348132935}
  serializedVersion: 2
  m_LocalRotation: {x: 0.67921716, y: 0.004741866, z: -0.73388237, w: 0.0076374956}
  m_LocalPosition: {x: -0.0035, y: -0.0021, z: -0.1035}
  m_LocalScale: {x: 1.25, y: 1.25, z: 1.25}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 575532522}
  - {fileID: 1225283453}
  - {fileID: 2061034818}
  - {fileID: 1894696657}
  - {fileID: 1670991136}
  - {fileID: 1474401530}
  - {fileID: 1494960593}
  - {fileID: 1524913898}
  - {fileID: 1824668575}
  m_Father: {fileID: 364032248}
  m_LocalEulerAnglesHint: {x: 180, y: 100.685, z: -0.000015258789}
--- !u!1 &1355618819
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1355618820}
  - component: {fileID: 1355618822}
  - component: {fileID: 1355618821}
  m_Layer: 0
  m_Name: Middle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1355618820
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355618819}
  serializedVersion: 2
  m_LocalRotation: {x: 0.002400933, y: -0.055880643, z: 0.006804867, w: 0.9984114}
  m_LocalPosition: {x: -0.031118786, y: 0, z: 0.00000006854534}
  m_LocalScale: {x: 1.000003, y: 1.0000048, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1749422815}
  m_Father: {fileID: 653956371}
  m_LocalEulerAnglesHint: {x: 0.19600001, y: 2.519, z: 0.80300003}
--- !u!136 &1355618822
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355618819}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.0075
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.006, y: 0, z: 0.002}
--- !u!65 &1355618821
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1355618819}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &1416829109
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1416829110}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1416829110
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1416829109}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000075772396, y: 0.0000004423782, z: -0.000001301989, w: 1}
  m_LocalPosition: {x: -0.0166, y: -0.0016, z: 0.0007}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1212297660}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1467414176
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1467414177}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1467414177
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1467414176}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000034403058, y: -0.000003005378, z: -0.000007307157, w: 1}
  m_LocalPosition: {x: -0.0207, y: -0.0021, z: 0.0019}
  m_LocalScale: {x: 1, y: 0.9999999, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 119766581}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1474401529
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1474401530}
  - component: {fileID: 1474401531}
  m_Layer: 0
  m_Name: OuterMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1474401530
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1474401529}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7414494, y: 0.016384002, z: -0.006615691, w: 0.6707761}
  m_LocalPosition: {x: 0.06556724, y: -0.0066842884, z: -0.14039922}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1348132936}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1474401531
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1474401529}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 2a6e1f0e0e98b0146b10490d296af584, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -6962415767791754656, guid: 17a48d918cf75e74aa527c89697f3d15, type: 3}
  m_Bones:
  - {fileID: 1348132936}
  - {fileID: 575532522}
  - {fileID: 1612374346}
  - {fileID: 213269458}
  - {fileID: 1972922847}
  - {fileID: 1225283453}
  - {fileID: 1044079233}
  - {fileID: 119766581}
  - {fileID: 1467414177}
  - {fileID: 2061034818}
  - {fileID: 875416241}
  - {fileID: 1551377984}
  - {fileID: 1080109415}
  - {fileID: 1894696657}
  - {fileID: 56547633}
  - {fileID: 1212297660}
  - {fileID: 1416829110}
  - {fileID: 1670991136}
  - {fileID: 1672542735}
  - {fileID: 1099739308}
  - {fileID: 578994284}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1348132936}
  m_AABB:
    m_Center: {x: -0.08041836, y: -0.0032454282, z: -0.020966716}
    m_Extent: {x: 0.11415337, y: 0.07609155, z: 0.06161832}
  m_DirtyAABB: 0
--- !u!1 &1494960592
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1494960593}
  - component: {fileID: 1494960594}
  m_Layer: 0
  m_Name: InnerMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1494960593
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1494960592}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7414494, y: 0.016383987, z: -0.0066156765, w: 0.6707761}
  m_LocalPosition: {x: 0.06556724, y: -0.0066842884, z: -0.14039922}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1348132936}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1494960594
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1494960592}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 93ad1d8ba4d32924d8350ad986f4c73a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 8936583283049568085, guid: 17a48d918cf75e74aa527c89697f3d15, type: 3}
  m_Bones:
  - {fileID: 1348132936}
  - {fileID: 575532522}
  - {fileID: 1612374346}
  - {fileID: 213269458}
  - {fileID: 1972922847}
  - {fileID: 1225283453}
  - {fileID: 1044079233}
  - {fileID: 119766581}
  - {fileID: 1467414177}
  - {fileID: 2061034818}
  - {fileID: 875416241}
  - {fileID: 1551377984}
  - {fileID: 1080109415}
  - {fileID: 1894696657}
  - {fileID: 56547633}
  - {fileID: 1212297660}
  - {fileID: 1416829110}
  - {fileID: 1670991136}
  - {fileID: 1672542735}
  - {fileID: 1099739308}
  - {fileID: 578994284}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1348132936}
  m_AABB:
    m_Center: {x: -0.09229668, y: -0.015225619, z: -0.018114224}
    m_Extent: {x: 0.080366306, y: 0.05974908, z: 0.04299726}
  m_DirtyAABB: 0
--- !u!1 &1509029464
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1509029465}
  - component: {fileID: 1509029467}
  - component: {fileID: 1509029466}
  m_Layer: 0
  m_Name: Ring2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1509029465
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1509029464}
  serializedVersion: 2
  m_LocalRotation: {x: 0.035337657, y: -0.02529748, z: 0.0024564678, w: 0.9990522}
  m_LocalPosition: {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
  m_LocalScale: {x: 1.0000026, y: 1.0000048, z: 0.9999995}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1722413317}
  m_Father: {fileID: 1670949134}
  m_LocalEulerAnglesHint: {x: 3.969, y: 6.7230005, z: 0.85400003}
--- !u!136 &1509029467
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1509029464}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.008
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.006, y: -0.001, z: 0.002}
--- !u!65 &1509029466
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1509029464}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &1510978139
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1510978140}
  - component: {fileID: 1510978143}
  - component: {fileID: 1510978142}
  - component: {fileID: 1510978141}
  m_Layer: 0
  m_Name: Middle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1510978140
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1510978139}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00037423725, y: -0.024309829, z: -0.029526183, w: 0.9992683}
  m_LocalPosition: {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  m_LocalScale: {x: 0.99999905, y: 1.000002, z: 1.0000026}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 844405221}
  m_Father: {fileID: 773422090}
  m_LocalEulerAnglesHint: {x: 0.20400001, y: 10.124001, z: -3.8260002}
--- !u!114 &1510978143
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1510978139}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 566922977}
  tipRadius: 0.00936
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1510978140}
  - {fileID: 844405221}
  - {fileID: 1340084910}
  poseData: []
  minGripRotPose:
  - {x: -0.001174541, y: 0.08824137, z: -0.033409286, w: 0.995538}
  - {x: 0.0007867855, y: 0.03867055, z: 0.012358556, w: 0.9991753}
  - {x: 0.0018642171, y: 0.02197182, z: 0.0069710515, w: 0.9997326}
  minGripPosPose:
  - {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  - {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  - {x: -0.031118786, y: 0, z: 0.00000006854534}
  maxGripRotPose:
  - {x: 0.0046602613, y: -0.7080081, z: 0.0036643716, w: 0.7061795}
  - {x: 0.00935024, y: -0.684121, z: 0.0081195, w: 0.7292634}
  - {x: 0.005361201, y: -0.52628064, z: 0.004829721, w: 0.85028034}
  maxGripPosPose:
  - {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  - {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  - {x: -0.031118786, y: 0, z: 0.00000006854534}
--- !u!136 &1510978142
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1510978139}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.042
  m_Direction: 0
  m_Center: {x: -0.015, y: 0, z: 0}
--- !u!65 &1510978141
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1510978139}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.02, y: 0, z: 0.0015}
--- !u!1 &1519989451
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1519989452}
  - component: {fileID: 1519989453}
  m_Layer: 0
  m_Name: OuterMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1519989452
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1519989451}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7414494, y: 0.016384002, z: -0.006615691, w: 0.6707761}
  m_LocalPosition: {x: 0.06556724, y: -0.0066842884, z: -0.14039922}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 773422090}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1519989453
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1519989451}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a7e7da7a43feb7e45bc3448f5679a9e4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -6962415767791754656, guid: 17a48d918cf75e74aa527c89697f3d15, type: 3}
  m_Bones:
  - {fileID: 773422090}
  - {fileID: 1185224720}
  - {fileID: 331204738}
  - {fileID: 2046532826}
  - {fileID: 1881497674}
  - {fileID: 710639190}
  - {fileID: 256103719}
  - {fileID: 284372187}
  - {fileID: 609451842}
  - {fileID: 1510978140}
  - {fileID: 844405221}
  - {fileID: 1340084910}
  - {fileID: 566922977}
  - {fileID: 100774092}
  - {fileID: 1670949134}
  - {fileID: 1509029465}
  - {fileID: 1722413317}
  - {fileID: 1642937139}
  - {fileID: 967168265}
  - {fileID: 1600000621}
  - {fileID: 1967358981}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 773422090}
  m_AABB:
    m_Center: {x: -0.08041836, y: -0.0032454282, z: -0.020966716}
    m_Extent: {x: 0.11415337, y: 0.07609155, z: 0.06161832}
  m_DirtyAABB: 0
--- !u!1 &1524913897
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1524913898}
  m_Layer: 0
  m_Name: Palm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1524913898
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1524913897}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6416945, y: -0.7662578, z: -0.031648926, w: -0.008686245}
  m_LocalPosition: {x: -0.0602, y: -0.0065, z: -0.0255}
  m_LocalScale: {x: 0.9600002, y: 0.9600002, z: 0.96000046}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1348132936}
  m_LocalEulerAnglesHint: {x: 0.83, y: 188.079, z: -80.365}
--- !u!1 &1551377983
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1551377984}
  - component: {fileID: 1551377985}
  m_Layer: 0
  m_Name: Middle2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1551377984
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1551377983}
  serializedVersion: 2
  m_LocalRotation: {x: 0.002400933, y: -0.055880643, z: 0.006804867, w: 0.9984114}
  m_LocalPosition: {x: -0.031118786, y: 0, z: 0.00000006854534}
  m_LocalScale: {x: 1.000003, y: 1.0000048, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1080109415}
  m_Father: {fileID: 875416241}
  m_LocalEulerAnglesHint: {x: 0.19600001, y: 2.519, z: 0.80300003}
--- !u!136 &1551377985
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1551377983}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.0075
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.006, y: 0, z: 0.002}
--- !u!1 &1570184224
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1570184225}
  - component: {fileID: 1570184226}
  m_Layer: 0
  m_Name: Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1570184225
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1570184224}
  serializedVersion: 2
  m_LocalRotation: {x: -0.006846468, y: -0.030227715, z: 0.09913848, w: -0.9945909}
  m_LocalPosition: {x: -0.0479, y: -0.0076, z: -0.0049}
  m_LocalScale: {x: 0.09, y: 0.09, z: 0.044347502}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1990414918}
  m_LocalEulerAnglesHint: {x: 1.124, y: 3.37, z: -11.352}
--- !u!64 &1570184226
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1570184224}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1577838259
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1577838260}
  - component: {fileID: 1577838263}
  - component: {fileID: 1577838262}
  - component: {fileID: 1577838261}
  - component: {fileID: 1041852684419629770}
  - component: {fileID: 2564520779050776064}
  - component: {fileID: 5757411027562109704}
  m_Layer: 0
  m_Name: Highlight Projection (R)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1577838260
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577838259}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: -0.79, y: 0.79, z: 0.79}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 773422090}
  m_Father: {fileID: 6162809413267167199}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1577838263
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577838259}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a19d3478546e174ba4693f363a23073, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 1262313376109179214}
  handProjection: {fileID: 1577838261}
  handProjectionVisuals:
  - {fileID: 773422090}
  speed: 15
  hideHand: 0
  handVisuals:
  - {fileID: 0}
  - {fileID: 0}
  useGrabTransition: 0
  grabTransitionOffset: 0
  grabDistanceMultiplyer: 2
  grabTransitionMultiplyer: 2
  grabPercent: 1
  OnStartProjection:
    m_PersistentCalls:
      m_Calls: []
  OnEndProjection:
    m_PersistentCalls:
      m_Calls: []
--- !u!54 &1577838262
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577838259}
  serializedVersion: 4
  m_Mass: 10
  m_Drag: 10
  m_AngularDrag: 30
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 2
--- !u!114 &1577838261
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577838259}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9413d460e98076241a1f46c91201217d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreMe: 0
  fingers:
  - {fileID: 1185224723}
  - {fileID: 710639193}
  - {fileID: 1510978143}
  - {fileID: 100774095}
  - {fileID: 1642937142}
  palmTransform: {fileID: 350036082}
  pinchPointTransform: {fileID: 0}
  left: 0
  reachDistance: 0.2
  enableMovement: 0
  follow: {fileID: 0}
  throwPower: 2
  gentleGrabSpeed: 1
  advancedFollowSettings: 1
  enableIK: 0
  swayStrength: 0
  gripOffset: 0.14
  usingPoseAreas: 1
  queryTriggerInteraction: 1
  usingHighlight: 0
  highlightLayers:
    serializedVersion: 2
    m_Bits: 0
  defaultHighlight: {fileID: 0}
  showAdvanced: 1
  noHandFriction: 1
  ignoreGrabCheckLayers:
    serializedVersion: 2
    m_Bits: 0
  grabType: 0
  grabCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 2
      outSlope: 2
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  minGrabTime: 0.1
  maxGrabTime: 0.2
  velocityGrabHandAmplifier: 120
  velocityGrabObjectAmplifier: 10
  grabOpenHandPoint: 0.5
  poseIndex: 0
  ignoreMe1: 0
  copyFromHand: {fileID: 0}
--- !u!114 &1041852684419629770
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577838259}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f45b4d990ab65e478cc05444d8e79a5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  maxMoveToDistance: 0.1
  maxMoveToAngle: 45
  maxFollowDistance: 0.5
  maxVelocity: 12
  followPositionStrength: 60
  startDrag: 20
  dragDamper: 3
  dragDamperDistance: 0.025
  minVelocityChange: 1
  minVelocityDistanceMulti: 5
  followRotationStrength: 100
  startAngularDrag: 20
  angleDragDamper: 3
  angleDragDamperDistance: 4
  minMass: 0.25
  maxMass: 10
  heldMassDivider: 2
  distanceMassDifference: 10
  distanceMassMaxDistance: 0.5
  angleMassDifference: 10
  angleMassMaxAngle: 45
  maxDistanceNoParentReleaseFrames: 1
  maxDistanceParentReleaseFrames: 5
--- !u!114 &2564520779050776064
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577838259}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 098d5f45d7e666742b7295b5ab142cee, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  defaultPoseTransitionTime: 0.3
  defaultPoseTransitionCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 1
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &5757411027562109704
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1577838259}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 62282a7c920d8134092c8af4e7d85f92, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  palmForwardRightDirection: 0.65
  highlightQuery: 2
  highlightCollidersNonAlloc:
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  highlightColliderNonAllocCount: 0
  foundHighlightGrabbables: []
--- !u!1 &1600000620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1600000621}
  - component: {fileID: 1600000623}
  - component: {fileID: 1600000622}
  m_Layer: 0
  m_Name: Pinky2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1600000621
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1600000620}
  serializedVersion: 2
  m_LocalRotation: {x: 0.037299104, y: -0.028552314, z: 0.013196694, w: 0.998809}
  m_LocalPosition: {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
  m_LocalScale: {x: 1.0000057, y: 1.0000064, z: 1.0000006}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1967358981}
  m_Father: {fileID: 967168265}
  m_LocalEulerAnglesHint: {x: 4.138, y: 3.0690002, z: 1.8570001}
--- !u!136 &1600000623
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1600000620}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.006
  m_Height: 0.025
  m_Direction: 0
  m_Center: {x: -0.004, y: -0.001, z: 0.0005}
--- !u!65 &1600000622
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1600000620}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.015, z: 0.015}
  m_Center: {x: -0.005, y: 0, z: 0.002}
--- !u!1 &1600512751
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1600512752}
  - component: {fileID: 1600512754}
  - component: {fileID: 1600512753}
  m_Layer: 0
  m_Name: Index1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1600512752
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1600512751}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0048813173, y: -0.03711859, z: -0.005189765, w: 0.99928546}
  m_LocalPosition: {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  m_LocalScale: {x: 0.9999992, y: 1.0000033, z: 1.0000032}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 984978722}
  m_Father: {fileID: 1628009438}
  m_LocalEulerAnglesHint: {x: 0.63500005, y: 5.677, z: -0.513}
--- !u!136 &1600512754
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1600512751}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.01, y: 0, z: 0}
--- !u!65 &1600512753
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1600512751}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.035, y: 0.02, z: 0.018}
  m_Center: {x: -0.015, y: 0, z: 0.002}
--- !u!1 &1612374345
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1612374346}
  - component: {fileID: 1612374347}
  m_Layer: 0
  m_Name: Thumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1612374346
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1612374345}
  serializedVersion: 2
  m_LocalRotation: {x: 0.012234439, y: -0.11092072, z: -0.035879955, w: 0.99310607}
  m_LocalPosition: {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  m_LocalScale: {x: 1.0000014, y: 1.000003, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 213269458}
  m_Father: {fileID: 575532522}
  m_LocalEulerAnglesHint: {x: -2.8560002, y: -66.236, z: -3.2760003}
--- !u!136 &1612374347
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1612374345}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.012, y: 0, z: 0}
--- !u!1 &1628009437
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1628009438}
  - component: {fileID: 1628009441}
  - component: {fileID: 1628009440}
  - component: {fileID: 1628009439}
  m_Layer: 0
  m_Name: Index
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1628009438
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1628009437}
  serializedVersion: 2
  m_LocalRotation: {x: 0.08284666, y: -0.01826318, z: 0.07108723, w: 0.9938559}
  m_LocalPosition: {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  m_LocalScale: {x: 0.9999983, y: 1.0000019, z: 1.0000032}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1600512752}
  m_Father: {fileID: 1780456924}
  m_LocalEulerAnglesHint: {x: 8.345, y: 11.473001, z: 9.206}
--- !u!114 &1628009441
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1628009437}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 25635430}
  tipRadius: 0.0084
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1628009438}
  - {fileID: 1600512752}
  - {fileID: 984978722}
  poseData: []
  minGripRotPose:
  - {x: 0.08016173, y: 0.093553856, z: 0.072388016, w: 0.9897383}
  - {x: 0.005312354, y: 0.04954708, z: -0.0047476185, w: 0.9987464}
  - {x: -0.0023328613, y: 0.008577391, z: 0.0023032972, w: 0.99995786}
  minGripPosPose:
  - {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  - {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  - {x: -0.029940333, y: 0, z: 0.000000023841856}
  maxGripRotPose:
  - {x: 0.073544726, y: -0.6994529, z: 0.04096306, w: 0.70970345}
  - {x: 0.0013731268, y: -0.5629508, z: -0.0069910423, w: 0.82645977}
  - {x: 0.00013496191, y: -0.73411435, z: 0.003275542, w: 0.679018}
  maxGripPosPose:
  - {x: -0.09321327, y: -0.034050062, z: 0.00014008701}
  - {x: -0.0331067, y: 0.000000085830685, z: 0.00000020623207}
  - {x: -0.029940333, y: 0, z: 0.000000023841856}
--- !u!136 &1628009440
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1628009437}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.043
  m_Direction: 0
  m_Center: {x: -0.015, y: 0, z: 0}
--- !u!65 &1628009439
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1628009437}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.02, y: 0, z: 0.0015}
--- !u!1 &1642937138
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1642937139}
  - component: {fileID: 1642937142}
  - component: {fileID: 1642937141}
  - component: {fileID: 1642937140}
  m_Layer: 0
  m_Name: Pinky
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1642937139
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1642937138}
  serializedVersion: 2
  m_LocalRotation: {x: -0.24505861, y: -0.021443876, z: -0.20070784, w: 0.94826305}
  m_LocalPosition: {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  m_LocalScale: {x: 0.9999999, y: 1.0000018, z: 1.0000019}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 967168265}
  m_Father: {fileID: 773422090}
  m_LocalEulerAnglesHint: {x: -25.218, y: 17.146, z: -30.575}
--- !u!114 &1642937142
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1642937138}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1967358981}
  tipRadius: 0.0066
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1642937139}
  - {fileID: 967168265}
  - {fileID: 1600000621}
  poseData: []
  minGripRotPose:
  - {x: -0.24657567, y: 0.08341347, z: -0.2230366, w: 0.9394133}
  - {x: 0.025245013, y: 0.02485968, z: 0.046426166, w: 0.9982932}
  - {x: 0.03652061, y: 0.026177457, z: 0.015219117, w: 0.99887407}
  minGripPosPose:
  - {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  - {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  - {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
  maxGripRotPose:
  - {x: -0.16609184, y: -0.659473, z: -0.006502538, w: 0.73312116}
  - {x: 0.052083563, y: -0.7682547, z: 0.0089439, w: 0.63795924}
  - {x: 0.039564617, y: -0.36282298, z: -0.000117167816, w: 0.93101776}
  maxGripPosPose:
  - {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  - {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  - {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
--- !u!136 &1642937141
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1642937138}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.03
  m_Direction: 0
  m_Center: {x: -0.009, y: 0, z: 0}
--- !u!65 &1642937140
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1642937138}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0.0015}
--- !u!1 &1665432979
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1665432980}
  - component: {fileID: 1665432983}
  - component: {fileID: 1665432982}
  - component: {fileID: 1665432981}
  m_Layer: 0
  m_Name: Ring
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1665432980
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665432979}
  serializedVersion: 2
  m_LocalRotation: {x: -0.12729833, y: -0.045578506, z: -0.14354861, w: 0.98036295}
  m_LocalPosition: {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  m_LocalScale: {x: 0.9999996, y: 1.000002, z: 1.0000023}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 399543312}
  m_Father: {fileID: 1780456924}
  m_LocalEulerAnglesHint: {x: -13.576, y: 8.870001, z: -18.892}
--- !u!114 &1665432983
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665432979}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 921870397}
  tipRadius: 0.009
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1665432980}
  - {fileID: 399543312}
  - {fileID: 358625792}
  poseData: []
  minGripRotPose:
  - {x: -0.12884803, y: 0.05640752, z: -0.15346065, w: 0.97809315}
  - {x: -0.00033126769, y: 0.06671702, z: 0.037344586, w: 0.9970728}
  - {x: 0.035008434, y: 0.058341164, z: 0.005403131, w: 0.9976681}
  minGripPosPose:
  - {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  - {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  - {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
  maxGripRotPose:
  - {x: -0.08535152, y: -0.66045374, z: -0.04609367, w: 0.7445746}
  - {x: 0.027625704, y: -0.699027, z: 0.025130453, w: 0.7141194}
  - {x: 0.03158009, y: -0.53494, z: -0.016046247, w: 0.8441472}
  maxGripPosPose:
  - {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  - {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  - {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
--- !u!136 &1665432982
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665432979}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.005, y: 0, z: 0}
--- !u!65 &1665432981
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1665432979}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0.0015}
--- !u!1 &1669061716
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1669061717}
  - component: {fileID: 1669061719}
  - component: {fileID: 1669061718}
  m_Layer: 0
  m_Name: Thumb1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1669061717
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1669061716}
  serializedVersion: 2
  m_LocalRotation: {x: 0.012234439, y: -0.11092072, z: -0.035879955, w: 0.99310607}
  m_LocalPosition: {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  m_LocalScale: {x: 1.0000014, y: 1.000003, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1980302040}
  m_Father: {fileID: 2057608971}
  m_LocalEulerAnglesHint: {x: -2.8560002, y: -66.236, z: -3.2760003}
--- !u!136 &1669061719
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1669061716}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.012, y: 0, z: 0}
--- !u!65 &1669061718
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1669061716}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0}
--- !u!1 &1670949133
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1670949134}
  - component: {fileID: 1670949136}
  - component: {fileID: 1670949135}
  m_Layer: 0
  m_Name: Ring1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1670949134
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1670949133}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0037359423, y: -0.04221879, z: 0.037158713, w: 0.99841017}
  m_LocalPosition: {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  m_LocalScale: {x: 1.0000008, y: 1.0000033, z: 1.0000023}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1509029465}
  m_Father: {fileID: 100774092}
  m_LocalEulerAnglesHint: {x: -0.323, y: 7.6440005, z: 4.268}
--- !u!136 &1670949136
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1670949133}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.01
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.005, y: 0, z: 0.002}
--- !u!65 &1670949135
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1670949133}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.03, y: 0.02, z: 0.018}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &1670991135
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1670991136}
  - component: {fileID: 1670991138}
  - component: {fileID: 1670991137}
  m_Layer: 0
  m_Name: Pinky
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1670991136
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1670991135}
  serializedVersion: 2
  m_LocalRotation: {x: -0.24505861, y: -0.021443876, z: -0.20070784, w: 0.94826305}
  m_LocalPosition: {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  m_LocalScale: {x: 0.9999999, y: 1.0000018, z: 1.0000019}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1672542735}
  m_Father: {fileID: 1348132936}
  m_LocalEulerAnglesHint: {x: -25.218, y: 17.146, z: -30.575}
--- !u!114 &1670991138
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1670991135}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 578994284}
  tipRadius: 0.0066
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1670991136}
  - {fileID: 1672542735}
  - {fileID: 1099739308}
  poseData: []
  minGripRotPose:
  - {x: -0.24657567, y: 0.08341347, z: -0.2230366, w: 0.9394133}
  - {x: 0.025245013, y: 0.02485968, z: 0.046426166, w: 0.9982932}
  - {x: 0.03652061, y: 0.026177457, z: 0.015219117, w: 0.99887407}
  minGripPosPose:
  - {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  - {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  - {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
  maxGripRotPose:
  - {x: -0.16609184, y: -0.659473, z: -0.006502538, w: 0.73312116}
  - {x: 0.052083563, y: -0.7682547, z: 0.0089439, w: 0.63795924}
  - {x: 0.039564617, y: -0.36282298, z: -0.000117167816, w: 0.93101776}
  maxGripPosPose:
  - {x: -0.080928504, y: 0.034541752, z: -0.008505775}
  - {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  - {x: -0.019739967, y: -0.000000028610229, z: 0.00000012397766}
--- !u!136 &1670991137
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1670991135}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.03
  m_Direction: 0
  m_Center: {x: -0.009, y: 0, z: 0}
--- !u!1 &1672542734
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1672542735}
  - component: {fileID: 1672542736}
  m_Layer: 0
  m_Name: Pinky1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1672542735
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1672542734}
  serializedVersion: 2
  m_LocalRotation: {x: 0.03042995, y: -0.090418056, z: 0.043205522, w: 0.9945009}
  m_LocalPosition: {x: -0.027588898, y: 0.000000042915342, z: -0.0000000166893}
  m_LocalScale: {x: 1.000002, y: 1.000004, z: 1.0000021}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1099739308}
  m_Father: {fileID: 1670991136}
  m_LocalEulerAnglesHint: {x: 2.7570002, y: 2.983, z: 5.3970003}
--- !u!136 &1672542736
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1672542734}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.03
  m_Direction: 0
  m_Center: {x: -0.004, y: 0, z: 0}
--- !u!1 &1677025919
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1677025920}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1677025920
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1677025919}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000072037797, y: -0.0000022402965, z: -0.0000032116654, w: 1}
  m_LocalPosition: {x: -0.0186, y: -0.0016, z: 0.0016}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1168953082}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1722413316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1722413317}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1722413317
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1722413316}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000075772396, y: 0.0000004423782, z: -0.000001301989, w: 1}
  m_LocalPosition: {x: -0.0166, y: -0.0016, z: 0.0007}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1509029465}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1749001115
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1749001116}
  - component: {fileID: 1749001119}
  - component: {fileID: 1749001118}
  - component: {fileID: 1749001117}
  m_Layer: 0
  m_Name: Thumb
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1749001116
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1749001115}
  serializedVersion: 2
  m_LocalRotation: {x: 0.35080808, y: 0.13090943, z: 0.40735877, w: 0.8329798}
  m_LocalPosition: {x: -0.024448793, y: -0.03049879, z: -0.015017874}
  m_LocalScale: {x: 1.0000002, y: 1.000002, z: 1.0000012}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1342902481}
  m_Father: {fileID: 1780456924}
  m_LocalEulerAnglesHint: {x: 81.382, y: -115.203, z: -53.660004}
--- !u!114 &1749001119
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1749001115}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1282546509}
  tipRadius: 0.012
  bendOffset: 0.03
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1749001116}
  - {fileID: 1342902481}
  - {fileID: 303320317}
  poseData: []
  minGripRotPose:
  - {x: 0.2964466, y: 0.21444798, z: 0.407665, w: 0.8366246}
  - {x: 0.01483373, y: -0.037764624, z: -0.034885716, w: 0.9985674}
  - {x: 0.0007297829, y: -0.047739774, z: -0.005937712, w: 0.9988419}
  minGripPosPose:
  - {x: -0.024700923, y: -0.030173011, z: -0.016420783}
  - {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  - {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
  maxGripRotPose:
  - {x: 0.60068303, y: -0.41362432, z: 0.307867, w: 0.6109933}
  - {x: -0.0052479487, y: -0.5465699, z: -0.037543483, w: 0.8365551}
  - {x: -0.002396468, y: -0.549858, z: -0.005481367, w: 0.8352368}
  maxGripPosPose:
  - {x: -0.0229, y: -0.0325, z: -0.0064}
  - {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  - {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
--- !u!136 &1749001118
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1749001115}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.012
  m_Height: 0.045
  m_Direction: 0
  m_Center: {x: -0.02, y: 0, z: 0}
--- !u!65 &1749001117
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1749001115}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.05, y: 0.02, z: 0.02}
  m_Center: {x: -0.02, y: 0, z: 0}
--- !u!1 &1749422814
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1749422815}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1749422815
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1749422814}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000072037797, y: -0.0000022402965, z: -0.0000032116654, w: 1}
  m_LocalPosition: {x: -0.0186, y: -0.0016, z: 0.0016}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1355618820}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1780456923
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1780456924}
  m_Layer: 0
  m_Name: Pivot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1780456924
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1780456923}
  serializedVersion: 2
  m_LocalRotation: {x: 0.67921716, y: 0.004741866, z: -0.73388237, w: 0.0076374956}
  m_LocalPosition: {x: -0.0035, y: -0.0021, z: -0.1035}
  m_LocalScale: {x: 1.25, y: 1.25, z: 1.25}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1749001116}
  - {fileID: 1628009438}
  - {fileID: 1137609619}
  - {fileID: 1665432980}
  - {fileID: 1305778227}
  - {fileID: 927954380}
  - {fileID: 1029820513}
  - {fileID: 998136199}
  - {fileID: 1910903169}
  m_Father: {fileID: 1907517617}
  m_LocalEulerAnglesHint: {x: 180, y: 100.685, z: -0.000015258789}
--- !u!1 &1824668574
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1824668575}
  - component: {fileID: 1824668576}
  m_Layer: 0
  m_Name: Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1824668575
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1824668574}
  serializedVersion: 2
  m_LocalRotation: {x: -0.006846468, y: -0.030227715, z: 0.09913848, w: -0.9945909}
  m_LocalPosition: {x: -0.0479, y: -0.0076, z: -0.0049}
  m_LocalScale: {x: 0.09, y: 0.09, z: 0.044347502}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1348132936}
  m_LocalEulerAnglesHint: {x: 1.124, y: 3.37, z: -11.352}
--- !u!64 &1824668576
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1824668574}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1833199285
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1833199286}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1833199286
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1833199285}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000061402106, y: -0.0000049649398, z: 0.0000024675394, w: 1}
  m_LocalPosition: {x: -0.0151, y: 0.0034, z: -0.0008}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1980302040}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1843319557
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1843319558}
  - component: {fileID: 1843319560}
  - component: {fileID: 1843319559}
  m_Layer: 0
  m_Name: Ring2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1843319558
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1843319557}
  serializedVersion: 2
  m_LocalRotation: {x: 0.035337657, y: -0.02529748, z: 0.0024564678, w: 0.9990522}
  m_LocalPosition: {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
  m_LocalScale: {x: 1.0000026, y: 1.0000048, z: 0.9999995}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1191312104}
  m_Father: {fileID: 1150899743}
  m_LocalEulerAnglesHint: {x: 3.969, y: 6.7230005, z: 0.85400003}
--- !u!136 &1843319560
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1843319557}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.008
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.006, y: -0.001, z: 0.002}
--- !u!65 &1843319559
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1843319557}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.025, y: 0.018, z: 0.015}
  m_Center: {x: -0.01, y: 0, z: 0.002}
--- !u!1 &1854517315
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1854517316}
  - component: {fileID: 1854517319}
  - component: {fileID: 1854517318}
  - component: {fileID: 1854517317}
  m_Layer: 0
  m_Name: Middle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1854517316
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1854517315}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00037423725, y: -0.024309829, z: -0.029526183, w: 0.9992683}
  m_LocalPosition: {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  m_LocalScale: {x: 0.99999905, y: 1.000002, z: 1.0000026}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 653956371}
  m_Father: {fileID: 1990414918}
  m_LocalEulerAnglesHint: {x: 0.20400001, y: 10.124001, z: -3.8260002}
--- !u!114 &1854517319
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1854517315}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1749422815}
  tipRadius: 0.00936
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1854517316}
  - {fileID: 653956371}
  - {fileID: 1355618820}
  poseData: []
  minGripRotPose:
  - {x: -0.001174541, y: 0.08824137, z: -0.033409286, w: 0.995538}
  - {x: 0.0007867855, y: 0.03867055, z: 0.012358556, w: 0.9991753}
  - {x: 0.0018642171, y: 0.02197182, z: 0.0069710515, w: 0.9997326}
  minGripPosPose:
  - {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  - {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  - {x: -0.031118786, y: 0, z: 0.00000006854534}
  maxGripRotPose:
  - {x: 0.0046602613, y: -0.7080081, z: 0.0036643716, w: 0.7061795}
  - {x: 0.00935024, y: -0.684121, z: 0.0081195, w: 0.7292634}
  - {x: 0.005361201, y: -0.52628064, z: 0.004829721, w: 0.85028034}
  maxGripPosPose:
  - {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  - {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  - {x: -0.031118786, y: 0, z: 0.00000006854534}
--- !u!136 &1854517318
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1854517315}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.042
  m_Direction: 0
  m_Center: {x: -0.015, y: 0, z: 0}
--- !u!65 &1854517317
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1854517315}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.02, y: 0, z: 0.0015}
--- !u!1 &1881497673
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1881497674}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1881497674
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1881497673}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000061402106, y: -0.0000049649398, z: 0.0000024675394, w: 1}
  m_LocalPosition: {x: -0.0151, y: 0.0034, z: -0.0008}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2046532826}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1894696656
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1894696657}
  - component: {fileID: 1894696659}
  - component: {fileID: 1894696658}
  m_Layer: 0
  m_Name: Ring
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1894696657
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1894696656}
  serializedVersion: 2
  m_LocalRotation: {x: -0.12729833, y: -0.045578506, z: -0.14354861, w: 0.98036295}
  m_LocalPosition: {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  m_LocalScale: {x: 0.9999996, y: 1.000002, z: 1.0000023}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 56547633}
  m_Father: {fileID: 1348132936}
  m_LocalEulerAnglesHint: {x: -13.576, y: 8.870001, z: -18.892}
--- !u!114 &1894696659
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1894696656}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1416829110}
  tipRadius: 0.009
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1894696657}
  - {fileID: 56547633}
  - {fileID: 1212297660}
  poseData: []
  minGripRotPose:
  - {x: -0.12884803, y: 0.05640752, z: -0.15346065, w: 0.97809315}
  - {x: -0.00033126769, y: 0.06671702, z: 0.037344586, w: 0.9970728}
  - {x: 0.035008434, y: 0.058341164, z: 0.005403131, w: 0.9976681}
  minGripPosPose:
  - {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  - {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  - {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
  maxGripRotPose:
  - {x: -0.08535152, y: -0.66045374, z: -0.04609367, w: 0.7445746}
  - {x: 0.027625704, y: -0.699027, z: 0.025130453, w: 0.7141194}
  - {x: 0.03158009, y: -0.53494, z: -0.016046247, w: 0.8441472}
  maxGripPosPose:
  - {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  - {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  - {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
--- !u!136 &1894696658
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1894696656}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.005, y: 0, z: 0}
--- !u!1 &1907517616
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1907517617}
  - component: {fileID: 1907517620}
  - component: {fileID: 1907517619}
  - component: {fileID: 1907517618}
  - component: {fileID: 153931231686173803}
  - component: {fileID: 2668657535860550280}
  - component: {fileID: 1789597222356850611}
  m_Layer: 0
  m_Name: Highlight Projection (L)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1907517617
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1907517616}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.79, y: 0.79, z: 0.79}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1780456924}
  m_Father: {fileID: 6162809413267167199}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1907517620
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1907517616}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a19d3478546e174ba4693f363a23073, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 1262313374629172509}
  handProjection: {fileID: 1907517618}
  handProjectionVisuals:
  - {fileID: 1780456924}
  speed: 15
  hideHand: 0
  handVisuals:
  - {fileID: 0}
  - {fileID: 0}
  useGrabTransition: 0
  grabTransitionOffset: 0
  grabDistanceMultiplyer: 2
  grabTransitionMultiplyer: 2
  grabPercent: 1
  OnStartProjection:
    m_PersistentCalls:
      m_Calls: []
  OnEndProjection:
    m_PersistentCalls:
      m_Calls: []
--- !u!54 &1907517619
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1907517616}
  serializedVersion: 4
  m_Mass: 10
  m_Drag: 10
  m_AngularDrag: 30
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 2
--- !u!114 &1907517618
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1907517616}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9413d460e98076241a1f46c91201217d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  ignoreMe: 0
  fingers:
  - {fileID: 1749001119}
  - {fileID: 1628009441}
  - {fileID: 1137609622}
  - {fileID: 1665432983}
  - {fileID: 1305778230}
  palmTransform: {fileID: 998136199}
  pinchPointTransform: {fileID: 0}
  left: 1
  reachDistance: 0.2
  enableMovement: 0
  follow: {fileID: 0}
  throwPower: 2
  gentleGrabSpeed: 1
  advancedFollowSettings: 1
  enableIK: 0
  swayStrength: 0
  gripOffset: 0.14
  usingPoseAreas: 1
  queryTriggerInteraction: 1
  usingHighlight: 0
  highlightLayers:
    serializedVersion: 2
    m_Bits: 0
  defaultHighlight: {fileID: 0}
  showAdvanced: 1
  noHandFriction: 1
  ignoreGrabCheckLayers:
    serializedVersion: 2
    m_Bits: 0
  grabType: 0
  grabCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 2
      outSlope: 2
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  minGrabTime: 0.1
  maxGrabTime: 0.2
  velocityGrabHandAmplifier: 120
  velocityGrabObjectAmplifier: 10
  grabOpenHandPoint: 0.5
  poseIndex: 0
  ignoreMe1: 0
  copyFromHand: {fileID: 0}
--- !u!114 &153931231686173803
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1907517616}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1f45b4d990ab65e478cc05444d8e79a5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  maxMoveToDistance: 0.1
  maxMoveToAngle: 45
  maxFollowDistance: 0.5
  maxVelocity: 12
  followPositionStrength: 60
  startDrag: 20
  dragDamper: 3
  dragDamperDistance: 0.025
  minVelocityChange: 1
  minVelocityDistanceMulti: 5
  followRotationStrength: 100
  startAngularDrag: 20
  angleDragDamper: 3
  angleDragDamperDistance: 4
  minMass: 0.25
  maxMass: 10
  heldMassDivider: 2
  distanceMassDifference: 10
  distanceMassMaxDistance: 0.5
  angleMassDifference: 10
  angleMassMaxAngle: 45
  maxDistanceNoParentReleaseFrames: 1
  maxDistanceParentReleaseFrames: 5
--- !u!114 &2668657535860550280
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1907517616}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 098d5f45d7e666742b7295b5ab142cee, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  defaultPoseTransitionTime: 0.3
  defaultPoseTransitionCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 1
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
--- !u!114 &1789597222356850611
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1907517616}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 62282a7c920d8134092c8af4e7d85f92, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  palmForwardRightDirection: 0.65
  highlightQuery: 2
  highlightCollidersNonAlloc:
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  - {fileID: 0}
  highlightColliderNonAllocCount: 0
  foundHighlightGrabbables: []
--- !u!1 &1910903168
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1910903169}
  - component: {fileID: 1910903170}
  m_Layer: 0
  m_Name: Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1910903169
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1910903168}
  serializedVersion: 2
  m_LocalRotation: {x: -0.006846468, y: -0.030227715, z: 0.09913848, w: -0.9945909}
  m_LocalPosition: {x: -0.0479, y: -0.0076, z: -0.0049}
  m_LocalScale: {x: 0.09, y: 0.09, z: 0.044347502}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1780456924}
  m_LocalEulerAnglesHint: {x: 1.124, y: 3.37, z: -11.352}
--- !u!64 &1910903170
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1910903168}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 1
  m_CookingOptions: 30
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &1926108553
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1926108554}
  m_Layer: 0
  m_Name: Palm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1926108554
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1926108553}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6416945, y: -0.7662578, z: -0.031648926, w: -0.008686245}
  m_LocalPosition: {x: -0.0602, y: -0.0065, z: -0.0255}
  m_LocalScale: {x: 0.9600002, y: 0.9600002, z: 0.96000046}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1990414918}
  m_LocalEulerAnglesHint: {x: 0.83, y: 188.079, z: -80.365}
--- !u!1 &1952727343
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1952727344}
  - component: {fileID: 1952727347}
  - component: {fileID: 1952727346}
  - component: {fileID: 1952727345}
  m_Layer: 0
  m_Name: Ring
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1952727344
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1952727343}
  serializedVersion: 2
  m_LocalRotation: {x: -0.12729833, y: -0.045578506, z: -0.14354861, w: 0.98036295}
  m_LocalPosition: {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  m_LocalScale: {x: 0.9999996, y: 1.000002, z: 1.0000023}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1150899743}
  m_Father: {fileID: 1990414918}
  m_LocalEulerAnglesHint: {x: -13.576, y: 8.870001, z: -18.892}
--- !u!114 &1952727347
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1952727343}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1191312104}
  tipRadius: 0.009
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 1952727344}
  - {fileID: 1150899743}
  - {fileID: 1843319558}
  poseData: []
  minGripRotPose:
  - {x: -0.12884803, y: 0.05640752, z: -0.15346065, w: 0.97809315}
  - {x: -0.00033126769, y: 0.06671702, z: 0.037344586, w: 0.9970728}
  - {x: 0.035008434, y: 0.058341164, z: 0.005403131, w: 0.9976681}
  minGripPosPose:
  - {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  - {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  - {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
  maxGripRotPose:
  - {x: -0.08535152, y: -0.66045374, z: -0.04609367, w: 0.7445746}
  - {x: 0.027625704, y: -0.699027, z: 0.025130453, w: 0.7141194}
  - {x: 0.03158009, y: -0.53494, z: -0.016046247, w: 0.8441472}
  maxGripPosPose:
  - {x: -0.08924932, y: 0.013058161, z: -0.0017072532}
  - {x: -0.033451367, y: -0.000000023841856, z: 0.00000020980835}
  - {x: -0.026750788, y: -0.000000038146972, z: 0.0000000023841857}
--- !u!136 &1952727346
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1952727343}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.035
  m_Direction: 0
  m_Center: {x: -0.005, y: 0, z: 0}
--- !u!65 &1952727345
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1952727343}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.04, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0.0015}
--- !u!1 &1959617163
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1959617164}
  - component: {fileID: 1959617165}
  m_Layer: 0
  m_Name: OuterMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1959617164
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1959617163}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7414494, y: 0.016384002, z: -0.006615691, w: 0.6707761}
  m_LocalPosition: {x: 0.06556724, y: -0.0066842884, z: -0.14039922}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1990414918}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1959617165
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1959617163}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 2a6e1f0e0e98b0146b10490d296af584, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 1
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -6962415767791754656, guid: 17a48d918cf75e74aa527c89697f3d15, type: 3}
  m_Bones:
  - {fileID: 1990414918}
  - {fileID: 2057608971}
  - {fileID: 1669061717}
  - {fileID: 1980302040}
  - {fileID: 1833199286}
  - {fileID: 1183926533}
  - {fileID: 492039277}
  - {fileID: 221408174}
  - {fileID: 348929178}
  - {fileID: 1854517316}
  - {fileID: 653956371}
  - {fileID: 1355618820}
  - {fileID: 1749422815}
  - {fileID: 1952727344}
  - {fileID: 1150899743}
  - {fileID: 1843319558}
  - {fileID: 1191312104}
  - {fileID: 1192699942}
  - {fileID: 965286014}
  - {fileID: 978501049}
  - {fileID: 1149393779}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 1990414918}
  m_AABB:
    m_Center: {x: -0.08041836, y: -0.0032454282, z: -0.020966716}
    m_Extent: {x: 0.11415337, y: 0.07609155, z: 0.06161832}
  m_DirtyAABB: 0
--- !u!1 &1967358980
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1967358981}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1967358981
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1967358980}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000008029863, y: 0.0000010747461, z: -0.0000007860363, w: 1}
  m_LocalPosition: {x: -0.01333, y: -0.00131, z: 0.00011}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1600000621}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1972922846
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1972922847}
  m_Layer: 0
  m_Name: Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1972922847
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1972922846}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000061402106, y: -0.0000049649398, z: 0.0000024675394, w: 1}
  m_LocalPosition: {x: -0.0151, y: 0.0034, z: -0.0008}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 213269458}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1980302039
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1980302040}
  - component: {fileID: 1980302042}
  - component: {fileID: 1980302041}
  m_Layer: 0
  m_Name: Thumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1980302040
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1980302039}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00029713922, y: -0.120069474, z: -0.0059749987, w: 0.9927475}
  m_LocalPosition: {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
  m_LocalScale: {x: 0.9999988, y: 1.0000035, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1833199286}
  m_Father: {fileID: 1669061717}
  m_LocalEulerAnglesHint: {x: -0.57500005, y: -66.714005, z: -0.374}
--- !u!136 &1980302042
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1980302039}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.004, y: 0.0025, z: 0}
--- !u!65 &1980302041
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1980302039}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.03, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0}
--- !u!1 &1990414917
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1990414918}
  m_Layer: 0
  m_Name: Pivot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1990414918
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1990414917}
  serializedVersion: 2
  m_LocalRotation: {x: 0.67921716, y: 0.004741866, z: -0.73388237, w: 0.0076374956}
  m_LocalPosition: {x: -0.0035, y: -0.0021, z: -0.1035}
  m_LocalScale: {x: 1.25, y: 1.25, z: 1.25}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2057608971}
  - {fileID: 1183926533}
  - {fileID: 1854517316}
  - {fileID: 1952727344}
  - {fileID: 1192699942}
  - {fileID: 1959617164}
  - {fileID: 155834669}
  - {fileID: 1926108554}
  - {fileID: 1570184225}
  m_Father: {fileID: 271303543}
  m_LocalEulerAnglesHint: {x: 180, y: 100.685, z: -0.000015258789}
--- !u!1 &2046532825
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2046532826}
  - component: {fileID: 2046532828}
  - component: {fileID: 2046532827}
  m_Layer: 0
  m_Name: Thumb2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2046532826
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2046532825}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00029713922, y: -0.120069474, z: -0.0059749987, w: 0.9927475}
  m_LocalPosition: {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
  m_LocalScale: {x: 0.9999988, y: 1.0000035, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1881497674}
  m_Father: {fileID: 331204738}
  m_LocalEulerAnglesHint: {x: -0.57500005, y: -66.714005, z: -0.374}
--- !u!136 &2046532828
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2046532825}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.009
  m_Height: 0.04
  m_Direction: 0
  m_Center: {x: -0.004, y: 0.0025, z: 0}
--- !u!65 &2046532827
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2046532825}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.03, y: 0.02, z: 0.02}
  m_Center: {x: -0.01, y: 0, z: 0}
--- !u!1 &2057608970
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2057608971}
  - component: {fileID: 2057608974}
  - component: {fileID: 2057608973}
  - component: {fileID: 2057608972}
  m_Layer: 0
  m_Name: Thumb
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2057608971
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2057608970}
  serializedVersion: 2
  m_LocalRotation: {x: 0.35080808, y: 0.13090943, z: 0.40735877, w: 0.8329798}
  m_LocalPosition: {x: -0.024448793, y: -0.03049879, z: -0.015017874}
  m_LocalScale: {x: 1.0000002, y: 1.000002, z: 1.0000012}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1669061717}
  m_Father: {fileID: 1990414918}
  m_LocalEulerAnglesHint: {x: 81.382, y: -115.203, z: -53.660004}
--- !u!114 &2057608974
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2057608970}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1833199286}
  tipRadius: 0.012
  bendOffset: 0.03
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 2057608971}
  - {fileID: 1669061717}
  - {fileID: 1980302040}
  poseData: []
  minGripRotPose:
  - {x: 0.2964466, y: 0.21444798, z: 0.407665, w: 0.8366246}
  - {x: 0.01483373, y: -0.037764624, z: -0.034885716, w: 0.9985674}
  - {x: 0.0007297829, y: -0.047739774, z: -0.005937712, w: 0.9988419}
  minGripPosPose:
  - {x: -0.024700923, y: -0.030173011, z: -0.016420783}
  - {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  - {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
  maxGripRotPose:
  - {x: 0.60068303, y: -0.41362432, z: 0.307867, w: 0.6109933}
  - {x: -0.0052479487, y: -0.5465699, z: -0.037543483, w: 0.8365551}
  - {x: -0.002396468, y: -0.549858, z: -0.005481367, w: 0.8352368}
  maxGripPosPose:
  - {x: -0.0229, y: -0.0325, z: -0.0064}
  - {x: -0.0420796, y: 0.00000004053116, z: -0.0000000667572}
  - {x: -0.03139709, y: 0.000000028610229, z: -0.00000016212464}
--- !u!136 &2057608973
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2057608970}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 0
  serializedVersion: 2
  m_Radius: 0.012
  m_Height: 0.045
  m_Direction: 0
  m_Center: {x: -0.02, y: 0, z: 0}
--- !u!65 &2057608972
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2057608970}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.05, y: 0.02, z: 0.02}
  m_Center: {x: -0.02, y: 0, z: 0}
--- !u!1 &2061034817
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2061034818}
  - component: {fileID: 2061034820}
  - component: {fileID: 2061034819}
  m_Layer: 0
  m_Name: Middle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2061034818
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2061034817}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00037423725, y: -0.024309829, z: -0.029526183, w: 0.9992683}
  m_LocalPosition: {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  m_LocalScale: {x: 0.99999905, y: 1.000002, z: 1.0000026}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 875416241}
  m_Father: {fileID: 1348132936}
  m_LocalEulerAnglesHint: {x: 0.20400001, y: 10.124001, z: -3.8260002}
--- !u!114 &2061034820
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2061034817}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7efad4f49bdc399409aae0e128b0ad2b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 0}
  fingerType: -1
  knuckleJoint: {fileID: 0}
  middleJoint: {fileID: 0}
  distalJoint: {fileID: 0}
  tip: {fileID: 1080109415}
  tipRadius: 0.00936
  bendOffset: 0
  fingerSmoothSpeed: 1
  secondaryOffset: 0
  fingerJoints:
  - {fileID: 2061034818}
  - {fileID: 875416241}
  - {fileID: 1551377984}
  poseData: []
  minGripRotPose:
  - {x: -0.001174541, y: 0.08824137, z: -0.033409286, w: 0.995538}
  - {x: 0.0007867855, y: 0.03867055, z: 0.012358556, w: 0.9991753}
  - {x: 0.0018642171, y: 0.02197182, z: 0.0069710515, w: 0.9997326}
  minGripPosPose:
  - {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  - {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  - {x: -0.031118786, y: 0, z: 0.00000006854534}
  maxGripRotPose:
  - {x: 0.0046602613, y: -0.7080081, z: 0.0036643716, w: 0.7061795}
  - {x: 0.00935024, y: -0.684121, z: 0.0081195, w: 0.7292634}
  - {x: 0.005361201, y: -0.52628064, z: 0.004829721, w: 0.85028034}
  maxGripPosPose:
  - {x: -0.096640125, y: -0.008566598, z: 0.002615863}
  - {x: -0.038624395, y: 0.000000042915342, z: 0.00000015228986}
  - {x: -0.031118786, y: 0, z: 0.00000006854534}
--- !u!136 &2061034819
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2061034817}
  m_Material: {fileID: 13400000, guid: 479698c6385d9244885db6a634635b6d, type: 2}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.011
  m_Height: 0.042
  m_Direction: 0
  m_Center: {x: -0.015, y: 0, z: 0}
--- !u!1 &330390219411307163
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6162809413267167199}
  m_Layer: 0
  m_Name: Robot Hands
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6162809413267167199
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 330390219411307163}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 364032248}
  - {fileID: 1577838260}
  - {fileID: 1907517617}
  - {fileID: 271303543}
  - {fileID: 9136378276058449578}
  - {fileID: 9136378277261632249}
  m_Father: {fileID: 7942002212320107156}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1133441698576026961
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7078973585685985395}
  m_Layer: 0
  m_Name: (L) Follow Offset Robot Hand - OpenXR Offset
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7078973585685985395
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1133441698576026961}
  serializedVersion: 2
  m_LocalRotation: {x: 0.67559016, y: 0, z: 0, w: 0.7372774}
  m_LocalPosition: {x: -0.05, y: -0.03, z: -0.003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7942002211305997992}
  m_LocalEulerAnglesHint: {x: 85, y: 0, z: 0}
--- !u!1 &1587491276519925304
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4941567059615228357}
  m_Layer: 0
  m_Name: (R) Follow Offset Robot Hand - OpenXR Offset
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4941567059615228357
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1587491276519925304}
  serializedVersion: 2
  m_LocalRotation: {x: 0.67559016, y: 0, z: 0, w: 0.7372774}
  m_LocalPosition: {x: 0.05, y: -0.03, z: -0.003}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7942002211973936967}
  m_LocalEulerAnglesHint: {x: 85, y: 0, z: 0}
--- !u!1 &7942002210253447912
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7942002210253447911}
  m_Layer: 0
  m_Name: (R) Follow Offset Robot Hand - Oculus Offset
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7942002210253447911
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002210253447912}
  serializedVersion: 2
  m_LocalRotation: {x: 0.08715578, y: 0, z: 0, w: 0.9961947}
  m_LocalPosition: {x: 0.03, y: -0.03, z: -0.01}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7942002211973936967}
  m_LocalEulerAnglesHint: {x: 10, y: 0, z: 0}
--- !u!1 &7942002210360757121
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7942002210360757120}
  m_Layer: 0
  m_Name: (L) Follow Offset Robot Hand - Oculus Offset
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7942002210360757120
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002210360757121}
  serializedVersion: 2
  m_LocalRotation: {x: 0.08715578, y: 0, z: 0, w: 0.9961947}
  m_LocalPosition: {x: -0.03, y: -0.03, z: -0.01}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7942002211305997992}
  m_LocalEulerAnglesHint: {x: 10, y: 0, z: 0}
--- !u!1 &7942002210478575251
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7942002210478575250}
  m_Layer: 0
  m_Name: (L) Follow Offset Classic Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7942002210478575250
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002210478575251}
  serializedVersion: 2
  m_LocalRotation: {x: 0.67379385, y: 0.002427932, z: 0.006329552, w: 0.7388883}
  m_LocalPosition: {x: -0.025000002, y: 0.027999999, z: -0.016999999}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7942002211305997992}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7942002210876816004
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7942002210876816003}
  m_Layer: 0
  m_Name: (R) Follow Offset Classic Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7942002210876816003
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002210876816004}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6738478, y: -0.006713093, z: -0.0028111415, w: 0.7388344}
  m_LocalPosition: {x: 0.025000002, y: 0.027999999, z: -0.016999999}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7942002211973936967}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7942002210973373748
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7942002210973373747}
  - component: {fileID: 7412725206770988215}
  - component: {fileID: 2143022915439084234}
  m_Layer: 0
  m_Name: XRPlayer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7942002210973373747
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002210973373748}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7942002212320107156}
  - {fileID: 6707951067419483823}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7412725206770988215
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002210973373748}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b661ef26e3b3e0c4ba5d9ecd8097bf6b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  defaultDevice: Oculus
  rightOffsets:
  - {fileID: 7942002210253447911}
  - {fileID: 7942002210876816003}
  leftOffsets:
  - {fileID: 7942002210360757120}
  - {fileID: 7942002210478575250}
  devices:
  - deviceNames:
    - Oculus
    position: {x: 0.04, y: -0.04, z: -0.023}
    rotation: {x: 16, y: 8, z: 8}
  - deviceNames:
    - Windows MR
    position: {x: 0.003, y: -0.005, z: -0.078}
    rotation: {x: 36, y: -12, z: 2}
  - deviceNames:
    - Vive
    position: {x: 0.035, y: -0.022, z: -0.11}
    rotation: {x: 50, y: -2, z: -11}
--- !u!114 &2143022915439084234
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002210973373748}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b661ef26e3b3e0c4ba5d9ecd8097bf6b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  defaultDevice: Oculus
  rightOffsets:
  - {fileID: 7942002210253447911}
  - {fileID: 7942002210876816003}
  leftOffsets:
  - {fileID: 7942002210360757120}
  - {fileID: 7942002210478575250}
  devices:
  - deviceNames:
    - Oculus
    position: {x: 0.05, y: 0, z: 0}
    rotation: {x: 85, y: 0, z: 0}
  - deviceNames:
    - Vive
    - Index
    position: {x: 0, y: 0, z: 0}
    rotation: {x: 72, y: -14, z: -15}
--- !u!1 &7942002211305997993
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7942002211305997992}
  - component: {fileID: 7942002211305997991}
  m_Layer: 0
  m_Name: Controller (left)
  m_TagString: GameController
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7942002211305997992
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211305997993}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.70710677}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7942002210360757120}
  - {fileID: 7078973585685985395}
  - {fileID: 7942002210478575250}
  m_Father: {fileID: 7942002212320107156}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 0}
--- !u!114 &7942002211305997991
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211305997993}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a2a9c34df4095f47b9ca8f975175f5b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Device: 1
  m_PoseSource: 4
  m_PoseProviderComponent: {fileID: 0}
  m_TrackingType: 0
  m_UpdateType: 0
  m_UseRelativeTransform: 0
--- !u!1 &7942002211509775672
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7942002211509775671}
  - component: {fileID: 7942002211509775667}
  - component: {fileID: 7942002211509775668}
  - component: {fileID: 7942002211509775669}
  - component: {fileID: 7942002211509775670}
  m_Layer: 0
  m_Name: FingerBending
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7942002211509775671
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211509775672}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9136378276058449578}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7942002211509775667
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211509775672}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 855e1c3bb96607b46842d2505034e037, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  controller: {fileID: 7942002211609493189}
  axis: 1
  bendOffsets:
  - -0.1
  - -0.1
  - 1
  - 1
  - 1
--- !u!114 &7942002211509775668
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211509775672}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 855e1c3bb96607b46842d2505034e037, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  controller: {fileID: 7942002211609493189}
  axis: 0
  bendOffsets:
  - 0
  - 1.1
  - 0
  - 0
  - 0
--- !u!114 &7942002211509775669
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211509775672}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 782cb9720318e0c4ab7353bfc2181ad6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  controller: {fileID: 7942002211609493189}
  button: 2
  bendOffsets:
  - 0.5
  - 0
  - 0
  - 0
  - 0
--- !u!114 &7942002211509775670
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211509775672}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 782cb9720318e0c4ab7353bfc2181ad6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  controller: {fileID: 7942002211609493189}
  button: 9
  bendOffsets:
  - 0.2
  - 0
  - 0
  - 0
  - 0
--- !u!1 &7942002211881670213
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7942002211881670212}
  - component: {fileID: 7942002211881670239}
  - component: {fileID: 7942002211881670209}
  - component: {fileID: 7942002211881670210}
  - component: {fileID: 7942002211881670211}
  m_Layer: 0
  m_Name: Camera (head)
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7942002211881670212
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211881670213}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0.134, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7942002212320107156}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!20 &7942002211881670239
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211881670213}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0.019607844}
  m_projectionMatrixMode: 1
  m_GateFitMode: 2
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.01
  far clip plane: 1000
  field of view: 90
  orthographic: 0
  orthographic size: 5
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 0
  m_AllowMSAA: 1
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!81 &7942002211881670209
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211881670213}
  m_Enabled: 1
--- !u!114 &7942002211881670210
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211881670213}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a2a9c34df4095f47b9ca8f975175f5b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Device: 0
  m_PoseSource: 2
  m_PoseProviderComponent: {fileID: 0}
  m_TrackingType: 0
  m_UpdateType: 0
  m_UseRelativeTransform: 0
--- !u!114 &7942002211881670211
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211881670213}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6d6e19972d8984547b7008d21841fe57, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  mode: 2
--- !u!1 &7942002211973936968
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7942002211973936967}
  - component: {fileID: 7942002211973936966}
  m_Layer: 0
  m_Name: Controller (right)
  m_TagString: GameController
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7942002211973936967
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211973936968}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: 0.7071068, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7942002210253447911}
  - {fileID: 4941567059615228357}
  - {fileID: 7942002210876816003}
  m_Father: {fileID: 7942002212320107156}
  m_LocalEulerAnglesHint: {x: 0, y: 90, z: 0}
--- !u!114 &7942002211973936966
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002211973936968}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a2a9c34df4095f47b9ca8f975175f5b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Device: 1
  m_PoseSource: 5
  m_PoseProviderComponent: {fileID: 0}
  m_TrackingType: 0
  m_UpdateType: 0
  m_UseRelativeTransform: 0
--- !u!1 &7942002212186492392
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7942002212186492391}
  - component: {fileID: 7942002212186492387}
  - component: {fileID: 7942002212186492388}
  - component: {fileID: 7942002212186492389}
  - component: {fileID: 7942002212186492390}
  m_Layer: 0
  m_Name: FingerBending
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7942002212186492391
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002212186492392}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9136378277261632249}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7942002212186492387
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002212186492392}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 855e1c3bb96607b46842d2505034e037, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  controller: {fileID: 7942002210455460502}
  axis: 1
  bendOffsets:
  - 0
  - 0
  - 1
  - 1
  - 1
--- !u!114 &7942002212186492388
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002212186492392}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 855e1c3bb96607b46842d2505034e037, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  controller: {fileID: 7942002210455460502}
  axis: 0
  bendOffsets:
  - 0
  - 1
  - 0
  - 0
  - 0
--- !u!114 &7942002212186492389
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002212186492392}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 782cb9720318e0c4ab7353bfc2181ad6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  controller: {fileID: 7942002210455460502}
  button: 2
  bendOffsets:
  - 0.5
  - 0
  - 0
  - 0
  - 0
--- !u!114 &7942002212186492390
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002212186492392}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 782cb9720318e0c4ab7353bfc2181ad6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  controller: {fileID: 7942002210455460502}
  button: 9
  bendOffsets:
  - 0.2
  - 0
  - 0
  - 0
  - 0
--- !u!1 &7942002212320107157
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7942002212320107156}
  m_Layer: 0
  m_Name: TrackerOffsets
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7942002212320107156
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002212320107157}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7942002211973936967}
  - {fileID: 7942002211305997992}
  - {fileID: 7942002211881670212}
  - {fileID: 6162809413267167199}
  - {fileID: 5823117661701342531}
  - {fileID: 5823117661886934677}
  m_Father: {fileID: 7942002210973373747}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &1497420959589980772
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 9136378276058449578}
    m_Modifications:
    - target: {fileID: 8858651398523657305, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8858651399898916291, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400097578398, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220161, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: hand
      value: 
      objectReference: {fileID: 1262313376109179214}
    - target: {fileID: 8858651400563220161, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: head
      value: 
      objectReference: {fileID: 7942002211881670239}
    - target: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_RootOrder
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220163, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_Name
      value: WristEvent
      objectReference: {fileID: 0}
    - target: {fileID: 8858651400563220163, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 8858651399861384401, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8858651399548370189}
  m_SourcePrefab: {fileID: 100100000, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
--- !u!1 &7942002210233446069 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8858651399861384401, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
  m_PrefabInstance: {fileID: 1497420959589980772}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &8858651399548370189
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7942002210233446069}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 132145a7047493646ad4aaa119028c20, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  includeChildren: 1
--- !u!4 &7942002210865644710 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 8858651400563220162, guid: 7183ed14a173e1c4aac8a77d79558326, type: 3}
  m_PrefabInstance: {fileID: 1497420959589980772}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2478419769345606506
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 9136378276058449578}
    m_Modifications:
    - target: {fileID: 1386156037009821333, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: primaryHand
      value: 
      objectReference: {fileID: 1262313376109179214}
    - target: {fileID: 1386156037009821333, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: secondaryHand
      value: 
      objectReference: {fileID: 1262313374629172509}
    - target: {fileID: 3506405257487811696, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3506405257487811696, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.04
      objectReference: {fileID: 0}
    - target: {fileID: 3506405257487811696, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3506405257487811696, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.99691737
      objectReference: {fileID: 0}
    - target: {fileID: 3506405257487811696, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.078459024
      objectReference: {fileID: 0}
    - target: {fileID: 3506405257487811696, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3506405257487811696, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3506405257487811696, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -9
      objectReference: {fileID: 0}
    - target: {fileID: 3506405257487811696, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3506405257487811696, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5499657418473704481, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: link
      value: 
      objectReference: {fileID: 7942002211609493189}
    - target: {fileID: 6956525015089675108, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
      propertyPath: m_Name
      value: GrabPointer
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
--- !u!4 &1354559460919019290 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3506405257487811696, guid: 79ea27ae647d42447b0f4780552660ef, type: 3}
  m_PrefabInstance: {fileID: 2478419769345606506}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2850394227092082480
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7942002210973373747}
    m_Modifications:
    - target: {fileID: 8834320319295017360, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: handLeft
      value: 
      objectReference: {fileID: 1262313374629172509}
    - target: {fileID: 8834320319295017360, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: handRight
      value: 
      objectReference: {fileID: 1262313376109179214}
    - target: {fileID: 8834320319295017360, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: headCamera
      value: 
      objectReference: {fileID: 7942002211881670239}
    - target: {fileID: 8834320319295017360, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: crouchHeight
      value: 0.2
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017360, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: forwardFollow
      value: 
      objectReference: {fileID: 7942002211881670212}
    - target: {fileID: 8834320319295017360, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: trackingContainer
      value: 
      objectReference: {fileID: 7942002212320107156}
    - target: {fileID: 8834320319295017362, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_Name
      value: AutoHandPlayer
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017374, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_Material
      value: 
      objectReference: {fileID: 13400000, guid: f9b2e3e858956064fb1d05dd47cdaca7, type: 2}
    - target: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_RootOrder
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 8834320319295017362, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
      insertIndex: -1
      addedObject: {fileID: 8086603477119862099}
  m_SourcePrefab: {fileID: 100100000, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
--- !u!114 &6707951067419483808 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 8834320319295017360, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
  m_PrefabInstance: {fileID: 2850394227092082480}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6707951067419483810}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d8a35128610d1b34892726ec96e587d0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &6707951067419483810 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8834320319295017362, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
  m_PrefabInstance: {fileID: 2850394227092082480}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &8086603477119862099
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6707951067419483810}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 09b6737f8be736a4ea7bb40614525174, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  moveController: {fileID: 7942002210455460502}
  turnController: {fileID: 7942002211609493189}
  player: {fileID: 6707951067419483808}
  moveAxis: 0
  turnAxis: 0
--- !u!4 &6707951067419483823 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 8834320319295017375, guid: fdc3ba53f4e81a040b0471bbbafbb10a, type: 3}
  m_PrefabInstance: {fileID: 2850394227092082480}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &2932314138472044742
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 9136378276058449578}
    m_Modifications:
    - target: {fileID: 2358261902754467598, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2358261902754467598, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.04
      objectReference: {fileID: 0}
    - target: {fileID: 2358261902754467598, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2358261902754467598, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.99691737
      objectReference: {fileID: 0}
    - target: {fileID: 2358261902754467598, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.078459024
      objectReference: {fileID: 0}
    - target: {fileID: 2358261902754467598, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2358261902754467598, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2358261902754467598, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -9
      objectReference: {fileID: 0}
    - target: {fileID: 2358261902754467598, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2358261902754467598, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2358261902754467599, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: m_Name
      value: UIPointer
      objectReference: {fileID: 0}
    - target: {fileID: 5081754464275087181, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
      propertyPath: link
      value: 
      objectReference: {fileID: 7942002211609493189}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
--- !u!4 &579722560795514824 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 2358261902754467598, guid: 432014264484f7b44afb25b027e8edb1, type: 3}
  m_PrefabInstance: {fileID: 2932314138472044742}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &3583256715182421967
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 9136378276058449578}
    m_Modifications:
    - target: {fileID: 1481182634574515286, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 9dfc825aed78fcd4ba02077103263b40, type: 2}
    - target: {fileID: 2607671808589667222, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 9dfc825aed78fcd4ba02077103263b40, type: 2}
    - target: {fileID: 3686317504681481292, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: m_Name
      value: TeleporterPointer
      objectReference: {fileID: 0}
    - target: {fileID: 8344767237475176937, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8344767237475176937, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.04
      objectReference: {fileID: 0}
    - target: {fileID: 8344767237475176937, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8344767237475176937, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.99691737
      objectReference: {fileID: 0}
    - target: {fileID: 8344767237475176937, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0.078459024
      objectReference: {fileID: 0}
    - target: {fileID: 8344767237475176937, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8344767237475176937, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8344767237475176937, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: -9
      objectReference: {fileID: 0}
    - target: {fileID: 8344767237475176937, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8344767237475176937, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
--- !u!4 &4788691345919026726 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 8344767237475176937, guid: 89dffe45961978543a2f1c5389c77fcf, type: 3}
  m_PrefabInstance: {fileID: 3583256715182421967}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &7942002210455460506
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6162809413267167199}
    m_Modifications:
    - target: {fileID: 165222653978168724, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222653978168726, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: buffer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222653978168726, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: strict
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 165222653978168726, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222653978168726, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: ignoreMaxHandDistance
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874929, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: MaxDistance
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: MinDistance
      value: 0.25
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.size
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].time
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].time
      value: 0.03714325
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].time
      value: 0.062857665
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].time
      value: 0.12571533
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].time
      value: 0.21714416
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].time
      value: 0.32
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].time
      value: 0.64
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].value
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].value
      value: 0.266922
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].value
      value: 0.13346863
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].value
      value: 0.0625
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].value
      value: 0.03125
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].value
      value: 0.015625
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].value
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].inSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].inSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].inSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].inSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].inSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].inSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].outSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].outSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].outSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].outSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].outSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].outSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outWeight
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 287771044707264305, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 492640576310161498, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalScale.x
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1272907027502396831, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: MaxDistance
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: MinDistance
      value: 0.25
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.size
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].time
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].time
      value: 0.03714325
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].time
      value: 0.062857665
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].time
      value: 0.12571533
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].time
      value: 0.21714416
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].time
      value: 0.32
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].time
      value: 0.64
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].value
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].value
      value: 0.266922
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].value
      value: 0.13346863
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].value
      value: 0.0625
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].value
      value: 0.03125
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].value
      value: 0.015625
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].value
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].inSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].inSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].inSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].inSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].inSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].inSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].outSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].outSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].outSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].outSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].outSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].outSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outWeight
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1813792077443834387, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1982879836239456625, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 2218293816570526395, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2361106867534118458, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2440931180534758656, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2901741127085864212, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: fingerSmoothSpeed
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 3445901078671437574, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3481152275737404598, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: fingerSmoothSpeed
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 4149695533559809519, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: guardTime
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4427432614651021071, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4730105175238802076, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4832715926935601055, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4943803637886291714, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4970480131156098315, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5237706236337434649, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5457610785231033771, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: fingerSmoothSpeed
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 5460294669156321847, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: fingerSmoothSpeed
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 5553579449110825140, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: fingerSmoothSpeed
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 5587752548456514330, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7330515273776888223, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7348704315564051146, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7519029198429550374, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7829225825335241985, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7868280509258151636, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8149646956712602908, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8266913655063000168, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8617030452219050936, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8617030452219050936, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_IsTrigger
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8685472768745978638, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Name
      value: RobotHand (L)
      objectReference: {fileID: 0}
    - target: {fileID: 8783913793033214316, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8846417437485729274, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8851530794163531841, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9199984175474563448, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: left
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: follow
      value: 
      objectReference: {fileID: 7078973585685985395}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: followPosition
      value: 
      objectReference: {fileID: 7942002210360757120}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: followRotation
      value: 
      objectReference: {fileID: 7942002210360757120}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: followPositionOffset.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: followPositionOffset.y
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7942002212186492391}
    - targetCorrespondingSourceObject: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      insertIndex: -1
      addedObject: {fileID: 692547948992490462}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 8685472768745978638, guid: 386242486c5972145be957989b61c7df, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7942002210455460502}
  m_SourcePrefab: {fileID: 100100000, guid: 386242486c5972145be957989b61c7df, type: 3}
--- !u!4 &500649281596835877 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7549441660140311231, guid: 386242486c5972145be957989b61c7df, type: 3}
  m_PrefabInstance: {fileID: 7942002210455460506}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1238642881241681853 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 9153324134324409639, guid: 386242486c5972145be957989b61c7df, type: 3}
  m_PrefabInstance: {fileID: 7942002210455460506}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1262313374629172509 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
  m_PrefabInstance: {fileID: 7942002210455460506}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1639125106728771988}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9413d460e98076241a1f46c91201217d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1639125106728771988 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8685472768745978638, guid: 386242486c5972145be957989b61c7df, type: 3}
  m_PrefabInstance: {fileID: 7942002210455460506}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &7942002210455460502
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1639125106728771988}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 245bee9b113e7144cb3549484bae2a0c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 1262313374629172509}
  grabButton: 0
  grabAxis: 1
  squeezeAxis: 0
  squeezeButton: 4
--- !u!4 &9136378277261632249 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
  m_PrefabInstance: {fileID: 7942002210455460506}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &7942002210590329957
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7942002212320107156}
    m_Modifications:
    - target: {fileID: 2120794439, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: grabAxis
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2120794439, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: grabButton
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2120794439, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: squeezeButton
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233266, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_Name
      value: Classic Hand (XR)(L)
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233266, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233267, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: follow
      value: 
      objectReference: {fileID: 7942002210478575250}
    - target: {fileID: 4537506675607233267, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: enableIK
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233267, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: showAdvanced
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233267, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: enableMovement
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233267, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: followPosition
      value: 
      objectReference: {fileID: 7942002210360757120}
    - target: {fileID: 4537506675607233267, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: followRotation
      value: 
      objectReference: {fileID: 7942002210360757120}
    - target: {fileID: 4537506675607233267, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: usingHighlight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: MaxDistance
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: MinDistance
      value: 0.25
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.size
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].time
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].time
      value: 0.03714325
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].time
      value: 0.062857665
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].time
      value: 0.12571533
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].time
      value: 0.21714416
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].time
      value: 0.32
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].time
      value: 0.64
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].value
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].value
      value: 0.266922
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].value
      value: 0.13346863
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].value
      value: 0.0625
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].value
      value: 0.03125
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].value
      value: 0.015625
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].value
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].inSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].inSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].inSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].inSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].inSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].inSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].outSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].outSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].outSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].outSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].outSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].outSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outWeight
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outWeight
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
--- !u!4 &5823117661886934677 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4537506675607233264, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
  m_PrefabInstance: {fileID: 7942002210590329957}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &5823117661886934678 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 4537506675607233267, guid: 393566584cd7aae4e92c16be3dd85bae, type: 3}
  m_PrefabInstance: {fileID: 7942002210590329957}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9413d460e98076241a1f46c91201217d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &7942002211115978119
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 5823117661701342531}
    m_Modifications:
    - target: {fileID: 6876478601354298033, guid: 035aaa70c735c164bbf1a8760fad9f76, type: 3}
      propertyPath: primaryHand
      value: 
      objectReference: {fileID: 5823117661701342528}
    - target: {fileID: 6876478601354298033, guid: 035aaa70c735c164bbf1a8760fad9f76, type: 3}
      propertyPath: secondaryHand
      value: 
      objectReference: {fileID: 5823117661886934678}
    - target: {fileID: 9006060476868069460, guid: 035aaa70c735c164bbf1a8760fad9f76, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 9006060476868069460, guid: 035aaa70c735c164bbf1a8760fad9f76, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9006060476868069460, guid: 035aaa70c735c164bbf1a8760fad9f76, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9006060476868069460, guid: 035aaa70c735c164bbf1a8760fad9f76, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 3231403016657467200, guid: 035aaa70c735c164bbf1a8760fad9f76, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7942002211115978116}
  m_SourcePrefab: {fileID: 100100000, guid: 035aaa70c735c164bbf1a8760fad9f76, type: 3}
--- !u!4 &1354559460991256019 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 9006060476868069460, guid: 035aaa70c735c164bbf1a8760fad9f76, type: 3}
  m_PrefabInstance: {fileID: 7942002211115978119}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &3556014343317557046 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 6876478601354298033, guid: 035aaa70c735c164bbf1a8760fad9f76, type: 3}
  m_PrefabInstance: {fileID: 7942002211115978119}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4823239764860811975}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1827e9caa3a42174ea1ead7cf86e7b2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &4823239764860811975 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 3231403016657467200, guid: 035aaa70c735c164bbf1a8760fad9f76, type: 3}
  m_PrefabInstance: {fileID: 7942002211115978119}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &7942002211115978116
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4823239764860811975}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347efddb19627c941bbe201c025bc04e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  pointGrab: {fileID: 3556014343317557046}
  link: {fileID: 0}
  pointInput: 0
  selectInput: 4
--- !u!1001 &7942002211609493193
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 6162809413267167199}
    m_Modifications:
    - target: {fileID: 165222653978168724, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222653978168726, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: buffer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222653978168726, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: strict
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 165222653978168726, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222653978168726, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: ignoreMaxHandDistance
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874929, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: MaxDistance
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: MinDistance
      value: 0.25
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.size
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].time
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].time
      value: 0.03714325
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].time
      value: 0.062857665
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].time
      value: 0.12571533
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].time
      value: 0.21714416
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].time
      value: 0.32
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].time
      value: 0.64
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].value
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].value
      value: 0.266922
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].value
      value: 0.13346863
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].value
      value: 0.0625
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].value
      value: 0.03125
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].value
      value: 0.015625
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].value
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].inSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].inSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].inSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].inSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].inSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].inSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].outSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].outSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].outSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].outSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].outSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].outSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outWeight
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 165222654096874931, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 287771044707264305, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 492640576310161498, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalScale.x
      value: -0.8
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalScale.y
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalScale.z
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1272907027502396831, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: MaxDistance
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: MinDistance
      value: 0.25
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.size
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].time
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].time
      value: 0.03714325
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].time
      value: 0.062857665
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].time
      value: 0.12571533
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].time
      value: 0.21714416
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].time
      value: 0.32
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].time
      value: 0.64
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].value
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].value
      value: 0.266922
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].value
      value: 0.13346863
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].value
      value: 0.0625
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].value
      value: 0.03125
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].value
      value: 0.015625
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].value
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].inSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].inSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].inSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].inSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].inSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].inSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].outSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].outSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].outSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].outSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].outSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].outSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outWeight
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1305530554600226291, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1813792077443834387, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 1982879836239456625, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_RootOrder
      value: 7
      objectReference: {fileID: 0}
    - target: {fileID: 2218293816570526395, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2361106867534118458, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2440931180534758656, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2901741127085864212, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: fingerSmoothSpeed
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 3445901078671437574, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3481152275737404598, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: fingerSmoothSpeed
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 4149695533559809519, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: guardTime
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4427432614651021071, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4730105175238802076, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4832715926935601055, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4943803637886291714, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4970480131156098315, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5237706236337434649, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5457610785231033771, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: fingerSmoothSpeed
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 5460294669156321847, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: fingerSmoothSpeed
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 5553579449110825140, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: fingerSmoothSpeed
      value: 0.8
      objectReference: {fileID: 0}
    - target: {fileID: 5587752548456514330, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7330515273776888223, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7348704315564051146, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7519029198429550374, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7829225825335241985, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7868280509258151636, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8149646956712602908, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8266913655063000168, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8617030452219050936, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Enabled
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8685472768745978638, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Name
      value: RobotHand (R)
      objectReference: {fileID: 0}
    - target: {fileID: 8783913793033214316, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8846417437485729274, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8851530794163531841, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9199984175474563448, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: m_Layer
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: follow
      value: 
      objectReference: {fileID: 4941567059615228357}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: showGizmos
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: maxVelocity
      value: 10
      objectReference: {fileID: 0}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: followPosition
      value: 
      objectReference: {fileID: 7942002210253447911}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: followRotation
      value: 
      objectReference: {fileID: 7942002210253447911}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: followPositionOffset.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
      propertyPath: followPositionOffset.y
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7942002211509775671}
    - targetCorrespondingSourceObject: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1354559460919019290}
    - targetCorrespondingSourceObject: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4788691345919026726}
    - targetCorrespondingSourceObject: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      insertIndex: -1
      addedObject: {fileID: 579722560795514824}
    - targetCorrespondingSourceObject: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7942002210865644710}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 8685472768745978638, guid: 386242486c5972145be957989b61c7df, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7942002211609493189}
  m_SourcePrefab: {fileID: 100100000, guid: 386242486c5972145be957989b61c7df, type: 3}
--- !u!4 &500649280100068470 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7549441660140311231, guid: 386242486c5972145be957989b61c7df, type: 3}
  m_PrefabInstance: {fileID: 7942002211609493193}
  m_PrefabAsset: {fileID: 0}
--- !u!4 &1238642880238791662 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 9153324134324409639, guid: 386242486c5972145be957989b61c7df, type: 3}
  m_PrefabInstance: {fileID: 7942002211609493193}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1262313376109179214 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 9201711370266972039, guid: 386242486c5972145be957989b61c7df, type: 3}
  m_PrefabInstance: {fileID: 7942002211609493193}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1639125108242449863}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9413d460e98076241a1f46c91201217d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1639125108242449863 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8685472768745978638, guid: 386242486c5972145be957989b61c7df, type: 3}
  m_PrefabInstance: {fileID: 7942002211609493193}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &7942002211609493189
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1639125108242449863}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 245bee9b113e7144cb3549484bae2a0c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 1262313376109179214}
  grabButton: 0
  grabAxis: 1
  squeezeAxis: 0
  squeezeButton: 4
--- !u!4 &9136378276058449578 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1224213067510249571, guid: 386242486c5972145be957989b61c7df, type: 3}
  m_PrefabInstance: {fileID: 7942002211609493193}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &7942002211847628723
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 7942002212320107156}
    m_Modifications:
    - target: {fileID: 2120794439, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: grabAxis
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 2120794439, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: grabButton
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 2120794439, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: squeezeButton
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233266, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_Name
      value: Classic Hand (XR)(R)
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233266, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233267, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: follow
      value: 
      objectReference: {fileID: 7942002210876816003}
    - target: {fileID: 4537506675607233267, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: enableIK
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233267, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: showAdvanced
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233267, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: enableMovement
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4537506675607233267, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: followPosition
      value: 
      objectReference: {fileID: 7942002210253447911}
    - target: {fileID: 4537506675607233267, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: followRotation
      value: 
      objectReference: {fileID: 7942002210253447911}
    - target: {fileID: 4537506675607233267, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: usingHighlight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: MaxDistance
      value: 25
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: MinDistance
      value: 0.25
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffMode
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.size
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].time
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].time
      value: 0.03714325
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].time
      value: 0.062857665
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].time
      value: 0.12571533
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].time
      value: 0.21714416
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].time
      value: 0.32
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].time
      value: 0.64
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].time
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].value
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].value
      value: 0.266922
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].value
      value: 0.13346863
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].value
      value: 0.0625
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].value
      value: 0.03125
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].value
      value: 0.015625
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].value
      value: 0.01
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: panLevelCustomCurve.m_Curve.Array.data[0].value
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].inSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].inSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].inSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].inSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].inSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].inSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outSlope
      value: -10.442891
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].inWeight
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outSlope
      value: -25.00996
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[2].outSlope
      value: -6.25249
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[3].outSlope
      value: -1.5631225
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[4].outSlope
      value: -0.39078063
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[5].outSlope
      value: -0.09769516
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[6].outSlope
      value: -0.02442379
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[7].outSlope
      value: -0.010003988
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[0].outWeight
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 9100094654249455996, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      propertyPath: rolloffCustomCurve.m_Curve.Array.data[1].outWeight
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      insertIndex: -1
      addedObject: {fileID: 1354559460991256019}
    - targetCorrespondingSourceObject: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      insertIndex: -1
      addedObject: {fileID: 4788691345173151357}
    - targetCorrespondingSourceObject: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
      insertIndex: -1
      addedObject: {fileID: 579722559053420622}
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 56ead9dc16628174989544076e2522d7, type: 3}
--- !u!114 &5823117661701342528 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 4537506675607233267, guid: 56ead9dc16628174989544076e2522d7, type: 3}
  m_PrefabInstance: {fileID: 7942002211847628723}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 9413d460e98076241a1f46c91201217d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &5823117661701342531 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4537506675607233264, guid: 56ead9dc16628174989544076e2522d7, type: 3}
  m_PrefabInstance: {fileID: 7942002211847628723}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &7942002211929958411
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 5823117661701342531}
    m_Modifications:
    - target: {fileID: 7366831769009221701, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
      propertyPath: m_RootOrder
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 7366831769009221701, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7366831769009221701, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7366831769009221701, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 7366831769009221700, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7942002211929958408}
  m_SourcePrefab: {fileID: 100100000, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
--- !u!4 &579722559053420622 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7366831769009221701, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
  m_PrefabInstance: {fileID: 7942002211929958411}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &579722559053420623 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7366831769009221700, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
  m_PrefabInstance: {fileID: 7942002211929958411}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &7942002211929958408
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 579722559053420623}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7fbab967b03b78f4093d38ef2637575f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  link: {fileID: 0}
  button: 4
  Pressed:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 2381703693156818688}
        m_TargetAssemblyTypeName: HandCanvasPointer, Assembly-CSharp
        m_MethodName: Press
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  Released:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 2381703693156818688}
        m_TargetAssemblyTypeName: HandCanvasPointer, Assembly-CSharp
        m_MethodName: Release
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &2381703693156818688 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5708921713935955723, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
  m_PrefabInstance: {fileID: 7942002211929958411}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 579722559053420623}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 596e2cdbad5de264abd332ce27c9e3e9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &7942002212340265980
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 5823117661701342531}
    m_Modifications:
    - target: {fileID: 3189517785069784449, guid: 550b13c7ab5987948bb95292d813fbde, type: 3}
      propertyPath: m_RootOrder
      value: 5
      objectReference: {fileID: 0}
    - target: {fileID: 3189517785069784449, guid: 550b13c7ab5987948bb95292d813fbde, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3189517785069784449, guid: 550b13c7ab5987948bb95292d813fbde, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3189517785069784449, guid: 550b13c7ab5987948bb95292d813fbde, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5405401283067383870, guid: 550b13c7ab5987948bb95292d813fbde, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 9dfc825aed78fcd4ba02077103263b40, type: 2}
    - target: {fileID: 8916475941093312510, guid: 550b13c7ab5987948bb95292d813fbde, type: 3}
      propertyPath: 'm_Materials.Array.data[0]'
      value: 
      objectReference: {fileID: 2100000, guid: 9dfc825aed78fcd4ba02077103263b40, type: 2}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 7828819849286420516, guid: 550b13c7ab5987948bb95292d813fbde, type: 3}
      insertIndex: -1
      addedObject: {fileID: 7942002212340265977}
  m_SourcePrefab: {fileID: 100100000, guid: 550b13c7ab5987948bb95292d813fbde, type: 3}
--- !u!1 &185253151234259928 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7828819849286420516, guid: 550b13c7ab5987948bb95292d813fbde, type: 3}
  m_PrefabInstance: {fileID: 7942002212340265980}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &7942002212340265977
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 185253151234259928}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c3bafe5313773b549b22ed738a6223b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  hand: {fileID: 3241196395405287035}
  role: 5
  button: 5
--- !u!114 &3241196395405287035 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 4813400343104777607, guid: 550b13c7ab5987948bb95292d813fbde, type: 3}
  m_PrefabInstance: {fileID: 7942002212340265980}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 185253151234259928}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 02e7dadae3689a64bb9a24e46177ce9d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!4 &4788691345173151357 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3189517785069784449, guid: 550b13c7ab5987948bb95292d813fbde, type: 3}
  m_PrefabInstance: {fileID: 7942002212340265980}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &8043519948301278107
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 9136378277261632249}
    m_Modifications:
    - target: {fileID: 7366831769009221701, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
      propertyPath: m_RootOrder
      value: 4
      objectReference: {fileID: 0}
    - target: {fileID: 7366831769009221701, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 7366831769009221701, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7366831769009221701, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 7366831769009221700, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
      insertIndex: -1
      addedObject: {fileID: 3144298149122578242}
  m_SourcePrefab: {fileID: 100100000, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
--- !u!4 &692547948992490462 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 7366831769009221701, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
  m_PrefabInstance: {fileID: 8043519948301278107}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &692547948992490463 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7366831769009221700, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
  m_PrefabInstance: {fileID: 8043519948301278107}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &3144298149122578242
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 692547948992490463}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7fbab967b03b78f4093d38ef2637575f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  link: {fileID: 7942002210455460502}
  button: 4
  Pressed:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 2349322973060443280}
        m_TargetAssemblyTypeName: HandCanvasPointer, Assembly-CSharp
        m_MethodName: Press
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  Released:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 2349322973060443280}
        m_TargetAssemblyTypeName: HandCanvasPointer, Assembly-CSharp
        m_MethodName: Release
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &2349322973060443280 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5708921713935955723, guid: c86a0d692b08bbb4f868a301c507084c, type: 3}
  m_PrefabInstance: {fileID: 8043519948301278107}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 692547948992490463}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 596e2cdbad5de264abd332ce27c9e3e9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
