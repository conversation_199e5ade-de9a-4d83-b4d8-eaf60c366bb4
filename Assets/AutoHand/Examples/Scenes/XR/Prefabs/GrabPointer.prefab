%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &6956525015089675108
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3506405257487811696}
  - component: {fileID: 1386156037009821333}
  - component: {fileID: 1380428005975433531}
  - component: {fileID: 5499657418473704481}
  - component: {fileID: 2805819825509059209}
  m_Layer: 0
  m_Name: GrabPointer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3506405257487811696
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6956525015089675108}
  serializedVersion: 2
  m_LocalRotation: {x: -0.078459024, y: 0, z: 0, w: 0.99691737}
  m_LocalPosition: {x: 0, y: 0.04, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: -9, y: 0, z: 0}
--- !u!114 &1386156037009821333
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6956525015089675108}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1827e9caa3a42174ea1ead7cf86e7b2f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  primaryHand: {fileID: 0}
  secondaryHand: {fileID: 0}
  forwardPointer: {fileID: 3506405257487811696}
  forwardSmoothingSpeed: 5
  line: {fileID: 1380428005975433531}
  maxRange: 20
  layers:
    serializedVersion: 2
    m_Bits: 0
  defaultTargetedMaterial: {fileID: 2100000, guid: 5f2dd013d4c137746ad1d43ef197affe, type: 2}
  defaultSelectedMaterial: {fileID: 2100000, guid: 168b16969c06412429c75c4d013165eb, type: 2}
  useInstantPull: 1
  useFlickPull: 0
  flickThreshold: 10
  pullGrabDistance: 0.1
  instantGrabAssist: 1
  catchAssistRadius: 0.1
  showEvents: 1
  OnPull:
    m_PersistentCalls:
      m_Calls: []
  StartPoint:
    m_PersistentCalls:
      m_Calls: []
  StopPoint:
    m_PersistentCalls:
      m_Calls: []
  StartTarget:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1380428005975433531}
        m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
        m_MethodName: set_material
        m_Mode: 2
        m_Arguments:
          m_ObjectArgument: {fileID: 2100000, guid: a23dd9a890df9154c85afc20164c615e, type: 2}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  StopTarget:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1380428005975433531}
        m_TargetAssemblyTypeName: UnityEngine.Renderer, UnityEngine
        m_MethodName: set_material
        m_Mode: 2
        m_Arguments:
          m_ObjectArgument: {fileID: 2100000, guid: a4a5a51db1695524d8de609e7d18d15a, type: 2}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Material, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  StartSelect:
    m_PersistentCalls:
      m_Calls: []
  StopSelect:
    m_PersistentCalls:
      m_Calls: []
--- !u!120 &1380428005975433531
LineRenderer:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6956525015089675108}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 0
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 0
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a4a5a51db1695524d8de609e7d18d15a, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_Positions: []
  m_Parameters:
    serializedVersion: 3
    widthMultiplier: 0.008
    widthCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0.06629834
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    colorGradient:
      serializedVersion: 2
      key0: {r: 1, g: 1, b: 1, a: 1}
      key1: {r: 1, g: 1, b: 1, a: 1}
      key2: {r: 0, g: 0, b: 0, a: 1}
      key3: {r: 0, g: 0, b: 0, a: 0}
      key4: {r: 0, g: 0, b: 0, a: 0}
      key5: {r: 0, g: 0, b: 0, a: 0}
      key6: {r: 0, g: 0, b: 0, a: 0}
      key7: {r: 0, g: 0, b: 0, a: 0}
      ctime0: 0
      ctime1: 65535
      ctime2: 0
      ctime3: 0
      ctime4: 0
      ctime5: 0
      ctime6: 0
      ctime7: 0
      atime0: 0
      atime1: 65535
      atime2: 8288
      atime3: 0
      atime4: 0
      atime5: 0
      atime6: 0
      atime7: 0
      m_Mode: 0
      m_ColorSpace: -1
      m_NumColorKeys: 2
      m_NumAlphaKeys: 2
    numCornerVertices: 0
    numCapVertices: 0
    alignment: 0
    textureMode: 0
    textureScale: {x: 1, y: 1}
    shadowBias: 0.5
    generateLightingData: 0
  m_MaskInteraction: 0
  m_UseWorldSpace: 1
  m_Loop: 0
  m_ApplyActiveColorSpace: 0
--- !u!114 &5499657418473704481
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6956525015089675108}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 347efddb19627c941bbe201c025bc04e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  pointGrab: {fileID: 1386156037009821333}
  link: {fileID: 0}
  pointInput: 0
  selectInput: 4
--- !u!114 &2805819825509059209
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6956525015089675108}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0d0cb31f61727554fbdb16f3d7cb8aa8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  lineRenderer: {fileID: 1380428005975433531}
  lineReticle: {fileID: 0}
  activateTime: 0.5
  useColorCurve: 0
  colorCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 1
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 1
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  widthCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 2
      outSlope: 2
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
