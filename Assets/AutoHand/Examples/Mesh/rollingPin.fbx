; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2020
		Month: 3
		Day: 4
		Hour: 23
		Minute: 19
		Second: 27
		Millisecond: 220
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "rollingPin.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "rollingPin.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5352719306521107567, "Model::rollingPin", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4774357076313614911, "Geometry::", "Mesh" {
		Vertices: *756 {
			a: 3.568278,-0.2895115,0.1671496,3.568278,-0.2895115,-0.1671496,3.068278,-0.2244275,0.1295733,3.068278,-0.2244275,-0.1295733,3.068278,0.2244276,-0.1295733,3.068278,0.2244276,0.1295733,2.468278,0.3400418,-0.1963232,2.468278,0.3400418,0.1963232,4.068278,5.516089E-08,-0.2591466,4.068278,-0.2244275,-0.1295733,4.068278,0.2244276,-0.1295733,4.068278,0.2244276,0.1295733,4.068278,-0.2244275,0.1295733,4.068278,6.60831E-08,0.2591466,3.568278,-0.2895115,0.1671496,3.568278,6.021625E-08,0.3342991,4.068278,-0.2244275,0.1295733,4.068278,6.60831E-08,0.2591466,3.068278,5.118197E-08,0.2591466,3.068278,0.2244276,0.1295733,3.568278,6.021625E-08,0.3342991,3.568278,0.2895116,0.1671496,2.268278,-0.3400417,0.1963232,2.268278,4.207433E-08,0.3926464,2.468278,-0.3400417,0.1963232,2.468278,4.505455E-08,0.3926464,4.068278,0.2244276,-0.1295733,4.068278,0.2244276,0.1295733,3.568278,0.2895116,-0.1671496,3.568278,0.2895116,0.1671496,3.568278,-0.2895115,-0.1671496,3.568278,4.612659E-08,-0.3342991,3.068278,-0.2244275,-0.1295733,3.068278,4.025976E-08,-0.2591466,2.268278,4.207433E-08,0.3926464,2.268278,0.3400418,0.1963232,2.468278,4.505455E-08,0.3926464,2.468278,0.3400418,0.1963232,3.568278,0.2895116,-0.1671496,3.568278,0.2895116,0.1671496,3.068278,0.2244276,-0.1295733,3.068278,0.2244276,0.1295733,3.068278,-0.2244275,0.1295733,3.068278,-0.2244275,-0.1295733,2.468278,-0.3400417,0.1963232,2.468278,-0.3400417,-0.1963232,3.568278,4.612659E-08,-0.3342991,4.068278,5.516089E-08,-0.2591466,3.568278,0.2895116,-0.1671496,4.068278,0.2244276,-0.1295733,-2.468278,-0.3400418,-0.1963232,-2.468278,-4.505452E-08,-0.3926464,-3.068278,-0.2244276,-0.1295733,-3.068278,-5.118185E-08,-0.2591466,3.068278,4.025976E-08,-0.2591466,3.568278,4.612659E-08,-0.3342991,3.068278,0.2244276,-0.1295733,3.568278,0.2895116,-0.1671496,-4.068278,-6.608298E-08,-0.2591466,-4.068278,0.2244275,-0.1295733,-4.068278,-0.2244276,-0.1295733,-4.068278,0.2244275,0.1295733,-4.068278,-0.2244276,0.1295733,-4.068278,-5.516077E-08,0.2591466,-4.068278,-6.608298E-08,-0.2591466,-3.568278,-6.021614E-08,-0.3342991,-4.068278,0.2244275,-0.1295733,-3.568278,0.2895115,-0.1671496,2.468278,-0.3400417,0.1963232,2.468278,4.505455E-08,0.3926464,3.068278,-0.2244275,0.1295733,3.068278,5.118197E-08,0.2591466,-3.568278,-0.2895116,0.1671496,-3.568278,-4.612649E-08,0.3342991,-3.068278,-0.2244276,0.1295733,-3.068278,-4.025964E-08,0.2591466,-3.568278,-0.2895116,-0.1671496,-3.068278,-0.2244276,-0.1295733,-3.568278,-6.021614E-08,-0.3342991,-3.068278,-5.118185E-08,-0.2591466,2.268278,2.552552E-08,-0.3926464,2.468278,2.850575E-08,-0.3926464,2.268278,0.3400418,-0.1963232,2.468278,0.3400418,-0.1963232,-2.468278,-0.3400418,0.1963232,-3.068278,-0.2244276,0.1295733,-2.468278,-2.850572E-08,0.3926464,-3.068278,-4.025964E-08,0.2591466,2.468278,2.850575E-08,-0.3926464,3.068278,4.025976E-08,-0.2591466,2.468278,0.3400418,-0.1963232,3.068278,0.2244276,-0.1295733,-2.468278,-0.3400418,-0.1963232,-2.268278,-0.3400418,-0.1963232,-2.468278,-4.505452E-08,-0.3926464,-2.268278,-4.207429E-08,-0.3926464,2.268278,-0.3400417,-0.1963232,2.468278,-0.3400417,-0.1963232,2.268278,2.552552E-08,-0.3926464,2.468278,2.850575E-08,-0.3926464,4.068278,-0.2244275,0.1295733,4.068278,-0.2244275,-0.1295733,3.568278,-0.2895115,0.1671496,3.568278,-0.2895115,-0.1671496,-3.568278,-0.2895116,0.1671496,-4.068278,-0.2244276,0.1295733,-3.568278,-4.612649E-08,0.3342991,-4.068278,-5.516077E-08,0.2591466,-3.068278,0.2244275,-0.1295733,-3.068278,0.2244275,0.1295733,-3.568278,0.2895115,-0.1671496,-3.568278,0.2895115,0.1671496,2.468278,0.3400418,-0.1963232,2.468278,0.3400418,0.1963232,2.268278,0.3400418,-0.1963232,2.268278,0.3400418,0.1963232,2.468278,-0.3400417,-0.1963232,3.068278,-0.2244275,-0.1295733,2.468278,2.850575E-08,-0.3926464,3.068278,4.025976E-08,-0.2591466,2.468278,4.505455E-08,0.3926464,2.468278,0.3400418,0.1963232,3.068278,5.118197E-08,0.2591466,3.068278,0.2244276,0.1295733,-2.268278,0.3400417,-0.1963232,-2.268278,0.3400417,0.1963232,-2.468278,0.3400417,-0.1963232,-2.468278,0.3400417,0.1963232,-2.468278,-0.3400418,0.1963232,-2.468278,-2.850572E-08,0.3926464,-2.268278,-0.3400418,0.1963232,-2.268278,-2.552549E-08,0.3926464,3.568278,-0.2895115,0.1671496,3.068278,-0.2244275,0.1295733,3.568278,6.021625E-08,0.3342991,3.068278,5.118197E-08,0.2591466,3.568278,-0.2895115,-0.1671496,4.068278,-0.2244275,-0.1295733,3.568278,4.612659E-08,-0.3342991,4.068278,5.516089E-08,-0.2591466,-2.468278,-2.850572E-08,0.3926464,-2.468278,0.3400417,0.1963232,-2.268278,-2.552549E-08,0.3926464,-2.268278,0.3400417,0.1963232,-2.468278,0.3400417,-0.1963232,-2.468278,0.3400417,0.1963232,-3.068278,0.2244275,-0.1295733,-3.068278,0.2244275,0.1295733,-2.468278,-0.3400418,0.1963232,-2.468278,-0.3400418,-0.1963232,-3.068278,-0.2244276,0.1295733,-3.068278,-0.2244276,-0.1295733,-3.068278,-4.025964E-08,0.2591466,-3.068278,0.2244275,0.1295733,-2.468278,-2.850572E-08,0.3926464,-2.468278,0.3400417,0.1963232,-3.568278,-4.612649E-08,0.3342991,-3.568278,0.2895115,0.1671496,-3.068278,-4.025964E-08,0.2591466,-3.068278,0.2244275,0.1295733,-2.268278,-0.3400418,-0.1963232,-2.468278,-0.3400418,-0.1963232,-2.268278,-0.3400418,0.1963232,-2.468278,-0.3400418,0.1963232,-2.468278,-4.505452E-08,-0.3926464,-2.268278,-4.207429E-08,-0.3926464,-2.468278,0.3400417,-0.1963232,-2.268278,0.3400417,-0.1963232,-3.568278,-0.2895116,0.1671496,-3.568278,-0.2895116,-0.1671496,-4.068278,-0.2244276,0.1295733,-4.068278,-0.2244276,-0.1295733,-3.568278,-6.021614E-08,-0.3342991,-3.068278,-5.118185E-08,-0.2591466,-3.568278,0.2895115,-0.1671496,-3.068278,0.2244275,-0.1295733,-3.068278,-0.2244276,0.1295733,-3.068278,-0.2244276,-0.1295733,-3.568278,-0.2895116,0.1671496,-3.568278,-0.2895116,-0.1671496,-3.568278,0.2895115,-0.1671496,-3.568278,0.2895115,0.1671496,-4.068278,0.2244275,-0.1295733,-4.068278,0.2244275,0.1295733,-4.068278,-5.516077E-08,0.2591466,-4.068278,0.2244275,0.1295733,-3.568278,-4.612649E-08,0.3342991,-3.568278,0.2895115,0.1671496,2.468278,-0.3400417,0.1963232,2.468278,-0.3400417,-0.1963232,2.268278,-0.3400417,0.1963232,2.268278,-0.3400417,-0.1963232,-3.068278,-5.118185E-08,-0.2591466,-2.468278,-4.505452E-08,-0.3926464,-3.068278,0.2244275,-0.1295733,-2.468278,0.3400417,-0.1963232,3.568278,6.021625E-08,0.3342991,3.568278,0.2895116,0.1671496,4.068278,6.60831E-08,0.2591466,4.068278,0.2244276,0.1295733,-3.568278,-0.2895116,-0.1671496,-3.568278,-6.021614E-08,-0.3342991,-4.068278,-0.2244276,-0.1295733,-4.068278,-6.608298E-08,-0.2591466,-2.268278,-0.7014806,-0.405,2.268278,-0.7014806,-0.405,-2.268278,-5.086937E-08,-0.81,2.268278,1.673043E-08,-0.81,2.268278,0.7014806,-0.405,2.268278,0.7014806,0.405,-2.268278,0.7014806,-0.405,-2.268278,0.7014806,0.405,-2.268278,-1.673043E-08,0.81,-2.268278,0.7014806,0.405,2.268278,5.086937E-08,0.81,2.268278,0.7014806,0.405,-2.268278,-0.7014806,0.405,-2.268278,-1.673043E-08,0.81,2.268278,-0.7014806,0.405,2.268278,5.086937E-08,0.81,2.268278,-0.7014806,0.405,2.268278,-0.7014806,-0.405,-2.268278,-0.7014806,0.405,-2.268278,-0.7014806,-0.405,2.268278,1.673043E-08,-0.81,2.268278,-0.7014806,-0.405,2.268278,0.7014806,-0.405,2.268278,2.552552E-08,-0.3926464,2.268278,0.3400418,-0.1963232,2.268278,0.7014806,0.405,2.268278,0.3400418,0.1963232,2.268278,4.207433E-08,0.3926464,2.268278,-0.3400417,-0.1963232,2.268278,-0.7014806,0.405,2.268278,-0.3400417,0.1963232,2.268278,5.086937E-08,0.81,-2.268278,-4.207429E-08,-0.3926464,-2.268278,-0.3400418,-0.1963232,-2.268278,-0.7014806,-0.405,-2.268278,-0.7014806,0.405,-2.268278,-0.3400418,0.1963232,-2.268278,-2.552549E-08,0.3926464,-2.268278,0.7014806,0.405,-2.268278,-1.673043E-08,0.81,-2.268278,0.7014806,-0.405,-2.268278,-5.086937E-08,-0.81,-2.268278,0.3400417,-0.1963232,-2.268278,0.3400417,0.1963232,-2.268278,-5.086937E-08,-0.81,2.268278,1.673043E-08,-0.81,-2.268278,0.7014806,-0.405,2.268278,0.7014806,-0.405
		} 
		PolygonVertexIndex: *420 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,9,-12,13,12,-12,14,16,-16,17,15,-17,18,20,-20,21,19,-21,22,24,-24,25,23,-25,26,28,-28,29,27,-29,30,32,-32,33,31,-33,34,36,-36,37,35,-37,38,40,-40,41,39,-41,42,44,-44,45,43,-45,46,48,-48,49,47,-49,50,52,-52,53,51,-53,54,56,-56,57,55,-57,58,60,-60,61,59,-61,62,61,-61,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,98,-98,99,97,-99,100,102,-102,103,101,-103,104,106,-106,107,105,-107,108,110,-110,111,109,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,130,-130,131,129,-131,132,134,-134,135,133,-135,136,138,-138,139,137,-139,140,142,-142,143,141,-143,144,146,-146,147,145,-147,148,150,-150,151,149,-151,152,154,-154,155,153,-155,156,158,-158,159,157,-159,160,162,-162,163,161,-163,164,166,-166,167,165,-167,168,170,-170,171,169,-171,172,174,-174,175,173,-175,176,178,-178,179,177,-179,180,182,-182,183,181,-183,184,186,-186,187,185,-187,188,190,-190,191,189,-191,192,194,-194,195,193,-195,196,198,-198,199,197,-199,200,202,-202,203,201,-203,204,206,-206,207,205,-207,208,210,-210,211,209,-211,212,214,-214,215,213,-215,216,218,-218,219,217,-219,220,222,-222,223,221,-223,224,226,-226,227,225,-227,228,227,-227,229,228,-227,230,228,-230,231,230,-230,227,232,-226,233,225,-233,229,233,-232,234,233,-233,231,233,-235,235,233,-230,236,238,-238,239,237,-239,240,237,-240,241,240,-240,242,241,-240,243,242,-240,236,244,-239,245,238,-245,246,244,-237,242,244,-247,247,242,-247,241,242,-248,248,250,-250,251,249,-251
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1260 {
				a: -0.129079,-0.9916344,0,-0.129079,-0.9916344,0,-0.129079,-0.9916344,0,-0.129079,-0.9916344,0,-0.129079,-0.9916344,0,-0.129079,-0.9916344,0,0.1892097,0.9819368,0,0.1892097,0.9819368,0,0.1892097,0.9819368,0,0.1892097,0.9819368,0,0.1892097,0.9819368,0,0.1892097,0.9819368,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.129079,-0.4958171,0.8587806,0.129079,-0.4958171,0.8587806,0.129079,-0.4958171,0.8587806,0.129079,-0.4958171,0.8587806,0.129079,-0.4958171,0.8587806,0.129079,-0.4958171,0.8587806,-0.129079,0.4958172,0.8587806,-0.129079,0.4958172,0.8587806,-0.129079,0.4958172,0.8587806,-0.129079,0.4958172,0.8587806,-0.129079,0.4958172,0.8587806,-0.129079,0.4958172,0.8587806,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0.129079,0.9916344,0,0.129079,0.9916344,0,0.129079,0.9916344,0,0.129079,0.9916344,0,0.129079,0.9916344,0,0.129079,0.9916344,0,-0.129079,-0.4958172,-0.8587806,-0.129079,-0.4958172,-0.8587806,-0.129079,-0.4958172,-0.8587806,-0.129079,-0.4958172,-0.8587806,-0.129079,-0.4958172,-0.8587806,-0.129079,-0.4958172,-0.8587806,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,-0.129079,0.9916344,0,-0.129079,0.9916344,0,-0.129079,0.9916344,0,-0.129079,0.9916344,0,-0.129079,0.9916344,0,-0.129079,0.9916344,0,0.1892097,-0.9819368,0,0.1892097,-0.9819368,0,0.1892097,-0.9819368,0,0.1892097,-0.9819368,0,0.1892097,-0.9819368,0,0.1892097,-0.9819368,0,0.129079,0.4958171,-0.8587806,0.129079,0.4958171,-0.8587806,0.129079,0.4958171,-0.8587806,0.129079,0.4958171,-0.8587806,0.129079,0.4958171,-0.8587806,0.129079,0.4958171,-0.8587806,-0.1892097,-0.4909683,-0.8503821,-0.1892097,-0.4909683,-0.8503821,-0.1892097,-0.4909683,-0.8503821,-0.1892097,-0.4909683,-0.8503821,-0.1892097,-0.4909683,-0.8503821,-0.1892097,-0.4909683,-0.8503821,-0.129079,0.4958171,-0.8587806,-0.129079,0.4958171,-0.8587806,-0.129079,0.4958171,-0.8587806,-0.129079,0.4958171,-0.8587806,-0.129079,0.4958171,-0.8587806,-0.129079,0.4958171,-0.8587806,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.129079,0.4958171,-0.8587806,-0.129079,0.4958171,-0.8587806,-0.129079,0.4958171,-0.8587806,-0.129079,0.4958171,-0.8587806,-0.129079,0.4958171,-0.8587806,-0.129079,0.4958171,-0.8587806,0.1892097,-0.4909683,0.8503821,0.1892097,-0.4909683,0.8503821,0.1892097,-0.4909683,0.8503821,0.1892097,-0.4909683,0.8503821,0.1892097,-0.4909683,0.8503821,0.1892097,-0.4909683,0.8503821,0.129079,-0.4958171,0.8587806,0.129079,-0.4958171,0.8587806,0.129079,-0.4958171,0.8587806,0.129079,-0.4958171,0.8587806,0.129079,-0.4958171,0.8587806,0.129079,-0.4958171,0.8587806,0.129079,-0.4958172,-0.8587806,0.129079,-0.4958172,-0.8587806,0.129079,-0.4958172,-0.8587806,0.129079,-0.4958172,-0.8587806,0.129079,-0.4958172,-0.8587806,0.129079,-0.4958172,-0.8587806,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,-0.1892097,-0.4909683,0.8503821,-0.1892097,-0.4909683,0.8503821,-0.1892097,-0.4909683,0.8503821,-0.1892097,-0.4909683,0.8503821,-0.1892097,-0.4909683,0.8503821,-0.1892097,-0.4909683,0.8503821,0.1892097,0.4909683,-0.8503821,0.1892097,0.4909683,-0.8503821,0.1892097,0.4909683,-0.8503821,0.1892097,0.4909683,-0.8503821,0.1892097,0.4909683,-0.8503821,0.1892097,0.4909683,-0.8503821,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0.129079,-0.9916344,0,0.129079,-0.9916344,0,0.129079,-0.9916344,0,0.129079,-0.9916344,0,0.129079,-0.9916344,0,0.129079,-0.9916344,0,-0.129079,-0.4958171,0.8587806,-0.129079,-0.4958171,0.8587806,-0.129079,-0.4958171,0.8587806,-0.129079,-0.4958171,0.8587806,-0.129079,-0.4958171,0.8587806,-0.129079,-0.4958171,0.8587806,0.129079,0.9916344,0,0.129079,0.9916344,0,0.129079,0.9916344,0,0.129079,0.9916344,0,0.129079,0.9916344,0,0.129079,0.9916344,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.1892097,-0.4909683,-0.8503821,0.1892097,-0.4909683,-0.8503821,0.1892097,-0.4909683,-0.8503821,0.1892097,-0.4909683,-0.8503821,0.1892097,-0.4909683,-0.8503821,0.1892097,-0.4909683,-0.8503821,0.1892097,0.4909683,0.8503821,0.1892097,0.4909683,0.8503821,0.1892097,0.4909683,0.8503821,0.1892097,0.4909683,0.8503821,0.1892097,0.4909683,0.8503821,0.1892097,0.4909683,0.8503821,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,-0.129079,-0.4958171,0.8587806,-0.129079,-0.4958171,0.8587806,-0.129079,-0.4958171,0.8587806,-0.129079,-0.4958171,0.8587806,-0.129079,-0.4958171,0.8587806,-0.129079,-0.4958171,0.8587806,0.129079,-0.4958172,-0.8587806,0.129079,-0.4958172,-0.8587806,0.129079,-0.4958172,-0.8587806,0.129079,-0.4958172,-0.8587806,0.129079,-0.4958172,-0.8587806,0.129079,-0.4958172,-0.8587806,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,-0.1892097,0.9819368,0,-0.1892097,0.9819368,0,-0.1892097,0.9819368,0,-0.1892097,0.9819368,0,-0.1892097,0.9819368,0,-0.1892097,0.9819368,0,-0.1892097,-0.9819368,0,-0.1892097,-0.9819368,0,-0.1892097,-0.9819368,0,-0.1892097,-0.9819368,0,-0.1892097,-0.9819368,0,-0.1892097,-0.9819368,0,-0.1892097,0.4909683,0.8503821,-0.1892097,0.4909683,0.8503821,-0.1892097,0.4909683,0.8503821,-0.1892097,0.4909683,0.8503821,-0.1892097,0.4909683,0.8503821,-0.1892097,0.4909683,0.8503821,0.129079,0.4958172,0.8587806,0.129079,0.4958172,0.8587806,0.129079,0.4958172,0.8587806,0.129079,0.4958172,0.8587806,0.129079,0.4958172,0.8587806,0.129079,0.4958172,0.8587806,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,-0.129079,-0.9916344,0,-0.129079,-0.9916344,0,-0.129079,-0.9916344,0,-0.129079,-0.9916344,0,-0.129079,-0.9916344,0,-0.129079,-0.9916344,0,0.129079,0.4958171,-0.8587806,0.129079,0.4958171,-0.8587806,0.129079,0.4958171,-0.8587806,0.129079,0.4958171,-0.8587806,0.129079,0.4958171,-0.8587806,0.129079,0.4958171,-0.8587806,0.129079,-0.9916344,0,0.129079,-0.9916344,0,0.129079,-0.9916344,0,0.129079,-0.9916344,0,0.129079,-0.9916344,0,0.129079,-0.9916344,0,-0.129079,0.9916344,0,-0.129079,0.9916344,0,-0.129079,0.9916344,0,-0.129079,0.9916344,0,-0.129079,0.9916344,0,-0.129079,0.9916344,0,-0.129079,0.4958172,0.8587806,-0.129079,0.4958172,0.8587806,-0.129079,0.4958172,0.8587806,-0.129079,0.4958172,0.8587806,-0.129079,0.4958172,0.8587806,-0.129079,0.4958172,0.8587806,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.1892097,0.4909683,-0.8503821,-0.1892097,0.4909683,-0.8503821,-0.1892097,0.4909683,-0.8503821,-0.1892097,0.4909683,-0.8503821,-0.1892097,0.4909683,-0.8503821,-0.1892097,0.4909683,-0.8503821,0.129079,0.4958172,0.8587806,0.129079,0.4958172,0.8587806,0.129079,0.4958172,0.8587806,0.129079,0.4958172,0.8587806,0.129079,0.4958172,0.8587806,0.129079,0.4958172,0.8587806,-0.129079,-0.4958172,-0.8587806,-0.129079,-0.4958172,-0.8587806,-0.129079,-0.4958172,-0.8587806,-0.129079,-0.4958172,-0.8587806,-0.129079,-0.4958172,-0.8587806,-0.129079,-0.4958172,-0.8587806,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *504 {
				a: 0.6580715,-14.07794,-0.6580669,-14.07794,0.5101331,-12.09283,-0.5101292,-12.09283,0.5101298,-11.69445,-0.5101324,-11.69445,0.7729249,-9.288795,-0.772927,-9.288795,1.020262,-2.150016E-08,0.5101311,-0.8835731,0.5101312,0.8835731,-0.5101311,0.8835731,-0.5101312,-0.8835731,-1.020262,2.150067E-08,13.79448,0.3681234,13.69667,1.680622,15.76311,0.6631812,15.68728,1.680622,12.0973,0.3899915,12.02148,1.407432,14.08792,0.3899915,13.9901,1.70249,8.930229,-0.7729258,8.930229,0.7729261,9.71763,-0.7729258,9.71763,0.7729262,0.5101286,-15.7688,-0.5101337,-15.7688,0.6580669,-13.78369,-0.6580714,-13.78369,-13.9901,-1.70249,-14.08792,-0.389991,-12.02148,-1.407432,-12.0973,-0.3899911,8.930229,-0.7729258,8.930229,0.7729261,9.71763,-0.7729258,9.71763,0.7729262,-0.6580715,14.07794,0.6580669,14.07794,-0.5101331,12.09283,0.5101292,12.09283,-0.5101298,11.69445,0.5101324,11.69445,-0.7729249,9.288795,0.772927,9.288795,-13.69667,-1.680622,-15.68728,-1.680622,-13.79448,-0.3681229,-15.76311,-0.6631807,9.317798,0.2403545,9.149926,1.777064,11.68069,0.7628359,11.5699,1.777064,-12.0973,0.3899914,-14.08792,0.3899914,-12.02148,1.407432,-13.9901,1.70249,-1.020262,-2.15003E-08,-0.5101312,0.8835731,-0.5101311,-0.8835731,0.5101311,0.8835731,0.5101312,-0.8835731,1.020262,2.150053E-08,15.68728,-1.680622,13.69667,-1.680622,15.76311,-0.6631812,13.79448,-0.3681234,9.317798,0.2403547,9.149926,1.777065,11.68069,0.7628361,11.5699,1.777065,-13.9901,-1.70249,-14.08792,-0.3899914,-12.02148,-1.407432,-12.0973,-0.3899914,13.9901,-1.70249,12.02148,-1.407432,14.08792,-0.3899915,12.0973,-0.3899915,-8.930229,-0.7729258,-9.71763,-0.7729258,-8.930229,0.7729261,-9.71763,0.7729262,-9.317798,0.2403544,-11.68069,0.7628357,-9.149926,1.777064,-11.5699,1.777064,-9.149926,-1.777064,-11.5699,-1.777064,-9.317798,-0.2403544,-11.68069,-0.7628357,9.71763,-0.7729262,8.930229,-0.7729261,9.71763,0.7729258,8.930229,0.7729258,-8.930229,-0.7729258,-9.71763,-0.7729258,-8.930229,0.7729261,-9.71763,0.7729262,-0.5101286,15.7688,0.5101337,15.7688,-0.6580669,13.78369,0.6580714,13.78369,-13.79448,0.3681229,-15.76311,0.6631807,-13.69667,1.680622,-15.68728,1.680622,0.5101331,12.09283,-0.5101292,12.09283,0.6580715,14.07794,-0.6580669,14.07794,-9.71763,-0.772926,-9.71763,0.772926,-8.930229,-0.772926,-8.930229,0.772926,-9.317798,0.2403548,-11.68069,0.7628362,-9.149926,1.777065,-11.5699,1.777065,9.149926,-1.777064,9.317798,-0.2403545,11.5699,-1.777064,11.68069,-0.7628359,8.930229,-0.772926,8.930229,0.772926,9.71763,-0.772926,9.71763,0.772926,-9.71763,-0.7729262,-9.71763,0.7729258,-8.930229,-0.7729261,-8.930229,0.7729258,13.9901,-1.70249,12.02148,-1.407432,14.08792,-0.389991,12.0973,-0.389991,-13.79448,0.3681235,-15.76311,0.6631814,-13.69667,1.680622,-15.68728,1.680622,-9.71763,-0.7729262,-9.71763,0.7729258,-8.930229,-0.7729261,-8.930229,0.7729258,-0.7729249,-9.288795,0.772927,-9.288795,-0.5101298,-11.69445,0.5101324,-11.69445,0.7729249,9.288795,-0.772927,9.288795,0.5101298,11.69445,-0.5101324,11.69445,-11.5699,-1.777065,-11.68069,-0.7628362,-9.149926,-1.777065,-9.317798,-0.2403548,-14.08792,0.389991,-13.9901,1.70249,-12.0973,0.3899911,-12.02148,1.407432,-8.930229,-0.772926,-9.71763,-0.772926,-8.930229,0.772926,-9.71763,0.772926,9.71763,-0.7729262,8.930229,-0.7729261,9.71763,0.7729258,8.930229,0.7729258,0.6580669,13.78369,-0.6580714,13.78369,0.5101286,15.7688,-0.5101337,15.7688,14.08792,0.389991,12.0973,0.389991,13.9901,1.70249,12.02148,1.407432,-0.5101331,-12.09283,0.5101292,-12.09283,-0.6580715,-14.07794,0.6580669,-14.07794,-0.6580669,-13.78369,0.6580714,-13.78369,-0.5101286,-15.7688,0.5101337,-15.7688,-15.68728,-1.680622,-15.76311,-0.6631814,-13.69667,-1.680622,-13.79448,-0.3681235,9.71763,0.772926,9.71763,-0.772926,8.930229,0.772926,8.930229,-0.772926,11.5699,-1.777065,9.149926,-1.777065,11.68069,-0.7628361,9.317798,-0.2403547,13.69667,-1.680622,13.79448,-0.368123,15.68728,-1.680622,15.76311,-0.6631808,13.79448,0.368123,13.69667,1.680622,15.76311,0.6631808,15.68728,1.680622,8.930229,-1.594488,-8.930229,-1.594488,8.930229,1.594488,-8.930229,1.594488,-8.930229,-1.594488,-8.930229,1.594488,8.930229,-1.594488,8.930229,1.594488,-8.930229,-1.594488,-8.930229,1.594488,8.930229,-1.594488,8.930229,1.594488,-8.930229,-1.594488,-8.930229,1.594488,8.930229,-1.594488,8.930229,1.594488,8.930229,1.594488,8.930229,-1.594488,-8.930229,1.594488,-8.930229,-1.594488,3.188976,-6.720264E-08,1.594488,-2.761734,1.594488,2.761734,1.545852,-3.257631E-08,0.772926,1.338747,-1.594488,2.761734,-0.7729259,1.338747,-1.545852,3.257647E-08,0.7729259,-1.338747,-1.594488,-2.761734,-0.772926,-1.338747,-3.188976,6.720264E-08,-1.545852,-3.257635E-08,-0.7729259,-1.338747,-1.594488,-2.761734,1.594488,-2.761734,0.772926,-1.338747,1.545852,3.257643E-08,1.594488,2.761734,3.188976,6.720265E-08,-1.594488,2.761734,-3.188976,-6.720263E-08,-0.772926,1.338747,0.7729259,1.338747,8.930229,-1.594488,-8.930229,-1.594488,8.930229,1.594488,-8.930229,1.594488
				}
			UVIndex: *420 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,9,11,13,12,11,14,16,15,17,15,16,18,20,19,21,19,20,22,24,23,25,23,24,26,28,27,29,27,28,30,32,31,33,31,32,34,36,35,37,35,36,38,40,39,41,39,40,42,44,43,45,43,44,46,48,47,49,47,48,50,52,51,53,51,52,54,56,55,57,55,56,58,60,59,61,59,60,62,61,60,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,90,89,91,89,90,92,94,93,95,93,94,96,98,97,99,97,98,100,102,101,103,101,102,104,106,105,107,105,106,108,110,109,111,109,110,112,114,113,115,113,114,116,118,117,119,117,118,120,122,121,123,121,122,124,126,125,127,125,126,128,130,129,131,129,130,132,134,133,135,133,134,136,138,137,139,137,138,140,142,141,143,141,142,144,146,145,147,145,146,148,150,149,151,149,150,152,154,153,155,153,154,156,158,157,159,157,158,160,162,161,163,161,162,164,166,165,167,165,166,168,170,169,171,169,170,172,174,173,175,173,174,176,178,177,179,177,178,180,182,181,183,181,182,184,186,185,187,185,186,188,190,189,191,189,190,192,194,193,195,193,194,196,198,197,199,197,198,200,202,201,203,201,202,204,206,205,207,205,206,208,210,209,211,209,210,212,214,213,215,213,214,216,218,217,219,217,218,220,222,221,223,221,222,224,226,225,227,225,226,228,227,226,229,228,226,230,228,229,231,230,229,227,232,225,233,225,232,229,233,231,234,233,232,231,233,234,235,233,229,236,238,237,239,237,238,240,237,239,241,240,239,242,241,239,243,242,239,236,244,238,245,238,244,246,244,236,242,244,246,247,242,246,241,242,247,248,250,249,251,249,250
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *140 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 13090, "Material::brownLight", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.9764706,0.772549,0.5490196
			P: "DiffuseColor", "Color", "", "A",0.9764706,0.772549,0.5490196
		}
	}

	Material: 20330, "Material::brown", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.827451,0.5647059,0.4039216
			P: "DiffuseColor", "Color", "", "A",0.827451,0.5647059,0.4039216
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh rollingPin, Model::RootNode
	C: "OO",5352719306521107567,0

	;Geometry::, Model::Mesh rollingPin
	C: "OO",4774357076313614911,5352719306521107567

	;Material::brownLight, Model::Mesh rollingPin
	C: "OO",13090,5352719306521107567

	;Material::brown, Model::Mesh rollingPin
	C: "OO",20330,5352719306521107567

}
