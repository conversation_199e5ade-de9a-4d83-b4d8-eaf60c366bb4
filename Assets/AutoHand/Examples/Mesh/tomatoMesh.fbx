; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2020
		Month: 3
		Day: 4
		Hour: 23
		Minute: 19
		Second: 30
		Millisecond: 855
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "tomato.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "tomato.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4958426700725659643, "Model::tomato", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4681535035854965672, "Geometry::", "Mesh" {
		Vertices: *684 {
			a: 0.711,0.19434,-5.414336E-15,0.5027529,0.19434,-0.5027529,0.474,0,-8.121504E-15,0.3351686,0,-0.3351686,-0.3351686,0,-0.3351686,-0.5027529,0.19434,-0.5027529,-0.474,0,-8.121504E-15,-0.711,0.19434,-5.414336E-15,-0.3351686,0,0.3351686,-0.474,0,-8.121504E-15,-0.5027529,0.19434,0.5027529,-0.711,0.19434,-5.414336E-15,0,0.19434,0.711,-9.023893E-16,0,0.474,-0.5027529,0.19434,0.5027529,-0.3351686,0,0.3351686,0.711,0.19434,-5.414336E-15,0.474,0,-8.121504E-15,0.5027529,0.19434,0.5027529,0.3351686,0,0.3351686,0.5027529,0.19434,0.5027529,0.3351686,0,0.3351686,0,0.19434,0.711,-9.023893E-16,0,0.474,0.5027529,0.19434,-0.5027529,0,0.19434,-0.711,0.3351686,0,-0.3351686,-9.023893E-16,0,-0.474,0.474,0,-8.121504E-15,0.3351686,0,-0.3351686,0.3351686,0,0.3351686,-9.023893E-16,0,0.474,-9.023893E-16,0,-0.474,-0.3351686,0,0.3351686,-0.3351686,0,-0.3351686,-0.474,0,-8.121504E-15,0,0.19434,-0.711,-0.5027529,0.19434,-0.5027529,-9.023893E-16,0,-0.474,-0.3351686,0,-0.3351686,0.711,0.19434,-5.414336E-15,0.5027529,0.19434,0.5027529,0.84846,0.58302,-2.707168E-15,0.5999518,0.58302,0.5999518,0,0.19434,-0.711,0.5027529,0.19434,-0.5027529,9.023893E-16,0.58302,-0.84846,0.5999518,0.58302,-0.5999518,0.5999518,0.58302,-0.5999518,0.5027529,0.19434,-0.5027529,0.84846,0.58302,-2.707168E-15,0.711,0.19434,-5.414336E-15,-0.5027529,0.19434,-0.5027529,0,0.19434,-0.711,-0.5999518,0.58302,-0.5999518,9.023893E-16,0.58302,-0.84846,0.5027529,0.19434,0.5027529,0,0.19434,0.711,0.5999518,0.58302,0.5999518,9.023893E-16,0.58302,0.84846,-0.5999518,0.58302,-0.5999518,-0.84846,0.58302,-2.707168E-15,-0.5027529,0.19434,-0.5027529,-0.711,0.19434,-5.414336E-15,-0.711,0.19434,-5.414336E-15,-0.84846,0.58302,-2.707168E-15,-0.5027529,0.19434,0.5027529,-0.5999518,0.58302,0.5999518,0,0.19434,0.711,-0.5027529,0.19434,0.5027529,9.023893E-16,0.58302,0.84846,-0.5999518,0.58302,0.5999518,-0.5999518,0.58302,-0.5999518,9.023893E-16,0.58302,-0.84846,-0.5027529,0.9717,-0.5027529,0,0.9717,-0.711,9.023893E-16,0.58302,0.84846,-0.5999518,0.58302,0.5999518,0,0.9717,0.711,-0.5027529,0.9717,0.5027529,-0.711,0.9717,-7.219114E-15,-0.5027529,0.9717,0.5027529,-0.84846,0.58302,-2.707168E-15,-0.5999518,0.58302,0.5999518,0.5027529,0.9717,0.5027529,0,0.9717,0.711,0.3351686,1.16604,0.3351686,-9.023893E-16,1.16604,0.474,0.5027529,0.9717,-0.5027529,0.3351686,1.16604,-0.3351686,0,0.9717,-0.711,-9.023893E-16,1.16604,-0.474,0,0.9717,-0.711,-9.023893E-16,1.16604,-0.474,-0.5027529,0.9717,-0.5027529,-0.3351686,1.16604,-0.3351686,0.711,0.9717,-7.219114E-15,0.5027529,0.9717,0.5027529,0.474,1.16604,-8.121504E-15,0.3351686,1.16604,0.3351686,0.711,0.9717,-7.219114E-15,0.474,1.16604,-8.121504E-15,0.5027529,0.9717,-0.5027529,0.3351686,1.16604,-0.3351686,-0.3351686,1.16604,0.3351686,-0.5027529,0.9717,0.5027529,-0.474,1.16604,-8.121504E-15,-0.711,0.9717,-7.219114E-15,-0.5999518,0.58302,-0.5999518,-0.5027529,0.9717,-0.5027529,-0.84846,0.58302,-2.707168E-15,-0.711,0.9717,-7.219114E-15,-0.3351686,1.16604,-0.3351686,-0.474,1.16604,-8.121504E-15,-0.5027529,0.9717,-0.5027529,-0.711,0.9717,-7.219114E-15,0.5999518,0.58302,0.5999518,9.023893E-16,0.58302,0.84846,0.5027529,0.9717,0.5027529,0,0.9717,0.711,0,0.9717,0.711,-0.5027529,0.9717,0.5027529,-9.023893E-16,1.16604,0.474,-0.3351686,1.16604,0.3351686,0.5999518,0.58302,-0.5999518,0.84846,0.58302,-2.707168E-15,0.5027529,0.9717,-0.5027529,0.711,0.9717,-7.219114E-15,0.711,0.9717,-7.219114E-15,0.84846,0.58302,-2.707168E-15,0.5027529,0.9717,0.5027529,0.5999518,0.58302,0.5999518,9.023893E-16,0.58302,-0.84846,0.5999518,0.58302,-0.5999518,0,0.9717,-0.711,0.5027529,0.9717,-0.5027529,0.474,1.16604,-8.121504E-15,0.3351686,1.16604,0.3351686,0.3351686,1.16604,-0.3351686,-9.023893E-16,1.16604,-0.474,0.1185,1.16604,-0.1185,-0.1185,1.16604,-0.1185,-0.3351686,1.16604,-0.3351686,-0.1185,1.16604,0.1185,0.1185,1.16604,0.1185,-9.023893E-16,1.16604,0.474,-0.3351686,1.16604,0.3351686,-0.474,1.16604,-8.121504E-15,0.04229742,1.33004,-0.04229742,0.04229742,1.33004,0.04229742,-0.04229742,1.33004,-0.04229742,-0.04229742,1.33004,0.04229742,-0.06934003,1.16604,-0.06934003,0.06934003,1.16604,-0.06934003,-0.04229742,1.33004,-0.04229742,0.04229742,1.33004,-0.04229742,0.06934003,1.16604,-0.06934003,0.06934003,1.16604,0.06934003,0.04229742,1.33004,-0.04229742,0.04229742,1.33004,0.04229742,0.06934003,1.16604,0.06934003,-0.06934003,1.16604,0.06934003,0.04229742,1.33004,0.04229742,-0.04229742,1.33004,0.04229742,-0.06934003,1.16604,-0.06934003,-0.04229742,1.33004,-0.04229742,-0.06934003,1.16604,0.06934003,-0.04229742,1.33004,0.04229742,0.06934003,1.16604,-0.06934003,-0.06934003,1.16604,-0.06934003,-0.06934003,1.16604,0.06934003,0.06934003,1.16604,0.06934003,0.29178,1.221365,0.0785,0.29178,1.221365,-0.0785,0.1185,1.16604,0.1185,0.1185,1.16604,-0.1185,0.1185,1.16604,0.1185,0.29178,1.221365,-0.0785,0.29178,1.221365,0.0785,0.1185,1.16604,-0.1185,0.4351101,1.376643,-2.375741E-09,0.29178,1.221365,-0.0785,0.29178,1.221365,0.0785,0.29178,1.221365,0.0785,0.29178,1.221365,-0.0785,0.4351101,1.376643,-2.375741E-09,0.0785,1.221365,0.29178,-0.0785,1.221365,0.29178,2.375544E-09,1.376643,0.4351101,2.375544E-09,1.376643,0.4351101,-0.0785,1.221365,0.29178,0.0785,1.221365,0.29178,0.1185,1.16604,0.1185,-0.1185,1.16604,0.1185,0.0785,1.221365,0.29178,-0.0785,1.221365,0.29178,0.0785,1.221365,0.29178,-0.1185,1.16604,0.1185,0.1185,1.16604,0.1185,-0.0785,1.221365,0.29178,-0.29178,1.221365,-0.0785,-0.4351101,1.376643,-2.342297E-09,-0.29178,1.221365,0.0785,-0.29178,1.221365,0.0785,-0.4351101,1.376643,-2.342297E-09,-0.29178,1.221365,-0.0785,-0.1185,1.16604,0.1185,-0.1185,1.16604,-0.1185,-0.29178,1.221365,0.0785,-0.29178,1.221365,-0.0785,-0.29178,1.221365,0.0785,-0.1185,1.16604,-0.1185,-0.1185,1.16604,0.1185,-0.29178,1.221365,-0.0785,-0.0785,1.221365,-0.29178,0.0785,1.221365,-0.29178,2.342279E-09,1.376643,-0.4351101,2.342279E-09,1.376643,-0.4351101,0.0785,1.221365,-0.29178,-0.0785,1.221365,-0.29178,0.1185,1.16604,-0.1185,0.0785,1.221365,-0.29178,-0.1185,1.16604,-0.1185,-0.0785,1.221365,-0.29178,-0.1185,1.16604,-0.1185,0.0785,1.221365,-0.29178,0.1185,1.16604,-0.1185,-0.0785,1.221365,-0.29178
		} 
		PolygonVertexIndex: *372 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,10,-10,11,9,-11,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,29,-32,33,32,-32,34,32,-34,35,34,-34,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,46,-46,47,45,-47,48,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,60,62,-62,63,61,-63,64,66,-66,67,65,-67,68,70,-70,71,69,-71,72,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,86,-86,87,85,-87,88,90,-90,91,89,-91,92,94,-94,95,93,-95,96,98,-98,99,97,-99,100,102,-102,103,101,-103,104,106,-106,107,105,-107,108,110,-110,111,109,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,130,-130,131,129,-131,132,134,-134,135,133,-135,136,138,-138,139,137,-139,140,137,-140,141,140,-140,142,141,-140,143,141,-143,140,144,-138,145,137,-145,143,145,-145,146,145,-144,142,146,-144,147,146,-143,148,150,-150,151,149,-151,152,154,-154,155,153,-155,156,158,-158,159,157,-159,160,162,-162,163,161,-163,164,166,-166,167,165,-167,144,140,-169,141,168,-141,169,168,-142,170,169,-142,168,171,-145,143,144,-172,170,143,-172,141,143,-171,172,174,-174,175,173,-175,176,178,-178,177,179,-177,180,182,-182,183,185,-185,186,188,-188,189,191,-191,192,194,-194,195,193,-195,196,198,-198,197,199,-197,200,202,-202,203,205,-205,206,208,-208,209,207,-209,210,212,-212,211,213,-211,214,216,-216,217,219,-219,220,222,-222,223,221,-223,224,226,-226,225,227,-225
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *1116 {
				a: 0.6132796,-0.747902,-0.2540288,0.6132796,-0.747902,-0.2540288,0.6132796,-0.747902,-0.2540288,0.6132796,-0.747902,-0.2540288,0.6132796,-0.747902,-0.2540288,0.6132796,-0.747902,-0.2540288,-0.6132796,-0.747902,-0.2540288,-0.6132796,-0.747902,-0.2540288,-0.6132796,-0.747902,-0.2540288,-0.6132796,-0.747902,-0.2540288,-0.6132796,-0.747902,-0.2540288,-0.6132796,-0.747902,-0.2540288,-0.6132796,-0.747902,0.2540288,-0.6132796,-0.747902,0.2540288,-0.6132796,-0.747902,0.2540288,-0.6132796,-0.747902,0.2540288,-0.6132796,-0.747902,0.2540288,-0.6132796,-0.747902,0.2540288,-0.2540288,-0.747902,0.6132796,-0.2540288,-0.747902,0.6132796,-0.2540288,-0.747902,0.6132796,-0.2540288,-0.747902,0.6132796,-0.2540288,-0.747902,0.6132796,-0.2540288,-0.747902,0.6132796,0.6132796,-0.747902,0.2540288,0.6132796,-0.747902,0.2540288,0.6132796,-0.747902,0.2540288,0.6132796,-0.747902,0.2540288,0.6132796,-0.747902,0.2540288,0.6132796,-0.747902,0.2540288,0.2540288,-0.747902,0.6132796,0.2540288,-0.747902,0.6132796,0.2540288,-0.747902,0.6132796,0.2540288,-0.747902,0.6132796,0.2540288,-0.747902,0.6132796,0.2540288,-0.747902,0.6132796,0.2540288,-0.747902,-0.6132796,0.2540288,-0.747902,-0.6132796,0.2540288,-0.747902,-0.6132796,0.2540288,-0.747902,-0.6132796,0.2540288,-0.747902,-0.6132796,0.2540288,-0.747902,-0.6132796,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.2540288,-0.747902,-0.6132796,-0.2540288,-0.747902,-0.6132796,-0.2540288,-0.747902,-0.6132796,-0.2540288,-0.747902,-0.6132796,-0.2540288,-0.747902,-0.6132796,-0.2540288,-0.747902,-0.6132796,0.8781912,-0.3105798,0.3637587,0.8781912,-0.3105798,0.3637587,0.8781912,-0.3105798,0.3637587,0.8781912,-0.3105798,0.3637587,0.8781912,-0.3105798,0.3637587,0.8781912,-0.3105798,0.3637587,0.3637587,-0.3105798,-0.8781912,0.3637587,-0.3105798,-0.8781912,0.3637587,-0.3105798,-0.8781912,0.3637587,-0.3105798,-0.8781912,0.3637587,-0.3105798,-0.8781912,0.3637587,-0.3105798,-0.8781912,0.8781912,-0.3105798,-0.3637587,0.8781912,-0.3105798,-0.3637587,0.8781912,-0.3105798,-0.3637587,0.8781912,-0.3105798,-0.3637587,0.8781912,-0.3105798,-0.3637587,0.8781912,-0.3105798,-0.3637587,-0.3637587,-0.3105798,-0.8781912,-0.3637587,-0.3105798,-0.8781912,-0.3637587,-0.3105798,-0.8781912,-0.3637587,-0.3105798,-0.8781912,-0.3637587,-0.3105798,-0.8781912,-0.3637587,-0.3105798,-0.8781912,0.3637587,-0.3105798,0.8781912,0.3637587,-0.3105798,0.8781912,0.3637587,-0.3105798,0.8781912,0.3637587,-0.3105798,0.8781912,0.3637587,-0.3105798,0.8781912,0.3637587,-0.3105798,0.8781912,-0.8781912,-0.3105798,-0.3637587,-0.8781912,-0.3105798,-0.3637587,-0.8781912,-0.3105798,-0.3637587,-0.8781912,-0.3105798,-0.3637587,-0.8781912,-0.3105798,-0.3637587,-0.8781912,-0.3105798,-0.3637587,-0.8781912,-0.3105798,0.3637587,-0.8781912,-0.3105798,0.3637587,-0.8781912,-0.3105798,0.3637587,-0.8781912,-0.3105798,0.3637587,-0.8781912,-0.3105798,0.3637587,-0.8781912,-0.3105798,0.3637587,-0.3637587,-0.3105798,0.8781912,-0.3637587,-0.3105798,0.8781912,-0.3637587,-0.3105798,0.8781912,-0.3637587,-0.3105798,0.8781912,-0.3637587,-0.3105798,0.8781912,-0.3637587,-0.3105798,0.8781912,-0.3637587,0.3105798,-0.8781912,-0.3637587,0.3105798,-0.8781912,-0.3637587,0.3105798,-0.8781912,-0.3637587,0.3105798,-0.8781912,-0.3637587,0.3105798,-0.8781912,-0.3637587,0.3105798,-0.8781912,-0.3637587,0.3105798,0.8781912,-0.3637587,0.3105798,0.8781912,-0.3637587,0.3105798,0.8781912,-0.3637587,0.3105798,0.8781912,-0.3637587,0.3105798,0.8781912,-0.3637587,0.3105798,0.8781912,-0.8781912,0.3105798,0.3637587,-0.8781912,0.3105798,0.3637587,-0.8781912,0.3105798,0.3637587,-0.8781912,0.3105798,0.3637587,-0.8781912,0.3105798,0.3637587,-0.8781912,0.3105798,0.3637587,0.2540288,0.747902,0.6132796,0.2540288,0.747902,0.6132796,0.2540288,0.747902,0.6132796,0.2540288,0.747902,0.6132796,0.2540288,0.747902,0.6132796,0.2540288,0.747902,0.6132796,0.2540288,0.747902,-0.6132796,0.2540288,0.747902,-0.6132796,0.2540288,0.747902,-0.6132796,0.2540288,0.747902,-0.6132796,0.2540288,0.747902,-0.6132796,0.2540288,0.747902,-0.6132796,-0.2540288,0.747902,-0.6132796,-0.2540288,0.747902,-0.6132796,-0.2540288,0.747902,-0.6132796,-0.2540288,0.747902,-0.6132796,-0.2540288,0.747902,-0.6132796,-0.2540288,0.747902,-0.6132796,0.6132796,0.747902,0.2540288,0.6132796,0.747902,0.2540288,0.6132796,0.747902,0.2540288,0.6132796,0.747902,0.2540288,0.6132796,0.747902,0.2540288,0.6132796,0.747902,0.2540288,0.6132796,0.747902,-0.2540288,0.6132796,0.747902,-0.2540288,0.6132796,0.747902,-0.2540288,0.6132796,0.747902,-0.2540288,0.6132796,0.747902,-0.2540288,0.6132796,0.747902,-0.2540288,-0.6132796,0.747902,0.2540288,-0.6132796,0.747902,0.2540288,-0.6132796,0.747902,0.2540288,-0.6132796,0.747902,0.2540288,-0.6132796,0.747902,0.2540288,-0.6132796,0.747902,0.2540288,-0.8781912,0.3105798,-0.3637587,-0.8781912,0.3105798,-0.3637587,-0.8781912,0.3105798,-0.3637587,-0.8781912,0.3105798,-0.3637587,-0.8781912,0.3105798,-0.3637587,-0.8781912,0.3105798,-0.3637587,-0.6132796,0.747902,-0.2540288,-0.6132796,0.747902,-0.2540288,-0.6132796,0.747902,-0.2540288,-0.6132796,0.747902,-0.2540288,-0.6132796,0.747902,-0.2540288,-0.6132796,0.747902,-0.2540288,0.3637587,0.3105798,0.8781912,0.3637587,0.3105798,0.8781912,0.3637587,0.3105798,0.8781912,0.3637587,0.3105798,0.8781912,0.3637587,0.3105798,0.8781912,0.3637587,0.3105798,0.8781912,-0.2540288,0.747902,0.6132796,-0.2540288,0.747902,0.6132796,-0.2540288,0.747902,0.6132796,-0.2540288,0.747902,0.6132796,-0.2540288,0.747902,0.6132796,-0.2540288,0.747902,0.6132796,0.8781912,0.3105798,-0.3637587,0.8781912,0.3105798,-0.3637587,0.8781912,0.3105798,-0.3637587,0.8781912,0.3105798,-0.3637587,0.8781912,0.3105798,-0.3637587,0.8781912,0.3105798,-0.3637587,0.8781912,0.3105798,0.3637587,0.8781912,0.3105798,0.3637587,0.8781912,0.3105798,0.3637587,0.8781912,0.3105798,0.3637587,0.8781912,0.3105798,0.3637587,0.8781912,0.3105798,0.3637587,0.3637587,0.3105798,-0.8781912,0.3637587,0.3105798,-0.8781912,0.3637587,0.3105798,-0.8781912,0.3637587,0.3105798,-0.8781912,0.3637587,0.3105798,-0.8781912,0.3637587,0.3105798,-0.8781912,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0.162697,-0.9866762,0,0.162697,-0.9866762,0,0.162697,-0.9866762,0,0.162697,-0.9866762,0,0.162697,-0.9866762,0,0.162697,-0.9866762,0.9866762,0.162697,0,0.9866762,0.162697,0,0.9866762,0.162697,0,0.9866762,0.162697,0,0.9866762,0.162697,0,0.9866762,0.162697,0,0,0.162697,0.9866762,0,0.162697,0.9866762,0,0.162697,0.9866762,0,0.162697,0.9866762,0,0.162697,0.9866762,0,0.162697,0.9866762,-0.9866762,0.162697,0,-0.9866762,0.162697,0,-0.9866762,0.162697,0,-0.9866762,0.162697,0,-0.9866762,0.162697,0,-0.9866762,0.162697,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.3041546,-0.9526226,0,0.3041546,-0.9526226,0,0.3041546,-0.9526226,0,0.3041546,-0.9526226,0,0.3041546,-0.9526226,0,0.3041546,-0.9526226,0,-0.3041546,0.9526226,0,-0.3041546,0.9526226,0,-0.3041546,0.9526226,0,-0.3041546,0.9526226,0,-0.3041546,0.9526226,0,-0.3041546,0.9526226,0,0.7348109,-0.6782718,0,0.7348109,-0.6782718,0,0.7348109,-0.6782718,0,-0.7348109,0.6782718,0,-0.7348109,0.6782718,0,-0.7348109,0.6782718,0,0,-0.6782718,0.7348109,0,-0.6782718,0.7348109,0,-0.6782718,0.7348109,0,0.6782718,-0.7348109,0,0.6782718,-0.7348109,0,0.6782718,-0.7348109,0,-0.9526226,0.3041546,0,-0.9526226,0.3041546,0,-0.9526226,0.3041546,0,-0.9526226,0.3041546,0,-0.9526226,0.3041546,0,-0.9526226,0.3041546,0,0.9526226,-0.3041546,0,0.9526226,-0.3041546,0,0.9526226,-0.3041546,0,0.9526226,-0.3041546,0,0.9526226,-0.3041546,0,0.9526226,-0.3041546,-0.7348109,-0.6782718,0,-0.7348109,-0.6782718,0,-0.7348109,-0.6782718,0,0.7348109,0.6782718,0,0.7348109,0.6782718,0,0.7348109,0.6782718,0,-0.3041546,-0.9526226,0,-0.3041546,-0.9526226,0,-0.3041546,-0.9526226,0,-0.3041546,-0.9526226,0,-0.3041546,-0.9526226,0,-0.3041546,-0.9526226,0,0.3041546,0.9526226,0,0.3041546,0.9526226,0,0.3041546,0.9526226,0,0.3041546,0.9526226,0,0.3041546,0.9526226,0,0.3041546,0.9526226,0,0,-0.6782718,-0.7348109,0,-0.6782718,-0.7348109,0,-0.6782718,-0.7348109,0,0.6782718,0.7348109,0,0.6782718,0.7348109,0,0.6782718,0.7348109,0,-0.9526226,-0.3041546,0,-0.9526226,-0.3041546,0,-0.9526226,-0.3041546,0,-0.9526226,-0.3041546,0,-0.9526226,-0.3041546,0,-0.9526226,-0.3041546,0,0.9526226,0.3041546,0,0.9526226,0.3041546,0,0.9526226,0.3041546,0,0.9526226,0.3041546,0,0.9526226,0.3041546,0,0.9526226,0.3041546
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *456 {
				a: -1.071212,2.442068,1.071212,2.442068,-0.7141415,1.289451,0.7141415,1.289451,-0.7141415,1.289451,-1.071212,2.442068,0.7141415,1.289451,1.071212,2.442068,0.7141415,1.289451,-0.7141415,1.289451,1.071212,2.442068,-1.071212,2.442068,1.071212,2.442068,0.7141415,1.289451,-1.071212,2.442068,-0.7141415,1.289451,1.071212,2.442068,0.7141415,1.289451,-1.071212,2.442068,-0.7141415,1.289451,1.071212,2.442068,0.7141415,1.289451,-1.071212,2.442068,-0.7141415,1.289451,-1.071212,2.442068,1.071212,2.442068,-0.7141415,1.289451,0.7141415,1.289451,1.866142,-3.197442E-14,1.319561,-1.319561,1.319561,1.319561,-3.552714E-15,1.866142,-3.552714E-15,-1.866142,-1.319561,1.319561,-1.319561,-1.319561,-1.866142,-3.197442E-14,-1.071212,2.442068,1.071212,2.442068,-0.7141415,1.289451,0.7141415,1.289451,1.071212,1.530482,-1.071212,1.530482,1.278313,3.14033,-1.278313,3.14033,1.071212,1.530482,-1.071212,1.530482,1.278313,3.14033,-1.278313,3.14033,1.278313,3.14033,1.071212,1.530482,-1.278313,3.14033,-1.071212,1.530482,1.071212,1.530482,-1.071212,1.530482,1.278313,3.14033,-1.278313,3.14033,1.071212,1.530482,-1.071212,1.530482,1.278313,3.14033,-1.278313,3.14033,-1.278313,3.14033,1.278313,3.14033,-1.071212,1.530482,1.071212,1.530482,-1.071212,1.530482,-1.278313,3.14033,1.071212,1.530482,1.278313,3.14033,1.071212,1.530482,-1.071212,1.530482,1.278313,3.14033,-1.278313,3.14033,1.278313,1.223356,-1.278313,1.223356,1.071212,2.833203,-1.071212,2.833203,1.278313,1.223356,-1.278313,1.223356,1.071212,2.833203,-1.071212,2.833203,-1.071212,2.833203,1.071212,2.833203,-1.278313,1.223356,1.278313,1.223356,1.071212,0.6052861,-1.071212,0.6052861,0.7141415,1.757904,-0.7141415,1.757904,-1.071212,0.6052861,-0.7141415,1.757904,1.071212,0.6052861,0.7141415,1.757904,-1.071212,0.6052861,-0.7141415,1.757904,1.071212,0.6052861,0.7141415,1.757904,1.071212,0.6052861,-1.071212,0.6052861,0.7141415,1.757904,-0.7141415,1.757904,-1.071212,0.6052861,-0.7141415,1.757904,1.071212,0.6052861,0.7141415,1.757904,0.7141415,1.757904,1.071212,0.6052861,-0.7141415,1.757904,-1.071212,0.6052861,-1.278313,1.223356,-1.071212,2.833203,1.278313,1.223356,1.071212,2.833203,-0.7141415,1.757904,0.7141415,1.757904,-1.071212,0.6052861,1.071212,0.6052861,1.278313,1.223356,-1.278313,1.223356,1.071212,2.833203,-1.071212,2.833203,1.071212,0.6052861,-1.071212,0.6052861,0.7141415,1.757904,-0.7141415,1.757904,1.278313,1.223356,-1.278313,1.223356,1.071212,2.833203,-1.071212,2.833203,1.071212,2.833203,1.278313,1.223356,-1.071212,2.833203,-1.278313,1.223356,1.278313,1.223356,-1.278313,1.223356,1.071212,2.833203,-1.071212,2.833203,-1.866142,-3.197442E-14,-1.319561,1.319561,-1.319561,-1.319561,3.552714E-15,-1.866142,-0.4665354,-0.4665354,0.4665354,-0.4665354,1.319561,-1.319561,0.4665354,0.4665354,-0.4665354,0.4665354,3.552714E-15,1.866142,1.319561,1.319561,1.866142,-3.197442E-14,-0.1665253,-0.1665253,-0.1665253,0.1665253,0.1665253,-0.1665253,0.1665253,0.1665253,0.2729923,4.485127,-0.2729923,4.485127,0.1665253,5.139516,-0.1665253,5.139516,0.2729923,4.485127,-0.2729923,4.485127,0.1665253,5.139516,-0.1665253,5.139516,0.2729923,4.485127,-0.2729923,4.485127,0.1665253,5.139516,-0.1665253,5.139516,-0.2729923,4.485127,-0.1665253,5.139516,0.2729923,4.485127,0.1665253,5.139516,-0.2729923,-0.2729923,0.2729923,-0.2729923,0.2729923,0.2729923,-0.2729923,0.2729923,-0.3090551,2.556851,0.3090551,2.556851,-0.4665354,1.840718,0.4665354,1.840718,-0.4665354,1.840718,0.3090551,2.556851,-0.3090551,2.556851,0.4665354,1.840718,-4.6347E-08,5.14447,0.3090551,4.312515,-0.3090551,4.312515,-0.3090551,4.312515,0.3090551,4.312515,-4.6347E-08,5.14447,0.3090551,4.312515,-0.3090551,4.312515,-4.634676E-08,5.14447,-4.634676E-08,5.14447,-0.3090551,4.312515,0.3090551,4.312515,0.4665354,1.840718,-0.4665354,1.840718,0.3090551,2.556851,-0.3090551,2.556851,0.3090551,2.556851,-0.4665354,1.840718,0.4665354,1.840718,-0.3090551,2.556851,-0.3090551,4.312515,-3.939491E-08,5.14447,0.3090551,4.312515,0.3090551,4.312515,-3.939491E-08,5.14447,-0.3090551,4.312515,0.4665354,1.840718,-0.4665354,1.840718,0.3090551,2.556851,-0.3090551,2.556851,0.3090551,2.556851,-0.4665354,1.840718,0.4665354,1.840718,-0.3090551,2.556851,0.3090551,4.312514,-0.3090551,4.312514,-3.939489E-08,5.14447,-3.939489E-08,5.14447,-0.3090551,4.312514,0.3090551,4.312514,-0.4665354,1.840718,-0.3090551,2.556851,0.4665354,1.840718,0.3090551,2.556851,0.4665354,1.840718,-0.3090551,2.556851,-0.4665354,1.840718,0.3090551,2.556851
				}
			UVIndex: *372 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,10,9,11,9,10,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,29,31,33,32,31,34,32,33,35,34,33,36,38,37,39,37,38,40,42,41,43,41,42,44,46,45,47,45,46,48,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,60,62,61,63,61,62,64,66,65,67,65,66,68,70,69,71,69,70,72,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,86,85,87,85,86,88,90,89,91,89,90,92,94,93,95,93,94,96,98,97,99,97,98,100,102,101,103,101,102,104,106,105,107,105,106,108,110,109,111,109,110,112,114,113,115,113,114,116,118,117,119,117,118,120,122,121,123,121,122,124,126,125,127,125,126,128,130,129,131,129,130,132,134,133,135,133,134,136,138,137,139,137,138,140,137,139,141,140,139,142,141,139,143,141,142,140,144,137,145,137,144,143,145,144,146,145,143,142,146,143,147,146,142,148,150,149,151,149,150,152,154,153,155,153,154,156,158,157,159,157,158,160,162,161,163,161,162,164,166,165,167,165,166,144,140,168,141,168,140,169,168,141,170,169,141,168,171,144,143,144,171,170,143,171,141,143,170,172,174,173,175,173,174,176,178,177,177,179,176,180,182,181,183,185,184,186,188,187,189,191,190,192,194,193,195,193,194,196,198,197,197,199,196,200,202,201,203,205,204,206,208,207,209,207,208,210,212,211,211,213,210,214,216,215,217,219,218,220,222,221,223,221,222,224,226,225,225,227,224
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *124 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 20302, "Material::red", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.9098039,0.3333333,0.3254902
			P: "DiffuseColor", "Color", "", "A",0.9098039,0.3333333,0.3254902
		}
	}

	Material: 17522, "Material::green", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.3411765,0.7372549,0.5921569
			P: "DiffuseColor", "Color", "", "A",0.3411765,0.7372549,0.5921569
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh tomato, Model::RootNode
	C: "OO",4958426700725659643,0

	;Geometry::, Model::Mesh tomato
	C: "OO",4681535035854965672,4958426700725659643

	;Material::red, Model::Mesh tomato
	C: "OO",20302,4958426700725659643

	;Material::green, Model::Mesh tomato
	C: "OO",17522,4958426700725659643

}
