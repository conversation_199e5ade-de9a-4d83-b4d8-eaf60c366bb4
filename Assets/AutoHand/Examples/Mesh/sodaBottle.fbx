; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2020
		Month: 3
		Day: 4
		Hour: 23
		Minute: 19
		Second: 28
		Millisecond: 468
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "sodaBottle.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "sodaBottle.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4794129346284245276, "Model::sodaBottle", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5397221950341057664, "Geometry::", "Mesh" {
		Vertices: *504 {
			a: 0.6975135,0,-7.219114E-15,0.3487568,0,-0.6040644,0.3487568,0,0.6040644,-0.3487568,0,0.6040644,-0.3487568,0,-0.6040644,-0.6975135,0,-7.219114E-15,-0.3487568,0,-0.6040644,0.3487568,0,-0.6040644,-0.45123,0.19224,-0.7815533,0.45123,0.19224,-0.7815533,-0.3487568,0,-0.6040644,-0.45123,0.19224,-0.7815533,-0.6975135,0,-7.219114E-15,-0.90246,0.19224,-7.219114E-15,-0.3487568,0,0.6040644,-0.6975135,0,-7.219114E-15,-0.45123,0.19224,0.7815533,-0.90246,0.19224,-7.219114E-15,0.90246,0.19224,-7.219114E-15,0.45123,0.19224,-0.7815533,0.6975135,0,-7.219114E-15,0.3487568,0,-0.6040644,0.3487568,0,0.6040644,-0.3487568,0,0.6040644,0.45123,0.19224,0.7815533,-0.45123,0.19224,0.7815533,0.90246,0.19224,-7.219114E-15,0.6975135,0,-7.219114E-15,0.45123,0.19224,0.7815533,0.3487568,0,0.6040644,-0.5064561,3.90412,7.219114E-15,-0.3031343,5.20412,2.165734E-14,-0.2532281,3.90412,0.4386039,-0.1515672,5.20412,0.262522,0.2532281,3.90412,0.4386039,-0.2532281,3.90412,0.4386039,0.1515672,5.20412,0.262522,-0.1515672,5.20412,0.262522,-0.2532281,3.90412,-0.4386039,0.2532281,3.90412,-0.4386039,-0.1515672,5.20412,-0.262522,0.1515672,5.20412,-0.262522,0.2532281,3.90412,-0.4386039,0.5064561,3.90412,7.219114E-15,0.1515672,5.20412,-0.262522,0.3031343,5.20412,2.165734E-14,-0.2532281,3.90412,-0.4386039,-0.1515672,5.20412,-0.262522,-0.5064561,3.90412,7.219114E-15,-0.3031343,5.20412,2.165734E-14,0.5064561,3.90412,7.219114E-15,0.2532281,3.90412,0.4386039,0.3031343,5.20412,2.165734E-14,0.1515672,5.20412,0.262522,0.90246,0.19224,-7.219114E-15,0.45123,0.19224,0.7815533,0.7629336,1.50412,3.609557E-15,0.3814668,1.50412,0.6607199,0.45123,0.19224,-0.7815533,0.90246,0.19224,-7.219114E-15,0.3814668,1.50412,-0.6607199,0.7629336,1.50412,3.609557E-15,-0.90246,0.19224,-7.219114E-15,-0.7629336,1.50412,3.609557E-15,-0.45123,0.19224,0.7815533,-0.3814668,1.50412,0.6607199,-0.45123,0.19224,-0.7815533,0.45123,0.19224,-0.7815533,-0.3814668,1.50412,-0.6607199,0.3814668,1.50412,-0.6607199,0.45123,0.19224,0.7815533,-0.45123,0.19224,0.7815533,0.3814668,1.50412,0.6607199,-0.3814668,1.50412,0.6607199,-0.45123,0.19224,-0.7815533,-0.3814668,1.50412,-0.6607199,-0.90246,0.19224,-7.219114E-15,-0.7629336,1.50412,3.609557E-15,-0.5064561,3.90412,7.219114E-15,-0.2532281,3.90412,0.4386039,-0.8341408,3.20412,1.082867E-14,-0.4170704,3.20412,0.7223871,0.4170704,3.20412,-0.7223871,0.8341408,3.20412,1.082867E-14,0.2532281,3.90412,-0.4386039,0.5064561,3.90412,7.219114E-15,0.5064561,3.90412,7.219114E-15,0.8341408,3.20412,1.082867E-14,0.2532281,3.90412,0.4386039,0.4170704,3.20412,0.7223871,-0.4170704,3.20412,-0.7223871,-0.2532281,3.90412,-0.4386039,-0.8341408,3.20412,1.082867E-14,-0.5064561,3.90412,7.219114E-15,-0.4170704,3.20412,-0.7223871,0.4170704,3.20412,-0.7223871,-0.2532281,3.90412,-0.4386039,0.2532281,3.90412,-0.4386039,0.4170704,3.20412,0.7223871,-0.4170704,3.20412,0.7223871,0.2532281,3.90412,0.4386039,-0.2532281,3.90412,0.4386039,0.3657822,5.20412,2.165734E-14,0.3031343,5.20412,2.165734E-14,0.1828911,5.20412,0.3167767,0.1515672,5.20412,0.262522,-0.1828911,5.20412,0.3167767,-0.1515672,5.20412,0.262522,-0.3031343,5.20412,2.165734E-14,-0.3657822,5.20412,2.165734E-14,0.1828911,5.20412,-0.3167767,0.1515672,5.20412,-0.262522,-0.1828911,5.20412,-0.3167767,-0.1515672,5.20412,-0.262522,0.3657822,5.60412,2.165734E-14,0.1828911,5.60412,0.3167767,0.1828911,5.60412,-0.3167767,-0.1828911,5.60412,-0.3167767,-0.1828911,5.60412,0.3167767,-0.3657822,5.60412,2.165734E-14,0.1828911,5.20412,0.3167767,-0.1828911,5.20412,0.3167767,0.1828911,5.60412,0.3167767,-0.1828911,5.60412,0.3167767,0.1828911,5.60412,-0.3167767,0.1828911,5.20412,-0.3167767,0.3657822,5.60412,2.165734E-14,0.3657822,5.20412,2.165734E-14,-0.3657822,5.20412,2.165734E-14,-0.3657822,5.60412,2.165734E-14,-0.1828911,5.20412,0.3167767,-0.1828911,5.60412,0.3167767,0.3657822,5.60412,2.165734E-14,0.3657822,5.20412,2.165734E-14,0.1828911,5.60412,0.3167767,0.1828911,5.20412,0.3167767,-0.1828911,5.20412,-0.3167767,0.1828911,5.20412,-0.3167767,-0.1828911,5.60412,-0.3167767,0.1828911,5.60412,-0.3167767,-0.1828911,5.20412,-0.3167767,-0.1828911,5.60412,-0.3167767,-0.3657822,5.20412,2.165734E-14,-0.3657822,5.60412,2.165734E-14,-0.4170704,3.20412,-0.7223871,-0.8341408,3.20412,1.082867E-14,-0.3814668,1.50412,-0.6607199,-0.7629336,1.50412,3.609557E-15,0.7629336,1.50412,3.609557E-15,0.3814668,1.50412,0.6607199,0.8341408,3.20412,1.082867E-14,0.4170704,3.20412,0.7223871,0.4170704,3.20412,-0.7223871,0.3814668,1.50412,-0.6607199,0.8341408,3.20412,1.082867E-14,0.7629336,1.50412,3.609557E-15,0.3814668,1.50412,0.6607199,-0.3814668,1.50412,0.6607199,0.4170704,3.20412,0.7223871,-0.4170704,3.20412,0.7223871,-0.7629336,1.50412,3.609557E-15,-0.8341408,3.20412,1.082867E-14,-0.3814668,1.50412,0.6607199,-0.4170704,3.20412,0.7223871,-0.3814668,1.50412,-0.6607199,0.3814668,1.50412,-0.6607199,-0.4170704,3.20412,-0.7223871,0.4170704,3.20412,-0.7223871
		} 
		PolygonVertexIndex: *276 {
			a: 0,2,-2,3,1,-3,4,1,-4,5,4,-4,6,8,-8,9,7,-9,10,12,-12,13,11,-13,14,16,-16,17,15,-17,18,20,-20,21,19,-21,22,24,-24,25,23,-25,26,28,-28,29,27,-29,30,32,-32,33,31,-33,34,36,-36,37,35,-37,38,40,-40,41,39,-41,42,44,-44,45,43,-45,46,48,-48,49,47,-49,50,52,-52,53,51,-53,54,56,-56,57,55,-57,58,60,-60,61,59,-61,62,64,-64,65,63,-65,66,68,-68,69,67,-69,70,72,-72,73,71,-73,74,76,-76,77,75,-77,78,80,-80,81,79,-81,82,84,-84,85,83,-85,86,88,-88,89,87,-89,90,92,-92,93,91,-93,94,96,-96,97,95,-97,98,100,-100,101,99,-101,102,104,-104,105,103,-105,106,105,-105,107,105,-107,108,107,-107,109,108,-107,102,103,-111,111,110,-104,112,110,-112,109,112,-109,113,112,-112,108,112,-114,114,116,-116,117,115,-117,118,115,-118,119,118,-118,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,130,-130,131,129,-131,132,134,-134,135,133,-135,136,138,-138,139,137,-139,140,142,-142,143,141,-143,144,146,-146,147,145,-147,148,150,-150,151,149,-151,152,154,-154,155,153,-155,156,158,-158,159,157,-159,160,162,-162,163,161,-163,164,166,-166,167,165,-167
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *828 {
				a: 0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.6783556,-0.7347338,0,-0.6783556,-0.7347338,0,-0.6783556,-0.7347338,0,-0.6783556,-0.7347338,0,-0.6783556,-0.7347338,0,-0.6783556,-0.7347338,-0.6362981,-0.6783556,-0.3673669,-0.6362981,-0.6783556,-0.3673669,-0.6362981,-0.6783556,-0.3673669,-0.6362981,-0.6783556,-0.3673669,-0.6362981,-0.6783556,-0.3673669,-0.6362981,-0.6783556,-0.3673669,-0.6362981,-0.6783556,0.3673669,-0.6362981,-0.6783556,0.3673669,-0.6362981,-0.6783556,0.3673669,-0.6362981,-0.6783556,0.3673669,-0.6362981,-0.6783556,0.3673669,-0.6362981,-0.6783556,0.3673669,0.6362981,-0.6783556,-0.3673669,0.6362981,-0.6783556,-0.3673669,0.6362981,-0.6783556,-0.3673669,0.6362981,-0.6783556,-0.3673669,0.6362981,-0.6783556,-0.3673669,0.6362981,-0.6783556,-0.3673669,0,-0.6783556,0.7347338,0,-0.6783556,0.7347338,0,-0.6783556,0.7347338,0,-0.6783556,0.7347338,0,-0.6783556,0.7347338,0,-0.6783556,0.7347338,0.6362981,-0.6783556,0.3673669,0.6362981,-0.6783556,0.3673669,0.6362981,-0.6783556,0.3673669,0.6362981,-0.6783556,0.3673669,0.6362981,-0.6783556,0.3673669,0.6362981,-0.6783556,0.3673669,-0.858189,0.134222,0.4954757,-0.858189,0.134222,0.4954757,-0.858189,0.134222,0.4954757,-0.858189,0.134222,0.4954757,-0.858189,0.134222,0.4954757,-0.858189,0.134222,0.4954757,0,0.134222,0.9909513,0,0.134222,0.9909513,0,0.134222,0.9909513,0,0.134222,0.9909513,0,0.134222,0.9909513,0,0.134222,0.9909513,0,0.134222,-0.9909513,0,0.134222,-0.9909513,0,0.134222,-0.9909513,0,0.134222,-0.9909513,0,0.134222,-0.9909513,0,0.134222,-0.9909513,0.858189,0.134222,-0.4954757,0.858189,0.134222,-0.4954757,0.858189,0.134222,-0.4954757,0.858189,0.134222,-0.4954757,0.858189,0.134222,-0.4954757,0.858189,0.134222,-0.4954757,-0.858189,0.134222,-0.4954757,-0.858189,0.134222,-0.4954757,-0.858189,0.134222,-0.4954757,-0.858189,0.134222,-0.4954757,-0.858189,0.134222,-0.4954757,-0.858189,0.134222,-0.4954757,0.858189,0.134222,0.4954757,0.858189,0.134222,0.4954757,0.858189,0.134222,0.4954757,0.858189,0.134222,0.4954757,0.858189,0.134222,0.4954757,0.858189,0.134222,0.4954757,0.8623751,0.09171882,0.4978925,0.8623751,0.09171882,0.4978925,0.8623751,0.09171882,0.4978925,0.8623751,0.09171882,0.4978925,0.8623751,0.09171882,0.4978925,0.8623751,0.09171882,0.4978925,0.8623751,0.09171882,-0.4978925,0.8623751,0.09171882,-0.4978925,0.8623751,0.09171882,-0.4978925,0.8623751,0.09171882,-0.4978925,0.8623751,0.09171882,-0.4978925,0.8623751,0.09171882,-0.4978925,-0.8623751,0.09171882,0.4978925,-0.8623751,0.09171882,0.4978925,-0.8623751,0.09171882,0.4978925,-0.8623751,0.09171882,0.4978925,-0.8623751,0.09171882,0.4978925,-0.8623751,0.09171882,0.4978925,0,0.09171883,-0.9957849,0,0.09171883,-0.9957849,0,0.09171883,-0.9957849,0,0.09171883,-0.9957849,0,0.09171883,-0.9957849,0,0.09171883,-0.9957849,0,0.09171883,0.9957849,0,0.09171883,0.9957849,0,0.09171883,0.9957849,0,0.09171883,0.9957849,0,0.09171883,0.9957849,0,0.09171883,0.9957849,-0.8623751,0.09171882,-0.4978925,-0.8623751,0.09171882,-0.4978925,-0.8623751,0.09171882,-0.4978925,-0.8623751,0.09171882,-0.4978925,-0.8623751,0.09171882,-0.4978925,-0.8623751,0.09171882,-0.4978925,-0.80258,0.3757044,0.4633698,-0.80258,0.3757044,0.4633698,-0.80258,0.3757044,0.4633698,-0.80258,0.3757044,0.4633698,-0.80258,0.3757044,0.4633698,-0.80258,0.3757044,0.4633698,0.80258,0.3757044,-0.4633698,0.80258,0.3757044,-0.4633698,0.80258,0.3757044,-0.4633698,0.80258,0.3757044,-0.4633698,0.80258,0.3757044,-0.4633698,0.80258,0.3757044,-0.4633698,0.80258,0.3757044,0.4633698,0.80258,0.3757044,0.4633698,0.80258,0.3757044,0.4633698,0.80258,0.3757044,0.4633698,0.80258,0.3757044,0.4633698,0.80258,0.3757044,0.4633698,-0.80258,0.3757044,-0.4633698,-0.80258,0.3757044,-0.4633698,-0.80258,0.3757044,-0.4633698,-0.80258,0.3757044,-0.4633698,-0.80258,0.3757044,-0.4633698,-0.80258,0.3757044,-0.4633698,0,0.3757045,-0.9267395,0,0.3757045,-0.9267395,0,0.3757045,-0.9267395,0,0.3757045,-0.9267395,0,0.3757045,-0.9267395,0,0.3757045,-0.9267395,0,0.3757045,0.9267395,0,0.3757045,0.9267395,0,0.3757045,0.9267395,0,0.3757045,0.9267395,0,0.3757045,0.9267395,0,0.3757045,0.9267395,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8654562,-0.03625098,-0.4996714,-0.8654562,-0.03625098,-0.4996714,-0.8654562,-0.03625098,-0.4996714,-0.8654562,-0.03625098,-0.4996714,-0.8654562,-0.03625098,-0.4996714,-0.8654562,-0.03625098,-0.4996714,0.8654562,-0.03625098,0.4996714,0.8654562,-0.03625098,0.4996714,0.8654562,-0.03625098,0.4996714,0.8654562,-0.03625098,0.4996714,0.8654562,-0.03625098,0.4996714,0.8654562,-0.03625098,0.4996714,0.8654562,-0.03625098,-0.4996714,0.8654562,-0.03625098,-0.4996714,0.8654562,-0.03625098,-0.4996714,0.8654562,-0.03625098,-0.4996714,0.8654562,-0.03625098,-0.4996714,0.8654562,-0.03625098,-0.4996714,0,-0.03625097,0.9993427,0,-0.03625097,0.9993427,0,-0.03625097,0.9993427,0,-0.03625097,0.9993427,0,-0.03625097,0.9993427,0,-0.03625097,0.9993427,-0.8654562,-0.03625098,0.4996714,-0.8654562,-0.03625098,0.4996714,-0.8654562,-0.03625098,0.4996714,-0.8654562,-0.03625098,0.4996714,-0.8654562,-0.03625098,0.4996714,-0.8654562,-0.03625098,0.4996714,0,-0.03625097,-0.9993427,0,-0.03625097,-0.9993427,0,-0.03625097,-0.9993427,0,-0.03625097,-0.9993427,0,-0.03625097,-0.9993427,0,-0.03625097,-0.9993427
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *336 {
				a: 2.746116,-2.842171E-14,1.373058,-2.378206,1.373058,2.378206,-1.373058,2.378206,-1.373058,-2.378206,-2.746116,-2.842171E-14,1.373058,1.61327,-1.373058,1.61327,1.776496,2.643371,-1.776496,2.643371,-1.373058,1.61327,-1.776496,2.643371,1.373058,1.61327,1.776496,2.643371,1.373058,1.61327,-1.373058,1.61327,1.776496,2.643371,-1.776496,2.643371,-1.776496,2.643371,1.776496,2.643371,-1.373058,1.61327,1.373058,1.61327,1.373058,1.61327,-1.373058,1.61327,1.776496,2.643371,-1.776496,2.643371,1.776496,2.643371,1.373058,1.61327,-1.776496,2.643371,-1.373058,1.61327,-0.9969609,14.99969,-0.5967211,20.16454,0.9969609,14.99969,0.5967211,20.16454,0.9969609,14.99969,-0.9969609,14.99969,0.5967211,20.16454,-0.5967211,20.16454,0.9969609,14.99969,-0.9969609,14.99969,0.5967211,20.16454,-0.5967211,20.16454,0.9969609,14.99969,-0.9969609,14.99969,0.5967211,20.16454,-0.5967211,20.16454,-0.9969609,14.99969,-0.5967211,20.16454,0.9969609,14.99969,0.5967211,20.16454,0.9969609,14.99969,-0.9969609,14.99969,0.5967211,20.16454,-0.5967211,20.16454,1.776496,0.4714431,-1.776496,0.4714431,1.501838,5.658187,-1.501838,5.658187,1.776496,0.4714431,-1.776496,0.4714431,1.501838,5.658187,-1.501838,5.658187,-1.776496,0.4714431,-1.501838,5.658187,1.776496,0.4714431,1.501838,5.658187,1.776496,0.4714431,-1.776496,0.4714431,1.501838,5.658187,-1.501838,5.658187,1.776496,0.4714431,-1.776496,0.4714431,1.501838,5.658187,-1.501838,5.658187,-1.776496,0.4714431,-1.501838,5.658187,1.776496,0.4714431,1.501838,5.658187,-0.9969609,13.59574,0.9969609,13.59574,-1.642009,10.62197,1.642009,10.62197,1.642009,10.62197,-1.642009,10.62197,0.9969609,13.59574,-0.9969609,13.59574,0.9969609,13.59574,1.642009,10.62197,-0.9969609,13.59574,-1.642009,10.62197,-1.642009,10.62197,-0.9969609,13.59574,1.642009,10.62197,0.9969609,13.59574,1.642009,10.62197,-1.642009,10.62197,0.9969609,13.59574,-0.9969609,13.59574,1.642009,10.62197,-1.642009,10.62197,0.9969609,13.59574,-0.9969609,13.59574,1.440087,8.526513E-14,1.193442,8.526513E-14,0.7200437,1.247152,0.5967211,1.033551,-0.7200437,1.247152,-0.5967211,1.033551,-1.193442,8.526513E-14,-1.440087,8.526513E-14,0.7200437,-1.247152,0.5967211,-1.033551,-0.7200437,-1.247152,-0.5967211,-1.033551,-1.440087,8.526513E-14,-0.7200437,1.247152,-0.7200437,-1.247152,0.7200437,-1.247152,0.7200437,1.247152,1.440087,8.526513E-14,0.7200437,20.48866,-0.7200437,20.48866,0.7200437,22.06347,-0.7200437,22.06347,0.7200437,22.06347,0.7200437,20.48866,-0.7200437,22.06347,-0.7200437,20.48866,-0.7200437,20.48866,-0.7200437,22.06347,0.7200437,20.48866,0.7200437,22.06347,0.7200437,22.06347,0.7200437,20.48866,-0.7200437,22.06347,-0.7200437,20.48866,0.7200437,20.48866,-0.7200437,20.48866,0.7200437,22.06347,-0.7200437,22.06347,-0.7200437,20.48866,-0.7200437,22.06347,0.7200437,20.48866,0.7200437,22.06347,-1.642009,12.70945,1.642009,12.70945,-1.501838,6.012138,1.501838,6.012138,1.501838,6.012138,-1.501838,6.012138,1.642009,12.70945,-1.642009,12.70945,1.642009,12.70945,1.501838,6.012138,-1.642009,12.70945,-1.501838,6.012138,1.501838,6.012138,-1.501838,6.012138,1.642009,12.70945,-1.642009,12.70945,-1.501838,6.012138,-1.642009,12.70945,1.501838,6.012138,1.642009,12.70945,1.501838,6.012138,-1.501838,6.012138,1.642009,12.70945,-1.642009,12.70945
				}
			UVIndex: *276 {
				a: 0,2,1,3,1,2,4,1,3,5,4,3,6,8,7,9,7,8,10,12,11,13,11,12,14,16,15,17,15,16,18,20,19,21,19,20,22,24,23,25,23,24,26,28,27,29,27,28,30,32,31,33,31,32,34,36,35,37,35,36,38,40,39,41,39,40,42,44,43,45,43,44,46,48,47,49,47,48,50,52,51,53,51,52,54,56,55,57,55,56,58,60,59,61,59,60,62,64,63,65,63,64,66,68,67,69,67,68,70,72,71,73,71,72,74,76,75,77,75,76,78,80,79,81,79,80,82,84,83,85,83,84,86,88,87,89,87,88,90,92,91,93,91,92,94,96,95,97,95,96,98,100,99,101,99,100,102,104,103,105,103,104,106,105,104,107,105,106,108,107,106,109,108,106,102,103,110,111,110,103,112,110,111,109,112,108,113,112,111,108,112,113,114,116,115,117,115,116,118,115,117,119,118,117,120,122,121,123,121,122,124,126,125,127,125,126,128,130,129,131,129,130,132,134,133,135,133,134,136,138,137,139,137,138,140,142,141,143,141,142,144,146,145,147,145,146,148,150,149,151,149,150,152,154,153,155,153,154,156,158,157,159,157,158,160,162,161,163,161,162,164,166,165,167,165,166
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *92 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19468, "Material::brownDark", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.6392157,0.3882353,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.6392157,0.3882353,0.2784314
		}
	}

	Material: 20302, "Material::red", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.9098039,0.3333333,0.3254902
			P: "DiffuseColor", "Color", "", "A",0.9098039,0.3333333,0.3254902
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh sodaBottle, Model::RootNode
	C: "OO",4794129346284245276,0

	;Geometry::, Model::Mesh sodaBottle
	C: "OO",5397221950341057664,4794129346284245276

	;Material::brownDark, Model::Mesh sodaBottle
	C: "OO",19468,4794129346284245276

	;Material::red, Model::Mesh sodaBottle
	C: "OO",20302,4794129346284245276

}
