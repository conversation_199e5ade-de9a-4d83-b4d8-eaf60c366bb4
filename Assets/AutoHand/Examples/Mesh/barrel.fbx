; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2020
		Month: 3
		Day: 4
		Hour: 23
		Minute: 19
		Second: 7
		Millisecond: 130
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "barrel.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "barrel.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 4796633471079767399, "Model::barrel", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5203921226452405152, "Geometry::", "Mesh" {
		Vertices: *1452 {
			a: 3.5,1.30049,-0.143026,3.8,1.30049,-0.143026,3.5,1.50049,-0.143026,3.8,2.19108,-0.143026,3.5,2.19108,-0.143026,-1.8,1.98847,-3.394531,-1.8,4.678593,-3.1,-1.8,4.800592,-3.394531,-1.8,6.789062,-1.406061,-1.8,6.494531,-1.284062,-1.8,6.789062,1.406061,-1.8,6.494531,1.284062,-1.8,4.678593,3.1,-1.8,4.800592,3.394531,-1.8,2.110469,3.1,-1.8,2.110469,-3.1,-1.8,1.373512E-15,-1.406061,-1.8,0.2945312,-1.284062,-1.8,1.373512E-15,1.406061,-1.8,0.2945312,1.284062,-1.8,1.98847,3.394531,3.8,2.19108,-0.143026,3.8,1.30049,-0.143026,3.8,2.250324,0,3.8,1.30049,0.143026,3.8,2.19108,0.143026,3.5,1.30049,0.143026,3.5,1.50049,0.143026,3.8,1.30049,0.143026,3.8,2.19108,0.143026,3.5,2.19108,0.143026,-1.8,4.800592,-3.394531,-1.8,6.789062,-1.406061,-2.4,4.800592,-3.394531,-2.4,6.789062,-1.406061,1.9,6.789062,-1.406061,1.9,6.789062,1.406061,1.3,6.789062,-1.406061,1.3,6.789062,1.406061,1.9,1.160516E-15,1.406061,1.9,2.581269E-16,-1.406061,1.3,9.023893E-16,1.406061,1.3,0,-1.406061,1.3,1.98847,3.394531,1.3,4.800592,3.394531,1.9,1.98847,3.394531,1.9,4.800592,3.394531,-2.4,1.98847,-3.394531,-1.8,1.98847,-3.394531,-2.4,4.800592,-3.394531,-1.8,4.800592,-3.394531,1.9,1.98847,-3.394531,1.9,4.678593,-3.1,1.9,4.800592,-3.394531,1.9,6.789062,-1.406061,1.9,6.494531,-1.284062,1.9,6.789062,1.406061,1.9,6.494531,1.284062,1.9,4.678593,3.1,1.9,4.800592,3.394531,1.9,2.110469,3.1,1.9,2.110469,-3.1,1.9,2.581269E-16,-1.406061,1.9,0.2945312,-1.284062,1.9,1.160516E-15,1.406061,1.9,0.2945312,1.284062,1.9,1.98847,3.394531,1.3,1.98847,-3.394531,1.9,1.98847,-3.394531,1.3,4.800592,-3.394531,1.9,4.800592,-3.394531,1.9,2.581269E-16,-1.406061,1.9,1.98847,-3.394531,1.3,0,-1.406061,1.3,1.98847,-3.394531,1.9,4.800592,-3.394531,1.9,6.789062,-1.406061,1.3,4.800592,-3.394531,1.3,6.789062,-1.406061,-1.8,6.789062,-1.406061,-1.8,6.789062,1.406061,-2.4,6.789062,-1.406061,-2.4,6.789062,1.406061,-1.8,1.373512E-15,1.406061,-1.8,1.373512E-15,-1.406061,-2.4,1.115386E-15,1.406061,-2.4,1.115386E-15,-1.406061,-1.8,4.800592,3.394531,-2.4,4.800592,3.394531,-1.8,6.789062,1.406061,-2.4,6.789062,1.406061,1.9,4.800592,3.394531,1.3,4.800592,3.394531,1.9,6.789062,1.406061,1.3,6.789062,1.406061,3.5,2.19108,0.143026,3.5,1.50049,0.143026,3.5,2.131837,0.286052,3.5,1.559733,0.286052,3.5,1.845785,0.4045385,-2.4,1.115386E-15,-1.406061,-1.8,1.373512E-15,-1.406061,-2.4,1.98847,-3.394531,-1.8,1.98847,-3.394531,-1.8,1.373512E-15,1.406061,-2.4,1.115386E-15,1.406061,-1.8,1.98847,3.394531,-2.4,1.98847,3.394531,3.5,1.845785,-0.4045385,3.5,1.559733,-0.286052,3.5,2.131837,-0.286052,3.5,2.19108,-0.143026,3.5,1.50049,-0.143026,1.9,1.160516E-15,1.406061,1.3,9.023893E-16,1.406061,1.9,1.98847,3.394531,1.3,1.98847,3.394531,-2.4,4.800592,-3.394531,-2.4,2.110469,-3.1,-2.4,1.98847,-3.394531,-2.4,1.115386E-15,-1.406061,-2.4,0.2945312,-1.284062,-2.4,1.115386E-15,1.406061,-2.4,0.2945312,1.284062,-2.4,2.110469,3.1,-2.4,1.98847,3.394531,-2.4,4.678593,3.1,-2.4,4.678593,-3.1,-2.4,6.789062,-1.406061,-2.4,6.494531,-1.284062,-2.4,6.789062,1.406061,-2.4,6.494531,1.284062,-2.4,4.800592,3.394531,-1.8,1.98847,3.394531,-2.4,1.98847,3.394531,-1.8,4.800592,3.394531,-2.4,4.800592,3.394531,3.8,1.30049,0.143026,3.8,1.30049,-0.143026,3.5,1.30049,0.143026,3.5,1.30049,-0.143026,1.3,4.800592,-3.394531,1.3,2.110469,-3.1,1.3,1.98847,-3.394531,1.3,0,-1.406061,1.3,0.2945312,-1.284062,1.3,9.023893E-16,1.406061,1.3,0.2945312,1.284062,1.3,2.110469,3.1,1.3,1.98847,3.394531,1.3,4.678593,3.1,1.3,4.678593,-3.1,1.3,6.789062,-1.406061,1.3,6.494531,-1.284062,1.3,6.789062,1.406061,1.3,6.494531,1.284062,1.3,4.800592,3.394531,3.8,2.250324,0,3.8,2.19108,0.143026,2.8,2.250324,0,3.5,2.19108,0.143026,3.5,2.131837,0.286052,2.8,2.131837,0.286052,3.5,1.559733,0.286052,3.5,1.50049,0.143026,2.8,1.559733,0.286052,3.5,1.479515,0.09238795,3.5,1.441246,0,2.8,1.441246,0,2.8,1.845785,-0.4045385,3.5,1.845785,-0.4045385,2.8,2.131837,-0.286052,3.5,2.131837,-0.286052,2.8,1.559733,-0.286052,3.5,1.559733,-0.286052,2.8,1.845785,-0.4045385,3.5,1.845785,-0.4045385,3.5,1.559733,0.286052,2.8,1.559733,0.286052,3.5,1.845785,0.4045385,2.8,1.845785,0.4045385,3.5,1.441246,0,3.5,1.50049,-0.143026,2.8,1.441246,0,3.5,1.559733,-0.286052,2.8,1.559733,-0.286052,3.5,2.131837,-0.286052,3.5,2.19108,-0.143026,2.8,2.131837,-0.286052,2.8,2.250324,0,3.8,2.250324,0,3.8,2.19108,-0.143026,3.5,1.845785,0.4045385,2.8,1.845785,0.4045385,3.5,2.131837,0.286052,2.8,2.131837,0.286052,3.5,1.30049,-0.143026,3.5,1.50049,-0.143026,3.5,1.30049,0.143026,3.5,1.441246,0,3.5,1.479515,0.09238795,3.5,1.50049,0.143026,3.2,0.2945312,-1.284062,3.2,2.110469,-3.1,3,0.2945312,-1.284062,1.9,2.110469,-3.1,1.9,0.2945312,-1.284062,-3.8,2.297676,2.648042,-3.5,2.297676,2.648042,-3.8,4.491386,2.648042,-3.5,4.491386,2.648042,-3.5,0.746489,-1.096855,-3.8,0.746489,-1.096855,-3.5,2.297676,-2.648042,-3.8,2.297676,-2.648042,3.3,6.494531,-1.284062,3.3,6.494531,1.284062,3.1,6.494531,-1.284062,1.9,6.494531,1.284062,1.9,6.494531,-1.284062,3.5,2.250324,-2.762362,3.5,2.110469,-3.1,3.2,2.250324,-2.762362,3.2,2.110469,-3.1,3.2,2.110469,-3.1,3.2,0.2945312,-1.284062,3.2,2.250324,-2.762362,3.2,0.6321694,-1.144208,2.9,0.2945312,1.284062,1.9,0.2945312,1.284062,2.9,2.110469,3.1,1.9,2.110469,3.1,1.9,2.110469,3.1,1.9,4.678593,3.1,2.9,2.110469,3.1,3.3,2.110469,3.1,3.3,4.678593,3.1,-3.5,0.746489,1.096855,-3.5,2.297676,2.648042,-3.8,0.746489,1.096855,-3.8,2.297676,2.648042,-3.5,0.746489,-1.096855,-3.5,0.746489,1.096855,-3.8,0.746489,-1.096855,-3.8,0.746489,1.096855,-3.5,6.042573,1.096855,-3.5,6.042573,-1.096855,-3.8,6.042573,1.096855,-3.8,6.042573,-1.096855,3.1,4.678593,-3.1,3.1,4.538739,-2.762362,3.1,6.494531,-1.284062,3.1,6.156893,-1.144208,-3.5,2.297676,-2.648042,-3.8,2.297676,-2.648042,-3.5,4.491386,-2.648042,-3.8,4.491386,-2.648042,-3.5,4.491386,2.648042,-3.5,6.042573,1.096855,-3.8,4.491386,2.648042,-3.8,6.042573,1.096855,-3.5,4.491386,-2.648042,-3.8,4.491386,-2.648042,-3.5,6.042573,-1.096855,-3.8,6.042573,-1.096855,3.2,0.2945312,-1.284062,3,0.2945312,-1.284062,3.2,0.6321694,-1.144208,3,0.6321694,-1.144208,2.9,0.2945312,1.284062,2.9,0.6321694,1.144208,3,0.2945312,1.284062,3,0.6321694,1.144208,3,0.2945312,1.284062,3,0.2945312,-1.284062,2.9,0.2945312,1.284062,1.9,0.2945312,-1.284062,1.9,0.2945312,1.284062,2.9,0.6321694,1.144208,2.9,0.2945312,1.284062,2.9,2.250324,2.762362,2.9,2.110469,3.1,3.5,4.678593,3.1,3.3,4.678593,3.1,3.5,6.494531,1.284062,3.3,6.494531,1.284062,1.9,4.678593,3.1,1.9,6.494531,1.284062,1.9,2.110469,-3.1,3.2,2.110469,-3.1,1.9,4.678593,-3.1,3.5,2.110469,-3.1,3.1,4.678593,-3.1,3.5,4.678593,-3.1,3.1,4.678593,-3.1,3.1,6.494531,-1.284062,1.9,4.678593,-3.1,1.9,6.494531,-1.284062,3.3,6.156893,1.144208,3.3,6.156893,-1.144208,2.8,6.156893,1.144208,3.1,6.156893,-1.144208,2.8,6.156893,-1.144208,3.5,6.156893,1.144208,3.5,4.538739,2.762362,3.5,6.494531,1.284062,3.5,4.678593,3.1,3.5,2.250324,-2.762362,3.2,2.250324,-2.762362,3.5,4.538739,-2.762362,2.8,4.538739,-2.762362,2.8,2.250324,-2.762362,3.1,4.538739,-2.762362,3.5,4.678593,-3.1,3.5,4.538739,-2.762362,3.1,4.678593,-3.1,3.1,4.538739,-2.762362,3.2,0.6321694,-1.144208,3,0.6321694,-1.144208,3.2,2.250324,-2.762362,2.8,2.250324,-2.762362,2.8,0.6321694,-1.144208,3.1,6.156893,-1.144208,3.3,6.156893,-1.144208,3.1,6.494531,-1.284062,3.3,6.494531,-1.284062,3.3,6.156893,1.144208,3.5,6.156893,1.144208,3.3,6.494531,1.284062,3.5,6.494531,1.284062,3.5,4.678593,-3.1,3.5,2.110469,-3.1,3.5,4.538739,-2.762362,3.5,2.250324,-2.762362,-3.8,4.678593,-3.1,-3.8,2.297676,-2.648042,-3.8,2.110469,-3.1,-3.8,0.2945312,-1.284062,-3.8,0.746489,-1.096855,-3.8,0.2945312,1.284062,-3.8,0.746489,1.096855,-3.8,2.297676,2.648042,-3.8,2.110469,3.1,-3.8,4.491386,2.648042,-3.8,4.491386,-2.648042,-3.8,6.494531,-1.284062,-3.8,6.042573,-1.096855,-3.8,6.494531,1.284062,-3.8,6.042573,1.096855,-3.8,4.678593,3.1,3,0.2945312,-1.284062,3,0.2945312,1.284062,3,0.6321694,-1.144208,3,0.6321694,1.144208,3,0.6321694,-1.144208,3,0.6321694,1.144208,2.8,0.6321694,-1.144208,2.9,0.6321694,1.144208,2.8,0.6321694,1.144208,2.8,0.6321694,0,3.5,4.538739,2.762362,3.5,6.156893,1.144208,3.3,4.538739,2.762362,3.3,6.156893,1.144208,2.8,4.538739,2.762362,2.8,6.156893,1.144208,2.8,2.250324,2.762362,2.9,2.250324,2.762362,2.8,4.538739,2.762362,3.3,2.250324,2.762362,3.3,4.538739,2.762362,3.5,4.678593,3.1,3.5,4.538739,2.762362,3.3,4.678593,3.1,3.3,4.538739,2.762362,3.3,6.494531,-1.284062,3.3,6.156893,-1.144208,3.3,6.494531,1.284062,3.3,6.156893,1.144208,3.3,2.110469,3.1,3.3,2.250324,2.762362,2.9,2.110469,3.1,2.9,2.250324,2.762362,2.9,0.6321694,1.144208,2.9,2.250324,2.762362,2.8,0.6321694,1.144208,2.8,2.250324,2.762362,3.3,4.538739,2.762362,3.3,2.250324,2.762362,3.3,4.678593,3.1,3.3,2.110469,3.1,3.1,4.538739,-2.762362,2.8,4.538739,-2.762362,3.1,6.156893,-1.144208,2.8,6.156893,-1.144208,1.3,4.678593,-3.1,1.3,6.494531,-1.284062,-1.8,4.678593,-3.1,-1.8,6.494531,-1.284062,-3.8,2.110469,3.1,-3.8,4.678593,3.1,-2.4,2.110469,3.1,-2.4,4.678593,3.1,-1.8,2.110469,3.1,-1.8,4.678593,3.1,1.3,2.110469,3.1,1.3,4.678593,3.1,1.3,0.2945312,1.284062,1.3,0.2945312,-1.284062,-1.8,0.2945312,1.284062,-1.8,0.2945312,-1.284062,1.3,0.2945312,1.284062,-1.8,0.2945312,1.284062,1.3,2.110469,3.1,-1.8,2.110469,3.1,-2.4,4.678593,-3.1,-2.4,6.494531,-1.284062,-3.8,4.678593,-3.1,-3.8,6.494531,-1.284062,-2.4,2.110469,-3.1,-3.8,2.110469,-3.1,-2.4,0.2945312,-1.284062,-3.8,0.2945312,-1.284062,1.3,6.494531,-1.284062,1.3,6.494531,1.284062,-1.8,6.494531,-1.284062,-1.8,6.494531,1.284062,-2.4,6.494531,-1.284062,-2.4,6.494531,1.284062,-3.8,6.494531,-1.284062,-3.8,6.494531,1.284062,1.3,0.2945312,-1.284062,1.3,2.110469,-3.1,-1.8,0.2945312,-1.284062,-1.8,2.110469,-3.1,-2.4,0.2945312,1.284062,-2.4,0.2945312,-1.284062,-3.8,0.2945312,1.284062,-3.8,0.2945312,-1.284062,-1.8,2.110469,-3.1,1.3,2.110469,-3.1,-1.8,4.678593,-3.1,1.3,4.678593,-3.1,1.3,4.678593,3.1,-1.8,4.678593,3.1,1.3,6.494531,1.284062,-1.8,6.494531,1.284062,-2.4,2.110469,3.1,-2.4,0.2945312,1.284062,-3.8,2.110469,3.1,-3.8,0.2945312,1.284062,-3.8,2.110469,-3.1,-2.4,2.110469,-3.1,-3.8,4.678593,-3.1,-2.4,4.678593,-3.1,-2.4,6.494531,1.284062,-2.4,4.678593,3.1,-3.8,6.494531,1.284062,-3.8,4.678593,3.1,-3.5,2.297676,-2.648042,-3.5,4.491386,-2.648042,-3.5,0.746489,-1.096855,-3.5,6.042573,-1.096855,-3.5,0.746489,1.096855,-3.5,6.042573,1.096855,-3.5,2.297676,2.648042,-3.5,4.491386,2.648042,2.8,4.538739,-2.762362,2.8,2.250324,-2.762362,2.8,6.156893,-1.144208,2.8,0.6321694,-1.144208,2.8,6.156893,1.144208,2.8,1.845785,-0.4045385,2.8,2.131837,-0.286052,2.8,2.250324,0,2.8,2.131837,0.286052,2.8,1.845785,0.4045385,2.8,0.6321694,0,2.8,1.559733,-0.286052,2.8,1.441246,0,2.8,1.559733,0.286052,2.8,0.6321694,1.144208,2.8,4.538739,2.762362,2.8,2.250324,2.762362
		} 
		PolygonVertexIndex: *924 {
			a: 0,2,-2,3,1,-3,4,3,-3,5,7,-7,8,6,-8,9,6,-9,10,9,-9,11,9,-11,12,11,-11,13,12,-11,14,12,-14,6,15,-6,16,5,-16,17,16,-16,18,16,-18,19,18,-18,14,18,-20,20,18,-15,13,20,-15,21,23,-23,24,22,-24,25,24,-24,26,28,-28,29,27,-29,30,27,-30,31,33,-33,34,32,-34,35,37,-37,38,36,-38,39,41,-41,42,40,-42,43,45,-45,46,44,-46,47,49,-49,50,48,-50,51,53,-53,54,52,-54,55,52,-55,56,55,-55,57,55,-57,58,57,-57,59,58,-57,60,58,-60,52,61,-52,62,51,-62,63,62,-62,64,62,-64,65,64,-64,60,64,-66,66,64,-61,59,66,-61,67,69,-69,70,68,-70,71,73,-73,74,72,-74,75,77,-77,78,76,-78,79,81,-81,82,80,-82,83,85,-85,86,84,-86,87,89,-89,90,88,-90,91,93,-93,94,92,-94,95,97,-97,98,96,-98,99,98,-98,100,102,-102,103,101,-103,104,106,-106,107,105,-107,108,110,-110,111,109,-111,112,109,-112,113,115,-115,116,114,-116,117,119,-119,120,118,-120,121,118,-121,122,121,-121,123,121,-123,124,123,-123,125,124,-123,126,124,-126,118,127,-118,128,117,-128,129,128,-128,130,128,-130,131,130,-130,126,130,-132,132,130,-127,125,132,-127,133,135,-135,136,134,-136,137,139,-139,140,138,-140,141,143,-143,144,142,-144,145,142,-145,146,145,-145,147,145,-147,148,147,-147,149,148,-147,150,148,-150,142,151,-142,152,141,-152,153,152,-152,154,152,-154,155,154,-154,150,154,-156,156,154,-151,149,156,-151,157,159,-159,160,158,-160,161,160,-160,162,161,-160,163,165,-165,166,164,-166,167,166,-166,168,167,-166,169,171,-171,172,170,-172,173,175,-175,176,174,-176,177,179,-179,180,178,-180,181,183,-183,184,182,-184,185,184,-184,186,188,-188,188,189,-188,189,190,-188,191,187,-191,192,194,-194,195,193,-195,196,198,-198,199,197,-199,200,199,-199,201,200,-199,202,204,-204,205,203,-205,206,205,-205,207,209,-209,210,208,-210,211,213,-213,214,212,-214,215,217,-217,218,216,-218,219,218,-218,220,222,-222,223,221,-223,224,226,-226,227,225,-227,228,230,-230,231,229,-231,232,234,-234,235,233,-235,236,233,-236,237,239,-239,240,238,-240,241,243,-243,244,242,-244,245,247,-247,248,246,-248,249,251,-251,252,250,-252,253,255,-255,256,254,-256,257,259,-259,260,258,-260,261,263,-263,264,262,-264,265,267,-267,268,266,-268,269,271,-271,272,270,-272,273,275,-275,276,274,-276,277,276,-276,278,280,-280,281,279,-281,282,284,-284,285,283,-285,286,283,-286,287,286,-286,288,290,-290,291,289,-291,292,291,-291,292,293,-292,294,296,-296,297,295,-297,298,300,-300,301,299,-301,302,301,-301,303,305,-305,306,304,-306,307,309,-309,309,310,-309,311,308,-311,312,310,-310,313,315,-315,316,314,-316,317,319,-319,320,318,-320,321,318,-321,322,324,-324,325,323,-325,326,328,-328,329,327,-329,330,332,-332,333,331,-333,334,336,-336,337,335,-337,338,335,-338,339,338,-338,340,338,-340,341,340,-340,342,341,-340,343,341,-343,335,344,-335,345,334,-345,346,345,-345,347,345,-347,348,347,-347,343,347,-349,349,347,-344,342,349,-344,350,352,-352,353,351,-353,354,356,-356,357,355,-357,358,357,-357,359,358,-357,360,362,-362,363,361,-363,364,363,-363,365,363,-365,366,368,-368,369,367,-369,370,369,-369,371,373,-373,374,372,-374,375,377,-377,378,376,-378,379,381,-381,382,380,-382,383,385,-385,386,384,-386,387,389,-389,390,388,-390,391,393,-393,394,392,-394,395,397,-397,398,396,-398,399,401,-401,402,400,-402,403,405,-405,406,404,-406,407,409,-409,410,408,-410,411,413,-413,414,412,-414,415,417,-417,418,416,-418,419,421,-421,422,420,-422,423,425,-425,426,424,-426,427,429,-429,430,428,-430,431,433,-433,434,432,-434,435,437,-437,438,436,-438,439,441,-441,442,440,-442,443,445,-445,446,444,-446,447,449,-449,450,448,-450,451,453,-453,454,452,-454,455,457,-457,458,456,-458,459,461,-461,462,460,-462,463,462,-462,464,462,-464,465,464,-464,466,464,-466,467,469,-469,470,468,-470,471,470,-470,472,470,-472,473,472,-472,474,473,-472,475,474,-472,476,475,-472,470,472,-478,478,477,-473,479,477,-479,480,477,-480,481,477,-481,476,481,-481,471,481,-477,482,481,-472,483,481,-483
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *2772 {
				a: 0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,0.3826834,-0.9238795,0,0.3826834,-0.9238795,0,0.3826834,-0.9238795,0,0.3826834,-0.9238795,0,0.3826834,-0.9238795,0,0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.3826834,0.9238795,0,-0.3826834,0.9238795,0,-0.3826834,0.9238795,0,-0.3826834,0.9238795,0,-0.3826834,0.9238795,0,-0.3826834,0.9238795,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.9238795,-0.3826834,0,0.3826834,0.9238795,0,0.3826834,0.9238795,0,0.3826834,0.9238795,0,0.3826834,0.9238795,0,0.3826834,0.9238795,0,0.3826834,0.9238795,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.3826834,0.9238795,0,-0.3826834,0.9238795,0,-0.3826834,0.9238795,0,-0.3826834,0.9238795,0,-0.3826834,0.9238795,0,-0.3826834,0.9238795,0,0.3826834,0.9238795,0,0.3826834,0.9238795,0,0.3826834,0.9238795,0,0.3826834,0.9238795,0,0.3826834,0.9238795,0,0.3826834,0.9238795,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.9238795,0.3826834,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,-0.3826834,-0.9238795,0,0.3826834,-0.9238795,0,0.3826834,-0.9238795,0,0.3826834,-0.9238795,0,0.3826834,-0.9238795,0,0.3826834,-0.9238795,0,0.3826834,-0.9238795,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,0,-0.9238795,0.3826834,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,-0.9238795,-0.3826834,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-0.7071068,-0.7071068,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,-0.7071068,0.7071068,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,0,0.7071068,0.7071068,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *968 {
				a: -13.77953,5.120038,-14.96063,5.120038,-13.77953,5.90744,-14.96063,8.6263,-13.77953,8.6263,13.3643,7.828623,12.20472,18.41966,13.3643,18.89997,5.535673,26.72859,5.055362,25.56902,-5.535673,26.72859,-5.055362,25.56902,-12.20472,18.41966,-13.3643,18.89997,-12.20472,8.308934,12.20472,8.308934,5.535673,8.623511E-15,5.055362,1.159572,-5.535673,8.623511E-15,-5.055362,1.159572,-13.3643,7.828623,0.5630944,8.6263,0.5630944,5.120038,0,8.859541,-0.5630944,5.120038,-0.5630944,8.6263,13.77953,5.120038,13.77953,5.90744,14.96063,5.120038,14.96063,8.6263,13.77953,8.6263,7.086614,3.914312,7.086614,14.98566,9.448819,3.914312,9.448819,14.98566,-7.480315,-5.535673,-7.480315,5.535673,-5.11811,-5.535673,-5.11811,5.535673,7.480315,5.535673,7.480315,-5.535673,5.11811,5.535673,5.11811,-5.535673,5.11811,7.828623,5.11811,18.89997,7.480315,7.828623,7.480315,18.89997,9.448819,7.828623,7.086614,7.828623,9.448819,18.89997,7.086614,18.89997,13.3643,7.828623,12.20472,18.41966,13.3643,18.89997,5.535673,26.72859,5.055362,25.56902,-5.535673,26.72859,-5.055362,25.56902,-12.20472,18.41966,-13.3643,18.89997,-12.20472,8.308934,12.20472,8.308934,5.535673,-1.881265E-15,5.055362,1.159572,-5.535673,1.671448E-15,-5.055362,1.159572,-13.3643,7.828623,-5.11811,7.828623,-7.480315,7.828623,-5.11811,18.89997,-7.480315,18.89997,-7.480315,3.914312,-7.480315,14.98566,-5.11811,3.914312,-5.11811,14.98566,-7.480315,3.914312,-7.480315,14.98566,-5.11811,3.914312,-5.11811,14.98566,7.086614,-5.535673,7.086614,5.535673,9.448819,-5.535673,9.448819,5.535673,-7.086614,5.535673,-7.086614,-5.535673,-9.448819,5.535673,-9.448819,-5.535673,-7.086614,3.914312,-9.448819,3.914312,-7.086614,14.98566,-9.448819,14.98566,7.480315,3.914312,5.11811,3.914312,7.480315,14.98566,5.11811,14.98566,-0.5630944,8.6263,-0.5630944,5.90744,-1.126189,8.393059,-1.126189,6.140681,-1.592671,7.26687,9.448819,3.914312,7.086614,3.914312,9.448819,14.98566,7.086614,14.98566,-7.086614,3.914312,-9.448819,3.914312,-7.086614,14.98566,-9.448819,14.98566,1.592671,7.26687,1.126189,6.140681,1.126189,8.393059,0.5630944,8.6263,0.5630944,5.90744,7.480315,3.914312,5.11811,3.914312,7.480315,14.98566,5.11811,14.98566,-13.3643,18.89997,-12.20472,8.308934,-13.3643,7.828623,-5.535673,7.423338E-15,-5.055362,1.159572,5.535673,7.423338E-15,5.055362,1.159572,12.20472,8.308934,13.3643,7.828623,12.20472,18.41966,-12.20472,18.41966,-5.535673,26.72859,-5.055362,25.56902,5.535673,26.72859,5.055362,25.56902,13.3643,18.89997,-7.086614,7.828623,-9.448819,7.828623,-7.086614,18.89997,-9.448819,18.89997,14.96063,0.5630944,14.96063,-0.5630944,13.77953,0.5630944,13.77953,-0.5630944,-13.3643,18.89997,-12.20472,8.308934,-13.3643,7.828623,-5.535673,-1.982509E-15,-5.055362,1.159572,5.535673,1.570205E-15,5.055362,1.159572,12.20472,8.308934,13.3643,7.828623,12.20472,18.41966,-12.20472,18.41966,-5.535673,26.72859,-5.055362,25.56902,5.535673,26.72859,5.055362,25.56902,13.3643,18.89997,14.96063,3.3904,14.96063,2.780911,11.02362,3.3904,13.77953,2.780911,13.77953,2.171422,11.02362,2.171422,13.77953,3.3904,13.77953,2.780911,11.02362,3.3904,13.77953,2.565123,13.77953,2.171422,11.02362,2.171422,-11.02362,6.104223,-13.77953,6.104223,-11.02362,7.323201,-13.77953,7.323201,-11.02362,6.104223,-13.77953,6.104223,-11.02362,7.323201,-13.77953,7.323201,13.77953,6.104223,11.02362,6.104223,13.77953,7.323201,11.02362,7.323201,-13.77953,2.171422,-13.77953,2.780911,-11.02362,2.171422,-13.77953,3.3904,-11.02362,3.3904,-13.77953,2.171422,-13.77953,2.780911,-11.02362,2.171422,-11.02362,3.3904,-14.96063,3.3904,-14.96063,2.780911,13.77953,6.104223,11.02362,6.104223,13.77953,7.323201,11.02362,7.323201,-0.5630944,5.120038,-0.5630944,5.90744,0.5630944,5.120038,-2.862243E-16,5.674199,0.3637321,5.824861,0.5630944,5.90744,-12.59842,4.394622,-12.59842,14.50535,-11.81102,4.394622,-7.480315,14.50535,-7.480315,4.394622,14.96063,9.045969,13.77953,9.045969,14.96063,17.68262,13.77953,17.68262,-13.77953,5.131658,-14.96063,5.131658,-13.77953,13.76831,-14.96063,13.76831,-12.99213,-5.055362,-12.99213,5.055362,-12.20472,-5.055362,-7.480315,5.055362,-7.480315,-5.055362,13.77953,-6.657197,13.77953,-8.096004,12.59842,-6.657197,12.59842,-8.096004,12.20472,8.308934,5.055362,1.159572,10.87544,8.859541,4.504755,2.488856,11.41732,4.394622,7.480315,4.394622,11.41732,14.50535,7.480315,14.50535,7.480315,8.308934,7.480315,18.41966,11.41732,8.308934,12.99213,8.308934,12.99213,18.41966,13.77953,5.131658,13.77953,13.76831,14.96063,5.131658,14.96063,13.76831,13.77953,-4.318327,13.77953,4.318327,14.96063,-4.318327,14.96063,4.318327,-13.77953,4.318327,-13.77953,-4.318327,-14.96063,4.318327,-14.96063,-4.318327,12.20472,18.41966,10.87544,17.86905,5.055362,25.56902,4.504755,24.23974,-13.77953,9.045969,-14.96063,9.045969,-13.77953,17.68262,-14.96063,17.68262,13.77953,5.131658,13.77953,13.76831,14.96063,5.131658,14.96063,13.76831,-13.77953,5.131658,-14.96063,5.131658,-13.77953,13.76831,-14.96063,13.76831,12.59842,-0.8632988,11.81102,-0.8632988,12.59842,0.5755078,11.81102,0.5755078,11.41732,-0.8632988,11.41732,0.5755078,11.81102,-0.8632988,11.81102,0.5755078,11.81102,5.055362,11.81102,-5.055362,11.41732,5.055362,7.480315,-5.055362,7.480315,5.055362,-4.504755,2.488856,-5.055362,1.159572,-10.87544,8.859541,-12.20472,8.308934,13.77953,4.394622,12.99213,4.394622,13.77953,14.50535,12.99213,14.50535,7.480315,4.394622,7.480315,14.50535,-7.480315,8.308934,-12.59842,8.308934,-7.480315,18.41966,-13.77953,8.308934,-12.20472,18.41966,-13.77953,18.41966,-12.20472,4.394622,-12.20472,14.50535,-7.480315,4.394622,-7.480315,14.50535,12.99213,4.504755,12.99213,-4.504755,11.02362,4.504755,12.20472,-4.504755,11.02362,-4.504755,-4.504755,24.23974,-10.87544,17.86905,-5.055362,25.56902,-12.20472,18.41966,13.77953,8.859541,12.59842,8.859541,13.77953,17.86905,11.02362,17.86905,11.02362,8.859541,12.20472,17.86905,13.77953,18.32459,13.77953,16.88579,12.20472,18.32459,12.20472,16.88579,12.59842,4.94523,11.81102,4.94523,12.59842,13.95474,11.02362,13.95474,11.02362,4.94523,-12.20472,24.11849,-12.99213,24.11849,-12.20472,25.5573,-12.99213,25.5573,-12.99213,24.11849,-13.77953,24.11849,-12.99213,25.5573,-13.77953,25.5573,12.20472,18.41966,12.20472,8.308934,10.87544,17.86905,10.87544,8.859541,-12.20472,18.41966,-10.42536,9.045969,-12.20472,8.308934,-5.055362,1.159572,-4.318327,2.938933,5.055362,1.159572,4.318327,2.938933,10.42536,9.045969,12.20472,8.308934,10.42536,17.68262,-10.42536,17.68262,-5.055362,25.56902,-4.318327,23.78966,5.055362,25.56902,4.318327,23.78966,12.20472,18.41966,5.055362,1.159572,-5.055362,1.159572,4.504755,2.488856,-4.504755,2.488856,-11.81102,-4.504755,-11.81102,4.504755,-11.02362,-4.504755,-11.41732,4.504755,-11.02362,4.504755,-11.02362,9.814287E-16,-13.77953,4.94523,-13.77953,13.95474,-12.99213,4.94523,-12.99213,13.95474,-11.02362,4.94523,-11.02362,13.95474,-11.02362,8.859541,-11.41732,8.859541,-11.02362,17.86905,-12.99213,8.859541,-12.99213,17.86905,13.77953,18.32459,13.77953,16.88579,12.99213,18.32459,12.99213,16.88579,5.055362,25.56902,4.504755,24.23974,-5.055362,25.56902,-4.504755,24.23974,-12.99213,-8.096004,-12.99213,-6.657197,-11.41732,-8.096004,-11.41732,-6.657197,-11.41732,4.94523,-11.41732,13.95474,-11.02362,4.94523,-11.02362,13.95474,-10.87544,17.86905,-10.87544,8.859541,-12.20472,18.41966,-12.20472,8.308934,12.20472,4.94523,11.02362,4.94523,12.20472,13.95474,11.02362,13.95474,-5.11811,4.394622,-5.11811,14.50535,7.086614,4.394622,7.086614,14.50535,-14.96063,8.308934,-14.96063,18.41966,-9.448819,8.308934,-9.448819,18.41966,-7.086614,8.308934,-7.086614,18.41966,5.11811,8.308934,5.11811,18.41966,5.11811,5.055362,5.11811,-5.055362,-7.086614,5.055362,-7.086614,-5.055362,5.11811,4.394622,-7.086614,4.394622,5.11811,14.50535,-7.086614,14.50535,9.448819,4.394622,9.448819,14.50535,14.96063,4.394622,14.96063,14.50535,9.448819,14.50535,14.96063,14.50535,9.448819,4.394622,14.96063,4.394622,-5.11811,-5.055362,-5.11811,5.055362,7.086614,-5.055362,7.086614,5.055362,9.448819,-5.055362,9.448819,5.055362,14.96063,-5.055362,14.96063,5.055362,-5.11811,4.394622,-5.11811,14.50535,7.086614,4.394622,7.086614,14.50535,-9.448819,5.055362,-9.448819,-5.055362,-14.96063,5.055362,-14.96063,-5.055362,7.086614,8.308934,-5.11811,8.308934,7.086614,18.41966,-5.11811,18.41966,5.11811,4.394622,-7.086614,4.394622,5.11811,14.50535,-7.086614,14.50535,-9.448819,14.50535,-9.448819,4.394622,-14.96063,14.50535,-14.96063,4.394622,14.96063,8.308934,9.448819,8.308934,14.96063,18.41966,9.448819,18.41966,-9.448819,14.50535,-9.448819,4.394622,-14.96063,14.50535,-14.96063,4.394622,-10.42536,9.045969,-10.42536,17.68262,-4.318327,2.938933,-4.318327,23.78966,4.318327,2.938933,4.318327,23.78966,10.42536,9.045969,10.42536,17.68262,10.87544,17.86905,10.87544,8.859541,4.504755,24.23974,4.504755,2.488856,-4.504755,24.23974,1.592671,7.26687,1.126189,8.393059,0,8.859541,-1.126189,8.393059,-1.592671,7.26687,0,2.488856,1.126189,6.140681,0,5.674199,-1.126189,6.140681,-4.504755,2.488856,-10.87544,17.86905,-10.87544,8.859541
				}
			UVIndex: *924 {
				a: 0,2,1,3,1,2,4,3,2,5,7,6,8,6,7,9,6,8,10,9,8,11,9,10,12,11,10,13,12,10,14,12,13,6,15,5,16,5,15,17,16,15,18,16,17,19,18,17,14,18,19,20,18,14,13,20,14,21,23,22,24,22,23,25,24,23,26,28,27,29,27,28,30,27,29,31,33,32,34,32,33,35,37,36,38,36,37,39,41,40,42,40,41,43,45,44,46,44,45,47,49,48,50,48,49,51,53,52,54,52,53,55,52,54,56,55,54,57,55,56,58,57,56,59,58,56,60,58,59,52,61,51,62,51,61,63,62,61,64,62,63,65,64,63,60,64,65,66,64,60,59,66,60,67,69,68,70,68,69,71,73,72,74,72,73,75,77,76,78,76,77,79,81,80,82,80,81,83,85,84,86,84,85,87,89,88,90,88,89,91,93,92,94,92,93,95,97,96,98,96,97,99,98,97,100,102,101,103,101,102,104,106,105,107,105,106,108,110,109,111,109,110,112,109,111,113,115,114,116,114,115,117,119,118,120,118,119,121,118,120,122,121,120,123,121,122,124,123,122,125,124,122,126,124,125,118,127,117,128,117,127,129,128,127,130,128,129,131,130,129,126,130,131,132,130,126,125,132,126,133,135,134,136,134,135,137,139,138,140,138,139,141,143,142,144,142,143,145,142,144,146,145,144,147,145,146,148,147,146,149,148,146,150,148,149,142,151,141,152,141,151,153,152,151,154,152,153,155,154,153,150,154,155,156,154,150,149,156,150,157,159,158,160,158,159,161,160,159,162,161,159,163,165,164,166,164,165,167,166,165,168,167,165,169,171,170,172,170,171,173,175,174,176,174,175,177,179,178,180,178,179,181,183,182,184,182,183,185,184,183,186,188,187,188,189,187,189,190,187,191,187,190,192,194,193,195,193,194,196,198,197,199,197,198,200,199,198,201,200,198,202,204,203,205,203,204,206,205,204,207,209,208,210,208,209,211,213,212,214,212,213,215,217,216,218,216,217,219,218,217,220,222,221,223,221,222,224,226,225,227,225,226,228,230,229,231,229,230,232,234,233,235,233,234,236,233,235,237,239,238,240,238,239,241,243,242,244,242,243,245,247,246,248,246,247,249,251,250,252,250,251,253,255,254,256,254,255,257,259,258,260,258,259,261,263,262,264,262,263,265,267,266,268,266,267,269,271,270,272,270,271,273,275,274,276,274,275,277,276,275,278,280,279,281,279,280,282,284,283,285,283,284,286,283,285,287,286,285,288,290,289,291,289,290,292,291,290,292,293,291,294,296,295,297,295,296,298,300,299,301,299,300,302,301,300,303,305,304,306,304,305,307,309,308,309,310,308,311,308,310,312,310,309,313,315,314,316,314,315,317,319,318,320,318,319,321,318,320,322,324,323,325,323,324,326,328,327,329,327,328,330,332,331,333,331,332,334,336,335,337,335,336,338,335,337,339,338,337,340,338,339,341,340,339,342,341,339,343,341,342,335,344,334,345,334,344,346,345,344,347,345,346,348,347,346,343,347,348,349,347,343,342,349,343,350,352,351,353,351,352,354,356,355,357,355,356,358,357,356,359,358,356,360,362,361,363,361,362,364,363,362,365,363,364,366,368,367,369,367,368,370,369,368,371,373,372,374,372,373,375,377,376,378,376,377,379,381,380,382,380,381,383,385,384,386,384,385,387,389,388,390,388,389,391,393,392,394,392,393,395,397,396,398,396,397,399,401,400,402,400,401,403,405,404,406,404,405,407,409,408,410,408,409,411,413,412,414,412,413,415,417,416,418,416,417,419,421,420,422,420,421,423,425,424,426,424,425,427,429,428,430,428,429,431,433,432,434,432,433,435,437,436,438,436,437,439,441,440,442,440,441,443,445,444,446,444,445,447,449,448,450,448,449,451,453,452,454,452,453,455,457,456,458,456,457,459,461,460,462,460,461,463,462,461,464,462,463,465,464,463,466,464,465,467,469,468,470,468,469,471,470,469,472,470,471,473,472,471,474,473,471,475,474,471,476,475,471,470,472,477,478,477,472,479,477,478,480,477,479,481,477,480,476,481,480,471,481,476,482,481,471,483,481,482
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *308 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 20310, "Material::greyLight", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.8431373,0.9019608,0.9529412
			P: "DiffuseColor", "Color", "", "A",0.8431373,0.9019608,0.9529412
		}
	}

	Material: 19468, "Material::brownDark", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.6392157,0.3882353,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.6392157,0.3882353,0.2784314
		}
	}

	Material: 20330, "Material::brown", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.827451,0.5647059,0.4039216
			P: "DiffuseColor", "Color", "", "A",0.827451,0.5647059,0.4039216
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh barrel, Model::RootNode
	C: "OO",4796633471079767399,0

	;Geometry::, Model::Mesh barrel
	C: "OO",5203921226452405152,4796633471079767399

	;Material::greyLight, Model::Mesh barrel
	C: "OO",20310,4796633471079767399

	;Material::brownDark, Model::Mesh barrel
	C: "OO",19468,4796633471079767399

	;Material::brown, Model::Mesh barrel
	C: "OO",20330,4796633471079767399

}
