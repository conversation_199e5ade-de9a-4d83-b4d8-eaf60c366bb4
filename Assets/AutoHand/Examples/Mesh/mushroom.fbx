; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2020
		Month: 3
		Day: 4
		Hour: 23
		Minute: 19
		Second: 21
		Millisecond: 370
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "mushroom.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "mushroom.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5573896729480189470, "Model::mushroom", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 5486860742578911846, "Geometry::", "Mesh" {
		Vertices: *396 {
			a: 0.5525242,0.6959999,-0.319,0.4103403,0.6959999,0.2369101,0.5525242,0.6959999,0.319,0,0.6959999,0.638,0,0.6959999,0.4738201,-0.5525242,0.6959999,0.319,-0.4103403,0.6959999,0.2369101,-0.4103403,0.6959999,-0.2369101,0.4103403,0.6959999,-0.2369101,0,0.6959999,-0.638,0,0.6959999,-0.4738201,-0.5525242,0.6959999,-0.319,0,0.986,-0.72094,0.6243523,0.986,-0.36047,0,1.392,-0.36047,0.3121762,1.392,-0.180235,0,0.986,0.72094,-0.6243523,0.986,0.36047,0,1.392,0.36047,-0.3121762,1.392,0.180235,0,0.6959999,-0.638,0.5525242,0.6959999,-0.319,0,0.986,-0.72094,0.6243523,0.986,-0.36047,-0.4103403,0.6959999,-0.2369101,-0.4103403,0.6959999,0.2369101,-0.3363445,0.87,-0.1941886,-0.3363445,0.87,0.1941886,-0.6243523,0.986,-0.36047,-0.3121762,1.392,-0.180235,-0.6243523,0.986,0.36047,-0.3121762,1.392,0.180235,0.6243523,0.986,-0.36047,0.6243523,0.986,0.36047,0.3121762,1.392,-0.180235,0.3121762,1.392,0.180235,0.6243523,0.986,-0.36047,0.5525242,0.6959999,-0.319,0.6243523,0.986,0.36047,0.5525242,0.6959999,0.319,0.3121762,1.392,-0.180235,0.3121762,1.392,0.180235,0,1.392,-0.36047,0,1.392,0.36047,-0.3121762,1.392,-0.180235,-0.3121762,1.392,0.180235,0.4103403,0.6959999,-0.2369101,0.3363445,0.87,-0.1941886,0.4103403,0.6959999,0.2369101,0.3363445,0.87,0.1941886,-0.6243523,0.986,-0.36047,0,0.986,-0.72094,-0.3121762,1.392,-0.180235,0,1.392,-0.36047,-0.6243523,0.986,-0.36047,-0.6243523,0.986,0.36047,-0.5525242,0.6959999,-0.319,-0.5525242,0.6959999,0.319,0,0.6959999,0.638,-0.5525242,0.6959999,0.319,0,0.986,0.72094,-0.6243523,0.986,0.36047,0.6243523,0.986,0.36047,0,0.986,0.72094,0.3121762,1.392,0.180235,0,1.392,0.36047,-0.5525242,0.6959999,-0.319,0,0.6959999,-0.638,-0.6243523,0.986,-0.36047,0,0.986,-0.72094,0.3363445,0.87,0.1941886,0.3363445,0.87,-0.1941886,0,0.87,0.3883772,0.1188598,0.87,0.06862374,0,0.87,0.1372475,-0.3363445,0.87,0.1941886,-0.1188598,0.87,0.06862374,-0.1188598,0.87,-0.06862374,0.1188598,0.87,-0.06862374,0,0.87,-0.3883772,0,0.87,-0.1372475,-0.3363445,0.87,-0.1941886,0.5525242,0.6959999,0.319,0,0.6959999,0.638,0.6243523,0.986,0.36047,0,0.986,0.72094,0.4103403,0.6959999,-0.2369101,0,0.6959999,-0.4738201,0.3363445,0.87,-0.1941886,0,0.87,-0.3883772,-0.4103403,0.6959999,0.2369101,0,0.6959999,0.4738201,-0.3363445,0.87,0.1941886,0,0.87,0.3883772,0,0.6959999,-0.4738201,-0.4103403,0.6959999,-0.2369101,0,0.87,-0.3883772,-0.3363445,0.87,-0.1941886,0,0.6959999,0.4738201,0.4103403,0.6959999,0.2369101,0,0.87,0.3883772,0.3363445,0.87,0.1941886,0.2056275,0,0.1187191,0.2056275,0,-0.1187191,0,0,0.2374381,0,0,-0.2374381,-0.2056275,0,0.1187191,-0.2056275,0,-0.1187191,-0.2056275,0,-0.1187191,0,0,-0.2374381,-0.1188598,0.87,-0.06862374,0,0.87,-0.1372475,0.2056275,0,-0.1187191,0.2056275,0,0.1187191,0.1188598,0.87,-0.06862374,0.1188598,0.87,0.06862374,0,0,0.2374381,-0.2056275,0,0.1187191,0,0.87,0.1372475,-0.1188598,0.87,0.06862374,0.2056275,0,0.1187191,0,0,0.2374381,0.1188598,0.87,0.06862374,0,0.87,0.1372475,0,0,-0.2374381,0.2056275,0,-0.1187191,0,0.87,-0.1372475,0.1188598,0.87,-0.06862374,-0.2056275,0,-0.1187191,-0.1188598,0.87,-0.06862374,-0.2056275,0,0.1187191,-0.1188598,0.87,0.06862374
		} 
		PolygonVertexIndex: *240 {
			a: 0,2,-2,3,1,-3,4,1,-4,5,4,-4,6,4,-6,7,6,-6,1,8,-1,9,0,-9,10,9,-9,7,9,-11,11,9,-8,5,11,-8,12,14,-14,15,13,-15,16,18,-18,19,17,-19,20,22,-22,23,21,-23,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,34,-34,35,33,-35,36,38,-38,39,37,-39,40,42,-42,43,41,-43,44,43,-43,45,43,-45,46,48,-48,49,47,-49,50,52,-52,53,51,-53,54,56,-56,57,55,-57,58,60,-60,61,59,-61,62,64,-64,65,63,-65,66,68,-68,69,67,-69,70,72,-72,73,71,-73,74,73,-73,75,74,-73,76,74,-76,77,76,-76,73,78,-72,79,71,-79,80,79,-79,77,79,-81,81,79,-78,75,81,-78,82,84,-84,85,83,-85,86,88,-88,89,87,-89,90,92,-92,93,91,-93,94,96,-96,97,95,-97,98,100,-100,101,99,-101,102,104,-104,105,103,-105,106,105,-105,107,105,-107,108,110,-110,111,109,-111,112,114,-114,115,113,-115,116,118,-118,119,117,-119,120,122,-122,123,121,-123,124,126,-126,127,125,-127,128,130,-130,131,129,-131
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *720 {
				a: 0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.396374,0.6095494,-0.6865399,0.396374,0.6095494,-0.6865399,0.396374,0.6095494,-0.6865399,0.396374,0.6095494,-0.6865399,0.396374,0.6095494,-0.6865399,0.396374,0.6095494,-0.6865399,-0.396374,0.6095494,0.6865399,-0.396374,0.6095494,0.6865399,-0.396374,0.6095494,0.6865399,-0.396374,0.6095494,0.6865399,-0.396374,0.6095494,0.6865399,-0.396374,0.6095494,0.6865399,0.4853347,-0.2404186,-0.8406243,0.4853347,-0.2404186,-0.8406243,0.4853347,-0.2404186,-0.8406243,0.4853347,-0.2404186,-0.8406243,0.4853347,-0.2404186,-0.8406243,0.4853347,-0.2404186,-0.8406243,0.9202437,-0.3913458,0,0.9202437,-0.3913458,0,0.9202437,-0.3913458,0,0.9202437,-0.3913458,0,0.9202437,-0.3913458,0,0.9202437,-0.3913458,0,-0.7927481,0.6095494,0,-0.7927481,0.6095494,0,-0.7927481,0.6095494,0,-0.7927481,0.6095494,0,-0.7927481,0.6095494,0,-0.7927481,0.6095494,0,0.7927481,0.6095494,0,0.7927481,0.6095494,0,0.7927481,0.6095494,0,0.7927481,0.6095494,0,0.7927481,0.6095494,0,0.7927481,0.6095494,0,0.9706693,-0.2404186,0,0.9706693,-0.2404186,0,0.9706693,-0.2404186,0,0.9706693,-0.2404186,0,0.9706693,-0.2404186,0,0.9706693,-0.2404186,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.9202437,-0.3913458,0,-0.9202437,-0.3913458,0,-0.9202437,-0.3913458,0,-0.9202437,-0.3913458,0,-0.9202437,-0.3913458,0,-0.9202437,-0.3913458,0,-0.396374,0.6095494,-0.6865399,-0.396374,0.6095494,-0.6865399,-0.396374,0.6095494,-0.6865399,-0.396374,0.6095494,-0.6865399,-0.396374,0.6095494,-0.6865399,-0.396374,0.6095494,-0.6865399,-0.9706693,-0.2404186,0,-0.9706693,-0.2404186,0,-0.9706693,-0.2404186,0,-0.9706693,-0.2404186,0,-0.9706693,-0.2404186,0,-0.9706693,-0.2404186,0,-0.4853347,-0.2404186,0.8406243,-0.4853347,-0.2404186,0.8406243,-0.4853347,-0.2404186,0.8406243,-0.4853347,-0.2404186,0.8406243,-0.4853347,-0.2404186,0.8406243,-0.4853347,-0.2404186,0.8406243,0.396374,0.6095494,0.6865399,0.396374,0.6095494,0.6865399,0.396374,0.6095494,0.6865399,0.396374,0.6095494,0.6865399,0.396374,0.6095494,0.6865399,0.396374,0.6095494,0.6865399,-0.4853347,-0.2404186,-0.8406243,-0.4853347,-0.2404186,-0.8406243,-0.4853347,-0.2404186,-0.8406243,-0.4853347,-0.2404186,-0.8406243,-0.4853347,-0.2404186,-0.8406243,-0.4853347,-0.2404186,-0.8406243,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.4853347,-0.2404186,0.8406243,0.4853347,-0.2404186,0.8406243,0.4853347,-0.2404186,0.8406243,0.4853347,-0.2404186,0.8406243,0.4853347,-0.2404186,0.8406243,0.4853347,-0.2404186,0.8406243,-0.4601219,-0.3913458,0.7969545,-0.4601219,-0.3913458,0.7969545,-0.4601219,-0.3913458,0.7969545,-0.4601219,-0.3913458,0.7969545,-0.4601219,-0.3913458,0.7969545,-0.4601219,-0.3913458,0.7969545,0.4601219,-0.3913458,-0.7969545,0.4601219,-0.3913458,-0.7969545,0.4601219,-0.3913458,-0.7969545,0.4601219,-0.3913458,-0.7969545,0.4601219,-0.3913458,-0.7969545,0.4601219,-0.3913458,-0.7969545,0.4601219,-0.3913458,0.7969545,0.4601219,-0.3913458,0.7969545,0.4601219,-0.3913458,0.7969545,0.4601219,-0.3913458,0.7969545,0.4601219,-0.3913458,0.7969545,0.4601219,-0.3913458,0.7969545,-0.4601219,-0.3913458,-0.7969545,-0.4601219,-0.3913458,-0.7969545,-0.4601219,-0.3913458,-0.7969545,-0.4601219,-0.3913458,-0.7969545,-0.4601219,-0.3913458,-0.7969545,-0.4601219,-0.3913458,-0.7969545,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.4975317,0.0992406,-0.8617502,-0.4975317,0.0992406,-0.8617502,-0.4975317,0.0992406,-0.8617502,-0.4975317,0.0992406,-0.8617502,-0.4975317,0.0992406,-0.8617502,-0.4975317,0.0992406,-0.8617502,0.9950635,0.0992406,0,0.9950635,0.0992406,0,0.9950635,0.0992406,0,0.9950635,0.0992406,0,0.9950635,0.0992406,0,0.9950635,0.0992406,0,-0.4975317,0.0992406,0.8617502,-0.4975317,0.0992406,0.8617502,-0.4975317,0.0992406,0.8617502,-0.4975317,0.0992406,0.8617502,-0.4975317,0.0992406,0.8617502,-0.4975317,0.0992406,0.8617502,0.4975317,0.0992406,0.8617502,0.4975317,0.0992406,0.8617502,0.4975317,0.0992406,0.8617502,0.4975317,0.0992406,0.8617502,0.4975317,0.0992406,0.8617502,0.4975317,0.0992406,0.8617502,0.4975317,0.0992406,-0.8617502,0.4975317,0.0992406,-0.8617502,0.4975317,0.0992406,-0.8617502,0.4975317,0.0992406,-0.8617502,0.4975317,0.0992406,-0.8617502,0.4975317,0.0992406,-0.8617502,-0.9950635,0.0992406,0,-0.9950635,0.0992406,0,-0.9950635,0.0992406,0,-0.9950635,0.0992406,0,-0.9950635,0.0992406,0,-0.9950635,0.0992406,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *264 {
				a: 2.175292,-1.255906,1.615513,0.9327168,2.175292,1.255906,0,2.511811,0,1.865434,-2.175292,1.255906,-1.615513,0.9327168,-1.615513,-0.9327168,1.615513,-0.9327168,0,-2.511811,0,-1.865434,-2.175292,-1.255906,1.419173,1.579039,-1.419173,1.579039,0.7095866,3.595348,-0.7095866,3.595348,1.419173,1.579039,-1.419173,1.579039,0.7095866,3.595348,-0.7095866,3.595348,1.255906,3.182767,-1.255906,3.182767,1.419173,4.358999,-1.419173,4.358999,0.9327168,1.889389,-0.9327168,1.889389,0.764522,2.633799,-0.764522,2.633799,-1.419173,1.579039,-0.7095866,3.595348,1.419173,1.579039,0.7095866,3.595348,1.419173,1.579039,-1.419173,1.579039,0.7095866,3.595348,-0.7095866,3.595348,1.419173,4.358999,1.255906,3.182767,-1.419173,4.358999,-1.255906,3.182767,-1.22904,-0.7095866,-1.22904,0.7095866,0,-1.419173,0,1.419173,1.22904,-0.7095866,1.22904,0.7095866,-0.9327168,1.889389,-0.764522,2.633799,0.9327168,1.889389,0.764522,2.633799,1.419173,1.579039,-1.419173,1.579039,0.7095866,3.595348,-0.7095866,3.595348,-1.419173,4.358999,1.419173,4.358999,-1.255906,3.182767,1.255906,3.182767,1.255906,3.182767,-1.255906,3.182767,1.419173,4.358999,-1.419173,4.358999,1.419173,1.579039,-1.419173,1.579039,0.7095866,3.595348,-0.7095866,3.595348,1.255906,3.182767,-1.255906,3.182767,1.419173,4.358999,-1.419173,4.358999,1.324191,0.764522,1.324191,-0.764522,0,1.529044,0.467952,0.2701722,0,0.5403444,-1.324191,0.764522,-0.467952,0.2701722,-0.467952,-0.2701722,0.467952,-0.2701722,0,-1.529044,0,-0.5403444,-1.324191,-0.764522,1.255906,3.182767,-1.255906,3.182767,1.419173,4.358999,-1.419173,4.358999,0.9327168,1.889389,-0.9327168,1.889389,0.764522,2.633799,-0.764522,2.633799,0.9327168,1.889389,-0.9327168,1.889389,0.764522,2.633799,-0.764522,2.633799,0.9327168,1.889389,-0.9327168,1.889389,0.764522,2.633799,-0.764522,2.633799,0.9327168,1.889389,-0.9327168,1.889389,0.764522,2.633799,-0.764522,2.633799,0.8095569,0.4673979,0.8095569,-0.4673979,0,0.9347958,0,-0.9347958,-0.8095569,0.4673979,-0.8095569,-0.4673979,0.4673979,-0.08034091,-0.4673979,-0.08034091,0.2701722,3.361848,-0.2701722,3.361848,0.4673979,-0.08034091,-0.4673979,-0.08034091,0.2701722,3.361848,-0.2701722,3.361848,0.4673979,-0.08034091,-0.4673979,-0.08034091,0.2701722,3.361848,-0.2701722,3.361848,0.4673979,-0.08034091,-0.4673979,-0.08034091,0.2701722,3.361848,-0.2701722,3.361848,0.4673979,-0.08034091,-0.4673979,-0.08034091,0.2701722,3.361848,-0.2701722,3.361848,-0.4673979,-0.08034091,-0.2701722,3.361848,0.4673979,-0.08034091,0.2701722,3.361848
				}
			UVIndex: *240 {
				a: 0,2,1,3,1,2,4,1,3,5,4,3,6,4,5,7,6,5,1,8,0,9,0,8,10,9,8,7,9,10,11,9,7,5,11,7,12,14,13,15,13,14,16,18,17,19,17,18,20,22,21,23,21,22,24,26,25,27,25,26,28,30,29,31,29,30,32,34,33,35,33,34,36,38,37,39,37,38,40,42,41,43,41,42,44,43,42,45,43,44,46,48,47,49,47,48,50,52,51,53,51,52,54,56,55,57,55,56,58,60,59,61,59,60,62,64,63,65,63,64,66,68,67,69,67,68,70,72,71,73,71,72,74,73,72,75,74,72,76,74,75,77,76,75,73,78,71,79,71,78,80,79,78,77,79,80,81,79,77,75,81,77,82,84,83,85,83,84,86,88,87,89,87,88,90,92,91,93,91,92,94,96,95,97,95,96,98,100,99,101,99,100,102,104,103,105,103,104,106,105,104,107,105,106,108,110,109,111,109,110,112,114,113,115,113,114,116,118,117,119,117,118,120,122,121,123,121,122,124,126,125,127,125,126,128,130,129,131,129,130
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *80 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 19468, "Material::brownDark", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.6392157,0.3882353,0.2784314
			P: "DiffuseColor", "Color", "", "A",0.6392157,0.3882353,0.2784314
		}
	}

	Material: 13090, "Material::brownLight", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.9764706,0.772549,0.5490196
			P: "DiffuseColor", "Color", "", "A",0.9764706,0.772549,0.5490196
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh mushroom, Model::RootNode
	C: "OO",5573896729480189470,0

	;Geometry::, Model::Mesh mushroom
	C: "OO",5486860742578911846,5573896729480189470

	;Material::brownDark, Model::Mesh mushroom
	C: "OO",19468,5573896729480189470

	;Material::brownLight, Model::Mesh mushroom
	C: "OO",13090,5573896729480189470

}
