fileFormatVersion: 2
guid: 88e2e1bfe11ec6c438b0ade27eaeded1
ModelImporter:
  serializedVersion: 22200
  internalIDToNameTable:
  - first:
      1: 100000
    second: b_r_forearm_stub
  - first:
      1: 100002
    second: b_r_index1
  - first:
      1: 100004
    second: b_r_index2
  - first:
      1: 100006
    second: b_r_index3
  - first:
      1: 100008
    second: b_r_index_null
  - first:
      1: 100010
    second: b_r_middle1
  - first:
      1: 100012
    second: b_r_middle2
  - first:
      1: 100014
    second: b_r_middle3
  - first:
      1: 100016
    second: b_r_middle_null
  - first:
      1: 100018
    second: b_r_pinky0
  - first:
      1: 100020
    second: b_r_pinky1
  - first:
      1: 100022
    second: b_r_pinky2
  - first:
      1: 100024
    second: b_r_pinky3
  - first:
      1: 100026
    second: b_r_pinky_null
  - first:
      1: 100028
    second: b_r_ring1
  - first:
      1: 100030
    second: b_r_ring2
  - first:
      1: 100032
    second: b_r_ring3
  - first:
      1: 100034
    second: b_r_ring_null
  - first:
      1: 100036
    second: b_r_thumb0
  - first:
      1: 100038
    second: b_r_thumb1
  - first:
      1: 100040
    second: b_r_thumb2
  - first:
      1: 100042
    second: b_r_thumb3
  - first:
      1: 100044
    second: b_r_thumb_null
  - first:
      1: 100046
    second: b_r_wrist
  - first:
      1: 100048
    second: //RootNode
  - first:
      1: 100050
    second: r_handMeshNode
  - first:
      1: 100052
    second: r_index_dip_fe_axis_marker
  - first:
      1: 100054
    second: r_index_finger_pad_marker
  - first:
      1: 100056
    second: r_index_finger_tip_marker
  - first:
      1: 100058
    second: r_index_fingernail_marker
  - first:
      1: 100060
    second: r_index_knuckle_marker
  - first:
      1: 100062
    second: r_index_mcp_aa_axis_marker
  - first:
      1: 100064
    second: r_index_mcp_fe_axis_marker
  - first:
      1: 100066
    second: r_index_palm_collider_base0_marker
  - first:
      1: 100068
    second: r_index_palm_collider_base1_marker
  - first:
      1: 100070
    second: r_index_palm_knuckle_marker
  - first:
      1: 100072
    second: r_index_pip_fe_axis_marker
  - first:
      1: 100074
    second: r_middle_dip_fe_axis_marker
  - first:
      1: 100076
    second: r_middle_finger_pad_marker
  - first:
      1: 100078
    second: r_middle_finger_tip_marker
  - first:
      1: 100080
    second: r_middle_fingernail_marker
  - first:
      1: 100082
    second: r_middle_knuckle_marker
  - first:
      1: 100084
    second: r_middle_mcp_aa_axis_marker
  - first:
      1: 100086
    second: r_middle_mcp_fe_axis_marker
  - first:
      1: 100088
    second: r_middle_palm_collider_base0_marker
  - first:
      1: 100090
    second: r_middle_palm_collider_base1_marker
  - first:
      1: 100092
    second: r_middle_palm_knuckle_marker
  - first:
      1: 100094
    second: r_middle_pip_fe_axis_marker
  - first:
      1: 100096
    second: r_palm_center_marker
  - first:
      1: 100098
    second: r_pinky_dip_fe_axis_marker
  - first:
      1: 100100
    second: r_pinky_finger_pad_marker
  - first:
      1: 100102
    second: r_pinky_finger_tip_marker
  - first:
      1: 100104
    second: r_pinky_fingernail_marker
  - first:
      1: 100106
    second: r_pinky_knuckle_marker
  - first:
      1: 100108
    second: r_pinky_mcp_aa_axis_marker
  - first:
      1: 100110
    second: r_pinky_mcp_fe_axis_marker
  - first:
      1: 100112
    second: r_pinky_palm_collider_base0_marker
  - first:
      1: 100114
    second: r_pinky_palm_collider_base1_marker
  - first:
      1: 100116
    second: r_pinky_palm_knuckle_marker
  - first:
      1: 100118
    second: r_pinky_pip_fe_axis_marker
  - first:
      1: 100120
    second: r_ring_dip_fe_axis_marker
  - first:
      1: 100122
    second: r_ring_finger_pad_marker
  - first:
      1: 100124
    second: r_ring_finger_tip_marker
  - first:
      1: 100126
    second: r_ring_fingernail_marker
  - first:
      1: 100128
    second: r_ring_knuckle_marker
  - first:
      1: 100130
    second: r_ring_mcp_aa_axis_marker
  - first:
      1: 100132
    second: r_ring_mcp_fe_axis_marker
  - first:
      1: 100134
    second: r_ring_palm_collider_base0_marker
  - first:
      1: 100136
    second: r_ring_palm_collider_base1_marker
  - first:
      1: 100138
    second: r_ring_palm_knuckle_marker
  - first:
      1: 100140
    second: r_ring_pip_fe_axis_marker
  - first:
      1: 100142
    second: r_thumb_cmc_aa_axis_marker
  - first:
      1: 100144
    second: r_thumb_cmc_fe_axis_marker
  - first:
      1: 100146
    second: r_thumb_finger_pad_marker
  - first:
      1: 100148
    second: r_thumb_finger_tip_marker
  - first:
      1: 100150
    second: r_thumb_fingernail_marker
  - first:
      1: 100152
    second: r_thumb_ip_fe_axis_marker
  - first:
      1: 100154
    second: r_thumb_knuckle_marker
  - first:
      1: 100156
    second: r_thumb_mcp_fe_axis_marker
  - first:
      1: 100158
    second: r_thumb_palm_knuckle_marker
  - first:
      4: 400000
    second: b_r_forearm_stub
  - first:
      4: 400002
    second: b_r_index1
  - first:
      4: 400004
    second: b_r_index2
  - first:
      4: 400006
    second: b_r_index3
  - first:
      4: 400008
    second: b_r_index_null
  - first:
      4: 400010
    second: b_r_middle1
  - first:
      4: 400012
    second: b_r_middle2
  - first:
      4: 400014
    second: b_r_middle3
  - first:
      4: 400016
    second: b_r_middle_null
  - first:
      4: 400018
    second: b_r_pinky0
  - first:
      4: 400020
    second: b_r_pinky1
  - first:
      4: 400022
    second: b_r_pinky2
  - first:
      4: 400024
    second: b_r_pinky3
  - first:
      4: 400026
    second: b_r_pinky_null
  - first:
      4: 400028
    second: b_r_ring1
  - first:
      4: 400030
    second: b_r_ring2
  - first:
      4: 400032
    second: b_r_ring3
  - first:
      4: 400034
    second: b_r_ring_null
  - first:
      4: 400036
    second: b_r_thumb0
  - first:
      4: 400038
    second: b_r_thumb1
  - first:
      4: 400040
    second: b_r_thumb2
  - first:
      4: 400042
    second: b_r_thumb3
  - first:
      4: 400044
    second: b_r_thumb_null
  - first:
      4: 400046
    second: b_r_wrist
  - first:
      4: 400048
    second: //RootNode
  - first:
      4: 400050
    second: r_handMeshNode
  - first:
      4: 400052
    second: r_index_dip_fe_axis_marker
  - first:
      4: 400054
    second: r_index_finger_pad_marker
  - first:
      4: 400056
    second: r_index_finger_tip_marker
  - first:
      4: 400058
    second: r_index_fingernail_marker
  - first:
      4: 400060
    second: r_index_knuckle_marker
  - first:
      4: 400062
    second: r_index_mcp_aa_axis_marker
  - first:
      4: 400064
    second: r_index_mcp_fe_axis_marker
  - first:
      4: 400066
    second: r_index_palm_collider_base0_marker
  - first:
      4: 400068
    second: r_index_palm_collider_base1_marker
  - first:
      4: 400070
    second: r_index_palm_knuckle_marker
  - first:
      4: 400072
    second: r_index_pip_fe_axis_marker
  - first:
      4: 400074
    second: r_middle_dip_fe_axis_marker
  - first:
      4: 400076
    second: r_middle_finger_pad_marker
  - first:
      4: 400078
    second: r_middle_finger_tip_marker
  - first:
      4: 400080
    second: r_middle_fingernail_marker
  - first:
      4: 400082
    second: r_middle_knuckle_marker
  - first:
      4: 400084
    second: r_middle_mcp_aa_axis_marker
  - first:
      4: 400086
    second: r_middle_mcp_fe_axis_marker
  - first:
      4: 400088
    second: r_middle_palm_collider_base0_marker
  - first:
      4: 400090
    second: r_middle_palm_collider_base1_marker
  - first:
      4: 400092
    second: r_middle_palm_knuckle_marker
  - first:
      4: 400094
    second: r_middle_pip_fe_axis_marker
  - first:
      4: 400096
    second: r_palm_center_marker
  - first:
      4: 400098
    second: r_pinky_dip_fe_axis_marker
  - first:
      4: 400100
    second: r_pinky_finger_pad_marker
  - first:
      4: 400102
    second: r_pinky_finger_tip_marker
  - first:
      4: 400104
    second: r_pinky_fingernail_marker
  - first:
      4: 400106
    second: r_pinky_knuckle_marker
  - first:
      4: 400108
    second: r_pinky_mcp_aa_axis_marker
  - first:
      4: 400110
    second: r_pinky_mcp_fe_axis_marker
  - first:
      4: 400112
    second: r_pinky_palm_collider_base0_marker
  - first:
      4: 400114
    second: r_pinky_palm_collider_base1_marker
  - first:
      4: 400116
    second: r_pinky_palm_knuckle_marker
  - first:
      4: 400118
    second: r_pinky_pip_fe_axis_marker
  - first:
      4: 400120
    second: r_ring_dip_fe_axis_marker
  - first:
      4: 400122
    second: r_ring_finger_pad_marker
  - first:
      4: 400124
    second: r_ring_finger_tip_marker
  - first:
      4: 400126
    second: r_ring_fingernail_marker
  - first:
      4: 400128
    second: r_ring_knuckle_marker
  - first:
      4: 400130
    second: r_ring_mcp_aa_axis_marker
  - first:
      4: 400132
    second: r_ring_mcp_fe_axis_marker
  - first:
      4: 400134
    second: r_ring_palm_collider_base0_marker
  - first:
      4: 400136
    second: r_ring_palm_collider_base1_marker
  - first:
      4: 400138
    second: r_ring_palm_knuckle_marker
  - first:
      4: 400140
    second: r_ring_pip_fe_axis_marker
  - first:
      4: 400142
    second: r_thumb_cmc_aa_axis_marker
  - first:
      4: 400144
    second: r_thumb_cmc_fe_axis_marker
  - first:
      4: 400146
    second: r_thumb_finger_pad_marker
  - first:
      4: 400148
    second: r_thumb_finger_tip_marker
  - first:
      4: 400150
    second: r_thumb_fingernail_marker
  - first:
      4: 400152
    second: r_thumb_ip_fe_axis_marker
  - first:
      4: 400154
    second: r_thumb_knuckle_marker
  - first:
      4: 400156
    second: r_thumb_mcp_fe_axis_marker
  - first:
      4: 400158
    second: r_thumb_palm_knuckle_marker
  - first:
      21: 2100000
    second: lambert2
  - first:
      43: 4300000
    second: r_handMeshNode
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: r_handMeshNode
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    removeConstantScaleCurves: 0
    motionNodeName: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importPhysicalCameras: 0
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    nodeNameCollisionStrategy: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 8
    minBoneWeight: 0.001
    optimizeBones: 1
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
    strictVertexDataChecks: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  importBlendShapeDeformPercent: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
AssetOrigin:
  serializedVersion: 1
  productId: 165323
  packageName: Auto Hand - VR Interaction
  packageVersion: 4.0.0
  assetPath: Assets/AutoHand/Examples/Mesh/OculusHand_R.fbx
  uploadId: 672110
