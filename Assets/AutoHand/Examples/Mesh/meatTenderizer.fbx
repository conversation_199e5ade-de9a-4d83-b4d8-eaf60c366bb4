; FBX 7.3.0 project file
; Copyright (C) 1997-2010 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7300
	CreationTimeStamp:  {
		Version: 1000
		Year: 2020
		Month: 3
		Day: 4
		Hour: 23
		Minute: 19
		Second: 20
		Millisecond: 755
	}
	Creator: "Model created by <PERSON><PERSON> (www.kenney.nl)"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: ""
			Subject: ""
			Author: ""
			Keywords: ""
			Revision: ""
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "meatTenderizer.fbx"
			P: "SrcDocumentUrl", "KString", "Url", "", "meatTenderizer.fbx"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", ""
			P: "Original|ApplicationVersion", "KString", "", "", ""
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", ""
			P: "LastSaved|ApplicationVersion", "KString", "", "", ""
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",10
		P: "OriginalUnitScaleFactor", "double", "Number", "",10
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",11
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",479181389250
		P: "CustomFrameRate", "double", "Number", "",-1
	}
}
; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 4
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfacePhong" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Phong"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
				P: "SpecularColor", "Color", "", "A",0.2,0.2,0.2
				P: "SpecularFactor", "Number", "", "A",1
				P: "ShininessExponent", "Number", "", "A",20
				P: "ReflectionColor", "Color", "", "A",0,0,0
				P: "ReflectionFactor", "Number", "", "A",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 2
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Model: 5576715385776920629, "Model::meatTenderizer", "Mesh" {
		Version: 232
		Properties70:  {
			P: "RotationOrder", "enum", "", "",4
			P: "RotationActive", "bool", "", "",1
			P: "InheritType", "enum", "", "",1
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
			P: "Lcl Translation", "Lcl Translation", "", "A+",0,0,0
			P: "Lcl Rotation", "Lcl Rotation", "", "A+",0,0,0
			P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
			P: "currentUVSet", "KString", "", "U", "map1"
		}
		Shading: T
		Culling: "CullingOff"
	}
	Geometry: 4905993588937503737, "Geometry::", "Mesh" {
		Vertices: *1566 {
			a: -1.989741,0.886153,1.37846,-2.186664,0.886153,1.37846,-1.989741,1.083076,1.37846,-2.186664,1.083076,1.37846,-2.285125,1.181537,1.181537,-2.383587,1.083076,1.37846,-2.678971,1.181537,1.181537,-2.580509,1.083076,1.37846,-1.891279,1.181537,1.181537,-1.989741,1.083076,1.37846,-2.186664,1.083076,1.37846,-1.497433,1.181537,1.181537,-1.595895,1.083076,1.37846,-1.792818,1.083076,1.37846,-2.186664,0.09846143,-1.37846,-2.186664,0.2953843,-1.37846,-2.285125,2.255973E-16,-1.181537,-2.285125,0.3938457,-1.181537,-2.186664,0.4923072,-1.37846,-2.186664,0.6892301,-1.37846,-2.285125,0.7876915,-1.181537,-2.186664,0.886153,-1.37846,-2.186664,1.083076,-1.37846,-2.285125,1.181537,-1.181537,-1.595895,0.886153,1.37846,-1.792818,0.886153,1.37846,-1.595895,1.083076,1.37846,-1.792818,1.083076,1.37846,-1.891279,0.7876915,-1.181537,-2.285125,0.7876915,-1.181537,-1.989741,0.6892301,-1.37846,-2.186664,0.6892301,-1.37846,-1.497433,0.7876915,-1.181537,-1.595895,0.6892301,-1.37846,-1.792818,0.6892301,-1.37846,-2.678971,0.7876915,-1.181537,-2.383587,0.6892301,-1.37846,-2.580509,0.6892301,-1.37846,-1.989741,0.4923072,1.37846,-2.186664,0.4923072,1.37846,-1.989741,0.6892301,1.37846,-2.186664,0.6892301,1.37846,-1.497433,0,1.181537,-1.891279,0,1.181537,-1.595895,0.09846143,1.37846,-1.792818,0.09846143,1.37846,-2.285125,0,1.181537,-1.989741,0.09846143,1.37846,-2.186664,0.09846143,1.37846,-2.678971,0,1.181537,-2.383587,0.09846143,1.37846,-2.580509,0.09846143,1.37846,-1.792818,0.4923072,-1.37846,-1.595895,0.4923072,-1.37846,-1.792818,0.6892301,-1.37846,-1.595895,0.6892301,-1.37846,-1.497433,0.3938457,1.181537,-1.497433,0,1.181537,-1.595895,0.2953843,1.37846,-1.595895,0.09846143,1.37846,-1.595895,0.4923072,1.37846,-1.497433,0.7876915,1.181537,-1.595895,0.6892301,1.37846,-1.497433,1.181537,1.181537,-1.595895,1.083076,1.37846,-1.595895,0.886153,1.37846,-1.891279,0.3938457,1.181537,-1.989741,0.4923072,1.37846,-1.891279,0.7876915,1.181537,-1.989741,0.6892301,1.37846,-1.891279,1.181537,1.181537,-1.989741,1.083076,1.37846,-1.989741,0.886153,1.37846,-1.891279,0,1.181537,-1.989741,0.2953843,1.37846,-1.989741,0.09846143,1.37846,-1.595895,0.4923072,1.37846,-1.792818,0.4923072,1.37846,-1.595895,0.6892301,1.37846,-1.792818,0.6892301,1.37846,-2.678971,1.181537,1.181537,-2.580509,1.083076,1.37846,-2.678971,0.7876915,1.181537,-2.580509,0.886153,1.37846,-2.678971,0.3938457,1.181537,-2.580509,0.4923072,1.37846,-2.580509,0.6892301,1.37846,-2.678971,0,1.181537,-2.580509,0.09846143,1.37846,-2.580509,0.2953843,1.37846,-1.891279,0.3938457,-1.181537,-2.285125,0.3938457,-1.181537,-1.989741,0.2953843,-1.37846,-2.186664,0.2953843,-1.37846,-2.678971,0.3938457,-1.181537,-2.383587,0.2953843,-1.37846,-2.580509,0.2953843,-1.37846,-1.497433,0.3938457,-1.181537,-1.595895,0.2953843,-1.37846,-1.792818,0.2953843,-1.37846,-1.891279,0.3938457,-1.181537,-1.989741,0.4923072,-1.37846,-2.285125,0.3938457,-1.181537,-2.186664,0.4923072,-1.37846,-2.383587,0.4923072,-1.37846,-2.678971,0.3938457,-1.181537,-2.580509,0.4923072,-1.37846,-1.497433,0.3938457,-1.181537,-1.595895,0.4923072,-1.37846,-1.792818,0.4923072,-1.37846,-2.285125,0.3938457,1.181537,-2.285125,0.7876915,1.181537,-2.186664,0.4923072,1.37846,-2.186664,0.6892301,1.37846,-2.285125,1.181537,1.181537,-2.186664,1.083076,1.37846,-2.186664,0.886153,1.37846,-2.285125,0,1.181537,-2.186664,0.09846143,1.37846,-2.186664,0.2953843,1.37846,-1.497433,1.181537,-1.181537,-1.497433,1.181537,-0.8815373,-1.891279,1.181537,-1.181537,-2.678971,1.181537,-0.8815373,-2.285125,1.181537,-1.181537,-2.678971,1.181537,-1.181537,-1.989741,0.6892301,-1.37846,-1.989741,0.4923072,-1.37846,-1.891279,0.7876915,-1.181537,-1.891279,0.3938457,-1.181537,-1.989741,0.2953843,-1.37846,-1.891279,2.255973E-16,-1.181537,-1.989741,0.09846143,-1.37846,-1.989741,0.886153,-1.37846,-1.989741,1.083076,-1.37846,-1.891279,1.181537,-1.181537,-1.891279,0,1.181537,-1.891279,0.3938457,1.181537,-1.792818,0.09846143,1.37846,-1.792818,0.2953843,1.37846,-1.891279,0.7876915,1.181537,-1.792818,0.4923072,1.37846,-1.792818,0.6892301,1.37846,-1.891279,1.181537,1.181537,-1.792818,1.083076,1.37846,-1.792818,0.886153,1.37846,-1.891279,0.3938457,1.181537,-2.285125,0.3938457,1.181537,-1.989741,0.4923072,1.37846,-2.186664,0.4923072,1.37846,-2.678971,0.3938457,1.181537,-2.383587,0.4923072,1.37846,-2.580509,0.4923072,1.37846,-1.497433,0.3938457,1.181537,-1.595895,0.4923072,1.37846,-1.792818,0.4923072,1.37846,-2.383587,0.886153,-1.37846,-2.285125,0.7876915,-1.181537,-2.383587,1.083076,-1.37846,-2.285125,1.181537,-1.181537,-2.383587,0.6892301,-1.37846,-2.383587,0.4923072,-1.37846,-2.285125,0.3938457,-1.181537,-2.383587,0.2953843,-1.37846,-2.285125,2.255973E-16,-1.181537,-2.383587,0.09846143,-1.37846,-2.580509,0.4923072,-1.37846,-2.383587,0.4923072,-1.37846,-2.580509,0.6892301,-1.37846,-2.383587,0.6892301,-1.37846,2.157782,0.5907686,-0.525698,2.728858,0.5907686,-0.525698,2.157782,1.046036,-0.262849,2.728858,1.046036,-0.262849,-1.005126,0.9846144,-0.227387,-1.005126,0.9846144,0,-1.497433,0.9846144,-0.227387,-1.005126,0.9846144,0.227387,-1.497433,0.9846144,0.227387,-1.497433,0.9846144,0,2.728858,0.1355008,0.262849,2.728858,0.1355008,-2.887646E-14,2.157782,0.1355008,0.262849,2.157782,0.1355008,-2.887646E-14,2.728858,0.1355008,-0.262849,2.157782,0.1355008,-0.262849,2.728858,0.1355008,0.262849,2.157782,0.1355008,0.262849,2.728858,0.5907686,0.525698,2.157782,0.5907686,0.525698,-1.005126,0.5907686,-0.4547739,-1.005126,0.5907686,-0.3021253,-1.005126,0.9846144,-0.227387,-1.005126,0.8524168,-0.1510626,-1.005126,0.9846144,0,-1.005126,0.8524168,0,-1.005126,0.9846144,0.227387,-1.005126,0.8524168,0.1510626,-1.005126,0.5907686,0.3021253,-1.005126,0.5907686,0.4547739,-1.005126,0.1969229,-0.227387,-1.005126,0.3291205,-0.1510626,-1.005126,0.1969229,0,-1.005126,0.3291205,0,-1.005126,0.3291205,0.1510626,-1.005126,0.1969229,0.227387,2.157782,1.046036,-0.262849,2.157782,1.046036,-2.887646E-14,2.005495,0.8942806,-0.1752327,2.005495,0.8942806,-2.165734E-14,2.157782,1.046036,0.262849,2.005495,0.8942806,0.1752327,-2.285125,0.7876915,-1.181537,-2.383587,0.886153,-1.37846,-2.678971,0.7876915,-1.181537,-2.580509,0.886153,-1.37846,-1.891279,0.7876915,-1.181537,-1.989741,0.886153,-1.37846,-2.186664,0.886153,-1.37846,-1.497433,0.7876915,-1.181537,-1.595895,0.886153,-1.37846,-1.792818,0.886153,-1.37846,2.005495,0.2872567,0.1752327,2.005495,0.5907686,0.3504653,2.157782,0.1355008,0.262849,2.157782,0.5907686,0.525698,2.157782,0.5907686,-0.525698,2.157782,1.046036,-0.262849,2.005495,0.5907686,-0.3504653,2.005495,0.8942806,-0.1752327,2.728858,1.046036,-2.887646E-14,2.728858,1.046036,0.262849,2.157782,1.046036,-2.887646E-14,2.157782,1.046036,0.262849,2.157782,1.046036,-0.262849,2.728858,1.046036,-0.262849,-2.285125,0.7876915,1.181537,-2.383587,0.6892301,1.37846,-2.678971,0.7876915,1.181537,-2.580509,0.6892301,1.37846,-1.891279,0.7876915,1.181537,-1.989741,0.6892301,1.37846,-2.186664,0.6892301,1.37846,-1.497433,0.7876915,1.181537,-1.595895,0.6892301,1.37846,-1.792818,0.6892301,1.37846,-1.792818,0.886153,-1.37846,-1.792818,1.083076,-1.37846,-1.891279,0.7876915,-1.181537,-1.891279,1.181537,-1.181537,-1.891279,0.3938457,-1.181537,-1.792818,0.6892301,-1.37846,-1.792818,0.4923072,-1.37846,-1.891279,2.255973E-16,-1.181537,-1.792818,0.2953843,-1.37846,-1.792818,0.09846143,-1.37846,-1.595895,0.09846143,-1.37846,-1.497433,2.255973E-16,-1.181537,-1.595895,0.2953843,-1.37846,-1.497433,0.3938457,-1.181537,-1.497433,0.7876915,-1.181537,-1.595895,0.4923072,-1.37846,-1.595895,0.6892301,-1.37846,-1.595895,0.886153,-1.37846,-1.595895,1.083076,-1.37846,-1.497433,1.181537,-1.181537,2.157782,0.5907686,-0.525698,2.005495,0.5907686,-0.3504653,2.157782,0.1355008,-0.262849,2.005495,0.2872567,-0.1752327,2.157782,0.1355008,-2.887646E-14,2.157782,0.1355008,-0.262849,2.005495,0.2872567,-2.165734E-14,2.005495,0.2872567,-0.1752327,2.005495,0.2872567,0.1752327,2.157782,0.1355008,0.262849,-2.383587,0.09846143,1.37846,-2.580509,0.09846143,1.37846,-2.383587,0.2953843,1.37846,-2.580509,0.2953843,1.37846,-1.891279,1.181537,-1.181537,-2.285125,1.181537,-1.181537,-1.989741,1.083076,-1.37846,-2.186664,1.083076,-1.37846,-1.497433,1.181537,-1.181537,-1.595895,1.083076,-1.37846,-1.792818,1.083076,-1.37846,-2.678971,1.181537,-1.181537,-2.383587,1.083076,-1.37846,-2.580509,1.083076,-1.37846,-1.792818,0.886153,-1.37846,-1.595895,0.886153,-1.37846,-1.792818,1.083076,-1.37846,-1.595895,1.083076,-1.37846,-2.285125,0.3938457,1.181537,-2.383587,0.4923072,1.37846,-2.285125,0.7876915,1.181537,-2.383587,0.6892301,1.37846,-2.285125,1.181537,1.181537,-2.383587,1.083076,1.37846,-2.383587,0.886153,1.37846,-2.285125,0,1.181537,-2.383587,0.2953843,1.37846,-2.383587,0.09846143,1.37846,-2.580509,0.4923072,-1.37846,-2.580509,0.6892301,-1.37846,-2.678971,0.3938457,-1.181537,-2.678971,0.7876915,-1.181537,-2.580509,0.886153,-1.37846,-2.580509,1.083076,-1.37846,-2.678971,1.181537,-1.181537,-2.678971,2.255973E-16,-1.181537,-2.580509,0.2953843,-1.37846,-2.580509,0.09846143,-1.37846,-2.186664,0.886153,-1.37846,-1.989741,0.886153,-1.37846,-2.186664,1.083076,-1.37846,-1.989741,1.083076,-1.37846,2.005495,0.8942806,0.1752327,2.157782,1.046036,0.262849,2.005495,0.5907686,0.3504653,2.157782,0.5907686,0.525698,-1.005126,0.5907686,0.4547739,-1.497433,0.5907686,0.4547739,-1.005126,0.9846144,0.227387,-1.497433,0.9846144,0.227387,-1.005126,0.1969229,0.227387,-1.005126,0.1969229,0,-1.497433,0.1969229,0.227387,-1.005126,0.1969229,-0.227387,-1.497433,0.1969229,-0.227387,-1.497433,0.1969229,0,-1.005126,0.1969229,0.227387,-1.497433,0.1969229,0.227387,-1.005126,0.5907686,0.4547739,-1.497433,0.5907686,0.4547739,-1.497433,0.5907686,-0.4547739,-1.005126,0.5907686,-0.4547739,-1.497433,0.9846144,-0.227387,-1.005126,0.9846144,-0.227387,-1.497433,0.1969229,-0.227387,-1.005126,0.1969229,-0.227387,-1.497433,0.5907686,-0.4547739,-1.005126,0.5907686,-0.4547739,2.728858,0.5907686,-0.525698,2.728858,0.1355008,-0.262849,2.728858,1.046036,-0.262849,2.728858,1.046036,-2.887646E-14,2.728858,0.1355008,-2.887646E-14,2.728858,1.046036,0.262849,2.728858,0.1355008,0.262849,2.728858,0.5907686,0.525698,2.157782,0.1355008,-0.262849,2.728858,0.1355008,-0.262849,2.157782,0.5907686,-0.525698,2.728858,0.5907686,-0.525698,2.728858,0.5907686,0.525698,2.157782,0.5907686,0.525698,2.728858,1.046036,0.262849,2.157782,1.046036,0.262849,-2.285125,2.255973E-16,-1.181537,-2.383587,0.09846143,-1.37846,-2.678971,2.255973E-16,-1.181537,-2.580509,0.09846143,-1.37846,-1.891279,2.255973E-16,-1.181537,-1.989741,0.09846143,-1.37846,-2.186664,0.09846143,-1.37846,-1.497433,2.255973E-16,-1.181537,-1.595895,0.09846143,-1.37846,-1.792818,0.09846143,-1.37846,-2.580509,0.09846143,-1.37846,-2.383587,0.09846143,-1.37846,-2.580509,0.2953843,-1.37846,-2.383587,0.2953843,-1.37846,-2.186664,0.09846143,-1.37846,-1.989741,0.09846143,-1.37846,-2.186664,0.2953843,-1.37846,-1.989741,0.2953843,-1.37846,-1.891279,0.3938457,1.181537,-1.989741,0.2953843,1.37846,-2.285125,0.3938457,1.181537,-2.186664,0.2953843,1.37846,-1.497433,0.3938457,1.181537,-1.595895,0.2953843,1.37846,-1.792818,0.2953843,1.37846,-2.383587,0.2953843,1.37846,-2.678971,0.3938457,1.181537,-2.580509,0.2953843,1.37846,-2.580509,0.886153,-1.37846,-2.383587,0.886153,-1.37846,-2.580509,1.083076,-1.37846,-2.383587,1.083076,-1.37846,-1.497433,2.255973E-16,-0.8815373,-1.497433,2.255973E-16,-1.181537,-2.678971,2.255973E-16,-0.8815373,-1.891279,2.255973E-16,-1.181537,-2.285125,2.255973E-16,-1.181537,-2.678971,2.255973E-16,-1.181537,-1.595895,0.09846143,1.37846,-1.792818,0.09846143,1.37846,-1.595895,0.2953843,1.37846,-1.792818,0.2953843,1.37846,-1.891279,0.7876915,1.181537,-2.285125,0.7876915,1.181537,-1.989741,0.886153,1.37846,-2.186664,0.886153,1.37846,-2.678971,0.7876915,1.181537,-2.383587,0.886153,1.37846,-2.580509,0.886153,1.37846,-1.497433,0.7876915,1.181537,-1.595895,0.886153,1.37846,-1.792818,0.886153,1.37846,-2.383587,0.886153,1.37846,-2.580509,0.886153,1.37846,-2.383587,1.083076,1.37846,-2.580509,1.083076,1.37846,-2.678971,0.3938457,-1.181537,-2.678971,0.7876915,-1.181537,-2.678971,2.255973E-16,-1.181537,-2.678971,1.181537,-1.181537,-2.678971,2.255973E-16,-0.8815373,-2.678971,1.181537,-0.8815373,-1.989741,0.09846143,1.37846,-2.186664,0.09846143,1.37846,-1.989741,0.2953843,1.37846,-2.186664,0.2953843,1.37846,-2.383587,0.4923072,1.37846,-2.580509,0.4923072,1.37846,-2.383587,0.6892301,1.37846,-2.580509,0.6892301,1.37846,-1.792818,0.09846143,-1.37846,-1.595895,0.09846143,-1.37846,-1.792818,0.2953843,-1.37846,-1.595895,0.2953843,-1.37846,-2.186664,0.4923072,-1.37846,-1.989741,0.4923072,-1.37846,-2.186664,0.6892301,-1.37846,-1.989741,0.6892301,-1.37846,-1.497433,1.181537,0.8815373,-1.497433,1.181537,1.181537,-2.678971,1.181537,0.8815373,-1.891279,1.181537,1.181537,-2.285125,1.181537,1.181537,-2.678971,1.181537,1.181537,-1.497433,0,1.181537,-1.497433,0,0.8815373,-1.891279,0,1.181537,-2.678971,0,0.8815373,-2.285125,0,1.181537,-2.678971,0,1.181537,-2.678971,0,0.8815373,-2.678971,1.181537,0.8815373,-2.678971,0,1.181537,-2.678971,1.181537,1.181537,-2.678971,0.3938457,1.181537,-2.678971,0.7876915,1.181537,-1.497433,0.7876915,-1.181537,-1.497433,0.3938457,-1.181537,-1.497433,1.181537,-1.181537,-1.497433,2.255973E-16,-1.181537,-1.497433,1.181537,-0.8815373,-1.497433,2.255973E-16,-0.8815373,-1.497433,0.9846144,-0.227387,-1.497433,1.181537,0.8815373,-1.497433,0.5907686,-0.4547739,-1.497433,0.9846144,0,-1.497433,0.9846144,0.227387,-1.497433,0.5907686,0.4547739,-1.497433,0.1969229,-0.227387,-1.497433,0,0.8815373,-1.497433,1.181537,1.181537,-1.497433,0,1.181537,-1.497433,0.3938457,1.181537,-1.497433,0.7876915,1.181537,-1.497433,0.1969229,0,-1.497433,0.1969229,0.227387,-1.005126,0.5907686,-0.3021253,2.005495,0.5907686,-0.3504653,-1.005126,0.8524168,-0.1510626,2.005495,0.8942806,-0.1752327,2.005495,0.2872567,-2.165734E-14,2.005495,0.2872567,-0.1752327,-1.005126,0.3291205,0,-1.005126,0.3291205,-0.1510626,-1.005126,0.3291205,0.1510626,2.005495,0.2872567,0.1752327,2.005495,0.8942806,-0.1752327,2.005495,0.8942806,-2.165734E-14,-1.005126,0.8524168,-0.1510626,-1.005126,0.8524168,0,2.005495,0.8942806,0.1752327,-1.005126,0.8524168,0.1510626,2.005495,0.2872567,-0.1752327,2.005495,0.5907686,-0.3504653,-1.005126,0.3291205,-0.1510626,-1.005126,0.5907686,-0.3021253,2.005495,0.2872567,0.1752327,-1.005126,0.3291205,0.1510626,2.005495,0.5907686,0.3504653,-1.005126,0.5907686,0.3021253,2.005495,0.5907686,0.3504653,-1.005126,0.5907686,0.3021253,2.005495,0.8942806,0.1752327,-1.005126,0.8524168,0.1510626,2.005495,0.5907686,-0.3504653,2.005495,0.2872567,-0.1752327,2.005495,0.8942806,-0.1752327,2.005495,0.8942806,-2.165734E-14,2.005495,0.8942806,-0.1752327,2.005495,0.2872567,-0.1752327,2.005495,0.2872567,-2.165734E-14,2.005495,0.8942806,-2.165734E-14,2.005495,0.2872567,-0.1752327,2.005495,0.8942806,0.1752327,2.005495,0.8942806,-2.165734E-14,2.005495,0.2872567,-2.165734E-14,2.005495,0.2872567,0.1752327,2.005495,0.8942806,0.1752327,2.005495,0.2872567,-2.165734E-14,2.005495,0.5907686,0.3504653,2.005495,0.8942806,0.1752327,2.005495,0.2872567,0.1752327
		} 
		PolygonVertexIndex: *966 {
			a: 0,2,-2,3,1,-3,4,6,-6,7,5,-7,8,4,-10,10,9,-5,11,8,-13,13,12,-9,14,16,-16,17,15,-17,18,17,-20,20,19,-18,21,20,-23,23,22,-21,24,26,-26,27,25,-27,28,30,-30,31,29,-31,32,33,-29,34,28,-34,29,36,-36,37,35,-37,38,40,-40,41,39,-41,42,44,-44,45,43,-45,43,47,-47,48,46,-48,46,50,-50,51,49,-51,52,54,-54,55,53,-55,56,58,-58,59,57,-59,56,61,-61,62,60,-62,63,64,-62,65,61,-65,66,68,-68,69,67,-69,70,71,-69,72,68,-72,66,74,-74,75,73,-75,76,78,-78,79,77,-79,80,82,-82,83,81,-83,84,85,-83,86,82,-86,87,88,-85,89,84,-89,90,92,-92,93,91,-93,91,95,-95,96,94,-96,97,98,-91,99,90,-99,100,102,-102,103,101,-103,102,105,-105,106,104,-106,107,100,-109,109,108,-101,110,112,-112,113,111,-113,114,111,-116,116,115,-112,117,118,-111,119,110,-119,120,122,-122,123,121,-123,124,123,-123,125,123,-125,126,128,-128,129,127,-129,129,131,-131,132,130,-132,133,134,-129,135,128,-135,136,138,-138,139,137,-139,137,141,-141,142,140,-142,143,140,-145,145,144,-141,146,148,-148,149,147,-149,147,151,-151,152,150,-152,153,154,-147,155,146,-155,156,158,-158,159,157,-159,160,157,-162,162,161,-158,162,164,-164,165,163,-165,166,168,-168,169,167,-169,170,172,-172,173,171,-173,174,176,-176,177,175,-177,178,177,-177,179,178,-177,180,182,-182,183,181,-183,181,183,-185,185,184,-184,186,188,-188,189,187,-189,190,192,-192,193,191,-193,194,193,-193,195,193,-195,196,195,-195,197,195,-197,198,197,-197,199,198,-197,190,191,-201,201,200,-192,202,200,-202,203,202,-202,204,202,-204,205,202,-205,198,205,-205,199,205,-199,206,208,-208,209,207,-209,207,209,-211,211,210,-210,212,214,-214,215,213,-215,216,212,-218,218,217,-213,219,216,-221,221,220,-217,222,224,-224,225,223,-225,226,228,-228,229,227,-229,230,232,-232,233,231,-233,232,230,-235,235,234,-231,236,238,-238,239,237,-239,240,236,-242,242,241,-237,243,240,-245,245,244,-241,246,248,-248,249,247,-249,248,251,-251,252,250,-252,250,254,-254,255,253,-255,256,258,-258,259,257,-259,259,261,-261,262,260,-262,263,264,-261,265,260,-265,266,268,-268,269,267,-269,270,272,-272,273,271,-273,272,270,-275,275,274,-271,276,278,-278,279,277,-279,280,282,-282,283,281,-283,284,285,-281,286,280,-286,281,288,-288,289,287,-289,290,292,-292,293,291,-293,294,296,-296,297,295,-297,298,299,-297,300,296,-300,294,302,-302,303,301,-303,304,306,-306,307,305,-307,308,307,-310,310,309,-308,306,312,-312,313,311,-313,314,316,-316,317,315,-317,318,320,-320,321,319,-321,322,324,-324,325,323,-325,326,328,-328,329,327,-329,330,329,-329,331,330,-329,332,334,-334,335,333,-335,336,338,-338,339,337,-339,340,342,-342,343,341,-343,344,346,-346,347,345,-347,348,345,-348,349,348,-348,350,348,-350,351,350,-350,352,354,-354,355,353,-355,356,358,-358,359,357,-359,360,362,-362,363,361,-363,364,360,-366,366,365,-361,367,364,-369,369,368,-365,370,372,-372,373,371,-373,374,376,-376,377,375,-377,378,380,-380,381,379,-381,382,378,-384,384,383,-379,380,386,-386,387,385,-387,388,390,-390,391,389,-391,392,394,-394,395,393,-395,396,395,-395,397,396,-395,398,400,-400,401,399,-401,402,404,-404,405,403,-405,403,407,-407,408,406,-408,409,410,-403,411,402,-411,412,414,-414,415,413,-415,416,418,-418,419,417,-419,420,419,-419,421,419,-421,422,424,-424,425,423,-425,426,428,-428,429,427,-429,430,432,-432,433,431,-433,434,436,-436,437,435,-437,438,440,-440,441,439,-441,442,441,-441,443,442,-441,444,446,-446,447,445,-447,448,447,-447,449,447,-449,450,452,-452,453,451,-453,454,453,-453,455,453,-455,456,458,-458,459,457,-459,460,459,-459,461,459,-461,461,460,-463,463,462,-461,464,461,-463,465,462,-464,466,465,-464,467,466,-464,464,468,-462,469,461,-469,463,469,-468,463,470,-470,471,469,-471,472,471,-471,470,473,-473,474,469,-469,475,469,-475,467,469,-476,121,123,-439,440,438,-124,420,450,-422,451,421,-451,445,447,-393,394,392,-448,476,478,-478,479,477,-479,480,482,-482,483,481,-483,482,480,-485,485,484,-481,486,488,-488,489,487,-489,487,489,-491,491,490,-490,492,494,-494,495,493,-495,496,498,-498,499,497,-499,500,502,-502,503,501,-503,504,506,-506,507,509,-509,510,512,-512,513,515,-515,516,518,-518,519,521,-521
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *2898 {
				a: 0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,-0.8944272,0,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.8660254,-0.5,0,0,-1,0,0.8660254,-0.5,0,0,-1,0,0.8660254,-0.5,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.8660254,0.5,0,-0.8660254,0.5,0,-1,0,0,-1,0,0,-1,0,0,-0.8660254,0.5,0,-1,0,0,-1,0,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-0.8660254,-0.5,0,-1,0,0,-0.8660254,0.5,0,0,1,0,-0.8660254,0.5,0,0,1,0,-0.8660254,0.5,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0.7547957,0.5680779,-0.32798,-0.7547957,0.5680779,-0.32798,-0.7058705,0.7083408,0,-0.7058705,0.7083408,0,-0.7058705,0.7083408,0,-0.7547957,0.5680779,-0.32798,-0.7058705,0.7083408,0,-0.7058705,0.7083408,0,-0.7547957,0.5680779,0.32798,-0.7547957,0.5680779,0.32798,-0.7547957,0.5680779,0.32798,-0.7058705,0.7083408,0,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,-0.7547957,-0.5680779,0.32798,-0.7547957,-0.5680779,0.32798,-0.7547957,0,0.6559599,-0.7547957,0,0.6559599,-0.7547957,0,0.6559599,-0.7547957,-0.5680779,0.32798,-0.7547957,0,-0.6559599,-0.7547957,0,-0.6559599,-0.7547957,0.5680779,-0.32798,-0.7547957,0.5680779,-0.32798,-0.7547957,0.5680779,-0.32798,-0.7547957,0,-0.6559599,0,1,0,0,1,0,0,0.8660254,0.5,0,0.8660254,0.5,0,0.8660254,0.5,0,1,0,0,1,0,0,1,0,0,0.8660254,-0.5,0,0.8660254,-0.5,0,0.8660254,-0.5,0,1,0,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,0.8944272,0,-0.4472136,-0.7547957,0,-0.6559599,-0.7547957,-0.5680779,-0.32798,-0.7547957,0,-0.6559599,-0.7547957,-0.5680779,-0.32798,-0.7547957,0,-0.6559599,-0.7547957,-0.5680779,-0.32798,-0.7058705,-0.7083408,0,-0.7058705,-0.7083408,0,-0.7547957,-0.5680779,-0.32798,-0.7547957,-0.5680779,-0.32798,-0.7547957,-0.5680779,-0.32798,-0.7058705,-0.7083408,0,-0.7058705,-0.7083408,0,-0.7058705,-0.7083408,0,-0.7547957,-0.5680779,0.32798,-0.7547957,-0.5680779,0.32798,-0.7547957,-0.5680779,0.32798,-0.7058705,-0.7083408,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0.8944272,-0.4472136,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,0.8944272,0,0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,-0.8944272,0,-0.4472136,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,-0.7547957,0.5680779,0.32798,-0.7547957,0,0.6559599,-0.7547957,0.5680779,0.32798,-0.7547957,0,0.6559599,-0.7547957,0.5680779,0.32798,-0.7547957,0,0.6559599,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,0.5,0.8660254,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,-0.5,0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,0,-0.5,-0.8660254,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,-0.8660254,-0.5,0,0,-1,0,-0.8660254,-0.5,0,0,-1,0,-0.8660254,-0.5,0,0,-1,0,0,1,0,0.8660254,0.5,0,0,1,0,0.8660254,0.5,0,0,1,0,0.8660254,0.5,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,-0.8944272,-0.4472136,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0.8944272,0.4472136,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,-0.8944272,0.4472136,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,-0.01605443,0,-0.9998711,-0.01605443,0.8659137,-0.4999355,-0.01605443,0,-0.9998711,-0.01605443,0.8659137,-0.4999355,-0.01605443,0,-0.9998711,-0.01605443,0.8659137,-0.4999355,-0.013904,-0.9999033,0,-0.013904,-0.9999033,0,-0.01605443,-0.8659137,-0.4999355,-0.01605443,-0.8659137,-0.4999355,-0.01605443,-0.8659137,-0.4999355,-0.013904,-0.9999033,0,-0.013904,-0.9999033,0,-0.013904,-0.9999033,0,-0.01605443,-0.8659137,0.4999355,-0.01605443,-0.8659137,0.4999355,-0.01605443,-0.8659137,0.4999355,-0.013904,-0.9999033,0,-0.01605443,0.8659137,-0.4999355,-0.01605443,0.8659137,-0.4999355,-0.013904,0.9999033,0,-0.013904,0.9999033,0,-0.013904,0.9999033,0,-0.01605443,0.8659137,-0.4999355,-0.013904,0.9999033,0,-0.013904,0.9999033,0,-0.01605443,0.8659137,0.4999355,-0.01605443,0.8659137,0.4999355,-0.01605443,0.8659137,0.4999355,-0.013904,0.9999033,0,-0.01605443,-0.8659137,-0.4999355,-0.01605443,-0.8659137,-0.4999355,-0.01605443,0,-0.9998711,-0.01605443,0,-0.9998711,-0.01605443,0,-0.9998711,-0.01605443,-0.8659137,-0.4999355,-0.01605443,-0.8659137,0.4999355,-0.01605443,0,0.9998711,-0.01605443,-0.8659137,0.4999355,-0.01605443,0,0.9998711,-0.01605443,-0.8659137,0.4999355,-0.01605443,0,0.9998711,-0.01605443,0,0.9998711,-0.01605443,0.8659137,0.4999355,-0.01605443,0,0.9998711,-0.01605443,0.8659137,0.4999355,-0.01605443,0,0.9998711,-0.01605443,0.8659137,0.4999355,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0
			}
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "map1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *1044 {
				a: -7.833625,3.488791,-8.608912,3.488791,-7.833625,4.264078,-8.608912,4.264078,-8.996555,-2.080313,-9.384199,-2.94711,-10.54713,-2.080313,-10.15949,-2.94711,-7.445982,-2.080313,-7.833625,-2.94711,-8.608912,-2.94711,-5.895408,-2.080313,-6.283051,-2.94711,-7.058338,-2.94711,-1.004041,0.3876435,-1.004041,1.16293,-0.1372443,9.935845E-16,-0.1372443,1.550574,-1.004041,1.938217,-1.004041,2.713504,-0.1372443,3.101148,-1.004041,3.488791,-1.004041,4.264078,-0.1372443,4.651721,-6.283051,3.488791,-7.058338,3.488791,-6.283051,4.264078,-7.058338,4.264078,7.445982,-2.773751,8.996555,-2.773751,7.833625,-3.640548,8.608912,-3.640548,5.895408,-2.773751,6.283051,-3.640548,7.058338,-3.640548,10.54713,-2.773751,9.384199,-3.640548,10.15949,-3.640548,-7.833625,1.938217,-8.608912,1.938217,-7.833625,2.713504,-8.608912,2.713504,-5.895408,4.160626,-7.445982,4.160626,-6.283051,5.027423,-7.058338,5.027423,-8.996555,4.160626,-7.833625,5.027423,-8.608912,5.027423,-10.54713,4.160626,-9.384199,5.027423,-10.15949,5.027423,7.058338,1.938217,6.283051,1.938217,7.058338,2.713504,6.283051,2.713504,-6.797133,1.550574,-6.797133,-4.022116E-16,-7.66393,1.16293,-7.66393,0.3876435,-7.66393,1.938217,-6.797133,3.101148,-7.66393,2.713504,-6.797133,4.651721,-7.66393,4.264078,-7.66393,3.488791,-7.490571,1.550574,-8.357368,1.938217,-7.490571,3.101148,-8.357368,2.713504,-7.490571,4.651721,-8.357368,4.264078,-8.357368,3.488791,-7.490571,-2.750448E-16,-8.357368,1.16293,-8.357368,0.3876435,-6.283051,1.938217,-7.058338,1.938217,-6.283051,2.713504,-7.058338,2.713504,-0.5561934,4.651721,0.3106037,4.264078,-0.5561934,3.101148,0.3106037,3.488791,-0.5561934,1.550574,0.3106037,1.938217,0.3106037,2.713504,-0.5561934,1.690193E-15,0.3106037,0.3876435,0.3106037,1.16293,7.445982,-3.467189,8.996555,-3.467189,7.833625,-4.333986,8.608912,-4.333986,10.54713,-3.467189,9.384199,-4.333986,10.15949,-4.333986,5.895408,-3.467189,6.283051,-4.333986,7.058338,-4.333986,7.445982,4.854064,7.833625,5.720861,8.996555,4.854064,8.608912,5.720861,9.384199,5.720861,10.54713,4.854064,10.15949,5.720861,5.895408,4.854064,6.283051,5.720861,7.058338,5.720861,0.1372443,1.550574,0.1372443,3.101148,1.004041,1.938217,1.004041,2.713504,0.1372443,4.651721,1.004041,4.264078,1.004041,3.488791,0.1372443,1.275794E-15,1.004041,0.3876435,1.004041,1.16293,5.895408,-4.651721,5.895408,-3.470619,7.445982,-4.651721,10.54713,-3.470619,8.996555,-4.651721,10.54713,-4.651721,8.357368,2.713504,8.357368,1.938217,7.490571,3.101148,7.490571,1.550574,8.357368,1.16293,7.490571,5.386294E-16,8.357368,0.3876435,8.357368,3.488791,8.357368,4.264078,7.490571,4.651721,0.830682,1.101078E-15,0.830682,1.550574,1.697479,0.3876435,1.697479,1.16293,0.830682,3.101148,1.697479,1.938217,1.697479,2.713504,0.830682,4.651721,1.697479,4.264078,1.697479,3.488791,-7.445982,4.854064,-8.996555,4.854064,-7.833625,5.720861,-8.608912,5.720861,-10.54713,4.854064,-9.384199,5.720861,-10.15949,5.720861,-5.895408,4.854064,-6.283051,5.720861,-7.058338,5.720861,9.050805,3.488791,8.184009,3.101148,9.050805,4.264078,8.184009,4.651721,9.050805,2.713504,9.050805,1.938217,8.184009,1.550574,9.050805,1.16293,8.184009,4.327722E-16,9.050805,0.3876435,10.15949,1.938217,9.384199,1.938217,10.15949,2.713504,9.384199,2.713504,-8.495203,0.979416,-10.74354,0.979416,-8.495203,3.049093,-10.74354,3.049093,3.957191,-0.8952242,3.957191,6.311256E-15,5.895408,-0.8952242,3.957191,0.8952242,5.895408,0.8952242,5.895408,6.311256E-15,10.74354,1.034839,10.74354,-1.128238E-13,8.495203,1.034839,8.495203,-1.128238E-13,10.74354,-1.034839,8.495203,-1.034839,10.74354,0.979416,8.495203,0.979416,10.74354,3.049093,8.495203,3.049093,1.790448,2.325861,1.18947,2.325861,0.8952242,3.876435,0.5947348,3.355972,0,3.876435,0,3.355972,-0.8952242,3.876435,-0.5947348,3.355972,-1.18947,2.325861,-1.790448,2.325861,0.8952242,0.7752869,0.5947348,1.29575,0,0.7752869,0,1.29575,-0.5947348,1.29575,-0.8952242,0.7752869,-1.034839,8.924454,-1.281336E-13,8.924454,-0.6898924,8.078032,-9.869228E-14,8.078032,1.034839,8.924454,0.6898924,8.078032,8.996555,5.547502,9.384199,6.414299,10.54713,5.547502,10.15949,6.414299,7.445982,5.547502,7.833625,6.414299,8.608912,6.414299,5.895408,5.547502,6.283051,6.414299,7.058338,6.414299,5.699956,-0.8928116,6.220684,0.3849398,6.353604,-1.531687,7.134696,0.3849398,-7.134696,3.965261,-6.353604,5.881888,-6.220684,3.965261,-5.699956,5.243013,-10.74354,-1.034365E-13,-10.74354,1.034839,-8.495203,-1.034365E-13,-8.495203,1.034839,-8.495203,-1.034839,-10.74354,-1.034839,-8.996555,-2.773751,-9.384199,-3.640548,-10.54713,-2.773751,-10.15949,-3.640548,-7.445982,-2.773751,-7.833625,-3.640548,-8.608912,-3.640548,-5.895408,-2.773751,-6.283051,-3.640548,-7.058338,-3.640548,-1.697479,3.488791,-1.697479,4.264078,-0.830682,3.101148,-0.830682,4.651721,-0.830682,1.550574,-1.697479,2.713504,-1.697479,1.938217,-0.830682,1.585623E-15,-1.697479,1.16293,-1.697479,0.3876435,7.66393,0.3876435,6.797133,5.780251E-16,7.66393,1.16293,6.797133,1.550574,6.797133,3.101148,7.66393,1.938217,7.66393,2.713504,7.66393,3.488791,7.66393,4.264078,6.797133,4.651721,-7.134696,0.3849398,-6.220684,0.3849398,-6.353604,-1.531687,-5.699956,-0.8928116,-9.635074E-14,-5.64094,-1.034839,-5.64094,-6.915254E-14,-4.794518,-0.6898924,-4.794518,0.6898924,-4.794518,1.034839,-5.64094,-9.384199,0.3876435,-10.15949,0.3876435,-9.384199,1.16293,-10.15949,1.16293,7.445982,-2.080313,8.996555,-2.080313,7.833625,-2.94711,8.608912,-2.94711,5.895408,-2.080313,6.283051,-2.94711,7.058338,-2.94711,10.54713,-2.080313,9.384199,-2.94711,10.15949,-2.94711,7.058338,3.488791,6.283051,3.488791,7.058338,4.264078,6.283051,4.264078,-8.184009,1.550574,-9.050805,1.938217,-8.184009,3.101148,-9.050805,2.713504,-8.184009,4.651721,-9.050805,4.264078,-9.050805,3.488791,-8.184009,-7.723449E-16,-9.050805,1.16293,-9.050805,0.3876435,-0.3106037,1.938217,-0.3106037,2.713504,0.5561934,1.550574,0.5561934,3.101148,-0.3106037,3.488791,-0.3106037,4.264078,0.5561934,4.651721,0.5561934,1.767013E-15,-0.3106037,1.16293,-0.3106037,0.3876435,8.608912,3.488791,7.833625,3.488791,8.608912,4.264078,7.833625,4.264078,5.699956,5.243013,6.353604,5.881888,6.220684,3.965261,7.134696,3.965261,-3.957191,1.11903,-5.895408,1.11903,-3.957191,2.909479,-5.895408,2.909479,-3.957191,0.8952242,-3.957191,1.514701E-15,-5.895408,0.8952242,-3.957191,-0.8952242,-5.895408,-0.8952242,-5.895408,1.514701E-15,-3.957191,1.11903,-5.895408,1.11903,-3.957191,2.909479,-5.895408,2.909479,5.895408,1.11903,3.957191,1.11903,5.895408,2.909479,3.957191,2.909479,5.895408,1.11903,3.957191,1.11903,5.895408,2.909479,3.957191,2.909479,2.069677,2.325861,1.034839,0.5334678,1.034839,4.118254,1.136868E-13,4.118254,1.136868E-13,0.5334678,-1.034839,4.118254,-1.034839,0.5334678,-2.069677,2.325861,-8.495203,0.979416,-10.74354,0.979416,-8.495203,3.049093,-10.74354,3.049093,10.74354,0.979416,8.495203,0.979416,10.74354,3.049093,8.495203,3.049093,8.996555,4.160626,9.384199,5.027423,10.54713,4.160626,10.15949,5.027423,7.445982,4.160626,7.833625,5.027423,8.608912,5.027423,5.895408,4.160626,6.283051,5.027423,7.058338,5.027423,10.15949,0.3876435,9.384199,0.3876435,10.15949,1.16293,9.384199,1.16293,8.608912,0.3876435,7.833625,0.3876435,8.608912,1.16293,7.833625,1.16293,-7.445982,-3.467189,-7.833625,-4.333986,-8.996555,-3.467189,-8.608912,-4.333986,-5.895408,-3.467189,-6.283051,-4.333986,-7.058338,-4.333986,-9.384199,-4.333986,-10.54713,-3.467189,-10.15949,-4.333986,10.15949,3.488791,9.384199,3.488791,10.15949,4.264078,9.384199,4.264078,-5.895408,-3.470619,-5.895408,-4.651721,-10.54713,-3.470619,-7.445982,-4.651721,-8.996555,-4.651721,-10.54713,-4.651721,-6.283051,0.3876435,-7.058338,0.3876435,-6.283051,1.16293,-7.058338,1.16293,-7.445982,5.547502,-8.996555,5.547502,-7.833625,6.414299,-8.608912,6.414299,-10.54713,5.547502,-9.384199,6.414299,-10.15949,6.414299,-5.895408,5.547502,-6.283051,6.414299,-7.058338,6.414299,-9.384199,3.488791,-10.15949,3.488791,-9.384199,4.264078,-10.15949,4.264078,-4.651721,1.550574,-4.651721,3.101148,-4.651721,8.881784E-16,-4.651721,4.651721,-3.470619,8.881784E-16,-3.470619,4.651721,-7.833625,0.3876435,-8.608912,0.3876435,-7.833625,1.16293,-8.608912,1.16293,-9.384199,1.938217,-10.15949,1.938217,-9.384199,2.713504,-10.15949,2.713504,7.058338,0.3876435,6.283051,0.3876435,7.058338,1.16293,6.283051,1.16293,8.608912,1.938217,7.833625,1.938217,8.608912,2.713504,7.833625,2.713504,5.895408,3.470619,5.895408,4.651721,10.54713,3.470619,7.445982,4.651721,8.996555,4.651721,10.54713,4.651721,-5.895408,4.651721,-5.895408,3.470619,-7.445982,4.651721,-10.54713,3.470619,-8.996555,4.651721,-10.54713,4.651721,3.470619,0,3.470619,4.651721,4.651721,0,4.651721,4.651721,4.651721,1.550574,4.651721,3.101148,4.651721,3.101148,4.651721,1.550574,4.651721,4.651721,4.651721,8.881784E-16,3.470619,4.651721,3.470619,8.881784E-16,0.8952242,3.876435,-3.470619,4.651721,1.790448,2.325861,0,3.876435,-0.8952242,3.876435,-1.790448,2.325861,0.8952242,0.7752869,-3.470619,0,-4.651721,4.651721,-4.651721,0,-4.651721,1.550574,-4.651721,3.101148,0,0.7752869,-0.8952242,0.7752869,3.937584,1.387957,-7.916782,1.387957,3.947132,2.577388,-7.905706,2.767697,1.470198E-12,-7.87916,-0.6898924,-7.87916,-7.79577E-13,3.974824,-0.5947348,3.974824,0.5947348,3.974824,0.6898924,-7.87916,-0.6898924,7.943838,-1.277282E-12,7.943838,-0.5947348,-3.910147,5.974227E-13,-3.910147,0.6898924,7.943838,0.5947348,-3.910147,-7.905706,1.260942,-7.916782,2.640682,3.947132,1.451251,3.937584,2.640682,7.905706,1.260942,-3.947132,1.451251,7.916782,2.640682,-3.937584,2.640682,7.916782,1.387957,-3.937584,1.387957,7.905706,2.767697,-3.947132,2.577388,-0.830682,1.550574,-0.830682,1.585623E-15,-1.697479,1.16293,-0.830682,1.550574,-0.830682,1.585623E-15,-1.697479,1.16293,-0.830682,1.550574,-0.830682,1.585623E-15,-1.697479,1.16293,-0.830682,1.550574,-0.830682,1.585623E-15,-1.697479,1.16293,-0.830682,1.550574,-0.830682,1.585623E-15,-1.697479,1.16293,-0.830682,1.550574,-0.830682,1.585623E-15,-1.697479,1.16293
				}
			UVIndex: *966 {
				a: 0,2,1,3,1,2,4,6,5,7,5,6,8,4,9,10,9,4,11,8,12,13,12,8,14,16,15,17,15,16,18,17,19,20,19,17,21,20,22,23,22,20,24,26,25,27,25,26,28,30,29,31,29,30,32,33,28,34,28,33,29,36,35,37,35,36,38,40,39,41,39,40,42,44,43,45,43,44,43,47,46,48,46,47,46,50,49,51,49,50,52,54,53,55,53,54,56,58,57,59,57,58,56,61,60,62,60,61,63,64,61,65,61,64,66,68,67,69,67,68,70,71,68,72,68,71,66,74,73,75,73,74,76,78,77,79,77,78,80,82,81,83,81,82,84,85,82,86,82,85,87,88,84,89,84,88,90,92,91,93,91,92,91,95,94,96,94,95,97,98,90,99,90,98,100,102,101,103,101,102,102,105,104,106,104,105,107,100,108,109,108,100,110,112,111,113,111,112,114,111,115,116,115,111,117,118,110,119,110,118,120,122,121,123,121,122,124,123,122,125,123,124,126,128,127,129,127,128,129,131,130,132,130,131,133,134,128,135,128,134,136,138,137,139,137,138,137,141,140,142,140,141,143,140,144,145,144,140,146,148,147,149,147,148,147,151,150,152,150,151,153,154,146,155,146,154,156,158,157,159,157,158,160,157,161,162,161,157,162,164,163,165,163,164,166,168,167,169,167,168,170,172,171,173,171,172,174,176,175,177,175,176,178,177,176,179,178,176,180,182,181,183,181,182,181,183,184,185,184,183,186,188,187,189,187,188,190,192,191,193,191,192,194,193,192,195,193,194,196,195,194,197,195,196,198,197,196,199,198,196,190,191,200,201,200,191,202,200,201,203,202,201,204,202,203,205,202,204,198,205,204,199,205,198,206,208,207,209,207,208,207,209,210,211,210,209,212,214,213,215,213,214,216,212,217,218,217,212,219,216,220,221,220,216,222,224,223,225,223,224,226,228,227,229,227,228,230,232,231,233,231,232,232,230,234,235,234,230,236,238,237,239,237,238,240,236,241,242,241,236,243,240,244,245,244,240,246,248,247,249,247,248,248,251,250,252,250,251,250,254,253,255,253,254,256,258,257,259,257,258,259,261,260,262,260,261,263,264,260,265,260,264,266,268,267,269,267,268,270,272,271,273,271,272,272,270,274,275,274,270,276,278,277,279,277,278,280,282,281,283,281,282,284,285,280,286,280,285,281,288,287,289,287,288,290,292,291,293,291,292,294,296,295,297,295,296,298,299,296,300,296,299,294,302,301,303,301,302,304,306,305,307,305,306,308,307,309,310,309,307,306,312,311,313,311,312,314,316,315,317,315,316,318,320,319,321,319,320,322,324,323,325,323,324,326,328,327,329,327,328,330,329,328,331,330,328,332,334,333,335,333,334,336,338,337,339,337,338,340,342,341,343,341,342,344,346,345,347,345,346,348,345,347,349,348,347,350,348,349,351,350,349,352,354,353,355,353,354,356,358,357,359,357,358,360,362,361,363,361,362,364,360,365,366,365,360,367,364,368,369,368,364,370,372,371,373,371,372,374,376,375,377,375,376,378,380,379,381,379,380,382,378,383,384,383,378,380,386,385,387,385,386,388,390,389,391,389,390,392,394,393,395,393,394,396,395,394,397,396,394,398,400,399,401,399,400,402,404,403,405,403,404,403,407,406,408,406,407,409,410,402,411,402,410,412,414,413,415,413,414,416,418,417,419,417,418,420,419,418,421,419,420,422,424,423,425,423,424,426,428,427,429,427,428,430,432,431,433,431,432,434,436,435,437,435,436,438,440,439,441,439,440,442,441,440,443,442,440,444,446,445,447,445,446,448,447,446,449,447,448,450,452,451,453,451,452,454,453,452,455,453,454,456,458,457,459,457,458,460,459,458,461,459,460,461,460,462,463,462,460,464,461,462,465,462,463,466,465,463,467,466,463,464,468,461,469,461,468,463,469,467,463,470,469,471,469,470,472,471,470,470,473,472,474,469,468,475,469,474,467,469,475,121,123,438,440,438,123,420,450,421,451,421,450,445,447,392,394,392,447,476,478,477,479,477,478,480,482,481,483,481,482,482,480,484,485,484,480,486,488,487,489,487,488,487,489,490,491,490,489,492,494,493,495,493,494,496,498,497,499,497,498,500,502,501,503,501,502,504,506,505,507,509,508,510,512,511,513,515,514,516,518,517,519,521,520
			}
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: ""
			MappingInformationType: "ByPolygon"
			ReferenceInformationType: "IndexToDirect"
			Materials: *322 {
				a: 0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,2,2,2,2,2,2,
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTexture"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
	}

	Material: 20310, "Material::greyLight", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.8431373,0.9019608,0.9529412
			P: "DiffuseColor", "Color", "", "A",0.8431373,0.9019608,0.9529412
		}
	}

	Material: 19440, "Material::greyDark", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",0.5294118,0.6,0.6666667
			P: "DiffuseColor", "Color", "", "A",0.5294118,0.6,0.6666667
		}
	}

	Material: 19436, "Material::_defaultMat", "" {
		Version: 102
		ShadingModel: "phong"
		MultiLayer: 0
		Properties70:  {
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "DiffuseColor", "Color", "", "A",1,1,1
		}
	}
}
; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::Mesh meatTenderizer, Model::RootNode
	C: "OO",5576715385776920629,0

	;Geometry::, Model::Mesh meatTenderizer
	C: "OO",4905993588937503737,5576715385776920629

	;Material::greyLight, Model::Mesh meatTenderizer
	C: "OO",20310,5576715385776920629

	;Material::greyDark, Model::Mesh meatTenderizer
	C: "OO",19440,5576715385776920629

	;Material::_defaultMat, Model::Mesh meatTenderizer
	C: "OO",19436,5576715385776920629

}
