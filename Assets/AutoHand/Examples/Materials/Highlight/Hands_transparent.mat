%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Hands_transparent
  m_Shader: {fileID: 4800000, guid: 5e0c88c3d888e4489929314c7c1ebf4b, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - _EMISSION
  m_LightmapFlags: 1
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _Bump:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Cube:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DepthTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _InconfidenceTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: 824c12d1408a54d7fa756f82f12139a6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BumpScale: 1
    - _ChromaAlphaCutoff: 0.01
    - _ChromaShadows: 0.02
    - _ChromaToleranceA: 20
    - _ChromaToleranceB: 15
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _FadeEnd: 0.5
    - _FadeStart: 0.5
    - _Fresnel: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Intensity: 0.5
    - _InvFade: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _OutlineWidth: 0.005
    - _Parallax: 0.02
    - _RimPower: 3.82
    - _Shininess: 0.75
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _Thickness: 0.5
    - _UVSec: 0
    - _Visible: 1
    - _ZWrite: 1
    - _face: 0
    - _linearToSrgb: 0
    - _premultiply: 0
    m_Colors:
    - _Color: {r: 0.6544118, g: 0.8140972, b: 1, a: 0.478}
    - _EmisColor: {r: 0.2, g: 0.2, b: 0.2, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _InnerColor: {r: 0.057627246, g: 0.22133458, b: 0.33018863, a: 1}
    - _MyColor: {r: 0.87058824, g: 0.87058824, b: 0.87058824, a: 1}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _ReflectColor: {r: 1, g: 0, b: 0, a: 0.5019608}
    - _RimColor: {r: 1, g: 1, b: 1, a: 1}
    - _SpecColor: {r: 0.50735295, g: 0.50735295, b: 0.50735295, a: 1}
    - _TintColor: {r: 0.5, g: 0.5, b: 0.5, a: 0.5}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
