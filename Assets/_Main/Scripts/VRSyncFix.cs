using UnityEngine;
using Mirror;

namespace VRMultiplayer
{
    /// <summary>
    /// Quick fix component for VR sync issues
    /// Add this to VRPlayer objects to ensure proper synchronization
    /// </summary>
    public class VRSyncFix : NetworkBehaviour
    {
        [Header("Sync Fix Settings")]
        [SerializeField] private bool forceSync = true;
        [SerializeField] private float forceSyncRate = 10f;
        [SerializeField] private bool showDebugInfo = true;
        
        private VRPlayer vrPlayer;
        private float lastForceSyncTime;
        
        // Simple sync variables that definitely work
        [SyncVar(hook = nameof(OnHeadPositionChanged))]
        private Vector3 syncHeadPosition;
        
        [SyncVar(hook = nameof(OnHeadRotationChanged))]
        private Quaternion syncHeadRotation;
        
        [SyncVar(hook = nameof(OnLeftHandPositionChanged))]
        private Vector3 syncLeftHandPosition;
        
        [SyncVar(hook = nameof(OnRightHandPositionChanged))]
        private Vector3 syncRightHandPosition;
        
        private void Start()
        {
            vrPlayer = GetComponent<VRPlayer>();
            
            if (showDebugInfo)
            {
                Debug.Log($"[VRSyncFix] Started on {gameObject.name} - IsLocal: {isLocalPlayer}");
            }
        }
        
        private void Update()
        {
            if (isLocalPlayer && forceSync)
            {
                if (Time.time - lastForceSyncTime > 1f / forceSyncRate)
                {
                    ForceSyncData();
                    lastForceSyncTime = Time.time;
                }
            }
        }
        
        private void ForceSyncData()
        {
            if (vrPlayer == null) return;
            
            // Force update sync vars
            if (vrPlayer.GetHead() != null)
            {
                syncHeadPosition = vrPlayer.GetHead().position;
                syncHeadRotation = vrPlayer.GetHead().rotation;
            }
            
            if (vrPlayer.GetLeftHand() != null)
            {
                syncLeftHandPosition = vrPlayer.GetLeftHand().position;
            }
            
            if (vrPlayer.GetRightHand() != null)
            {
                syncRightHandPosition = vrPlayer.GetRightHand().position;
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"[VRSyncFix] Force synced data - Head: {syncHeadPosition}");
            }
        }
        
        // SyncVar hooks - these will be called on remote clients
        private void OnHeadPositionChanged(Vector3 oldValue, Vector3 newValue)
        {
            if (isLocalPlayer) return;
            
            if (vrPlayer != null && vrPlayer.GetHead() != null)
            {
                vrPlayer.GetHead().position = newValue;
                
                if (showDebugInfo)
                {
                    Debug.Log($"[VRSyncFix] Remote head position updated: {newValue}");
                }
            }
        }
        
        private void OnHeadRotationChanged(Quaternion oldValue, Quaternion newValue)
        {
            if (isLocalPlayer) return;
            
            if (vrPlayer != null && vrPlayer.GetHead() != null)
            {
                vrPlayer.GetHead().rotation = newValue;
            }
        }
        
        private void OnLeftHandPositionChanged(Vector3 oldValue, Vector3 newValue)
        {
            if (isLocalPlayer) return;
            
            if (vrPlayer != null && vrPlayer.GetLeftHand() != null)
            {
                vrPlayer.GetLeftHand().position = newValue;
                
                if (showDebugInfo)
                {
                    Debug.Log($"[VRSyncFix] Remote left hand position updated: {newValue}");
                }
            }
        }
        
        private void OnRightHandPositionChanged(Vector3 oldValue, Vector3 newValue)
        {
            if (isLocalPlayer) return;
            
            if (vrPlayer != null && vrPlayer.GetRightHand() != null)
            {
                vrPlayer.GetRightHand().position = newValue;
                
                if (showDebugInfo)
                {
                    Debug.Log($"[VRSyncFix] Remote right hand position updated: {newValue}");
                }
            }
        }
        
        // Manual sync methods
        [ContextMenu("Force Sync Now")]
        public void ForceSyncNow()
        {
            if (isLocalPlayer)
            {
                ForceSyncData();
                Debug.Log("[VRSyncFix] Manual sync triggered");
            }
        }
        
        [ContextMenu("Test Remote Visibility")]
        public void TestRemoteVisibility()
        {
            if (!isLocalPlayer)
            {
                // Create a visible marker for remote player
                GameObject marker = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                marker.name = "RemotePlayerMarker";
                marker.transform.SetParent(transform);
                marker.transform.localPosition = Vector3.up * 2f;
                marker.transform.localScale = Vector3.one * 0.3f;
                
                // Make it bright red
                var renderer = marker.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material.color = Color.red;
                }
                
                // Remove collider
                var collider = marker.GetComponent<Collider>();
                if (collider != null)
                {
                    DestroyImmediate(collider);
                }
                
                Debug.Log("[VRSyncFix] Created visibility marker for remote player");
            }
        }
        
        // Public methods for debugging
        public void EnableDebugInfo()
        {
            showDebugInfo = true;
        }
        
        public void DisableDebugInfo()
        {
            showDebugInfo = false;
        }
        
        public void SetSyncRate(float rate)
        {
            forceSyncRate = Mathf.Clamp(rate, 1f, 60f);
        }
        
        // Get current sync status
        public string GetSyncStatus()
        {
            string status = $"VRSyncFix Status:\n";
            status += $"- IsLocal: {isLocalPlayer}\n";
            status += $"- HasAuthority: {authority}\n";
            status += $"- ForceSync: {forceSync}\n";
            status += $"- SyncRate: {forceSyncRate}\n";
            status += $"- Head Pos: {syncHeadPosition}\n";
            status += $"- Left Hand: {syncLeftHandPosition}\n";
            status += $"- Right Hand: {syncRightHandPosition}";
            
            return status;
        }
        
        private void OnGUI()
        {
            if (!showDebugInfo || isLocalPlayer) return;
            
            // Show sync info for remote players
            GUI.Box(new Rect(10, Screen.height - 150, 300, 140), "Remote Player Sync");
            
            GUILayout.BeginArea(new Rect(20, Screen.height - 130, 280, 120));
            
            GUILayout.Label($"Player: {gameObject.name}");
            GUILayout.Label($"Head: {syncHeadPosition.ToString("F2")}");
            GUILayout.Label($"Left Hand: {syncLeftHandPosition.ToString("F2")}");
            GUILayout.Label($"Right Hand: {syncRightHandPosition.ToString("F2")}");
            
            if (GUILayout.Button("Create Visibility Marker"))
            {
                TestRemoteVisibility();
            }
            
            GUILayout.EndArea();
        }
    }
}
