using UnityEngine;
using UnityEngine.UI;
using Mirror;
using TMPro;

namespace VRMultiplayer
{
    public class VRMultiplayerDemo : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private Button hostButton;
        [SerializeField] private Button clientButton;
        [SerializeField] private But<PERSON> disconnectButton;
        [SerializeField] private TMP_InputField ipInputField;
        [SerializeField] private TextMeshProUGUI statusText;
        [SerializeField] private TextMeshProUGUI playersCountText;
        [SerializeField] private TextMeshProUGUI performanceText;
        
        [Header("Demo Objects")]
        [SerializeField] private GameObject[] demoObjects;
        [SerializeField] private Transform spawnArea;
        [SerializeField] private int maxDemoObjects = 10;
        
        private VRNetworkManager networkManager;
        private VROptimizer vrOptimizer;
        private float updateUITimer = 0f;
        private const float UI_UPDATE_INTERVAL = 0.5f;
        
        private void Start()
        {
            // Find components
            networkManager = FindObjectOfType<VRNetworkManager>();
            vrOptimizer = FindObjectOfType<VROptimizer>();
            
            // Setup UI
            SetupUI();
            
            // Auto-optimize on start
            if (vrOptimizer != null)
            {
                vrOptimizer.OptimizeForPlatform();
            }
            
            // Set default IP for testing
            if (ipInputField != null)
            {
                ipInputField.text = "************"; // Change this to your test IP
            }
        }
        
        private void Update()
        {
            updateUITimer += Time.deltaTime;
            if (updateUITimer >= UI_UPDATE_INTERVAL)
            {
                UpdateUI();
                updateUITimer = 0f;
            }
        }
        
        private void SetupUI()
        {
            // Setup buttons
            if (hostButton != null)
                hostButton.onClick.AddListener(StartHost);
                
            if (clientButton != null)
                clientButton.onClick.AddListener(StartClient);
                
            if (disconnectButton != null)
                disconnectButton.onClick.AddListener(Disconnect);
            
            UpdateUI();
        }
        
        private void UpdateUI()
        {
            bool isNetworkActive = NetworkServer.active || NetworkClient.active;
            
            // Update button states
            if (hostButton != null)
                hostButton.interactable = !isNetworkActive;
                
            if (clientButton != null)
                clientButton.interactable = !isNetworkActive;
                
            if (disconnectButton != null)
                disconnectButton.interactable = isNetworkActive;
            
            // Update status text
            if (statusText != null)
            {
                string status = "Disconnected";
                if (NetworkServer.active && NetworkClient.active)
                    status = "Host";
                else if (NetworkClient.active)
                    status = "Client";
                else if (NetworkServer.active)
                    status = "Server";
                    
                statusText.text = $"Status: {status}";
            }
            
            // Update players count
            if (playersCountText != null)
            {
                int playerCount = 0;
                if (NetworkServer.active)
                {
                    playerCount = NetworkServer.connections.Count;
                }
                playersCountText.text = $"Players: {playerCount}";
            }
            
            // Update performance info
            if (performanceText != null && vrOptimizer != null)
            {
                float fps = vrOptimizer.GetCurrentFPS();
                float frameTime = vrOptimizer.GetAverageFrameTime() * 1000f; // Convert to ms
                performanceText.text = $"FPS: {fps:F1}\nFrame Time: {frameTime:F1}ms";
            }
        }
        
        public void StartHost()
        {
            if (networkManager != null)
            {
                networkManager.StartHostButton();
                Debug.Log("Starting as Host...");
            }
        }
        
        public void StartClient()
        {
            if (networkManager != null)
            {
                // Set IP if provided
                if (ipInputField != null && !string.IsNullOrEmpty(ipInputField.text))
                {
                    networkManager.networkAddress = ipInputField.text;
                }
                
                networkManager.StartClientButton();
                Debug.Log($"Starting as Client, connecting to: {networkManager.networkAddress}");
            }
        }
        
        public void Disconnect()
        {
            if (networkManager != null)
            {
                networkManager.DisconnectButton();
                Debug.Log("Disconnecting...");
            }
        }
        
        [ContextMenu("Spawn Demo Objects")]
        public void SpawnDemoObjects()
        {
            if (!NetworkServer.active)
            {
                Debug.LogWarning("Can only spawn objects when hosting/server");
                return;
            }
            
            if (demoObjects == null || demoObjects.Length == 0)
            {
                Debug.LogWarning("No demo objects assigned");
                return;
            }
            
            Vector3 spawnCenter = spawnArea != null ? spawnArea.position : Vector3.up * 2f;
            
            for (int i = 0; i < maxDemoObjects; i++)
            {
                // Random position around spawn area
                Vector3 randomOffset = new Vector3(
                    Random.Range(-2f, 2f),
                    Random.Range(0f, 1f),
                    Random.Range(-2f, 2f)
                );
                
                Vector3 spawnPosition = spawnCenter + randomOffset;
                
                // Random rotation
                Quaternion spawnRotation = Quaternion.Euler(
                    Random.Range(0f, 360f),
                    Random.Range(0f, 360f),
                    Random.Range(0f, 360f)
                );
                
                // Pick random demo object
                GameObject prefab = demoObjects[Random.Range(0, demoObjects.Length)];
                
                // Spawn and setup networking
                GameObject spawnedObject = Instantiate(prefab, spawnPosition, spawnRotation);
                
                // Add network components if missing
                if (spawnedObject.GetComponent<NetworkIdentity>() == null)
                    spawnedObject.AddComponent<NetworkIdentity>();
                    
                if (spawnedObject.GetComponent<NetworkGrabbable>() == null)
                    spawnedObject.AddComponent<NetworkGrabbable>();
                    
                if (spawnedObject.GetComponent<RigidbodySync>() == null && spawnedObject.GetComponent<Rigidbody>() != null)
                    spawnedObject.AddComponent<RigidbodySync>();
                
                // Spawn on network
                NetworkServer.Spawn(spawnedObject);
                
                Debug.Log($"Spawned demo object: {spawnedObject.name}");
            }
        }
        
        [ContextMenu("Clear Demo Objects")]
        public void ClearDemoObjects()
        {
            if (!NetworkServer.active)
            {
                Debug.LogWarning("Can only clear objects when hosting/server");
                return;
            }
            
            // Find all NetworkGrabbable objects and destroy them
            NetworkGrabbable[] networkGrabbables = FindObjectsOfType<NetworkGrabbable>();
            
            foreach (var grabbable in networkGrabbables)
            {
                if (grabbable.GetComponent<VRPlayer>() == null) // Don't destroy players
                {
                    NetworkServer.Destroy(grabbable.gameObject);
                }
            }
            
            Debug.Log($"Cleared {networkGrabbables.Length} demo objects");
        }
        
        // Quality control methods
        public void SetQualityLow()
        {
            if (vrOptimizer != null)
                vrOptimizer.SetQualityLevel(0);
        }
        
        public void SetQualityMedium()
        {
            if (vrOptimizer != null)
                vrOptimizer.SetQualityLevel(1);
        }
        
        public void SetQualityHigh()
        {
            if (vrOptimizer != null)
                vrOptimizer.SetQualityLevel(2);
        }
        
        // Debug methods
        [ContextMenu("Log Network Info")]
        public void LogNetworkInfo()
        {
            Debug.Log($"Network Server Active: {NetworkServer.active}");
            Debug.Log($"Network Client Active: {NetworkClient.active}");
            Debug.Log($"Network Address: {networkManager?.networkAddress}");
            Debug.Log($"Connected Players: {NetworkServer.connections.Count}");
            
            if (NetworkServer.active)
            {
                foreach (var conn in NetworkServer.connections.Values)
                {
                    Debug.Log($"Connection {conn.connectionId}: {conn.address}");
                }
            }
        }
        
        [ContextMenu("Log VR Players")]
        public void LogVRPlayers()
        {
            VRPlayer[] vrPlayers = FindObjectsOfType<VRPlayer>();
            Debug.Log($"Found {vrPlayers.Length} VR Players:");
            
            foreach (var player in vrPlayers)
            {
                Debug.Log($"Player {player.netId}: Local={player.IsLocalVRPlayer}, Authority={player.authority}");
            }
        }
        
        [ContextMenu("Log Performance")]
        public void LogPerformance()
        {
            if (vrOptimizer != null)
            {
                Debug.Log($"Current FPS: {vrOptimizer.GetCurrentFPS():F1}");
                Debug.Log($"Average Frame Time: {vrOptimizer.GetAverageFrameTime() * 1000f:F1}ms");
            }
            
            Debug.Log($"Quality Level: {QualitySettings.GetQualityLevel()}");
            Debug.Log($"Target Frame Rate: {Application.targetFrameRate}");
        }
    }
}
