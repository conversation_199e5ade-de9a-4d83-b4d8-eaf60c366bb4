using UnityEngine;
using Mirror;
using Mirror.Discovery;
using System.Collections.Generic;
using System.Net;

namespace VRMultiplayer
{
    public class VRNetworkManager : NetworkManager
    {
        [Header("VR Multiplayer Settings")]
        [SerializeField] private GameObject vrPlayerPrefab;
        [SerializeField] private bool autoStartAsHost = false;
        [SerializeField] private bool enableLANDiscovery = true;
        
        [Header("UI References")]
        [SerializeField] private GameObject hostButton;
        [SerializeField] private GameObject clientButton;
        [SerializeField] private GameObject disconnectButton;
        [SerializeField] private TMPro.TextMeshProUGUI statusText;
        [SerializeField] private TMPro.TMP_InputField ipInputField;
        
        private NetworkDiscovery networkDiscovery;
        private Dictionary<long, ServerResponse> discoveredServers = new Dictionary<long, ServerResponse>();
        
        public override void Start()
        {
            base.Start();
            
            // Setup network discovery
            if (enableLANDiscovery)
            {
                networkDiscovery = GetComponent<NetworkDiscovery>();
                if (networkDiscovery == null)
                    networkDiscovery = gameObject.AddComponent<NetworkDiscovery>();
                    
                networkDiscovery.OnServerFound.AddListener(OnServerFound);
            }
            
            // Auto-start as host if enabled
            if (autoStartAsHost && !NetworkServer.active && !NetworkClient.active)
            {
                StartHost();
            }
            
            UpdateUI();
        }
        
        public void StartHostButton()
        {
            if (NetworkServer.active || NetworkClient.active)
            {
                Debug.LogWarning("Network already active");
                return;
            }
            
            StartHost();
            
            if (enableLANDiscovery && networkDiscovery != null)
            {
                networkDiscovery.AdvertiseServer();
            }
        }
        
        public void StartClientButton()
        {
            if (NetworkClient.active)
            {
                Debug.LogWarning("Client already active");
                return;
            }
            
            // Try to connect to manually entered IP first
            if (ipInputField != null && !string.IsNullOrEmpty(ipInputField.text))
            {
                networkAddress = ipInputField.text;
                StartClient();
            }
            // Otherwise try to find servers via LAN discovery
            else if (enableLANDiscovery && networkDiscovery != null)
            {
                discoveredServers.Clear();
                networkDiscovery.StartDiscovery();
                
                // Wait a bit and connect to first found server
                Invoke(nameof(ConnectToFirstFoundServer), 2f);
            }
            else
            {
                Debug.LogError("No IP address provided and LAN discovery disabled");
            }
        }
        
        public void DisconnectButton()
        {
            if (NetworkServer.active && NetworkClient.active)
            {
                StopHost();
            }
            else if (NetworkClient.active)
            {
                StopClient();
            }
            else if (NetworkServer.active)
            {
                StopServer();
            }
            
            if (networkDiscovery != null)
            {
                networkDiscovery.StopDiscovery();
            }
        }
        
        private void ConnectToFirstFoundServer()
        {
            if (discoveredServers.Count > 0)
            {
                var firstServer = new List<ServerResponse>(discoveredServers.Values)[0];
                networkAddress = firstServer.EndPoint.Address.ToString();
                StartClient();
                networkDiscovery.StopDiscovery();
            }
            else
            {
                Debug.LogWarning("No servers found via LAN discovery");
                UpdateStatusText("No servers found. Try entering IP manually.");
            }
        }
        
        private void OnServerFound(ServerResponse info)
        {
            discoveredServers[info.serverId] = info;
            Debug.Log($"Found server: {info.EndPoint.Address}:{info.EndPoint.Port}");
        }
        
        public override void OnStartHost()
        {
            base.OnStartHost();
            UpdateStatusText("Started as Host");
            UpdateUI();
        }
        
        public override void OnStartClient()
        {
            base.OnStartClient();
            UpdateStatusText("Connecting to server...");
            UpdateUI();
        }
        
        public override void OnClientConnect()
        {
            base.OnClientConnect();
            UpdateStatusText("Connected to server");
            UpdateUI();
        }
        
        public override void OnClientDisconnect()
        {
            base.OnClientDisconnect();
            UpdateStatusText("Disconnected from server");
            UpdateUI();
        }
        
        public override void OnStopHost()
        {
            base.OnStopHost();
            UpdateStatusText("Stopped hosting");
            UpdateUI();
        }
        
        public override void OnStopClient()
        {
            base.OnStopClient();
            UpdateStatusText("Client stopped");
            UpdateUI();
        }
        
        public override void OnServerAddPlayer(NetworkConnectionToClient conn)
        {
            // Try to use VRPlayerSpawner if available
            VRPlayerSpawner spawner = FindObjectOfType<VRPlayerSpawner>();
            if (spawner != null && spawner.HasValidSetup())
            {
                spawner.SpawnVRPlayer(conn);
                Debug.Log($"VR Player spawned via VRPlayerSpawner for connection {conn.connectionId}");
                return;
            }

            // Fallback to default spawning
            GameObject player = Instantiate(vrPlayerPrefab);
            NetworkServer.AddPlayerForConnection(conn, player);

            Debug.Log($"Player added for connection {conn.connectionId}");
        }
        
        private void UpdateUI()
        {
            bool isActive = NetworkServer.active || NetworkClient.active;
            
            if (hostButton != null)
                hostButton.SetActive(!isActive);
                
            if (clientButton != null)
                clientButton.SetActive(!isActive);
                
            if (disconnectButton != null)
                disconnectButton.SetActive(isActive);
        }
        
        private void UpdateStatusText(string message)
        {
            if (statusText != null)
                statusText.text = message;
                
            Debug.Log($"VRNetworkManager: {message}");
        }
        
        // Editor helper method for auto-configuration
        [ContextMenu("Auto Configure")]
        public void AutoConfigure()
        {
            // Find VR player prefab
            if (vrPlayerPrefab == null)
            {
                var foundPrefab = Resources.Load<GameObject>("VRPlayer");
                if (foundPrefab != null)
                    vrPlayerPrefab = foundPrefab;
            }
            
            // Setup transport if not set
            if (transport == null)
            {
                var kcp = GetComponent<kcp2k.KcpTransport>();
                if (kcp == null)
                    kcp = gameObject.AddComponent<kcp2k.KcpTransport>();
                transport = kcp;
            }
            
            Debug.Log("VRNetworkManager auto-configured");
        }
    }
}
