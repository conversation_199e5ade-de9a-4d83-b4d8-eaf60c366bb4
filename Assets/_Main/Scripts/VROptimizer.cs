using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using ShadowQuality = UnityEngine.ShadowQuality;
using ShadowResolution = UnityEngine.ShadowResolution;

namespace VRMultiplayer
{
    public class VROptimizer : MonoBehaviour
    {
        [Header("Performance Settings")]
        [SerializeField] private bool autoOptimizeOnStart = true;
        [SerializeField] private bool enableDynamicBatching = true;
        [SerializeField] private bool enableGPUInstancing = true;
        [SerializeField] private bool enableSRPBatcher = true;
        
        [Header("Quality Settings")]
        [SerializeField] private int targetFrameRate = 72; // Quest 2/3 target
        [SerializeField] private bool adaptiveQuality = true;
        [SerializeField] private float minFrameTime = 0.013f; // ~77 FPS
        [SerializeField] private float maxFrameTime = 0.020f; // ~50 FPS
        
        [Header("Rendering Settings")]
        [SerializeField] private bool useFixedFoveatedRendering = true;
        [SerializeField] private bool enableOcclusionCulling = true;
        [SerializeField] private float renderScale = 1.0f;
        
        [Header("Network Optimization")]
        [SerializeField] private bool optimizeNetworkSendRates = true;
        [SerializeField] private float lowPerformanceSendRate = 10f;
        [SerializeField] private float normalSendRate = 20f;
        [SerializeField] private float highPerformanceSendRate = 30f;
        
        private float frameTimeAccumulator = 0f;
        private int frameCount = 0;
        private float averageFrameTime = 0f;
        private int currentQualityLevel = 1; // 0=Low, 1=Medium, 2=High
        
        private void Start()
        {
            if (autoOptimizeOnStart)
            {
                OptimizeForPlatform();
            }
            
            Application.targetFrameRate = targetFrameRate;
        }
        
        private void Update()
        {
            if (adaptiveQuality)
            {
                UpdatePerformanceMetrics();
                AdjustQualityBasedOnPerformance();
            }
        }
        
        public void OptimizeForPlatform()
        {
            // Detect platform and apply optimizations
            if (Application.platform == RuntimePlatform.Android)
            {
                OptimizeForQuest();
            }
            else if (Application.platform == RuntimePlatform.WebGLPlayer)
            {
                OptimizeForWebGL();
            }
            else
            {
                OptimizeForPC();
            }
            
            ApplyGeneralOptimizations();
        }
        
        private void OptimizeForQuest()
        {
            Debug.Log("Optimizing for Quest platform");
            
            // Quest-specific optimizations
            QualitySettings.SetQualityLevel(1); // Medium quality
            
            // Enable Fixed Foveated Rendering if available
            if (useFixedFoveatedRendering)
            {
                #if UNITY_ANDROID && !UNITY_EDITOR
                try
                {
                    // Quest FFR settings
                    UnityEngine.XR.XRSettings.eyeTextureResolutionScale = 0.8f;
                }
                catch (System.Exception e)
                {
                    Debug.LogWarning($"Could not set FFR: {e.Message}");
                }
                #endif
            }
            
            // Reduce render scale for better performance
            renderScale = 0.8f;
            SetRenderScale(renderScale);
            
            // Optimize physics
            Physics.defaultSolverIterations = 4;
            Physics.defaultSolverVelocityIterations = 1;
            
            // Reduce shadow quality
            QualitySettings.shadows = ShadowQuality.HardOnly;
            QualitySettings.shadowResolution = ShadowResolution.Low;
            QualitySettings.shadowDistance = 10f;
        }
        
        private void OptimizeForWebGL()
        {
            Debug.Log("Optimizing for WebGL platform");
            
            // WebGL-specific optimizations
            QualitySettings.SetQualityLevel(0); // Low quality for WebGL
            
            // Disable expensive features
            QualitySettings.shadows = ShadowQuality.Disable;
            QualitySettings.realtimeReflectionProbes = false;
            
            // Reduce render scale significantly
            renderScale = 0.6f;
            SetRenderScale(renderScale);
            
            // Optimize physics for WebGL
            Physics.defaultSolverIterations = 2;
            Physics.defaultSolverVelocityIterations = 1;
            Time.fixedDeltaTime = 0.02f; // 50Hz physics
            
            // Reduce network send rates
            if (optimizeNetworkSendRates)
            {
                UpdateNetworkSendRates(lowPerformanceSendRate);
            }
        }
        
        private void OptimizeForPC()
        {
            Debug.Log("Optimizing for PC platform");
            
            // PC can handle higher quality
            QualitySettings.SetQualityLevel(2); // High quality
            
            renderScale = 1.0f;
            SetRenderScale(renderScale);
            
            // Better physics for PC
            Physics.defaultSolverIterations = 6;
            Physics.defaultSolverVelocityIterations = 2;
            
            if (optimizeNetworkSendRates)
            {
                UpdateNetworkSendRates(highPerformanceSendRate);
            }
        }
        
        private void ApplyGeneralOptimizations()
        {
            // Enable batching
            if (enableDynamicBatching)
            {
                // Dynamic batching is enabled in Player Settings
            }
            
            if (enableGPUInstancing)
            {
                // GPU Instancing is enabled per material
            }
            
            if (enableSRPBatcher)
            {
                GraphicsSettings.useScriptableRenderPipelineBatching = true;
            }
            
            // Enable occlusion culling
            if (enableOcclusionCulling)
            {
                Camera.main.useOcclusionCulling = true;
            }
            
            // Optimize garbage collection
            System.GC.Collect();
        }
        
        private void UpdatePerformanceMetrics()
        {
            frameTimeAccumulator += Time.unscaledDeltaTime;
            frameCount++;
            
            // Update average every 30 frames
            if (frameCount >= 30)
            {
                averageFrameTime = frameTimeAccumulator / frameCount;
                frameTimeAccumulator = 0f;
                frameCount = 0;
            }
        }
        
        private void AdjustQualityBasedOnPerformance()
        {
            if (averageFrameTime > maxFrameTime && currentQualityLevel > 0)
            {
                // Performance is poor, reduce quality
                currentQualityLevel--;
                ApplyQualityLevel(currentQualityLevel);
                Debug.Log($"Reduced quality to level {currentQualityLevel}");
            }
            else if (averageFrameTime < minFrameTime && currentQualityLevel < 2)
            {
                // Performance is good, increase quality
                currentQualityLevel++;
                ApplyQualityLevel(currentQualityLevel);
                Debug.Log($"Increased quality to level {currentQualityLevel}");
            }
        }
        
        private void ApplyQualityLevel(int level)
        {
            switch (level)
            {
                case 0: // Low
                    SetRenderScale(0.6f);
                    QualitySettings.shadows = ShadowQuality.Disable;
                    if (optimizeNetworkSendRates)
                        UpdateNetworkSendRates(lowPerformanceSendRate);
                    break;
                    
                case 1: // Medium
                    SetRenderScale(0.8f);
                    QualitySettings.shadows = ShadowQuality.HardOnly;
                    if (optimizeNetworkSendRates)
                        UpdateNetworkSendRates(normalSendRate);
                    break;
                    
                case 2: // High
                    SetRenderScale(1.0f);
                    QualitySettings.shadows = ShadowQuality.All;
                    if (optimizeNetworkSendRates)
                        UpdateNetworkSendRates(highPerformanceSendRate);
                    break;
            }
        }
        
        private void SetRenderScale(float scale)
        {
            // Set render scale for URP
            var urpAsset = GraphicsSettings.defaultRenderPipeline as UniversalRenderPipelineAsset;
            if (urpAsset != null)
            {
                // Note: This requires reflection or custom URP asset modification
                // For now, we'll use XR settings if available
                #if UNITY_XR_ENABLED
                UnityEngine.XR.XRSettings.renderViewportScale = scale;
                #endif
            }
        }
        
        private void UpdateNetworkSendRates(float sendRate)
        {
            // Update all VRPlayer send rates
            VRPlayer[] vrPlayers = FindObjectsOfType<VRPlayer>();
            foreach (var player in vrPlayers)
            {
                // This would require exposing sendRate in VRPlayer
                // player.SetSendRate(sendRate);
            }
            
            // Update all RigidbodySync send rates
            RigidbodySync[] rigidbodySyncs = FindObjectsOfType<RigidbodySync>();
            foreach (var sync in rigidbodySyncs)
            {
                // This would require exposing sendRate in RigidbodySync
                // sync.SetSendRate(sendRate);
            }
        }
        
        // Public methods for manual control
        public void SetQualityLevel(int level)
        {
            currentQualityLevel = Mathf.Clamp(level, 0, 2);
            ApplyQualityLevel(currentQualityLevel);
        }
        
        public void ForceOptimization()
        {
            OptimizeForPlatform();
        }
        
        public float GetAverageFrameTime()
        {
            return averageFrameTime;
        }
        
        public float GetCurrentFPS()
        {
            return averageFrameTime > 0 ? 1f / averageFrameTime : 0f;
        }
    }
}
