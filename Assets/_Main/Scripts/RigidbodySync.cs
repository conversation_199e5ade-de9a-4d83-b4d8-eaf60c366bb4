using UnityEngine;
using Mirror;

namespace VRMultiplayer
{
    [RequireComponent(typeof(Rigidbody))]
    public class RigidbodySync : NetworkBehaviour
    {
        [Header("Sync Settings")]
        [SerializeField] private float sendRate = 20f;
        [SerializeField] private bool syncVelocity = true;
        [SerializeField] private bool syncAngularVelocity = true;
        [SerializeField] private bool smoothRemoteMovement = true;
        [SerializeField] private float smoothingSpeed = 15f;
        [SerializeField] private float snapThreshold = 2f;
        
        [Header("Optimization")]
        [SerializeField] private float minMoveDistance = 0.01f;
        [SerializeField] private float minRotateAngle = 1f;
        [SerializeField] private bool onlyUpdateWhenChanged = true;
        
        // Network sync variables
        [SyncVar] private Vector3 networkPosition;
        [SyncVar] private Quaternion networkRotation;
        [SyncVar] private Vector3 networkVelocity;
        [SyncVar] private Vector3 networkAngularVelocity;
        [SyncVar] private bool networkIsKinematic;
        
        // Components
        private Rigidbody rb;
        private Transform cachedTransform;
        
        // Smoothing
        private Vector3 targetPosition;
        private Quaternion targetRotation;
        private Vector3 targetVelocity;
        private Vector3 targetAngularVelocity;
        
        // Optimization
        private Vector3 lastSentPosition;
        private Quaternion lastSentRotation;
        private float lastSendTime;
        private bool hasAuthority;
        
        private void Awake()
        {
            rb = GetComponent<Rigidbody>();
            cachedTransform = transform;
            
            // Initialize targets
            targetPosition = cachedTransform.position;
            targetRotation = cachedTransform.rotation;
            targetVelocity = Vector3.zero;
            targetAngularVelocity = Vector3.zero;
        }
        
        public override void OnStartClient()
        {
            base.OnStartClient();
            
            // Initialize network values for smooth interpolation
            if (!hasAuthority)
            {
                targetPosition = networkPosition;
                targetRotation = networkRotation;
                targetVelocity = networkVelocity;
                targetAngularVelocity = networkAngularVelocity;
            }
        }
        
        public override void OnStartAuthority()
        {
            base.OnStartAuthority();
            hasAuthority = true;
            
            // Make sure rigidbody is not kinematic when we have authority
            if (rb != null && rb.isKinematic && !networkIsKinematic)
            {
                rb.isKinematic = false;
            }
        }
        
        public override void OnStopAuthority()
        {
            base.OnStopAuthority();
            hasAuthority = false;
        }
        
        private void FixedUpdate()
        {
            if (hasAuthority)
            {
                // Send data to server at specified rate
                if (Time.fixedTime - lastSendTime > 1f / sendRate)
                {
                    if (ShouldSendUpdate())
                    {
                        SendRigidbodyData();
                        lastSendTime = Time.fixedTime;
                    }
                }
            }
            else
            {
                // Apply received data for non-authority clients
                if (smoothRemoteMovement)
                {
                    SmoothRemoteMovement();
                }
                else
                {
                    ApplyNetworkTransform();
                }
            }
        }
        
        private bool ShouldSendUpdate()
        {
            if (!onlyUpdateWhenChanged)
                return true;
                
            // Check if position or rotation changed significantly
            bool positionChanged = Vector3.Distance(cachedTransform.position, lastSentPosition) > minMoveDistance;
            bool rotationChanged = Quaternion.Angle(cachedTransform.rotation, lastSentRotation) > minRotateAngle;
            
            return positionChanged || rotationChanged || rb.linearVelocity.magnitude > 0.1f || rb.angularVelocity.magnitude > 0.1f;
        }
        
        [Command]
        private void SendRigidbodyData()
        {
            networkPosition = cachedTransform.position;
            networkRotation = cachedTransform.rotation;
            networkIsKinematic = rb.isKinematic;
            
            if (syncVelocity)
                networkVelocity = rb.linearVelocity;
                
            if (syncAngularVelocity)
                networkAngularVelocity = rb.angularVelocity;
            
            lastSentPosition = cachedTransform.position;
            lastSentRotation = cachedTransform.rotation;
        }
        
        private void SmoothRemoteMovement()
        {
            // Update targets when network values change significantly
            if (Vector3.Distance(targetPosition, networkPosition) > 0.01f)
                targetPosition = networkPosition;
            if (Quaternion.Angle(targetRotation, networkRotation) > 1f)
                targetRotation = networkRotation;
            if (syncVelocity && Vector3.Distance(targetVelocity, networkVelocity) > 0.01f)
                targetVelocity = networkVelocity;
            if (syncAngularVelocity && Vector3.Distance(targetAngularVelocity, networkAngularVelocity) > 0.01f)
                targetAngularVelocity = networkAngularVelocity;
            
            // Check if we need to snap instead of smooth (large distance)
            float distance = Vector3.Distance(cachedTransform.position, targetPosition);
            if (distance > snapThreshold)
            {
                // Snap to position
                cachedTransform.position = targetPosition;
                cachedTransform.rotation = targetRotation;
                if (rb != null)
                {
                    rb.position = targetPosition;
                    rb.rotation = targetRotation;
                    if (syncVelocity)
                        rb.linearVelocity = targetVelocity;
                    if (syncAngularVelocity)
                        rb.angularVelocity = targetAngularVelocity;
                }
            }
            else
            {
                // Smooth interpolation
                float deltaTime = Time.fixedDeltaTime * smoothingSpeed;
                
                Vector3 newPosition = Vector3.Lerp(cachedTransform.position, targetPosition, deltaTime);
                Quaternion newRotation = Quaternion.Lerp(cachedTransform.rotation, targetRotation, deltaTime);
                
                if (rb != null && !rb.isKinematic)
                {
                    rb.MovePosition(newPosition);
                    rb.MoveRotation(newRotation);
                    
                    if (syncVelocity)
                        rb.linearVelocity = Vector3.Lerp(rb.linearVelocity, targetVelocity, deltaTime);
                    if (syncAngularVelocity)
                        rb.angularVelocity = Vector3.Lerp(rb.angularVelocity, targetAngularVelocity, deltaTime);
                }
                else
                {
                    cachedTransform.position = newPosition;
                    cachedTransform.rotation = newRotation;
                }
            }
            
            // Update kinematic state
            if (rb != null && rb.isKinematic != networkIsKinematic)
            {
                rb.isKinematic = networkIsKinematic;
            }
        }
        
        private void ApplyNetworkTransform()
        {
            cachedTransform.position = networkPosition;
            cachedTransform.rotation = networkRotation;
            
            if (rb != null)
            {
                rb.position = networkPosition;
                rb.rotation = networkRotation;
                
                if (syncVelocity)
                    rb.linearVelocity = networkVelocity;
                if (syncAngularVelocity)
                    rb.angularVelocity = networkAngularVelocity;
                    
                if (rb.isKinematic != networkIsKinematic)
                    rb.isKinematic = networkIsKinematic;
            }
        }
        
        // Public methods for external control
        public void SetAuthority(bool hasAuth)
        {
            if (hasAuth && !hasAuthority)
            {
                // Request authority from server
                CmdRequestAuthority();
            }
            else if (!hasAuth && hasAuthority)
            {
                // Release authority
                CmdReleaseAuthority();
            }
        }
        
        [Command]
        private void CmdRequestAuthority()
        {
            // Server assigns authority
            netIdentity.AssignClientAuthority(connectionToClient);
        }
        
        [Command]
        private void CmdReleaseAuthority()
        {
            // Server removes authority
            netIdentity.RemoveClientAuthority();
        }
        
        // Utility methods
        public bool HasNetworkAuthority => hasAuthority;
        public Rigidbody GetRigidbody() => rb;
        
        // Force immediate sync (useful for teleportation, etc.)
        public void ForceSyncNow()
        {
            if (hasAuthority)
            {
                SendRigidbodyData();
            }
        }
    }
}
