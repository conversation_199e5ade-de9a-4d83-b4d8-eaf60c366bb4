using UnityEngine;
using Mirror;

namespace VRMultiplayer
{
    /// <summary>
    /// Helper script to spawn VR players for testing
    /// This ensures proper player spawning and helps debug multiplayer scenarios
    /// </summary>
    public class VRPlayerSpawner : NetworkBehaviour
    {
        [Header("Spawning Settings")]
        [SerializeField] private GameObject vrPlayerPrefab;
        [SerializeField] private Transform[] spawnPoints;
        [SerializeField] private bool autoSpawnOnStart = true;
        [SerializeField] private bool debugSpawning = true;
        
        [Header("Test Mode (Editor Only)")]
        [SerializeField] private bool enableTestMode = false;
        [SerializeField] private bool spawnDummyPlayers = false;
        [SerializeField] private int dummyPlayerCount = 1;
        
        private VRNetworkManager networkManager;
        
        private void Start()
        {
            networkManager = FindObjectOfType<VRNetworkManager>();
            
            if (debugSpawning)
            {
                Debug.Log("[VRPlayerSpawner] Started - Auto spawn: " + autoSpawnOnStart);
            }
            
            // Setup spawn points if not assigned
            if (spawnPoints == null || spawnPoints.Length == 0)
            {
                CreateDefaultSpawnPoints();
            }
            
            #if UNITY_EDITOR
            if (enableTestMode && Application.isPlaying)
            {
                StartCoroutine(TestModeSetup());
            }
            #endif
        }
        
        private void CreateDefaultSpawnPoints()
        {
            spawnPoints = new Transform[4];
            
            for (int i = 0; i < 4; i++)
            {
                GameObject spawnPoint = new GameObject($"SpawnPoint_{i}");
                spawnPoint.transform.SetParent(transform);
                
                // Position spawn points in a circle
                float angle = i * 90f * Mathf.Deg2Rad;
                Vector3 position = new Vector3(
                    Mathf.Cos(angle) * 2f,
                    0f,
                    Mathf.Sin(angle) * 2f
                );
                spawnPoint.transform.position = position;
                spawnPoints[i] = spawnPoint.transform;
            }
            
            if (debugSpawning)
            {
                Debug.Log("[VRPlayerSpawner] Created default spawn points");
            }
        }
        
        #if UNITY_EDITOR
        private System.Collections.IEnumerator TestModeSetup()
        {
            yield return new System.TimeSpan(0, 0, 2); // Wait 2 seconds
            
            if (spawnDummyPlayers && NetworkServer.active)
            {
                SpawnDummyPlayers();
            }
        }
        
        private void SpawnDummyPlayers()
        {
            if (vrPlayerPrefab == null)
            {
                Debug.LogError("[VRPlayerSpawner] VR Player Prefab not assigned!");
                return;
            }
            
            for (int i = 0; i < dummyPlayerCount; i++)
            {
                Vector3 spawnPos = GetSpawnPosition(i + 1); // +1 because local player is 0
                GameObject dummyPlayer = Instantiate(vrPlayerPrefab, spawnPos, Quaternion.identity);
                dummyPlayer.name = $"DummyVRPlayer_{i}";
                
                // Remove local player components
                var vrPlayer = dummyPlayer.GetComponent<VRPlayer>();
                if (vrPlayer != null)
                {
                    // Force it to be a remote player
                    vrPlayer.SetAsRemotePlayer();
                }
                
                NetworkServer.Spawn(dummyPlayer);
                
                Debug.Log($"[VRPlayerSpawner] Spawned dummy player {i} at {spawnPos}");
            }
        }
        #endif
        
        public Vector3 GetSpawnPosition(int playerIndex)
        {
            if (spawnPoints != null && spawnPoints.Length > 0)
            {
                int spawnIndex = playerIndex % spawnPoints.Length;
                return spawnPoints[spawnIndex].position;
            }
            
            // Fallback: circle spawn
            float angle = playerIndex * 90f * Mathf.Deg2Rad;
            return new Vector3(
                Mathf.Cos(angle) * 2f,
                0f,
                Mathf.Sin(angle) * 2f
            );
        }
        
        public void SpawnVRPlayer(NetworkConnection conn)
        {
            if (vrPlayerPrefab == null)
            {
                Debug.LogError("[VRPlayerSpawner] VR Player Prefab not assigned!");
                return;
            }
            
            Vector3 spawnPos = GetSpawnPosition(NetworkServer.connections.Count - 1);
            GameObject player = Instantiate(vrPlayerPrefab, spawnPos, Quaternion.identity);
            
            NetworkServer.AddPlayerForConnection(conn, player);
            
            if (debugSpawning)
            {
                Debug.Log($"[VRPlayerSpawner] Spawned VR Player for connection {conn.connectionId} at {spawnPos}");
            }
        }
        
        // Manual spawning methods
        [ContextMenu("Spawn Test Player")]
        public void SpawnTestPlayer()
        {
            if (!NetworkServer.active)
            {
                Debug.LogWarning("[VRPlayerSpawner] Server not active!");
                return;
            }
            
            #if UNITY_EDITOR
            SpawnDummyPlayers();
            #endif
        }
        
        [ContextMenu("Clear All Players")]
        public void ClearAllPlayers()
        {
            VRPlayer[] players = FindObjectsOfType<VRPlayer>();
            foreach (var player in players)
            {
                if (player.gameObject.name.Contains("Dummy"))
                {
                    if (NetworkServer.active)
                        NetworkServer.Destroy(player.gameObject);
                    else
                        DestroyImmediate(player.gameObject);
                }
            }
            
            Debug.Log("[VRPlayerSpawner] Cleared all dummy players");
        }
        
        // Public methods for NetworkManager integration
        public void SetVRPlayerPrefab(GameObject prefab)
        {
            vrPlayerPrefab = prefab;
        }
        
        public GameObject GetVRPlayerPrefab()
        {
            return vrPlayerPrefab;
        }
        
        public bool HasValidSetup()
        {
            return vrPlayerPrefab != null && spawnPoints != null && spawnPoints.Length > 0;
        }
        
        private void OnDrawGizmos()
        {
            if (spawnPoints == null) return;
            
            // Draw spawn points
            Gizmos.color = Color.green;
            for (int i = 0; i < spawnPoints.Length; i++)
            {
                if (spawnPoints[i] == null) continue;
                
                Vector3 pos = spawnPoints[i].position;
                Gizmos.DrawWireSphere(pos, 0.5f);
                Gizmos.DrawLine(pos, pos + Vector3.up * 2f);
                
                #if UNITY_EDITOR
                UnityEditor.Handles.Label(pos + Vector3.up * 2.2f, $"Spawn {i}");
                #endif
            }
            
            // Draw connections between spawn points
            Gizmos.color = Color.yellow;
            for (int i = 0; i < spawnPoints.Length; i++)
            {
                if (spawnPoints[i] == null) continue;
                
                int nextIndex = (i + 1) % spawnPoints.Length;
                if (spawnPoints[nextIndex] != null)
                {
                    Gizmos.DrawLine(spawnPoints[i].position, spawnPoints[nextIndex].position);
                }
            }
        }
    }
}
