using UnityEngine;
using UnityEditor;
using Mirror;
using Autohand;
using VRMultiplayer;

namespace VRMultiplayer.Editor
{
    public class VRMultiplayerSetup : EditorWindow
    {
        private bool autoStartAsHost = false;
        private bool enableLANDiscovery = true;
        private GameObject vrPlayerPrefab;
        
        [MenuItem("VR Multiplayer/Setup Scene")]
        public static void ShowWindow()
        {
            GetWindow<VRMultiplayerSetup>("VR Multiplayer Setup");
        }
        
        private void OnGUI()
        {
            GUILayout.Label("VR Multiplayer Scene Setup", EditorStyles.boldLabel);
            GUILayout.Space(10);

            autoStartAsHost = EditorGUILayout.Toggle("Auto Start as Host", autoStartAsHost);
            enableLANDiscovery = EditorGUILayout.Toggle("Enable LAN Discovery", enableLANDiscovery);
            vrPlayerPrefab = (GameObject)EditorGUILayout.ObjectField("VR Player Prefab", vrPlayerPrefab, typeof(GameObject), false);

            GUILayout.Space(20);

            if (GUILayout.Button("🚀 Complete Setup (All-in-One)", GUILayout.Height(50)))
            {
                CompleteSetup();
            }

            GUILayout.Space(10);

            EditorGUILayout.LabelField("Manual Setup Steps:", EditorStyles.boldLabel);

            if (GUILayout.Button("1. Auto Configure Scene", GUILayout.Height(30)))
            {
                AutoConfigureScene();
            }

            if (GUILayout.Button("2. Create VR Player Prefab", GUILayout.Height(30)))
            {
                CreateVRPlayerPrefab();
            }

            if (GUILayout.Button("3. Setup Grabbable Objects", GUILayout.Height(30)))
            {
                SetupGrabbableObjects();
            }

            if (GUILayout.Button("4. Create Demo Objects", GUILayout.Height(30)))
            {
                CreateDemoObjects();
            }

            GUILayout.Space(20);
            GUILayout.Label("Quick Actions:", EditorStyles.boldLabel);

            if (GUILayout.Button("Fix Common Issues"))
            {
                FixCommonIssues();
            }

            if (GUILayout.Button("Validate Setup"))
            {
                ValidateSetup();
            }

            GUILayout.Space(20);
            GUILayout.Label("Instructions:", EditorStyles.boldLabel);
            GUILayout.Label("• Use 'Complete Setup' for automatic configuration");
            GUILayout.Label("• Or follow manual steps 1-4 in order");
            GUILayout.Label("• Use 'Fix Common Issues' if you encounter problems");
            GUILayout.Label("• Build and test on Quest devices");
        }
        
        private void AutoConfigureScene()
        {
            // Find or create NetworkManager
            VRNetworkManager networkManager = FindObjectOfType<VRNetworkManager>();
            if (networkManager == null)
            {
                GameObject nmGO = new GameObject("VRNetworkManager");
                networkManager = nmGO.AddComponent<VRNetworkManager>();
                
                // Add KCP transport
                var kcpTransport = nmGO.AddComponent<kcp2k.KcpTransport>();
                networkManager.transport = kcpTransport;
                
                // Add Network Discovery
                if (enableLANDiscovery)
                {
                    nmGO.AddComponent<Mirror.Discovery.NetworkDiscovery>();
                }
                
                Debug.Log("Created VRNetworkManager with KCP transport");
            }
            
            // Configure NetworkManager
            networkManager.AutoConfigure();
            
            // Set VR player prefab if provided
            if (vrPlayerPrefab != null)
            {
                networkManager.playerPrefab = vrPlayerPrefab;
            }
            
            // Create simple UI for testing
            CreateNetworkUI(networkManager);
            
            // Mark scene as dirty
            EditorUtility.SetDirty(networkManager);
            UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty(
                UnityEditor.SceneManagement.EditorSceneManager.GetActiveScene());
            
            Debug.Log("VR Multiplayer scene configured successfully!");
        }
        
        private void CreateVRPlayerPrefab()
        {
            // Find AutoHandPlayer prefab
            string[] guids = AssetDatabase.FindAssets("AutoHandPlayer t:Prefab");
            GameObject autoHandPlayerPrefab = null;
            
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject _prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (_prefab.GetComponent<AutoHandPlayer>() != null)
                {
                    autoHandPlayerPrefab = _prefab;
                    break;
                }
            }
            
            if (autoHandPlayerPrefab == null)
            {
                Debug.LogError("AutoHandPlayer prefab not found! Make sure AutoHand is properly imported.");
                return;
            }
            
            // Create VR Player prefab
            GameObject vrPlayerInstance = PrefabUtility.InstantiatePrefab(autoHandPlayerPrefab) as GameObject;
            vrPlayerInstance.name = "VRPlayer";
            
            // Add network components
            NetworkIdentity netId = vrPlayerInstance.GetComponent<NetworkIdentity>();
            if (netId == null)
                netId = vrPlayerInstance.AddComponent<NetworkIdentity>();
                
            VRPlayer vrPlayer = vrPlayerInstance.GetComponent<VRPlayer>();
            if (vrPlayer == null)
                vrPlayer = vrPlayerInstance.AddComponent<VRPlayer>();
            
            // Configure NetworkIdentity
            netId.sceneId = 0; // This will be a spawned prefab
            
            // Create prefab
            string prefabPath = "Assets/_Main/Prefabs/VRPlayer.prefab";
            System.IO.Directory.CreateDirectory("Assets/_Main/Prefabs");
            
            GameObject prefab = PrefabUtility.SaveAsPrefabAsset(vrPlayerInstance, prefabPath);
            DestroyImmediate(vrPlayerInstance);
            
            // Set as player prefab in NetworkManager
            VRNetworkManager networkManager = FindObjectOfType<VRNetworkManager>();
            if (networkManager != null)
            {
                networkManager.playerPrefab = prefab;
                EditorUtility.SetDirty(networkManager);
            }
            
            vrPlayerPrefab = prefab;
            
            Debug.Log($"VR Player prefab created at {prefabPath}");
        }
        
        private void SetupGrabbableObjects()
        {
            Grabbable[] grabbables = FindObjectsOfType<Grabbable>();
            int count = 0;
            
            foreach (Grabbable grabbable in grabbables)
            {
                // Skip if already has NetworkGrabbable
                if (grabbable.GetComponent<NetworkGrabbable>() != null)
                    continue;
                
                // Add NetworkIdentity if missing
                NetworkIdentity netId = grabbable.GetComponent<NetworkIdentity>();
                if (netId == null)
                    netId = grabbable.gameObject.AddComponent<NetworkIdentity>();
                
                // Add NetworkGrabbable
                NetworkGrabbable networkGrabbable = grabbable.gameObject.AddComponent<NetworkGrabbable>();
                
                // Add RigidbodySync if has Rigidbody
                Rigidbody rb = grabbable.GetComponent<Rigidbody>();
                if (rb != null && grabbable.GetComponent<RigidbodySync>() == null)
                {
                    grabbable.gameObject.AddComponent<RigidbodySync>();
                }
                
                EditorUtility.SetDirty(grabbable.gameObject);
                count++;
            }
            
            Debug.Log($"Setup networking for {count} grabbable objects");
        }
        
        private void CreateNetworkUI(VRNetworkManager networkManager)
        {
            // Create Canvas for UI
            GameObject canvasGO = GameObject.Find("NetworkUI");
            if (canvasGO == null)
            {
                canvasGO = new GameObject("NetworkUI");
                Canvas canvas = canvasGO.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasGO.AddComponent<UnityEngine.UI.CanvasScaler>();
                canvasGO.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            }
            
            // Create simple buttons for testing
            CreateButton(canvasGO.transform, "Host", new Vector2(-200, 100), () => {
                VRNetworkManager nm = FindObjectOfType<VRNetworkManager>();
                if (nm != null) nm.StartHostButton();
            });
            
            CreateButton(canvasGO.transform, "Client", new Vector2(0, 100), () => {
                VRNetworkManager nm = FindObjectOfType<VRNetworkManager>();
                if (nm != null) nm.StartClientButton();
            });
            
            CreateButton(canvasGO.transform, "Disconnect", new Vector2(200, 100), () => {
                VRNetworkManager nm = FindObjectOfType<VRNetworkManager>();
                if (nm != null) nm.DisconnectButton();
            });
        }
        
        private void CreateButton(Transform parent, string text, Vector2 position, System.Action onClick)
        {
            GameObject buttonGO = new GameObject(text + "Button");
            buttonGO.transform.SetParent(parent);
            
            RectTransform rectTransform = buttonGO.AddComponent<RectTransform>();
            rectTransform.anchoredPosition = position;
            rectTransform.sizeDelta = new Vector2(160, 30);
            
            UnityEngine.UI.Image image = buttonGO.AddComponent<UnityEngine.UI.Image>();
            UnityEngine.UI.Button button = buttonGO.AddComponent<UnityEngine.UI.Button>();
            
            // Add text
            GameObject textGO = new GameObject("Text");
            textGO.transform.SetParent(buttonGO.transform);
            
            RectTransform textRect = textGO.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
            
            UnityEngine.UI.Text textComponent = textGO.AddComponent<UnityEngine.UI.Text>();
            textComponent.text = text;
            textComponent.alignment = TextAnchor.MiddleCenter;
            textComponent.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            
            // Set button click event
            button.onClick.AddListener(() => onClick?.Invoke());
        }

        private void CompleteSetup()
        {
            Debug.Log("Starting complete VR Multiplayer setup...");

            try
            {
                AutoConfigureScene();
                CreateVRPlayerPrefab();
                SetupGrabbableObjects();
                CreateDemoObjects();
                FixCommonIssues();

                Debug.Log("✅ Complete setup finished successfully!");
                EditorUtility.DisplayDialog("Setup Complete",
                    "VR Multiplayer setup completed successfully!\n\n" +
                    "You can now build and test on Quest devices.", "OK");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Setup failed: {e.Message}");
                EditorUtility.DisplayDialog("Setup Failed",
                    $"Setup encountered an error:\n{e.Message}\n\n" +
                    "Please check the console for details.", "OK");
            }
        }

        private void CreateDemoObjects()
        {
            Debug.Log("Creating demo objects...");

            // Create demo cubes
            for (int i = 0; i < 3; i++)
            {
                GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
                cube.name = $"DemoCube_{i}";
                cube.transform.position = new Vector3(i * 2f, 2f, 0f);

                // Add Rigidbody
                Rigidbody rb = cube.AddComponent<Rigidbody>();
                rb.mass = 1f;

                // Add AutoHand Grabbable
                var grabbable = cube.AddComponent<Autohand.Grabbable>();

                // Add Network components
                cube.AddComponent<NetworkIdentity>();
                cube.AddComponent<NetworkGrabbable>();
                cube.AddComponent<RigidbodySync>();

                EditorUtility.SetDirty(cube);
            }

            // Create demo spheres
            for (int i = 0; i < 3; i++)
            {
                GameObject sphere = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                sphere.name = $"DemoSphere_{i}";
                sphere.transform.position = new Vector3(i * 2f, 3f, 2f);

                // Add Rigidbody
                Rigidbody rb = sphere.AddComponent<Rigidbody>();
                rb.mass = 0.5f;

                // Add AutoHand Grabbable
                var grabbable = sphere.AddComponent<Autohand.Grabbable>();

                // Add Network components
                sphere.AddComponent<NetworkIdentity>();
                sphere.AddComponent<NetworkGrabbable>();
                sphere.AddComponent<RigidbodySync>();

                EditorUtility.SetDirty(sphere);
            }

            Debug.Log("Created demo objects for testing");
        }

        private void FixCommonIssues()
        {
            Debug.Log("Fixing common issues...");

            // Fix missing using statements in scripts
            // This would require more complex file manipulation

            // Ensure all NetworkIdentities have proper scene IDs
            NetworkIdentity[] netIds = FindObjectsOfType<NetworkIdentity>();
            foreach (var netId in netIds)
            {
                if (netId.sceneId == 0 && !PrefabUtility.IsPartOfPrefabAsset(netId.gameObject))
                {
                    // This is a scene object, ensure it has a scene ID
                    EditorUtility.SetDirty(netId);
                }
            }

            // Ensure VROptimizer exists
            VROptimizer optimizer = FindObjectOfType<VROptimizer>();
            if (optimizer == null)
            {
                GameObject optimizerGO = new GameObject("VROptimizer");
                optimizerGO.AddComponent<VROptimizer>();
                Debug.Log("Added VROptimizer to scene");
            }

            // Ensure demo script exists
            VRMultiplayerDemo demo = FindObjectOfType<VRMultiplayerDemo>();
            if (demo == null)
            {
                GameObject demoGO = new GameObject("VRMultiplayerDemo");
                demoGO.AddComponent<VRMultiplayerDemo>();
                Debug.Log("Added VRMultiplayerDemo to scene");
            }

            Debug.Log("Common issues fixed");
        }

        private void ValidateSetup()
        {
            Debug.Log("Validating VR Multiplayer setup...");

            bool isValid = true;
            System.Text.StringBuilder issues = new System.Text.StringBuilder();

            // Check NetworkManager
            VRNetworkManager networkManager = FindObjectOfType<VRNetworkManager>();
            if (networkManager == null)
            {
                issues.AppendLine("❌ VRNetworkManager not found");
                isValid = false;
            }
            else
            {
                Debug.Log("✅ VRNetworkManager found");

                if (networkManager.playerPrefab == null)
                {
                    issues.AppendLine("❌ Player prefab not assigned");
                    isValid = false;
                }
                else
                {
                    Debug.Log("✅ Player prefab assigned");
                }
            }

            // Check VR Player prefab
            if (vrPlayerPrefab != null)
            {
                VRPlayer vrPlayer = vrPlayerPrefab.GetComponent<VRPlayer>();
                NetworkIdentity netId = vrPlayerPrefab.GetComponent<NetworkIdentity>();

                if (vrPlayer == null)
                {
                    issues.AppendLine("❌ VRPlayer component missing on prefab");
                    isValid = false;
                }

                if (netId == null)
                {
                    issues.AppendLine("❌ NetworkIdentity missing on prefab");
                    isValid = false;
                }
            }

            // Check grabbable objects
            Autohand.Grabbable[] grabbables = FindObjectsOfType<Autohand.Grabbable>();
            int networkGrabbableCount = 0;

            foreach (var grabbable in grabbables)
            {
                if (grabbable.GetComponent<NetworkGrabbable>() != null)
                    networkGrabbableCount++;
            }

            Debug.Log($"✅ Found {grabbables.Length} grabbables, {networkGrabbableCount} with networking");

            // Show results
            if (isValid)
            {
                EditorUtility.DisplayDialog("Validation Passed",
                    "✅ VR Multiplayer setup is valid!\n\n" +
                    "Ready for build and testing.", "OK");
            }
            else
            {
                EditorUtility.DisplayDialog("Validation Failed",
                    "❌ Setup validation failed:\n\n" + issues.ToString() +
                    "\nPlease fix these issues before building.", "OK");
            }
        }
    }
}
