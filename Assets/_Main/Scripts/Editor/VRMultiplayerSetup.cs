using UnityEngine;
using UnityEditor;
using Mirror;
using Autohand;
using VRMultiplayer;

namespace VRMultiplayer.Editor
{
    public class VRMultiplayerSetup : EditorWindow
    {
        private bool autoStartAsHost = false;
        private bool enableLANDiscovery = true;
        private GameObject vrPlayerPrefab;
        
        [MenuItem("VR Multiplayer/Setup Scene")]
        public static void ShowWindow()
        {
            GetWindow<VRMultiplayerSetup>("VR Multiplayer Setup");
        }
        
        private void OnGUI()
        {
            GUILayout.Label("VR Multiplayer Scene Setup", EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            autoStartAsHost = EditorGUILayout.Toggle("Auto Start as Host", autoStartAsHost);
            enableLANDiscovery = EditorGUILayout.Toggle("Enable LAN Discovery", enableLANDiscovery);
            vrPlayerPrefab = (GameObject)EditorGUILayout.ObjectField("VR Player Prefab", vrPlayerPrefab, typeof(GameObject), false);
            
            GUILayout.Space(20);
            
            if (GUILayout.Button("Auto Configure Scene", GUILayout.Height(40)))
            {
                AutoConfigureScene();
            }
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("Create VR Player Prefab", GUILayout.Height(30)))
            {
                CreateVRPlayerPrefab();
            }
            
            if (GUILayout.Button("Setup Grabbable Objects", GUILayout.Height(30)))
            {
                SetupGrabbableObjects();
            }
            
            GUILayout.Space(20);
            GUILayout.Label("Instructions:", EditorStyles.boldLabel);
            GUILayout.Label("1. Click 'Auto Configure Scene' to setup NetworkManager");
            GUILayout.Label("2. Click 'Create VR Player Prefab' to create player prefab");
            GUILayout.Label("3. Click 'Setup Grabbable Objects' to add networking to grabbables");
            GUILayout.Label("4. Build and test on Quest devices");
        }
        
        private void AutoConfigureScene()
        {
            // Find or create NetworkManager
            VRNetworkManager networkManager = FindObjectOfType<VRNetworkManager>();
            if (networkManager == null)
            {
                GameObject nmGO = new GameObject("VRNetworkManager");
                networkManager = nmGO.AddComponent<VRNetworkManager>();
                
                // Add KCP transport
                var kcpTransport = nmGO.AddComponent<kcp2k.KcpTransport>();
                networkManager.transport = kcpTransport;
                
                // Add Network Discovery
                if (enableLANDiscovery)
                {
                    nmGO.AddComponent<Mirror.Discovery.NetworkDiscovery>();
                }
                
                Debug.Log("Created VRNetworkManager with KCP transport");
            }
            
            // Configure NetworkManager
            networkManager.AutoConfigure();
            
            // Set VR player prefab if provided
            if (vrPlayerPrefab != null)
            {
                networkManager.playerPrefab = vrPlayerPrefab;
            }
            
            // Create simple UI for testing
            CreateNetworkUI(networkManager);
            
            // Mark scene as dirty
            EditorUtility.SetDirty(networkManager);
            UnityEditor.SceneManagement.EditorSceneManager.MarkSceneDirty(
                UnityEditor.SceneManagement.EditorSceneManager.GetActiveScene());
            
            Debug.Log("VR Multiplayer scene configured successfully!");
        }
        
        private void CreateVRPlayerPrefab()
        {
            // Find AutoHandPlayer prefab
            string[] guids = AssetDatabase.FindAssets("AutoHandPlayer t:Prefab");
            GameObject autoHandPlayerPrefab = null;
            
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (prefab.GetComponent<AutoHandPlayer>() != null)
                {
                    autoHandPlayerPrefab = prefab;
                    break;
                }
            }
            
            if (autoHandPlayerPrefab == null)
            {
                Debug.LogError("AutoHandPlayer prefab not found! Make sure AutoHand is properly imported.");
                return;
            }
            
            // Create VR Player prefab
            GameObject vrPlayerInstance = PrefabUtility.InstantiatePrefab(autoHandPlayerPrefab) as GameObject;
            vrPlayerInstance.name = "VRPlayer";
            
            // Add network components
            NetworkIdentity netId = vrPlayerInstance.GetComponent<NetworkIdentity>();
            if (netId == null)
                netId = vrPlayerInstance.AddComponent<NetworkIdentity>();
                
            VRPlayer vrPlayer = vrPlayerInstance.GetComponent<VRPlayer>();
            if (vrPlayer == null)
                vrPlayer = vrPlayerInstance.AddComponent<VRPlayer>();
            
            // Configure NetworkIdentity
            netId.sceneId = 0; // This will be a spawned prefab
            
            // Create prefab
            string prefabPath = "Assets/_Main/Prefabs/VRPlayer.prefab";
            System.IO.Directory.CreateDirectory("Assets/_Main/Prefabs");
            
            GameObject prefab = PrefabUtility.SaveAsPrefabAsset(vrPlayerInstance, prefabPath);
            DestroyImmediate(vrPlayerInstance);
            
            // Set as player prefab in NetworkManager
            VRNetworkManager networkManager = FindObjectOfType<VRNetworkManager>();
            if (networkManager != null)
            {
                networkManager.playerPrefab = prefab;
                EditorUtility.SetDirty(networkManager);
            }
            
            vrPlayerPrefab = prefab;
            
            Debug.Log($"VR Player prefab created at {prefabPath}");
        }
        
        private void SetupGrabbableObjects()
        {
            Grabbable[] grabbables = FindObjectsOfType<Grabbable>();
            int count = 0;
            
            foreach (Grabbable grabbable in grabbables)
            {
                // Skip if already has NetworkGrabbable
                if (grabbable.GetComponent<NetworkGrabbable>() != null)
                    continue;
                
                // Add NetworkIdentity if missing
                NetworkIdentity netId = grabbable.GetComponent<NetworkIdentity>();
                if (netId == null)
                    netId = grabbable.gameObject.AddComponent<NetworkIdentity>();
                
                // Add NetworkGrabbable
                NetworkGrabbable networkGrabbable = grabbable.gameObject.AddComponent<NetworkGrabbable>();
                
                // Add RigidbodySync if has Rigidbody
                Rigidbody rb = grabbable.GetComponent<Rigidbody>();
                if (rb != null && grabbable.GetComponent<RigidbodySync>() == null)
                {
                    grabbable.gameObject.AddComponent<RigidbodySync>();
                }
                
                EditorUtility.SetDirty(grabbable.gameObject);
                count++;
            }
            
            Debug.Log($"Setup networking for {count} grabbable objects");
        }
        
        private void CreateNetworkUI(VRNetworkManager networkManager)
        {
            // Create Canvas for UI
            GameObject canvasGO = GameObject.Find("NetworkUI");
            if (canvasGO == null)
            {
                canvasGO = new GameObject("NetworkUI");
                Canvas canvas = canvasGO.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                canvasGO.AddComponent<UnityEngine.UI.CanvasScaler>();
                canvasGO.AddComponent<UnityEngine.UI.GraphicRaycaster>();
            }
            
            // Create simple buttons for testing
            CreateButton(canvasGO.transform, "Host", new Vector2(-200, 100), () => {
                VRNetworkManager nm = FindObjectOfType<VRNetworkManager>();
                if (nm != null) nm.StartHostButton();
            });
            
            CreateButton(canvasGO.transform, "Client", new Vector2(0, 100), () => {
                VRNetworkManager nm = FindObjectOfType<VRNetworkManager>();
                if (nm != null) nm.StartClientButton();
            });
            
            CreateButton(canvasGO.transform, "Disconnect", new Vector2(200, 100), () => {
                VRNetworkManager nm = FindObjectOfType<VRNetworkManager>();
                if (nm != null) nm.DisconnectButton();
            });
        }
        
        private void CreateButton(Transform parent, string text, Vector2 position, System.Action onClick)
        {
            GameObject buttonGO = new GameObject(text + "Button");
            buttonGO.transform.SetParent(parent);
            
            RectTransform rectTransform = buttonGO.AddComponent<RectTransform>();
            rectTransform.anchoredPosition = position;
            rectTransform.sizeDelta = new Vector2(160, 30);
            
            UnityEngine.UI.Image image = buttonGO.AddComponent<UnityEngine.UI.Image>();
            UnityEngine.UI.Button button = buttonGO.AddComponent<UnityEngine.UI.Button>();
            
            // Add text
            GameObject textGO = new GameObject("Text");
            textGO.transform.SetParent(buttonGO.transform);
            
            RectTransform textRect = textGO.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
            
            UnityEngine.UI.Text textComponent = textGO.AddComponent<UnityEngine.UI.Text>();
            textComponent.text = text;
            textComponent.alignment = TextAnchor.MiddleCenter;
            textComponent.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            
            // Set button click event
            button.onClick.AddListener(() => onClick?.Invoke());
        }
    }
}
