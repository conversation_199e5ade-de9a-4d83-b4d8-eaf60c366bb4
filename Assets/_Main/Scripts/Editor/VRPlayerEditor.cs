using UnityEngine;
using UnityEditor;
using VRMultiplayer;

namespace VRMultiplayer.Editor
{
    [CustomEditor(typeof(VRPlayer))]
    public class VRPlayerEditor : UnityEditor.Editor
    {
        private VRPlayer vrPlayer;
        
        private void OnEnable()
        {
            vrPlayer = (VRPlayer)target;
        }
        
        public override void OnInspectorGUI()
        {
            // Draw default inspector
            DrawDefaultInspector();
            
            GUILayout.Space(20);
            
            // Setup section
            EditorGUILayout.LabelField("VR Setup", EditorStyles.boldLabel);
            
            GUILayout.BeginHorizontal();
            
            if (GUILayout.Button("🔧 Auto Setup References", GUILayout.Height(35)))
            {
                SetupVRReferences();
            }
            
            if (GUILayout.Button("🔍 Validate Setup", GUILayout.Height(35)))
            {
                ValidateVRSetup();
            }
            
            GUILayout.EndHorizontal();
            
            GUILayout.Space(10);
            
            // Quick actions
            EditorGUILayout.LabelField("Quick Actions", EditorStyles.boldLabel);
            
            GUILayout.BeginHorizontal();
            
            if (GUILayout.Button("Create Visuals"))
            {
                CreateVisualObjects();
            }
            
            if (GUILayout.Button("Clear Visuals"))
            {
                ClearVisualObjects();
            }
            
            GUILayout.EndHorizontal();
            
            GUILayout.Space(10);
            
            // Status display
            DisplaySetupStatus();
            
            // Apply changes
            if (GUI.changed)
            {
                EditorUtility.SetDirty(vrPlayer);
            }
        }
        
        private void SetupVRReferences()
        {
            Undo.RecordObject(vrPlayer, "Setup VR References");
            
            vrPlayer.SetupVRReferences();
            
            Debug.Log($"VRPlayer '{vrPlayer.name}' references setup completed");
            EditorUtility.SetDirty(vrPlayer);
        }
        
        private void ValidateVRSetup()
        {
            bool isValid = true;
            System.Text.StringBuilder issues = new System.Text.StringBuilder();
            
            // Check AutoHandPlayer
            var autoHandPlayer = vrPlayer.GetAutoHandPlayer();
            if (autoHandPlayer == null)
            {
                issues.AppendLine("❌ AutoHandPlayer component missing");
                isValid = false;
            }
            else
            {
                // Check head/camera
                if (vrPlayer.GetHead() == null)
                {
                    issues.AppendLine("❌ Head transform not assigned");
                    isValid = false;
                }
                
                // Check hands
                if (vrPlayer.GetLeftHand() == null)
                {
                    issues.AppendLine("❌ Left hand transform not assigned");
                    isValid = false;
                }
                
                if (vrPlayer.GetRightHand() == null)
                {
                    issues.AppendLine("❌ Right hand transform not assigned");
                    isValid = false;
                }
            }
            
            // Check NetworkIdentity
            var netId = vrPlayer.GetComponent<NetworkIdentity>();
            if (netId == null)
            {
                issues.AppendLine("❌ NetworkIdentity component missing");
                isValid = false;
            }
            
            // Show results
            if (isValid)
            {
                EditorUtility.DisplayDialog("Validation Passed", 
                    "✅ VRPlayer setup is valid!", "OK");
            }
            else
            {
                EditorUtility.DisplayDialog("Validation Failed", 
                    "❌ VRPlayer setup issues found:\n\n" + issues.ToString(), "OK");
            }
        }
        
        private void CreateVisualObjects()
        {
            Undo.RecordObject(vrPlayer, "Create Visual Objects");
            
            // This calls the private method through reflection or we can make it public
            vrPlayer.SetupVRReferences(); // This includes visual setup
            
            Debug.Log($"Visual objects created for VRPlayer '{vrPlayer.name}'");
            EditorUtility.SetDirty(vrPlayer);
        }
        
        private void ClearVisualObjects()
        {
            Undo.RecordObject(vrPlayer, "Clear Visual Objects");
            
            // Find and destroy visual objects
            Transform headVisual = vrPlayer.transform.Find("HeadVisual");
            Transform leftHandVisual = vrPlayer.transform.Find("LeftHandVisual");
            Transform rightHandVisual = vrPlayer.transform.Find("RightHandVisual");
            Transform remoteOnly = vrPlayer.transform.Find("RemoteOnly");
            
            if (headVisual != null)
                DestroyImmediate(headVisual.gameObject);
            if (leftHandVisual != null)
                DestroyImmediate(leftHandVisual.gameObject);
            if (rightHandVisual != null)
                DestroyImmediate(rightHandVisual.gameObject);
            if (remoteOnly != null)
                DestroyImmediate(remoteOnly.gameObject);
            
            Debug.Log($"Visual objects cleared for VRPlayer '{vrPlayer.name}'");
            EditorUtility.SetDirty(vrPlayer);
        }
        
        private void DisplaySetupStatus()
        {
            EditorGUILayout.LabelField("Setup Status", EditorStyles.boldLabel);
            
            // AutoHandPlayer status
            var autoHandPlayer = vrPlayer.GetAutoHandPlayer();
            string autoHandStatus = autoHandPlayer != null ? "✅ Found" : "❌ Missing";
            EditorGUILayout.LabelField($"AutoHandPlayer: {autoHandStatus}");
            
            // Head status
            var head = vrPlayer.GetHead();
            string headStatus = head != null ? "✅ Assigned" : "❌ Missing";
            EditorGUILayout.LabelField($"Head Transform: {headStatus}");
            
            // Hands status
            var leftHand = vrPlayer.GetLeftHand();
            var rightHand = vrPlayer.GetRightHand();
            string leftHandStatus = leftHand != null ? "✅ Assigned" : "❌ Missing";
            string rightHandStatus = rightHand != null ? "✅ Assigned" : "❌ Missing";
            EditorGUILayout.LabelField($"Left Hand: {leftHandStatus}");
            EditorGUILayout.LabelField($"Right Hand: {rightHandStatus}");
            
            // NetworkIdentity status
            var netId = vrPlayer.GetComponent<NetworkIdentity>();
            string netIdStatus = netId != null ? "✅ Found" : "❌ Missing";
            EditorGUILayout.LabelField($"NetworkIdentity: {netIdStatus}");
            
            // Overall status
            bool isSetupComplete = autoHandPlayer != null && head != null && 
                                 leftHand != null && rightHand != null && netId != null;
            
            GUILayout.Space(5);
            
            if (isSetupComplete)
            {
                EditorGUILayout.HelpBox("✅ VRPlayer setup is complete!", MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox("⚠️ VRPlayer setup is incomplete. Click 'Auto Setup References' to fix.", MessageType.Warning);
            }
        }
    }
    
    /// <summary>
    /// Custom property drawer for better VRPlayer inspector display
    /// </summary>
    [CustomPropertyDrawer(typeof(VRPlayer))]
    public class VRPlayerPropertyDrawer : PropertyDrawer
    {
        public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
        {
            EditorGUI.BeginProperty(position, label, property);
            
            // Draw the property field
            EditorGUI.PropertyField(position, property, label, true);
            
            EditorGUI.EndProperty();
        }
        
        public override float GetPropertyHeight(SerializedProperty property, GUIContent label)
        {
            return EditorGUI.GetPropertyHeight(property, label, true);
        }
    }
}
