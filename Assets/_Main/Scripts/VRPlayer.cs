using UnityEngine;
using <PERSON>;
using Autohand;

namespace VRMultiplayer
{
    public class VRPlayer : NetworkBehaviour
    {
        [Header("VR Components")]
        [SerializeField] private Transform head;
        [SerializeField] private Transform leftHand;
        [SerializeField] private Transform rightHand;
        [SerializeField] private Camera vrCamera;
        [SerializeField] private AudioListener audioListener;
        [SerializeField] private AutoHandPlayer autoHandPlayer;
        
        [<PERSON><PERSON>("Remote Player Visuals")]
        [SerializeField] private GameObject headVisual;
        [SerializeField] private GameObject leftHandVisual;
        [SerializeField] private GameObject rightHandVisual;
        [SerializeField] private GameObject localOnlyObjects;
        [SerializeField] private GameObject remoteOnlyObjects;
        
        [Header("Sync Settings")]
        [SerializeField] private float sendRate = 20f;
        [SerializeField] private bool smoothRemoteMovement = true;
        [SerializeField] private float smoothingSpeed = 15f;
        
        // Network sync variables
        [SyncVar] private Vector3 networkHeadPosition;
        [SyncVar] private Quaternion networkHeadRotation;
        [SyncVar] private Vector3 networkLeftHandPosition;
        [SyncVar] private Quaternion networkLeftHandRotation;
        [SyncVar] private Vector3 networkRightHandPosition;
        [SyncVar] private Quaternion networkRightHandRotation;
        
        // Smoothing targets for remote players
        private Vector3 targetHeadPosition;
        private Quaternion targetHeadRotation;
        private Vector3 targetLeftHandPosition;
        private Quaternion targetLeftHandRotation;
        private Vector3 targetRightHandPosition;
        private Quaternion targetRightHandRotation;
        
        private float lastSendTime;
        
        public override void OnStartLocalPlayer()
        {
            base.OnStartLocalPlayer();
            
            // Enable local player components
            if (vrCamera != null)
                vrCamera.enabled = true;
                
            if (audioListener != null)
                audioListener.enabled = true;
                
            if (autoHandPlayer != null)
                autoHandPlayer.enabled = true;
                
            if (localOnlyObjects != null)
                localOnlyObjects.SetActive(true);
                
            if (remoteOnlyObjects != null)
                remoteOnlyObjects.SetActive(false);
                
            // Find VR components if not assigned
            FindVRComponents();
            
            Debug.Log("Local VR Player started");
        }
        
        public override void OnStartClient()
        {
            base.OnStartClient();
            
            if (!isLocalPlayer)
            {
                // Disable local player components for remote players
                if (vrCamera != null)
                    vrCamera.enabled = false;
                    
                if (audioListener != null)
                    audioListener.enabled = false;
                    
                if (autoHandPlayer != null)
                    autoHandPlayer.enabled = false;
                    
                if (localOnlyObjects != null)
                    localOnlyObjects.SetActive(false);
                    
                if (remoteOnlyObjects != null)
                    remoteOnlyObjects.SetActive(true);
                    
                // Initialize smoothing targets
                InitializeSmoothingTargets();
                
                Debug.Log("Remote VR Player started");
            }
        }
        
        private void FindVRComponents()
        {
            if (autoHandPlayer == null)
                autoHandPlayer = GetComponent<AutoHandPlayer>();
                
            if (autoHandPlayer != null)
            {
                if (head == null && autoHandPlayer.headCamera != null)
                    head = autoHandPlayer.headCamera.transform;
                    
                if (vrCamera == null)
                    vrCamera = autoHandPlayer.headCamera;
                    
                if (leftHand == null && autoHandPlayer.handLeft != null)
                    leftHand = autoHandPlayer.handLeft.transform;
                    
                if (rightHand == null && autoHandPlayer.handRight != null)
                    rightHand = autoHandPlayer.handRight.transform;
            }
            
            if (audioListener == null)
                audioListener = GetComponentInChildren<AudioListener>();
        }
        
        private void InitializeSmoothingTargets()
        {
            if (head != null)
            {
                targetHeadPosition = head.position;
                targetHeadRotation = head.rotation;
            }
            
            if (leftHand != null)
            {
                targetLeftHandPosition = leftHand.position;
                targetLeftHandRotation = leftHand.rotation;
            }
            
            if (rightHand != null)
            {
                targetRightHandPosition = rightHand.position;
                targetRightHandRotation = rightHand.rotation;
            }
        }
        
        private void Update()
        {
            if (isLocalPlayer)
            {
                // Send VR data to server at specified rate
                if (Time.time - lastSendTime > 1f / sendRate)
                {
                    SendVRData();
                    lastSendTime = Time.time;
                }
            }
            else
            {
                // Smooth remote player movement
                if (smoothRemoteMovement)
                {
                    SmoothRemoteMovement();
                }
                else
                {
                    ApplyNetworkTransforms();
                }
            }
        }
        
        [Command]
        private void SendVRData()
        {
            if (head != null)
            {
                networkHeadPosition = head.position;
                networkHeadRotation = head.rotation;
            }
            
            if (leftHand != null)
            {
                networkLeftHandPosition = leftHand.position;
                networkLeftHandRotation = leftHand.rotation;
            }
            
            if (rightHand != null)
            {
                networkRightHandPosition = rightHand.position;
                networkRightHandRotation = rightHand.rotation;
            }
        }
        
        private void SmoothRemoteMovement()
        {
            // Update targets when network values change
            if (Vector3.Distance(targetHeadPosition, networkHeadPosition) > 0.01f)
                targetHeadPosition = networkHeadPosition;
            if (Quaternion.Angle(targetHeadRotation, networkHeadRotation) > 1f)
                targetHeadRotation = networkHeadRotation;
                
            if (Vector3.Distance(targetLeftHandPosition, networkLeftHandPosition) > 0.01f)
                targetLeftHandPosition = networkLeftHandPosition;
            if (Quaternion.Angle(targetLeftHandRotation, networkLeftHandRotation) > 1f)
                targetLeftHandRotation = networkLeftHandRotation;
                
            if (Vector3.Distance(targetRightHandPosition, networkRightHandPosition) > 0.01f)
                targetRightHandPosition = networkRightHandPosition;
            if (Quaternion.Angle(targetRightHandRotation, networkRightHandRotation) > 1f)
                targetRightHandRotation = networkRightHandRotation;
            
            // Smooth interpolation
            float deltaTime = Time.deltaTime * smoothingSpeed;
            
            if (head != null)
            {
                head.position = Vector3.Lerp(head.position, targetHeadPosition, deltaTime);
                head.rotation = Quaternion.Lerp(head.rotation, targetHeadRotation, deltaTime);
            }
            
            if (leftHand != null)
            {
                leftHand.position = Vector3.Lerp(leftHand.position, targetLeftHandPosition, deltaTime);
                leftHand.rotation = Quaternion.Lerp(leftHand.rotation, targetLeftHandRotation, deltaTime);
            }
            
            if (rightHand != null)
            {
                rightHand.position = Vector3.Lerp(rightHand.position, targetRightHandPosition, deltaTime);
                rightHand.rotation = Quaternion.Lerp(rightHand.rotation, targetRightHandRotation, deltaTime);
            }
        }
        
        private void ApplyNetworkTransforms()
        {
            if (head != null)
            {
                head.position = networkHeadPosition;
                head.rotation = networkHeadRotation;
            }
            
            if (leftHand != null)
            {
                leftHand.position = networkLeftHandPosition;
                leftHand.rotation = networkLeftHandRotation;
            }
            
            if (rightHand != null)
            {
                rightHand.position = networkRightHandPosition;
                rightHand.rotation = networkRightHandRotation;
            }
        }
        
        // Public methods for external access
        public bool IsLocalVRPlayer => isLocalPlayer;
        public Transform GetHead() => head;
        public Transform GetLeftHand() => leftHand;
        public Transform GetRightHand() => rightHand;
        public AutoHandPlayer GetAutoHandPlayer() => autoHandPlayer;

        // Setup methods
        [ContextMenu("Auto Setup VR References")]
        public void AutoSetupVRReferences()
        {
            SetupVRReferences();
        }

        public void SetupVRReferences()
        {
            // Find AutoHandPlayer if not assigned
            if (autoHandPlayer == null)
                autoHandPlayer = GetComponent<AutoHandPlayer>();

            if (autoHandPlayer == null)
                autoHandPlayer = GetComponentInChildren<AutoHandPlayer>();

            if (autoHandPlayer != null)
            {
                // Setup head/camera references
                if (head == null && autoHandPlayer.headCamera != null)
                    head = autoHandPlayer.headCamera.transform;

                if (vrCamera == null)
                    vrCamera = autoHandPlayer.headCamera;

                // Setup hand references
                if (leftHand == null && autoHandPlayer.handLeft != null)
                    leftHand = autoHandPlayer.handLeft.transform;

                if (rightHand == null && autoHandPlayer.handRight != null)
                    rightHand = autoHandPlayer.handRight.transform;

                Debug.Log("VRPlayer: AutoHand references setup successfully");
            }
            else
            {
                Debug.LogWarning("VRPlayer: AutoHandPlayer component not found!");
            }

            // Find AudioListener if not assigned
            if (audioListener == null)
                audioListener = GetComponentInChildren<AudioListener>();

            // Setup visual references automatically
            SetupVisualReferences();

            Debug.Log("VRPlayer: All references setup completed");
        }

        private void SetupVisualReferences()
        {
            // Find or create visual objects
            if (headVisual == null)
            {
                headVisual = transform.Find("HeadVisual")?.gameObject;
                if (headVisual == null && head != null)
                {
                    // Create head visual
                    headVisual = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                    headVisual.name = "HeadVisual";
                    headVisual.transform.SetParent(head);
                    headVisual.transform.localPosition = Vector3.zero;
                    headVisual.transform.localScale = Vector3.one * 0.2f;

                    // Remove collider
                    if (headVisual.GetComponent<Collider>())
                        DestroyImmediate(headVisual.GetComponent<Collider>());
                }
            }

            if (leftHandVisual == null)
            {
                leftHandVisual = transform.Find("LeftHandVisual")?.gameObject;
                if (leftHandVisual == null && leftHand != null)
                {
                    // Create left hand visual
                    leftHandVisual = GameObject.CreatePrimitive(PrimitiveType.Cube);
                    leftHandVisual.name = "LeftHandVisual";
                    leftHandVisual.transform.SetParent(leftHand);
                    leftHandVisual.transform.localPosition = Vector3.zero;
                    leftHandVisual.transform.localScale = Vector3.one * 0.1f;

                    // Remove collider
                    if (leftHandVisual.GetComponent<Collider>())
                        DestroyImmediate(leftHandVisual.GetComponent<Collider>());
                }
            }

            if (rightHandVisual == null)
            {
                rightHandVisual = transform.Find("RightHandVisual")?.gameObject;
                if (rightHandVisual == null && rightHand != null)
                {
                    // Create right hand visual
                    rightHandVisual = GameObject.CreatePrimitive(PrimitiveType.Cube);
                    rightHandVisual.name = "RightHandVisual";
                    rightHandVisual.transform.SetParent(rightHand);
                    rightHandVisual.transform.localPosition = Vector3.zero;
                    rightHandVisual.transform.localScale = Vector3.one * 0.1f;

                    // Remove collider
                    if (rightHandVisual.GetComponent<Collider>())
                        DestroyImmediate(rightHandVisual.GetComponent<Collider>());
                }
            }

            // Setup local/remote only objects
            if (localOnlyObjects == null)
            {
                localOnlyObjects = transform.Find("LocalOnly")?.gameObject;
                if (localOnlyObjects == null)
                {
                    localOnlyObjects = new GameObject("LocalOnly");
                    localOnlyObjects.transform.SetParent(transform);
                    localOnlyObjects.transform.localPosition = Vector3.zero;
                }
            }

            if (remoteOnlyObjects == null)
            {
                remoteOnlyObjects = transform.Find("RemoteOnly")?.gameObject;
                if (remoteOnlyObjects == null)
                {
                    remoteOnlyObjects = new GameObject("RemoteOnly");
                    remoteOnlyObjects.transform.SetParent(transform);
                    remoteOnlyObjects.transform.localPosition = Vector3.zero;

                    // Move visual objects to remote only
                    if (headVisual != null)
                        headVisual.transform.SetParent(remoteOnlyObjects.transform);
                    if (leftHandVisual != null)
                        leftHandVisual.transform.SetParent(remoteOnlyObjects.transform);
                    if (rightHandVisual != null)
                        rightHandVisual.transform.SetParent(remoteOnlyObjects.transform);
                }
            }
        }
    }
}
