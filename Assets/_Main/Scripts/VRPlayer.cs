using UnityEngine;
using <PERSON>;
using Autohand;

namespace VRMultiplayer
{
    public class VRPlayer : NetworkBehaviour
    {
        [Header("VR Components")]
        [SerializeField] private Transform head;
        [SerializeField] private Transform leftHand;
        [SerializeField] private Transform rightHand;
        [SerializeField] private Camera vrCamera;
        [SerializeField] private AudioListener audioListener;
        [SerializeField] private AutoHandPlayer autoHandPlayer;
        
        [Header("Remote Player Visuals")]
        [SerializeField] private GameObject headVisual;
        [SerializeField] private Hand leftHandRemote;
        [SerializeField] private Hand rightHandRemote;
        [SerializeField] private GameObject localOnlyObjects;
        [SerializeField] private GameObject remoteOnlyObjects;
        
        [Header("Sync Settings")]
        [SerializeField] private float sendRate = 20f;
        [SerializeField] private bool smoothRemoteMovement = true;
        [SerializeField] private float smoothingSpeed = 15f;
        [SerializeField] private bool syncHandPoses = true;

        // Network sync variables
        [SyncVar] private Vector3 networkHeadPosition;
        [SyncVar] private Quaternion networkHeadRotation;
        [SyncVar] private Vector3 networkLeftHandPosition;
        [SyncVar] private Quaternion networkLeftHandRotation;
        [SyncVar] private Vector3 networkRightHandPosition;
        [SyncVar] private Quaternion networkRightHandRotation;

        // Hand pose sync variables
        [SyncVar] private float networkLeftGripAxis;
        [SyncVar] private float networkLeftSqueezeAxis;
        [SyncVar] private float networkRightGripAxis;
        [SyncVar] private float networkRightSqueezeAxis;
        [SyncVar] private bool networkLeftIsGrabbing;
        [SyncVar] private bool networkRightIsGrabbing;
        
        // Smoothing targets for remote players
        private Vector3 targetHeadPosition;
        private Quaternion targetHeadRotation;
        private Vector3 targetLeftHandPosition;
        private Quaternion targetLeftHandRotation;
        private Vector3 targetRightHandPosition;
        private Quaternion targetRightHandRotation;

        // Hand pose smoothing targets
        private float targetLeftGripAxis;
        private float targetLeftSqueezeAxis;
        private float targetRightGripAxis;
        private float targetRightSqueezeAxis;
        
        private float lastSendTime;
        
        public override void OnStartLocalPlayer()
        {
            base.OnStartLocalPlayer();
            
            // Enable local player components
            if (vrCamera != null)
                vrCamera.enabled = true;
                
            if (audioListener != null)
                audioListener.enabled = true;
                
            if (autoHandPlayer != null)
                autoHandPlayer.enabled = true;
                
            if (localOnlyObjects != null)
                localOnlyObjects.SetActive(true);
                
            if (remoteOnlyObjects != null)
                remoteOnlyObjects.SetActive(false);
                
            // Find VR components if not assigned
            FindVRComponents();
            
            Debug.Log("Local VR Player started");
        }
        
        public override void OnStartClient()
        {
            base.OnStartClient();
            
            if (!isLocalPlayer)
            {
                // Disable local player components for remote players
                if (vrCamera != null)
                    vrCamera.enabled = false;
                    
                if (audioListener != null)
                    audioListener.enabled = false;
                    
                if (autoHandPlayer != null)
                    autoHandPlayer.enabled = false;
                    
                if (localOnlyObjects != null)
                    localOnlyObjects.SetActive(false);
                    
                if (remoteOnlyObjects != null)
                    remoteOnlyObjects.SetActive(true);

                // Initialize smoothing targets
                InitializeSmoothingTargets();

                // Setup remote hand models
                SetupRemoteHandModels();

                Debug.Log("Remote VR Player started");
            }
        }
        
        private void FindVRComponents()
        {
            if (autoHandPlayer == null)
                autoHandPlayer = GetComponent<AutoHandPlayer>();
                
            if (autoHandPlayer != null)
            {
                if (head == null && autoHandPlayer.headCamera != null)
                    head = autoHandPlayer.headCamera.transform;
                    
                if (vrCamera == null)
                    vrCamera = autoHandPlayer.headCamera;
                    
                if (leftHand == null && autoHandPlayer.handLeft != null)
                    leftHand = autoHandPlayer.handLeft.transform;
                    
                if (rightHand == null && autoHandPlayer.handRight != null)
                    rightHand = autoHandPlayer.handRight.transform;
            }
            
            if (audioListener == null)
                audioListener = GetComponentInChildren<AudioListener>();
        }
        
        private void InitializeSmoothingTargets()
        {
            if (head != null)
            {
                targetHeadPosition = head.position;
                targetHeadRotation = head.rotation;
            }
            
            if (leftHand != null)
            {
                targetLeftHandPosition = leftHand.position;
                targetLeftHandRotation = leftHand.rotation;
            }
            
            if (rightHand != null)
            {
                targetRightHandPosition = rightHand.position;
                targetRightHandRotation = rightHand.rotation;
            }

            // Initialize hand pose targets
            targetLeftGripAxis = 0f;
            targetLeftSqueezeAxis = 0f;
            targetRightGripAxis = 0f;
            targetRightSqueezeAxis = 0f;
        }

        private void SetupRemoteHandModels()
        {
            if (autoHandPlayer == null) return;

            // Create remote hand models based on local hands
            if (leftHandRemote == null && autoHandPlayer.handLeft != null)
            {
                CreateRemoteHand(autoHandPlayer.handLeft, true);
            }

            if (rightHandRemote == null && autoHandPlayer.handRight != null)
            {
                CreateRemoteHand(autoHandPlayer.handRight, false);
            }
        }

        private void CreateRemoteHand(Hand originalHand, bool isLeft)
        {
            if (originalHand == null) return;

            // Duplicate the hand for remote visualization
            GameObject remoteHandObj = Instantiate(originalHand.gameObject);
            remoteHandObj.name = isLeft ? "RemoteLeftHand" : "RemoteRightHand";

            // Get the Hand component
            Hand remoteHand = remoteHandObj.GetComponent<Hand>();
            if (remoteHand == null) return;

            // Disable interaction components for remote hand
            remoteHand.enabled = false; // Disable hand logic

            // Remove physics components that shouldn't be on remote hands
            Rigidbody rb = remoteHandObj.GetComponent<Rigidbody>();
            if (rb != null) DestroyImmediate(rb);

            // Remove colliders to prevent interference
            Collider[] colliders = remoteHandObj.GetComponentsInChildren<Collider>();
            foreach (var col in colliders)
            {
                DestroyImmediate(col);
            }

            // Remove any grabbing/interaction scripts
            var grabbables = remoteHandObj.GetComponentsInChildren<Grabbable>();
            foreach (var grab in grabbables)
            {
                DestroyImmediate(grab);
            }

            // Parent to remote only objects
            if (remoteOnlyObjects != null)
            {
                remoteHandObj.transform.SetParent(remoteOnlyObjects.transform);
            }
            else
            {
                remoteHandObj.transform.SetParent(transform);
            }

            // Position at the corresponding hand position
            Transform targetHand = isLeft ? leftHand : rightHand;
            if (targetHand != null)
            {
                remoteHandObj.transform.position = targetHand.position;
                remoteHandObj.transform.rotation = targetHand.rotation;
            }

            // Store reference
            if (isLeft)
                leftHandRemote = remoteHand;
            else
                rightHandRemote = remoteHand;

            Debug.Log($"Created remote hand model: {remoteHandObj.name}");
        }
        
        private void Update()
        {
            if (isLocalPlayer)
            {
                // Send VR data to server at specified rate
                if (Time.time - lastSendTime > 1f / sendRate)
                {
                    SendVRData();
                    lastSendTime = Time.time;
                }
            }
            else
            {
                // Smooth remote player movement
                if (smoothRemoteMovement)
                {
                    SmoothRemoteMovement();
                }
                else
                {
                    ApplyNetworkTransforms();
                }
            }
        }
        
        [Command]
        private void SendVRData()
        {
            if (head != null)
            {
                networkHeadPosition = head.position;
                networkHeadRotation = head.rotation;
            }

            if (leftHand != null)
            {
                networkLeftHandPosition = leftHand.position;
                networkLeftHandRotation = leftHand.rotation;
            }

            if (rightHand != null)
            {
                networkRightHandPosition = rightHand.position;
                networkRightHandRotation = rightHand.rotation;
            }

            // Send hand pose data
            if (syncHandPoses && autoHandPlayer != null)
            {
                if (autoHandPlayer.handLeft != null)
                {
                    networkLeftGripAxis = autoHandPlayer.handLeft.GetGripAxis();
                    networkLeftSqueezeAxis = autoHandPlayer.handLeft.GetSqueezeAxis();
                    networkLeftIsGrabbing = autoHandPlayer.handLeft.IsGrabbing();
                }

                if (autoHandPlayer.handRight != null)
                {
                    networkRightGripAxis = autoHandPlayer.handRight.GetGripAxis();
                    networkRightSqueezeAxis = autoHandPlayer.handRight.GetSqueezeAxis();
                    networkRightIsGrabbing = autoHandPlayer.handRight.IsGrabbing();
                }
            }
        }
        
        private void SmoothRemoteMovement()
        {
            // Update targets when network values change
            if (Vector3.Distance(targetHeadPosition, networkHeadPosition) > 0.01f)
                targetHeadPosition = networkHeadPosition;
            if (Quaternion.Angle(targetHeadRotation, networkHeadRotation) > 1f)
                targetHeadRotation = networkHeadRotation;

            if (Vector3.Distance(targetLeftHandPosition, networkLeftHandPosition) > 0.01f)
                targetLeftHandPosition = networkLeftHandPosition;
            if (Quaternion.Angle(targetLeftHandRotation, networkLeftHandRotation) > 1f)
                targetLeftHandRotation = networkLeftHandRotation;

            if (Vector3.Distance(targetRightHandPosition, networkRightHandPosition) > 0.01f)
                targetRightHandPosition = networkRightHandPosition;
            if (Quaternion.Angle(targetRightHandRotation, networkRightHandRotation) > 1f)
                targetRightHandRotation = networkRightHandRotation;

            // Update hand pose targets
            if (syncHandPoses)
            {
                if (Mathf.Abs(targetLeftGripAxis - networkLeftGripAxis) > 0.01f)
                    targetLeftGripAxis = networkLeftGripAxis;
                if (Mathf.Abs(targetLeftSqueezeAxis - networkLeftSqueezeAxis) > 0.01f)
                    targetLeftSqueezeAxis = networkLeftSqueezeAxis;
                if (Mathf.Abs(targetRightGripAxis - networkRightGripAxis) > 0.01f)
                    targetRightGripAxis = networkRightGripAxis;
                if (Mathf.Abs(targetRightSqueezeAxis - networkRightSqueezeAxis) > 0.01f)
                    targetRightSqueezeAxis = networkRightSqueezeAxis;
            }

            // Smooth interpolation
            float deltaTime = Time.deltaTime * smoothingSpeed;

            if (head != null)
            {
                head.position = Vector3.Lerp(head.position, targetHeadPosition, deltaTime);
                head.rotation = Quaternion.Lerp(head.rotation, targetHeadRotation, deltaTime);
            }

            // Update remote hand models instead of original hands
            if (leftHandRemote != null)
            {
                leftHandRemote.transform.position = Vector3.Lerp(leftHandRemote.transform.position, targetLeftHandPosition, deltaTime);
                leftHandRemote.transform.rotation = Quaternion.Lerp(leftHandRemote.transform.rotation, targetLeftHandRotation, deltaTime);

                // Apply hand poses
                if (syncHandPoses)
                {
                    float currentLeftGrip = Mathf.Lerp(leftHandRemote.GetGripAxis(), targetLeftGripAxis, deltaTime);
                    float currentLeftSqueeze = Mathf.Lerp(leftHandRemote.GetSqueezeAxis(), targetLeftSqueezeAxis, deltaTime);
                    leftHandRemote.SetGrip(currentLeftGrip, currentLeftSqueeze);
                }
            }

            if (rightHandRemote != null)
            {
                rightHandRemote.transform.position = Vector3.Lerp(rightHandRemote.transform.position, targetRightHandPosition, deltaTime);
                rightHandRemote.transform.rotation = Quaternion.Lerp(rightHandRemote.transform.rotation, targetRightHandRotation, deltaTime);

                // Apply hand poses
                if (syncHandPoses)
                {
                    float currentRightGrip = Mathf.Lerp(rightHandRemote.GetGripAxis(), targetRightGripAxis, deltaTime);
                    float currentRightSqueeze = Mathf.Lerp(rightHandRemote.GetSqueezeAxis(), targetRightSqueezeAxis, deltaTime);
                    rightHandRemote.SetGrip(currentRightGrip, currentRightSqueeze);
                }
            }
        }
        
        private void ApplyNetworkTransforms()
        {
            if (head != null)
            {
                head.position = networkHeadPosition;
                head.rotation = networkHeadRotation;
            }

            // Apply to remote hand models instead of original hands
            if (leftHandRemote != null)
            {
                leftHandRemote.transform.position = networkLeftHandPosition;
                leftHandRemote.transform.rotation = networkLeftHandRotation;

                if (syncHandPoses)
                {
                    leftHandRemote.SetGrip(networkLeftGripAxis, networkLeftSqueezeAxis);
                }
            }

            if (rightHandRemote != null)
            {
                rightHandRemote.transform.position = networkRightHandPosition;
                rightHandRemote.transform.rotation = networkRightHandRotation;

                if (syncHandPoses)
                {
                    rightHandRemote.SetGrip(networkRightGripAxis, networkRightSqueezeAxis);
                }
            }
        }
        
        // Public methods for external access
        public bool IsLocalVRPlayer => isLocalPlayer;
        public Transform GetHead() => head;
        public Transform GetLeftHand() => leftHand;
        public Transform GetRightHand() => rightHand;
        public AutoHandPlayer GetAutoHandPlayer() => autoHandPlayer;

        // Setup methods
        [ContextMenu("Auto Setup VR References")]
        public void AutoSetupVRReferences()
        {
            SetupVRReferences();
        }

        public void SetupVRReferences()
        {
            // Find AutoHandPlayer if not assigned
            if (autoHandPlayer == null)
                autoHandPlayer = GetComponent<AutoHandPlayer>();

            if (autoHandPlayer == null)
                autoHandPlayer = GetComponentInChildren<AutoHandPlayer>();

            if (autoHandPlayer != null)
            {
                // Setup head/camera references
                if (head == null && autoHandPlayer.headCamera != null)
                    head = autoHandPlayer.headCamera.transform;

                if (vrCamera == null)
                    vrCamera = autoHandPlayer.headCamera;

                // Setup hand references
                if (leftHand == null && autoHandPlayer.handLeft != null)
                    leftHand = autoHandPlayer.handLeft.transform;

                if (rightHand == null && autoHandPlayer.handRight != null)
                    rightHand = autoHandPlayer.handRight.transform;

                Debug.Log("VRPlayer: AutoHand references setup successfully");
            }
            else
            {
                Debug.LogWarning("VRPlayer: AutoHandPlayer component not found!");
            }

            // Find AudioListener if not assigned
            if (audioListener == null)
                audioListener = GetComponentInChildren<AudioListener>();

            // Setup visual references automatically
            SetupVisualReferences();

            Debug.Log("VRPlayer: All references setup completed");
        }

        private void SetupVisualReferences()
        {
            // Find or create head visual
            if (headVisual == null)
            {
                headVisual = transform.Find("HeadVisual")?.gameObject;
                if (headVisual == null && head != null)
                {
                    // Create head visual
                    headVisual = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                    headVisual.name = "HeadVisual";
                    headVisual.transform.SetParent(head);
                    headVisual.transform.localPosition = Vector3.zero;
                    headVisual.transform.localScale = Vector3.one * 0.2f;

                    // Remove collider
                    if (headVisual.GetComponent<Collider>())
                        DestroyImmediate(headVisual.GetComponent<Collider>());
                }
            }

            // Setup remote hand models (these will be actual Hand components, not simple visuals)
            SetupRemoteHandModels();

            // Setup local/remote only objects
            if (localOnlyObjects == null)
            {
                localOnlyObjects = transform.Find("LocalOnly")?.gameObject;
                if (localOnlyObjects == null)
                {
                    localOnlyObjects = new GameObject("LocalOnly");
                    localOnlyObjects.transform.SetParent(transform);
                    localOnlyObjects.transform.localPosition = Vector3.zero;
                }
            }

            if (remoteOnlyObjects == null)
            {
                remoteOnlyObjects = transform.Find("RemoteOnly")?.gameObject;
                if (remoteOnlyObjects == null)
                {
                    remoteOnlyObjects = new GameObject("RemoteOnly");
                    remoteOnlyObjects.transform.SetParent(transform);
                    remoteOnlyObjects.transform.localPosition = Vector3.zero;

                    // Move visual objects to remote only
                    if (headVisual != null)
                        headVisual.transform.SetParent(remoteOnlyObjects.transform);
                    if (leftHandRemote != null)
                        leftHandRemote.transform.SetParent(remoteOnlyObjects.transform);
                    if (rightHandRemote != null)
                        rightHandRemote.transform.SetParent(remoteOnlyObjects.transform);
                }
            }
        }
    }
}
