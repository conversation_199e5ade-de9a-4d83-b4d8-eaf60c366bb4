using UnityEngine;
using <PERSON>;
using Autohand;

namespace VRMultiplayer
{
    public class VRPlayer : NetworkBehaviour
    {
        [Header("VR Components")]
        [SerializeField] private Transform head;
        [SerializeField] private Transform leftHand;
        [SerializeField] private Transform rightHand;
        [SerializeField] private Camera vrCamera;
        [SerializeField] private AudioListener audioListener;
        [SerializeField] private AutoHandPlayer autoHandPlayer;
        
        [<PERSON><PERSON>("Remote Player Visuals")]
        [SerializeField] private GameObject headVisual;
        [SerializeField] private GameObject leftHandVisual;
        [SerializeField] private GameObject rightHandVisual;
        [SerializeField] private GameObject localOnlyObjects;
        [SerializeField] private GameObject remoteOnlyObjects;
        
        [Header("Sync Settings")]
        [SerializeField] private float sendRate = 20f;
        [SerializeField] private bool smoothRemoteMovement = true;
        [SerializeField] private float smoothingSpeed = 15f;
        
        // Network sync variables
        [SyncVar] private Vector3 networkHeadPosition;
        [SyncVar] private Quaternion networkHeadRotation;
        [SyncVar] private Vector3 networkLeftHandPosition;
        [SyncVar] private Quaternion networkLeftHandRotation;
        [SyncVar] private Vector3 networkRightHandPosition;
        [SyncVar] private Quaternion networkRightHandRotation;
        
        // Smoothing targets for remote players
        private Vector3 targetHeadPosition;
        private Quaternion targetHeadRotation;
        private Vector3 targetLeftHandPosition;
        private Quaternion targetLeftHandRotation;
        private Vector3 targetRightHandPosition;
        private Quaternion targetRightHandRotation;
        
        private float lastSendTime;
        
        public override void OnStartLocalPlayer()
        {
            base.OnStartLocalPlayer();
            
            // Enable local player components
            if (vrCamera != null)
                vrCamera.enabled = true;
                
            if (audioListener != null)
                audioListener.enabled = true;
                
            if (autoHandPlayer != null)
                autoHandPlayer.enabled = true;
                
            if (localOnlyObjects != null)
                localOnlyObjects.SetActive(true);
                
            if (remoteOnlyObjects != null)
                remoteOnlyObjects.SetActive(false);
                
            // Find VR components if not assigned
            FindVRComponents();
            
            Debug.Log("Local VR Player started");
        }
        
        public override void OnStartClient()
        {
            base.OnStartClient();
            
            if (!isLocalPlayer)
            {
                // Disable local player components for remote players
                if (vrCamera != null)
                    vrCamera.enabled = false;
                    
                if (audioListener != null)
                    audioListener.enabled = false;
                    
                if (autoHandPlayer != null)
                    autoHandPlayer.enabled = false;
                    
                if (localOnlyObjects != null)
                    localOnlyObjects.SetActive(false);
                    
                if (remoteOnlyObjects != null)
                    remoteOnlyObjects.SetActive(true);
                    
                // Initialize smoothing targets
                InitializeSmoothingTargets();
                
                Debug.Log("Remote VR Player started");
            }
        }
        
        private void FindVRComponents()
        {
            if (autoHandPlayer == null)
                autoHandPlayer = GetComponent<AutoHandPlayer>();
                
            if (autoHandPlayer != null)
            {
                if (head == null && autoHandPlayer.headCamera != null)
                    head = autoHandPlayer.headCamera.transform;
                    
                if (vrCamera == null)
                    vrCamera = autoHandPlayer.headCamera;
                    
                if (leftHand == null && autoHandPlayer.handLeft != null)
                    leftHand = autoHandPlayer.handLeft.transform;
                    
                if (rightHand == null && autoHandPlayer.handRight != null)
                    rightHand = autoHandPlayer.handRight.transform;
            }
            
            if (audioListener == null)
                audioListener = GetComponentInChildren<AudioListener>();
        }
        
        private void InitializeSmoothingTargets()
        {
            if (head != null)
            {
                targetHeadPosition = head.position;
                targetHeadRotation = head.rotation;
            }
            
            if (leftHand != null)
            {
                targetLeftHandPosition = leftHand.position;
                targetLeftHandRotation = leftHand.rotation;
            }
            
            if (rightHand != null)
            {
                targetRightHandPosition = rightHand.position;
                targetRightHandRotation = rightHand.rotation;
            }
        }
        
        private void Update()
        {
            if (isLocalPlayer)
            {
                // Send VR data to server at specified rate
                if (Time.time - lastSendTime > 1f / sendRate)
                {
                    SendVRData();
                    lastSendTime = Time.time;
                }
            }
            else
            {
                // Smooth remote player movement
                if (smoothRemoteMovement)
                {
                    SmoothRemoteMovement();
                }
                else
                {
                    ApplyNetworkTransforms();
                }
            }
        }
        
        [Command]
        private void SendVRData()
        {
            if (head != null)
            {
                networkHeadPosition = head.position;
                networkHeadRotation = head.rotation;
            }
            
            if (leftHand != null)
            {
                networkLeftHandPosition = leftHand.position;
                networkLeftHandRotation = leftHand.rotation;
            }
            
            if (rightHand != null)
            {
                networkRightHandPosition = rightHand.position;
                networkRightHandRotation = rightHand.rotation;
            }
        }
        
        private void SmoothRemoteMovement()
        {
            // Update targets when network values change
            if (Vector3.Distance(targetHeadPosition, networkHeadPosition) > 0.01f)
                targetHeadPosition = networkHeadPosition;
            if (Quaternion.Angle(targetHeadRotation, networkHeadRotation) > 1f)
                targetHeadRotation = networkHeadRotation;
                
            if (Vector3.Distance(targetLeftHandPosition, networkLeftHandPosition) > 0.01f)
                targetLeftHandPosition = networkLeftHandPosition;
            if (Quaternion.Angle(targetLeftHandRotation, networkLeftHandRotation) > 1f)
                targetLeftHandRotation = networkLeftHandRotation;
                
            if (Vector3.Distance(targetRightHandPosition, networkRightHandPosition) > 0.01f)
                targetRightHandPosition = networkRightHandPosition;
            if (Quaternion.Angle(targetRightHandRotation, networkRightHandRotation) > 1f)
                targetRightHandRotation = networkRightHandRotation;
            
            // Smooth interpolation
            float deltaTime = Time.deltaTime * smoothingSpeed;
            
            if (head != null)
            {
                head.position = Vector3.Lerp(head.position, targetHeadPosition, deltaTime);
                head.rotation = Quaternion.Lerp(head.rotation, targetHeadRotation, deltaTime);
            }
            
            if (leftHand != null)
            {
                leftHand.position = Vector3.Lerp(leftHand.position, targetLeftHandPosition, deltaTime);
                leftHand.rotation = Quaternion.Lerp(leftHand.rotation, targetLeftHandRotation, deltaTime);
            }
            
            if (rightHand != null)
            {
                rightHand.position = Vector3.Lerp(rightHand.position, targetRightHandPosition, deltaTime);
                rightHand.rotation = Quaternion.Lerp(rightHand.rotation, targetRightHandRotation, deltaTime);
            }
        }
        
        private void ApplyNetworkTransforms()
        {
            if (head != null)
            {
                head.position = networkHeadPosition;
                head.rotation = networkHeadRotation;
            }
            
            if (leftHand != null)
            {
                leftHand.position = networkLeftHandPosition;
                leftHand.rotation = networkLeftHandRotation;
            }
            
            if (rightHand != null)
            {
                rightHand.position = networkRightHandPosition;
                rightHand.rotation = networkRightHandRotation;
            }
        }
        
        // Public methods for external access
        public bool IsLocalVRPlayer => isLocalPlayer;
        public Transform GetHead() => head;
        public Transform GetLeftHand() => leftHand;
        public Transform GetRightHand() => rightHand;
        public AutoHandPlayer GetAutoHandPlayer() => autoHandPlayer;
    }
}
