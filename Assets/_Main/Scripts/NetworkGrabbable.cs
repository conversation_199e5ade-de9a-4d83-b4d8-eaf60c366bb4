using UnityEngine;
using Mirror;
using Autohand;

namespace VRMultiplayer
{
    [RequireComponent(typeof(Grabbable))]
    public class NetworkGrabbable : NetworkBehaviour
    {
        [Header("Network Grabbable Settings")]
        [SerializeField] private bool autoAssignAuthority = true;
        [SerializeField] private bool releaseAuthorityOnDrop = true;
        [SerializeField] private float authorityTimeout = 30f; // Release authority after this time if not grabbed
        
        // Components
        private Grabbable grabbable;
        private RigidbodySync rigidbodySync;
        private NetworkIdentity netId;
        
        // State tracking
        [SyncVar] private uint currentGrabberNetId;
        [SyncVar] private bool isGrabbed;
        private VRPlayer currentGrabber;
        private float lastGrabTime;
        private bool hasLocalAuthority;
        
        // Events
        public System.Action<VRPlayer> OnNetworkGrabbed;
        public System.Action<VRPlayer> OnNetworkReleased;
        
        private void Awake()
        {
            grabbable = GetComponent<Grabbable>();
            rigidbodySync = GetComponent<RigidbodySync>();
            netId = GetComponent<NetworkIdentity>();
            
            if (rigidbodySync == null)
            {
                rigidbodySync = gameObject.AddComponent<RigidbodySync>();
            }
        }
        
        private void Start()
        {
            // Subscribe to grabbable events
            if (grabbable != null)
            {
                grabbable.OnGrabEvent += OnGrabbed;
                grabbable.OnReleaseEvent += OnReleased;
            }
        }
        
        private void OnDestroy()
        {
            // Unsubscribe from events
            if (grabbable != null)
            {
                grabbable.OnGrabEvent -= OnGrabbed;
                grabbable.OnReleaseEvent -= OnReleased;
            }
        }
        
        private void Update()
        {
            // Check for authority timeout
            if (hasLocalAuthority && !isGrabbed && Time.time - lastGrabTime > authorityTimeout)
            {
                ReleaseAuthority();
            }
        }
        
        private void OnGrabbed(Hand hand, Grabbable grab)
        {
            // Find the VR player that owns this hand
            VRPlayer vrPlayer = hand.GetComponentInParent<VRPlayer>();
            if (vrPlayer != null && vrPlayer.IsLocalVRPlayer)
            {
                // Request authority for this object
                if (autoAssignAuthority)
                {
                    RequestAuthority(vrPlayer);
                }
                
                lastGrabTime = Time.time;
            }
        }
        
        private void OnReleased(Hand hand, Grabbable grab)
        {
            // Find the VR player that owns this hand
            VRPlayer vrPlayer = hand.GetComponentInParent<VRPlayer>();
            if (vrPlayer != null && vrPlayer.IsLocalVRPlayer)
            {
                // Release authority if configured to do so
                if (releaseAuthorityOnDrop)
                {
                    ReleaseAuthority();
                }
                else
                {
                    lastGrabTime = Time.time; // Reset timeout
                }
            }
        }
        
        public void RequestAuthority(VRPlayer grabber)
        {
            if (grabber.IsLocalVRPlayer)
            {
                CmdRequestAuthority(grabber.netId);
            }
        }
        
        public void ReleaseAuthority()
        {
            if (hasLocalAuthority)
            {
                CmdReleaseAuthority();
            }
        }
        
        [Command(requiresAuthority = false)]
        private void CmdRequestAuthority(uint grabberNetId, NetworkConnectionToClient sender = null)
        {
            // Check if object is already grabbed by someone else
            if (isGrabbed && currentGrabberNetId != grabberNetId)
            {
                // Object is already grabbed by another player
                TargetGrabFailed(sender);
                return;
            }
            
            // Find the grabber's network identity
            if (NetworkServer.spawned.TryGetValue(grabberNetId, out NetworkIdentity grabberNetIdentity))
            {
                VRPlayer grabberPlayer = grabberNetIdentity.GetComponent<VRPlayer>();
                if (grabberPlayer != null)
                {
                    // Assign authority to the grabber's connection
                    netId.AssignClientAuthority(grabberNetIdentity.connectionToClient);
                    
                    // Update sync vars
                    currentGrabberNetId = grabberNetId;
                    isGrabbed = true;
                    
                    // Notify all clients
                    RpcOnGrabbed(grabberNetId);
                    
                    Debug.Log($"Authority assigned to player {grabberNetId} for object {gameObject.name}");
                }
            }
        }
        
        [Command(requiresAuthority = false)]
        private void CmdReleaseAuthority(NetworkConnectionToClient sender = null)
        {
            // Remove client authority
            if (netId.connectionToClient != null)
            {
                netId.RemoveClientAuthority();
            }
            
            // Update sync vars
            uint previousGrabber = currentGrabberNetId;
            currentGrabberNetId = 0;
            isGrabbed = false;
            
            // Notify all clients
            RpcOnReleased(previousGrabber);
            
            Debug.Log($"Authority released for object {gameObject.name}");
        }
        
        [TargetRpc]
        private void TargetGrabFailed(NetworkConnection target)
        {
            Debug.LogWarning($"Failed to grab {gameObject.name} - already grabbed by another player");
            
            // Force release the object locally if it was grabbed
            if (grabbable != null && grabbable.HeldBy() != null)
            {
                grabbable.HeldBy().Release();
            }
        }
        
        [ClientRpc]
        private void RpcOnGrabbed(uint grabberNetId)
        {
            // Find the grabber player
            if (NetworkClient.spawned.TryGetValue(grabberNetId, out NetworkIdentity grabberNetIdentity))
            {
                currentGrabber = grabberNetIdentity.GetComponent<VRPlayer>();
                OnNetworkGrabbed?.Invoke(currentGrabber);
            }
        }
        
        [ClientRpc]
        private void RpcOnReleased(uint previousGrabberNetId)
        {
            // Find the previous grabber player
            VRPlayer previousGrabber = null;
            if (NetworkClient.spawned.TryGetValue(previousGrabberNetId, out NetworkIdentity grabberNetIdentity))
            {
                previousGrabber = grabberNetIdentity.GetComponent<VRPlayer>();
            }
            
            currentGrabber = null;
            OnNetworkReleased?.Invoke(previousGrabber);
        }
        
        public override void OnStartAuthority()
        {
            base.OnStartAuthority();
            hasLocalAuthority = true;
            
            // Enable rigidbody sync authority
            if (rigidbodySync != null)
            {
                rigidbodySync.SetAuthority(true);
            }
            
            Debug.Log($"Gained authority over {gameObject.name}");
        }
        
        public override void OnStopAuthority()
        {
            base.OnStopAuthority();
            hasLocalAuthority = false;
            
            // Disable rigidbody sync authority
            if (rigidbodySync != null)
            {
                rigidbodySync.SetAuthority(false);
            }
            
            Debug.Log($"Lost authority over {gameObject.name}");
        }
        
        // Public properties and methods
        public bool IsGrabbed => isGrabbed;
        public VRPlayer CurrentGrabber => currentGrabber;
        public bool HasAuthority => hasLocalAuthority;
        public Grabbable GetGrabbable() => grabbable;
        
        // Force grab/release (for special cases)
        public void ForceGrab(VRPlayer grabber)
        {
            if (grabber.IsLocalVRPlayer)
            {
                CmdRequestAuthority(grabber.netId);
            }
        }
        
        public void ForceRelease()
        {
            if (hasLocalAuthority)
            {
                CmdReleaseAuthority();
            }
        }
        
        // Utility method to check if a specific player is grabbing this object
        public bool IsGrabbedBy(VRPlayer player)
        {
            return isGrabbed && currentGrabber == player;
        }
        
        // Get the hand that's currently grabbing this object (if any)
        public Hand GetGrabbingHand()
        {
            if (grabbable != null)
            {
                return grabbable.HeldBy();
            }
            return null;
        }
    }
}
