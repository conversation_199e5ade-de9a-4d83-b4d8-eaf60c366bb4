using UnityEngine;
using Autohand;
using Mirror;

namespace VRMultiplayer
{
    /// <summary>
    /// Helper component to sync hand poses and animations for remote players
    /// This ensures that remote players see realistic hand movements and grabbing animations
    /// </summary>
    public class HandPoseSync : NetworkBehaviour
    {
        [Header("Hand Pose Sync Settings")]
        [SerializeField] private Hand localHand;
        [SerializeField] private Hand remoteHand;
        [SerializeField] private bool isLeftHand = false;
        [SerializeField] private float poseUpdateRate = 30f;
        [SerializeField] private bool debugMode = false;
        
        [Header("Pose Smoothing")]
        [SerializeField] private bool smoothPoses = true;
        [SerializeField] private float poseSmoothSpeed = 10f;
        
        // Network sync variables for detailed hand pose
        [SyncVar] private float networkGripAxis;
        [SyncVar] private float networkSqueezeAxis;
        [SyncVar] private bool networkIsGrabbing;
        [SyncVar] private bool networkIsSqueezing;
        [SyncVar] private Vector3 networkHandPosition;
        [SyncVar] private Quaternion networkHandRotation;
        
        // Finger pose data (simplified)
        [SyncVar] private float networkThumbBend;
        [SyncVar] private float networkIndexBend;
        [SyncVar] private float networkMiddleBend;
        [SyncVar] private float networkRingBend;
        [SyncVar] private float networkPinkyBend;
        
        // Smoothing targets
        private float targetGripAxis;
        private float targetSqueezeAxis;
        private float targetThumbBend;
        private float targetIndexBend;
        private float targetMiddleBend;
        private float targetRingBend;
        private float targetPinkyBend;
        
        private float lastSendTime;
        private VRPlayer vrPlayer;
        
        private void Start()
        {
            vrPlayer = GetComponentInParent<VRPlayer>();
            
            // Auto-find hands if not assigned
            if (localHand == null || remoteHand == null)
            {
                AutoFindHands();
            }
            
            // Initialize targets
            InitializeTargets();
        }
        
        private void AutoFindHands()
        {
            if (vrPlayer == null) return;
            
            var autoHandPlayer = vrPlayer.GetAutoHandPlayer();
            if (autoHandPlayer == null) return;
            
            if (isLeftHand)
            {
                localHand = autoHandPlayer.handLeft;
                // Find remote left hand
                Transform remoteLeftTransform = vrPlayer.transform.Find("RemoteLeftHand");
                if (remoteLeftTransform != null)
                    remoteHand = remoteLeftTransform.GetComponent<Hand>();
            }
            else
            {
                localHand = autoHandPlayer.handRight;
                // Find remote right hand
                Transform remoteRightTransform = vrPlayer.transform.Find("RemoteRightHand");
                if (remoteRightTransform != null)
                    remoteHand = remoteRightTransform.GetComponent<Hand>();
            }
        }
        
        private void InitializeTargets()
        {
            if (localHand != null)
            {
                targetGripAxis = localHand.GetGripAxis();
                targetSqueezeAxis = localHand.GetSqueezeAxis();
            }
            
            // Initialize finger targets
            targetThumbBend = 0f;
            targetIndexBend = 0f;
            targetMiddleBend = 0f;
            targetRingBend = 0f;
            targetPinkyBend = 0f;
        }
        
        private void Update()
        {
            if (vrPlayer == null) return;
            
            if (vrPlayer.IsLocalVRPlayer)
            {
                // Send hand pose data at specified rate
                if (Time.time - lastSendTime > 1f / poseUpdateRate)
                {
                    SendHandPoseData();
                    lastSendTime = Time.time;
                }
            }
            else
            {
                // Apply received hand pose data to remote hand
                ApplyRemoteHandPose();
            }
        }
        
        [Command]
        private void SendHandPoseData()
        {
            if (localHand == null) return;
            
            // Basic hand data
            networkGripAxis = localHand.GetGripAxis();
            networkSqueezeAxis = localHand.GetSqueezeAxis();
            networkIsGrabbing = localHand.IsGrabbing();
            networkIsSqueezing = localHand.IsSqueezing();
            networkHandPosition = localHand.transform.position;
            networkHandRotation = localHand.transform.rotation;
            
            // Finger bend data (simplified)
            if (localHand.fingers != null && localHand.fingers.Length > 0)
            {
                networkThumbBend = GetFingerBend(0);
                networkIndexBend = GetFingerBend(1);
                networkMiddleBend = GetFingerBend(2);
                networkRingBend = GetFingerBend(3);
                networkPinkyBend = GetFingerBend(4);
            }
            
            if (debugMode)
            {
                Debug.Log($"[HandPoseSync] Sent pose data - Grip: {networkGripAxis:F2}, Squeeze: {networkSqueezeAxis:F2}, Grabbing: {networkIsGrabbing}");
            }
        }
        
        private float GetFingerBend(int fingerIndex)
        {
            if (localHand.fingers == null || fingerIndex >= localHand.fingers.Length)
                return 0f;
                
            var finger = localHand.fingers[fingerIndex];
            if (finger == null) return 0f;
            
            // Calculate average bend of finger joints
            float totalBend = 0f;
            int jointCount = 0;
            
            // This is a simplified calculation - you might want to implement more detailed finger tracking
            // For now, we'll use the grip axis as an approximation
            return localHand.GetGripAxis();
        }
        
        private void ApplyRemoteHandPose()
        {
            if (remoteHand == null) return;
            
            if (smoothPoses)
            {
                // Update targets
                UpdateTargets();
                
                // Smooth interpolation
                float deltaTime = Time.deltaTime * poseSmoothSpeed;
                
                float currentGrip = Mathf.Lerp(remoteHand.GetGripAxis(), targetGripAxis, deltaTime);
                float currentSqueeze = Mathf.Lerp(remoteHand.GetSqueezeAxis(), targetSqueezeAxis, deltaTime);
                
                // Apply smoothed values
                remoteHand.SetGrip(currentGrip, currentSqueeze);
                
                // Apply finger poses
                ApplyFingerPoses(deltaTime);
            }
            else
            {
                // Direct application
                remoteHand.SetGrip(networkGripAxis, networkSqueezeAxis);
                ApplyFingerPoses(1f);
            }
            
            // Update hand position (this might be handled by VRPlayer, but we can ensure it here)
            remoteHand.transform.position = networkHandPosition;
            remoteHand.transform.rotation = networkHandRotation;
            
            if (debugMode)
            {
                Debug.Log($"[HandPoseSync] Applied pose - Grip: {networkGripAxis:F2}, Squeeze: {networkSqueezeAxis:F2}");
            }
        }
        
        private void UpdateTargets()
        {
            if (Mathf.Abs(targetGripAxis - networkGripAxis) > 0.01f)
                targetGripAxis = networkGripAxis;
            if (Mathf.Abs(targetSqueezeAxis - networkSqueezeAxis) > 0.01f)
                targetSqueezeAxis = networkSqueezeAxis;
                
            // Update finger targets
            if (Mathf.Abs(targetThumbBend - networkThumbBend) > 0.01f)
                targetThumbBend = networkThumbBend;
            if (Mathf.Abs(targetIndexBend - networkIndexBend) > 0.01f)
                targetIndexBend = networkIndexBend;
            if (Mathf.Abs(targetMiddleBend - networkMiddleBend) > 0.01f)
                targetMiddleBend = networkMiddleBend;
            if (Mathf.Abs(targetRingBend - networkRingBend) > 0.01f)
                targetRingBend = networkRingBend;
            if (Mathf.Abs(targetPinkyBend - networkPinkyBend) > 0.01f)
                targetPinkyBend = networkPinkyBend;
        }
        
        private void ApplyFingerPoses(float lerpFactor)
        {
            if (remoteHand.fingers == null) return;
            
            // Apply finger bends (simplified)
            for (int i = 0; i < remoteHand.fingers.Length && i < 5; i++)
            {
                if (remoteHand.fingers[i] == null) continue;
                
                float targetBend = GetTargetFingerBend(i);
                float currentBend = remoteHand.fingers[i].GetCurrentBend();
                float newBend = Mathf.Lerp(currentBend, targetBend, lerpFactor);
                
                remoteHand.fingers[i].SetFingerBend(newBend);
            }
        }
        
        private float GetTargetFingerBend(int fingerIndex)
        {
            switch (fingerIndex)
            {
                case 0: return targetThumbBend;
                case 1: return targetIndexBend;
                case 2: return targetMiddleBend;
                case 3: return targetRingBend;
                case 4: return targetPinkyBend;
                default: return 0f;
            }
        }
        
        // Public methods for external control
        public void SetLocalHand(Hand hand)
        {
            localHand = hand;
        }
        
        public void SetRemoteHand(Hand hand)
        {
            remoteHand = hand;
        }
        
        public bool IsSetupCorrectly()
        {
            return localHand != null && remoteHand != null && vrPlayer != null;
        }
        
        // Debug methods
        [ContextMenu("Debug Hand Pose")]
        public void DebugHandPose()
        {
            if (localHand != null)
            {
                Debug.Log($"Local Hand - Grip: {localHand.GetGripAxis():F2}, Squeeze: {localHand.GetSqueezeAxis():F2}, Grabbing: {localHand.IsGrabbing()}");
            }
            
            if (remoteHand != null)
            {
                Debug.Log($"Remote Hand - Grip: {remoteHand.GetGripAxis():F2}, Squeeze: {remoteHand.GetSqueezeAxis():F2}");
            }
        }
    }
}
