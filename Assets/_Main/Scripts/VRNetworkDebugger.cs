using UnityEngine;
using Mirror;
using System.Collections.Generic;

namespace VRMultiplayer
{
    public class VRNetworkDebugger : MonoBehaviour
    {
        [Header("Debug Settings")]
        [SerializeField] private bool showDebugGUI = true;
        [SerializeField] private bool logNetworkEvents = true;
        [SerializeField] private float updateInterval = 1f;
        
        [Header("Visual Debug")]
        [SerializeField] private bool showPlayerPositions = true;
        [SerializeField] private Color localPlayerColor = Color.green;
        [SerializeField] private Color remotePlayerColor = Color.red;
        [SerializeField] private float gizmoSize = 0.2f;
        
        private VRNetworkManager networkManager;
        private List<VRPlayer> allPlayers = new List<VRPlayer>();
        private float lastUpdateTime;
        private Rect debugRect = new Rect(10, 10, 400, 300);
        
        private void Start()
        {
            networkManager = FindObjectOfType<VRNetworkManager>();
            
            if (logNetworkEvents)
            {
                Debug.Log("[VRNetworkDebugger] Started - monitoring network events");
            }
        }
        
        private void Update()
        {
            if (Time.time - lastUpdateTime > updateInterval)
            {
                UpdatePlayerList();
                lastUpdateTime = Time.time;
            }
        }
        
        private void UpdatePlayerList()
        {
            allPlayers.Clear();
            allPlayers.AddRange(FindObjectsOfType<VRPlayer>());
            
            if (logNetworkEvents && allPlayers.Count > 0)
            {
                Debug.Log($"[VRNetworkDebugger] Found {allPlayers.Count} VR players");
                
                foreach (var player in allPlayers)
                {
                    string playerType = player.IsLocalVRPlayer ? "LOCAL" : "REMOTE";
                    Vector3 headPos = player.GetHead() != null ? player.GetHead().position : Vector3.zero;
                    Debug.Log($"[VRNetworkDebugger] Player {player.netId} ({playerType}): Head at {headPos}");
                }
            }
        }
        
        private void OnGUI()
        {
            if (!showDebugGUI) return;
            
            GUI.Box(debugRect, "VR Network Debug");
            
            GUILayout.BeginArea(new Rect(debugRect.x + 10, debugRect.y + 25, debugRect.width - 20, debugRect.height - 35));
            
            // Network status
            GUILayout.Label($"Network Status:", EditorGUIUtility.isProSkin ? GUI.skin.label : GUI.skin.box);
            GUILayout.Label($"Server Active: {NetworkServer.active}");
            GUILayout.Label($"Client Active: {NetworkClient.active}");
            GUILayout.Label($"Connected: {NetworkClient.isConnected}");
            
            if (networkManager != null)
            {
                GUILayout.Label($"Network Address: {networkManager.networkAddress}");
            }
            
            GUILayout.Space(10);
            
            // Players info
            GUILayout.Label($"Players ({allPlayers.Count}):");
            
            foreach (var player in allPlayers)
            {
                if (player == null) continue;
                
                string playerInfo = $"Player {player.netId}: ";
                playerInfo += player.IsLocalVRPlayer ? "LOCAL" : "REMOTE";
                
                if (player.hasAuthority)
                    playerInfo += " (Authority)";
                    
                GUILayout.Label(playerInfo);
                
                // Show position info
                if (player.GetHead() != null)
                {
                    Vector3 headPos = player.GetHead().position;
                    GUILayout.Label($"  Head: {headPos.ToString("F2")}");
                }
            }
            
            GUILayout.Space(10);
            
            // Debug controls
            if (GUILayout.Button("Refresh Player List"))
            {
                UpdatePlayerList();
            }
            
            if (GUILayout.Button("Log Network Info"))
            {
                LogDetailedNetworkInfo();
            }
            
            if (GUILayout.Button("Test Sync"))
            {
                TestSyncData();
            }
            
            GUILayout.EndArea();
        }
        
        private void LogDetailedNetworkInfo()
        {
            Debug.Log("=== VR Network Debug Info ===");
            Debug.Log($"NetworkServer.active: {NetworkServer.active}");
            Debug.Log($"NetworkClient.active: {NetworkClient.active}");
            Debug.Log($"NetworkClient.isConnected: {NetworkClient.isConnected}");
            Debug.Log($"NetworkServer.connections.Count: {NetworkServer.connections.Count}");
            
            if (NetworkServer.active)
            {
                foreach (var conn in NetworkServer.connections.Values)
                {
                    Debug.Log($"Server Connection {conn.connectionId}: {conn.address}");
                }
            }
            
            Debug.Log($"Total VRPlayers: {allPlayers.Count}");
            
            foreach (var player in allPlayers)
            {
                if (player == null) continue;
                
                Debug.Log($"VRPlayer {player.netId}:");
                Debug.Log($"  - IsLocalPlayer: {player.IsLocalVRPlayer}");
                Debug.Log($"  - HasAuthority: {player.hasAuthority}");
                Debug.Log($"  - Head: {(player.GetHead() != null ? player.GetHead().position.ToString("F2") : "NULL")}");
                Debug.Log($"  - LeftHand: {(player.GetLeftHand() != null ? player.GetLeftHand().position.ToString("F2") : "NULL")}");
                Debug.Log($"  - RightHand: {(player.GetRightHand() != null ? player.GetRightHand().position.ToString("F2") : "NULL")}");
            }
            
            Debug.Log("=== End Debug Info ===");
        }
        
        private void TestSyncData()
        {
            Debug.Log("=== Testing Sync Data ===");
            
            foreach (var player in allPlayers)
            {
                if (player == null) continue;
                
                // Enable debug sync for this player
                var vrPlayerScript = player.GetComponent<VRPlayer>();
                if (vrPlayerScript != null)
                {
                    // Use reflection to set debugSync field
                    var field = typeof(VRPlayer).GetField("debugSync", 
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                    if (field != null)
                    {
                        field.SetValue(vrPlayerScript, true);
                        Debug.Log($"Enabled debug sync for player {player.netId}");
                    }
                }
            }
            
            Debug.Log("Debug sync enabled for all players. Check console for sync messages.");
        }
        
        private void OnDrawGizmos()
        {
            if (!showPlayerPositions) return;
            
            foreach (var player in allPlayers)
            {
                if (player == null || player.GetHead() == null) continue;
                
                // Set color based on player type
                Gizmos.color = player.IsLocalVRPlayer ? localPlayerColor : remotePlayerColor;
                
                // Draw head position
                Vector3 headPos = player.GetHead().position;
                Gizmos.DrawWireSphere(headPos, gizmoSize);
                
                // Draw hands if available
                if (player.GetLeftHand() != null)
                {
                    Vector3 leftHandPos = player.GetLeftHand().position;
                    Gizmos.DrawWireCube(leftHandPos, Vector3.one * gizmoSize * 0.5f);
                    Gizmos.DrawLine(headPos, leftHandPos);
                }
                
                if (player.GetRightHand() != null)
                {
                    Vector3 rightHandPos = player.GetRightHand().position;
                    Gizmos.DrawWireCube(rightHandPos, Vector3.one * gizmoSize * 0.5f);
                    Gizmos.DrawLine(headPos, rightHandPos);
                }
                
                // Draw player ID
                #if UNITY_EDITOR
                UnityEditor.Handles.Label(headPos + Vector3.up * 0.3f, 
                    $"Player {player.netId}\n{(player.IsLocalVRPlayer ? "LOCAL" : "REMOTE")}");
                #endif
            }
        }
        
        // Public methods for external debugging
        public void EnableDebugForAllPlayers()
        {
            TestSyncData();
        }
        
        public void LogPlayerPositions()
        {
            Debug.Log("=== Player Positions ===");
            foreach (var player in allPlayers)
            {
                if (player == null) continue;
                
                string posInfo = $"Player {player.netId} ({(player.IsLocalVRPlayer ? "LOCAL" : "REMOTE")}):";
                
                if (player.GetHead() != null)
                    posInfo += $" Head={player.GetHead().position.ToString("F2")}";
                if (player.GetLeftHand() != null)
                    posInfo += $" LeftHand={player.GetLeftHand().position.ToString("F2")}";
                if (player.GetRightHand() != null)
                    posInfo += $" RightHand={player.GetRightHand().position.ToString("F2")}";
                    
                Debug.Log(posInfo);
            }
        }
        
        [ContextMenu("Force Update Player List")]
        public void ForceUpdatePlayerList()
        {
            UpdatePlayerList();
        }
        
        [ContextMenu("Log Network Info")]
        public void ForceLogNetworkInfo()
        {
            LogDetailedNetworkInfo();
        }
    }
}
