# VR Multiplayer System for Unity with AutoHand & Mirror

Hệ thống VR Multiplayer hoàn chỉnh cho Unity 6000.0.34f1, tối ưu cho Oculus Quest 2/3, PC và WebGL.

## Tính năng chính

- ✅ VR Multiplayer với AutoHand và Mirror
- ✅ Sync đầu và tay VR mượt mà
- ✅ Network authority cho grabbable objects
- ✅ LAN Discovery tự động
- ✅ Tối ưu hóa cho Quest, PC và WebGL
- ✅ Editor tool để setup 1-click
- ✅ Adaptive quality dựa trên performance

## Cấu trúc Scripts

### Core Scripts
- **VRNetworkManager.cs**: NetworkManager chính với LAN discovery
- **VRPlayer.cs**: Sync VR rig (head, hands) qua network
- **RigidbodySync.cs**: Sync physics objects với authority
- **NetworkGrabbable.cs**: Xử lý authority khi grab/release objects
- **VROptimizer.cs**: Tối ưu hóa performance cho từng platform

### Editor Tools
- **VRMultiplayerSetup.cs**: Editor window để setup scene tự động

## Hướng dẫn Setup

### 1. Auto Setup (Khuyến nghị)
1. Mở **Window > VR Multiplayer > Setup Scene**
2. Click **"Auto Configure Scene"** để tạo NetworkManager
3. Click **"Create VR Player Prefab"** để tạo player prefab
4. Click **"Setup Grabbable Objects"** để add networking cho grabbables

### 2. Manual Setup

#### Tạo NetworkManager:
```csharp
// Tạo GameObject mới
GameObject nmGO = new GameObject("VRNetworkManager");

// Add components
VRNetworkManager networkManager = nmGO.AddComponent<VRNetworkManager>();
kcp2k.KcpTransport transport = nmGO.AddComponent<kcp2k.KcpTransport>();
Mirror.Discovery.NetworkDiscovery discovery = nmGO.AddComponent<Mirror.Discovery.NetworkDiscovery>();

// Configure
networkManager.transport = transport;
```

#### Tạo VR Player Prefab:
1. Duplicate AutoHandPlayer prefab
2. Add **NetworkIdentity** component
3. Add **VRPlayer** component
4. Assign references (head, hands, camera, etc.)
5. Save as prefab và assign vào NetworkManager

#### Setup Grabbable Objects:
```csharp
// Cho mỗi grabbable object:
gameObject.AddComponent<NetworkIdentity>();
gameObject.AddComponent<NetworkGrabbable>();
gameObject.AddComponent<RigidbodySync>(); // Nếu có Rigidbody
```

## Cách sử dụng

### Host Game:
```csharp
VRNetworkManager networkManager = FindObjectOfType<VRNetworkManager>();
networkManager.StartHostButton();
```

### Join Game:
```csharp
// Tự động tìm server qua LAN
networkManager.StartClientButton();

// Hoặc connect trực tiếp qua IP
networkManager.networkAddress = "*************";
networkManager.StartClient();
```

### Disconnect:
```csharp
networkManager.DisconnectButton();
```

## Tối ưu hóa Performance

### VROptimizer Component
Add **VROptimizer** vào scene để tự động tối ưu:

```csharp
// Tối ưu thủ công
VROptimizer optimizer = FindObjectOfType<VROptimizer>();
optimizer.OptimizeForPlatform();

// Set quality level (0=Low, 1=Medium, 2=High)
optimizer.SetQualityLevel(1);
```

### Platform-specific Settings

#### Quest 2/3:
- Render Scale: 0.8x
- Quality: Medium
- Physics: 4 solver iterations
- Network Send Rate: 20Hz

#### WebGL:
- Render Scale: 0.6x
- Quality: Low
- Physics: 2 solver iterations
- Network Send Rate: 10Hz

#### PC:
- Render Scale: 1.0x
- Quality: High
- Physics: 6 solver iterations
- Network Send Rate: 30Hz

## Build Settings

### Quest Build:
1. **Platform**: Android
2. **XR Plugin**: Oculus XR Plugin
3. **Scripting Backend**: IL2CPP
4. **Target Architectures**: ARM64
5. **Minimum API Level**: 23
6. **Target API Level**: 32+

### WebGL Build:
1. **Platform**: WebGL
2. **Compression Format**: Gzip
3. **Code Optimization**: Size
4. **Strip Engine Code**: Enabled

## Testing

### Local Testing:
1. Build cho Quest
2. Một Quest làm Host
3. Một Quest làm Client (auto-discover hoặc nhập IP)

### Editor Testing:
1. Play trong Editor làm Host
2. Build APK làm Client
3. Connect qua LAN

## Troubleshooting

### Common Issues:

#### "No servers found":
- Kiểm tra cùng WiFi network
- Tắt firewall tạm thời
- Thử nhập IP thủ công

#### "Authority not assigned":
- Kiểm tra NetworkIdentity trên grabbable
- Đảm bảo object có NetworkGrabbable component
- Check console logs cho errors

#### Performance issues:
- Giảm Network Send Rate
- Enable VROptimizer
- Giảm Render Scale
- Tắt shadows cho WebGL

### Debug Commands:
```csharp
// Check network status
Debug.Log($"Server: {NetworkServer.active}, Client: {NetworkClient.active}");

// Check authority
Debug.Log($"Has authority: {netIdentity.hasAuthority}");

// Performance metrics
VROptimizer optimizer = FindObjectOfType<VROptimizer>();
Debug.Log($"FPS: {optimizer.GetCurrentFPS()}");
```

## Advanced Features

### Custom Network Messages:
```csharp
// Send custom data
[Command]
void CmdCustomAction(string data)
{
    // Server logic
    RpcCustomAction(data);
}

[ClientRpc]
void RpcCustomAction(string data)
{
    // Client logic
}
```

### Authority Management:
```csharp
NetworkGrabbable grabbable = obj.GetComponent<NetworkGrabbable>();

// Force grab
grabbable.ForceGrab(vrPlayer);

// Force release
grabbable.ForceRelease();

// Check status
bool isGrabbed = grabbable.IsGrabbed;
VRPlayer grabber = grabbable.CurrentGrabber;
```

## Dependencies

- Unity 6000.0.34f1+
- AutoHand VR Interaction Toolkit
- Mirror Networking
- Oculus XR Plugin (cho Quest)
- XR Interaction Toolkit (optional)

## Support

Để được hỗ trợ, vui lòng:
1. Check console logs cho errors
2. Verify network connectivity
3. Test với simple scene trước
4. Check Unity/Quest compatibility

---

**Lưu ý**: Hệ thống này được tối ưu cho Quest 2/3 nhưng cũng hoạt động trên PC VR và WebGL. Luôn test trên target platform trước khi deploy.
