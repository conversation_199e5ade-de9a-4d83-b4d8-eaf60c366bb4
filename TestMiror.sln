
Microsoft Visual Studio Solution File, Format Version 11.00
# Visual Studio 2010
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AutoHandAssembly", "AutoHandAssembly.csproj", "{3eb88724-83f6-22aa-9cca-deabb4d4cc58}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Edgegap", "Edgegap.csproj", "{9d828c61-a66f-1ada-e616-579745d1c4a1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Unity.Mirror.CodeGen", "Unity.Mirror.CodeGen.csproj", "{30cb1e90-cf61-e483-6358-3656a9287074}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.Components", "Mirror.Components.csproj", "{50bad475-eb9f-3e56-36b3-f87dac61938b}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NaughtyAttributes.Core", "NaughtyAttributes.Core.csproj", "{21023906-c209-a280-1e29-78e5a331df82}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.Editor", "Mirror.Editor.csproj", "{004ebb73-6586-d026-f836-5cae3f0cdfb7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.Examples", "Mirror.Examples.csproj", "{98cd5523-d2c0-0464-9c31-25092a063b7d}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "kcp2k", "kcp2k.csproj", "{b5553746-f040-0c7f-ea4b-1dd40530a931}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror", "Mirror.csproj", "{8b7f94a5-ae18-09dc-4728-e3d72c8494f7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SimpleWebTransport", "SimpleWebTransport.csproj", "{76dc48c3-59c8-e6ea-97ff-619f724c24f2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Autohand.Editor", "Autohand.Editor.csproj", "{b0f17e5c-1c00-0aed-d182-6820a7ec8703}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.Authenticators", "Mirror.Authenticators.csproj", "{92c0bcfb-2b70-7bfe-bf15-c6b20e48e23e}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.Transports", "Mirror.Transports.csproj", "{e7f5e943-b3fc-fb7f-f1e6-e8b5c97820d6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "EncryptionTransportEditor", "EncryptionTransportEditor.csproj", "{51b2eda4-d4ea-4b47-ccce-00ffbddee616}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NaughtyAttributes.Editor", "NaughtyAttributes.Editor.csproj", "{4e3bdce2-6731-a9d6-e1d4-aafaacaa8519}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp", "Assembly-CSharp.csproj", "{9f422d3b-5e25-65b0-4a7d-28dea93a6a29}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Telepathy", "Telepathy.csproj", "{87cd3357-f54d-b193-fffe-3c2ddd12a07a}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Assembly-CSharp-Editor", "Assembly-CSharp-Editor.csproj", "{56e3b039-6fdf-5bb2-eac3-48bd57a304ea}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Autohand.XR", "Autohand.XR.csproj", "{0623169f-9d66-df4e-8216-56a3eb44aa40}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Wingman", "Wingman.csproj", "{868405f9-9c1d-a72e-570b-ffc56fdedf13}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Mirror.CompilerSymbols", "Mirror.CompilerSymbols.csproj", "{a35d96be-6b32-97e5-a962-1b49188f00ef}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Autohand.XR.Editor", "Autohand.XR.Editor.csproj", "{a19fee7f-ec78-25f6-1a60-b76adf1f3e2e}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{3eb88724-83f6-22aa-9cca-deabb4d4cc58}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3eb88724-83f6-22aa-9cca-deabb4d4cc58}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9d828c61-a66f-1ada-e616-579745d1c4a1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9d828c61-a66f-1ada-e616-579745d1c4a1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{30cb1e90-cf61-e483-6358-3656a9287074}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{30cb1e90-cf61-e483-6358-3656a9287074}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50bad475-eb9f-3e56-36b3-f87dac61938b}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50bad475-eb9f-3e56-36b3-f87dac61938b}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{21023906-c209-a280-1e29-78e5a331df82}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{21023906-c209-a280-1e29-78e5a331df82}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{004ebb73-6586-d026-f836-5cae3f0cdfb7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{004ebb73-6586-d026-f836-5cae3f0cdfb7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{98cd5523-d2c0-0464-9c31-25092a063b7d}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{98cd5523-d2c0-0464-9c31-25092a063b7d}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{b5553746-f040-0c7f-ea4b-1dd40530a931}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{b5553746-f040-0c7f-ea4b-1dd40530a931}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8b7f94a5-ae18-09dc-4728-e3d72c8494f7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8b7f94a5-ae18-09dc-4728-e3d72c8494f7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{76dc48c3-59c8-e6ea-97ff-619f724c24f2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{76dc48c3-59c8-e6ea-97ff-619f724c24f2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{b0f17e5c-1c00-0aed-d182-6820a7ec8703}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{b0f17e5c-1c00-0aed-d182-6820a7ec8703}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92c0bcfb-2b70-7bfe-bf15-c6b20e48e23e}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92c0bcfb-2b70-7bfe-bf15-c6b20e48e23e}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{e7f5e943-b3fc-fb7f-f1e6-e8b5c97820d6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{e7f5e943-b3fc-fb7f-f1e6-e8b5c97820d6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{51b2eda4-d4ea-4b47-ccce-00ffbddee616}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{51b2eda4-d4ea-4b47-ccce-00ffbddee616}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4e3bdce2-6731-a9d6-e1d4-aafaacaa8519}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4e3bdce2-6731-a9d6-e1d4-aafaacaa8519}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9f422d3b-5e25-65b0-4a7d-28dea93a6a29}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9f422d3b-5e25-65b0-4a7d-28dea93a6a29}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{87cd3357-f54d-b193-fffe-3c2ddd12a07a}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{87cd3357-f54d-b193-fffe-3c2ddd12a07a}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{56e3b039-6fdf-5bb2-eac3-48bd57a304ea}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{56e3b039-6fdf-5bb2-eac3-48bd57a304ea}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0623169f-9d66-df4e-8216-56a3eb44aa40}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0623169f-9d66-df4e-8216-56a3eb44aa40}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{868405f9-9c1d-a72e-570b-ffc56fdedf13}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{868405f9-9c1d-a72e-570b-ffc56fdedf13}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{a35d96be-6b32-97e5-a962-1b49188f00ef}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{a35d96be-6b32-97e5-a962-1b49188f00ef}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{a19fee7f-ec78-25f6-1a60-b76adf1f3e2e}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{a19fee7f-ec78-25f6-1a60-b76adf1f3e2e}.Debug|Any CPU.Build.0 = Debug|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
EndGlobal
